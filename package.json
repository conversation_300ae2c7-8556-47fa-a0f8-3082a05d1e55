{"name": "cube", "version": "1.1.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "ios:simulator": "IOS_SIMULATOR=true expo run:ios", "web": "expo start --web", "lint": "npx prettier --check ./src && npx eslint --max-warnings=0 ./src", "eas-build-pre-install": "npm config set legacy-peer-deps true", "build:local:ios": "npx eas-cli build --local --platform ios --profile phSit --no-wait --clear-cache --non-interactive", "applyEnv": "node ./scripts/applyEnv.js", "applyEnv:phDev": "node ./scripts/applyEnv.js phDev", "applyEnv:myDev": "node ./scripts/applyEnv.js myDev", "applyEnv:ibDev": "node ./scripts/applyEnv.js ibDev", "applyEnv:idDev": "node ./scripts/applyEnv.js idDev", "postinstall": "patch-package", "test": "jest", "verifyBundle": "node ./scripts/verifyBundle.js"}, "dependencies": {"@config-plugins/react-native-webrtc": "9.0.0", "@emotion/native": "^11.10.6", "@emotion/react": "^11.10.6", "@expo/html-elements": "^0.5.1", "@expo/webpack-config": "^19.0.0", "@gorhom/bottom-sheet": "^4.4.6", "@gorhom/portal": "^1.0.14", "@hookform/resolvers": "^3.1.0", "@neverdull-agency/expo-unlimited-secure-store": "^1.0.10", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^18.3.0", "@react-native-firebase/app": "^18.3.0", "@react-native-firebase/crashlytics": "^18.3.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/bottom-tabs": "^6.5.7", "@react-navigation/drawer": "^6.6.3", "@react-navigation/material-top-tabs": "^6.6.2", "@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "@ronradtke/react-native-markdown-display": "^8.0.0", "@shopify/flash-list": "1.7.3", "@shopify/react-native-skia": "1.5.0", "@tanstack/react-query": "^4.26.1", "@types/jest": "^29.5.12", "@types/ramda": "^0.29.2", "agent-guru": "^1.6.40", "axios": "^1.7.9", "compare-versions": "^6.0.0", "contentstack": "^3.17.1", "core-js": "^3.36.0", "crypto-es": "^2.0.4", "cube-ui-components": "^1.0.1-186", "d3": "^7.4.4", "d3-shape": "^3.2.0", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "expo": "~52.0.46", "expo-auth-session": "~6.0.0", "expo-av": "~15.0.1", "expo-blur": "~14.0.1", "expo-build-properties": "~0.13.3", "expo-camera": "~16.0.18", "expo-clipboard": "~7.0.0", "expo-constants": "~17.0.8", "expo-contacts": "~14.0.2", "expo-crypto": "~14.0.1", "expo-dev-client": "~5.0.20", "expo-device": "~7.0.3", "expo-document-picker": "~13.0.1", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.0", "expo-image": "~2.0.7", "expo-image-manipulator": "~13.0.5", "expo-image-picker": "~16.0.3", "expo-intent-launcher": "~12.0.1", "expo-linear-gradient": "~14.0.1", "expo-linking": "~7.0.3", "expo-local-authentication": "~15.0.1", "expo-media-library": "~17.0.6", "expo-network": "~7.0.2", "expo-notifications": "~0.29.14", "expo-screen-orientation": "~8.0.0", "expo-secure-store": "~14.0.0", "expo-sensors": "~14.0.1", "expo-sharing": "~13.0.0", "expo-sms": "~13.0.0", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.0", "expo-video": "~2.0.6", "expo-web-browser": "~14.0.2", "fuse.js": "^6.6.2", "google-libphonenumber": "^3.2.34", "humps": "^2.0.1", "i18next": "^22.4.11", "immer": "^9.0.19", "intl-pluralrules": "^1.3.1", "jest": "^29.2.1", "jest-expo": "~52.0.6", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.10.57", "lodash": "^4.17.21", "lottie-react-native": "7.1.0", "ramda": "^0.29.0", "react": "18.3.1", "react-dom": "18.3.1", "react-hook-form": "^7.43.5", "react-i18next": "^12.2.0", "react-native": "0.76.9", "react-native-blob-util": "^0.19.6", "react-native-calendars": "^1.1301.0", "react-native-circular-progress": "^1.4.1", "react-native-draggable-flatlist": "^4.0.1", "react-native-drax": "^0.10.3", "react-native-drop-shadow": "^1.0.0", "react-native-fast-opencv": "0.4.3-custom", "react-native-fast-shadow": "^0.1.1", "react-native-gesture-handler": "~2.20.2", "react-native-incall-manager": "^4.2.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-keyboard-controller": "^1.14.5", "react-native-mask-text": "^0.13.2", "react-native-modal": "^13.0.1", "react-native-outside-press": "^1.2.2", "react-native-pager-view": "6.5.1", "react-native-pdf": "^6.7.4", "react-native-qrcode-svg": "^6.3.2", "react-native-reanimated": "~3.16.1", "react-native-reanimated-zoom": "^0.3.3", "react-native-root-siblings": "^4.1.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-share": "^10.2.1", "react-native-svg": "15.8.0", "react-native-svg-charts": "^5.4.0", "react-native-tab-view": "^3.5.1", "react-native-uuid": "^2.0.1", "react-native-view-shot": "4.0.3", "react-native-vision-camera": "^4.6.4", "react-native-vision-camera-face-detector": "^1.8.1", "react-native-walkthrough-tooltip": "^1.6.0", "react-native-web": "~0.19.6", "react-native-webrtc": "118.0.7", "react-native-webview": "13.12.5", "react-native-worklets-core": "^1.5.0", "react-string-replace": "^1.1.0", "vision-camera-resize-plugin": "^3.2.0", "yup": "^1.0.2", "zustand": "^4.3.6"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-react": "^7.23.3", "@tanstack/eslint-plugin-query": "^4.34.1", "@types/d3": "^7.4.0", "@types/google-libphonenumber": "^7.4.30", "@types/humps": "^2.0.2", "@types/lodash": "^4.17.15", "@types/ramda": "^0.29.2", "@types/react": "~18.3.12", "@types/react-native-svg-charts": "^5.0.13", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "@welldone-software/why-did-you-render": "^7.0.1", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.0.0", "eslint-plugin-unused-imports": "^2.0.0", "metro-react-native-babel-preset": "^0.76.0", "obfuscator-io-metro-plugin": "^2.1.3", "patch-package": "^8.0.0", "prettier": "^2.8.4", "typescript": "~5.3.3"}, "resolutions": {"@neverdull-agency/expo-unlimited-secure-store/crypto-js": "4.2.0", "@expo/webpack-config/webpack-dev-server/http-proxy-middleware": "2.0.7", "@expo/webpack-config/expo-pwa/@expo/image-utils/semver": "7.6.3", "@expo/webpack-config/webpack": "5.95.0", "expo/@expo/cli/send": "0.19.1", "cross-spawn": "7.0.6", "serve-static": "1.16.2"}, "private": true, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}