const country = process.env.COUNTRY;
const build = process.env.BUILD;
const version = process.env.VERSION;
const baseUrl = process.env.BASE_URL;
const ocrUrl = process.env.OCR_URL;
const irisUrl = process.env.IRIS_URL;
const eIrisUrl = process.env.EIRIS_URL;
const salesConnectUrlForgotPassword =
  process.env.SALES_CONNECT_URL_FORGOT_PASSWORD;
const salesConnectUrlLogin = process.env.SALES_CONNECT_URL_LOGIN;
const fwdPintrAppleStoreUrl = process.env.FWD_PINTR_APPLE_STORE_URL;
const fwdPintrGooglePlayStoreUrl = process.env.FWD_PINTR_GOOGLE_PLAY_STORE_URL;
const smartUrl = process.env.SMART_URL;
const surveyUrl = process.env.SURVEY_URL;
const smartToken = process.env.SMART_TOKEN;
const smartUserToken = process.env.SMART_USER_TOKEN;
const smartUserAgent = process.env.SMART_USER_AGENT;
const dragonPayToken = process.env.DRAGON_PAY_TOKEN;
const dragonPaySecretKey = process.env.DRAGON_PAY_SECRET_KEY;
const ocrToken = process.env.OCR_TOKEN;
const idpClientId = process.env.IDP_CLIENT_ID;
const idpClientSecret = process.env.IDP_CLIENT_KEY;
const fullRemoteSellingEnabled = process.env.FULL_REMOTE_SELLING_ENABLED;
const enhancedRemoteSellingEnabled =
  process.env.ENHANCED_REMOTE_SELLING_ENABLED;
const aiBotContentStackId = process.env.AIBOT_CONTENT_STACK_ID;
const aiBotContentStackApiKey = process.env.AIBOT_CONTENT_STACK_API_KEY;
const aiBotContentStackDeliveryToken =
  process.env.AIBOT_CONTENT_STACK_DELIVERY_TOKEN;
const aiBotContentStackEnvironment =
  process.env.AIBOT_CONTENT_STACK_ENVIRONMENT;
const ecoachBaseUrl = process.env.ECOACH_BASE_URL;
const ecoachPublicConversationWebsocketUrl =
  process.env.ECOACH_PUBLIC_CONVERSATION_WEBSOCKET_URL;
const ecoachPublicAvatarWebsocketUrl =
  process.env.ECOACH_PUBLIC_AVATAR_WEBSOCKET_URL;
const ecoachPublicConversationApi = process.env.ECOACH_PUBLIC_CONVERSATION_API;
const ecoachTurnServerUrl = process.env.ECOACH_TURN_SERVER_URL;
const ecoachTurnServerUser = process.env.ECOACH_TURN_SERVER_USER;
const ecoachTurnServerPass = process.env.ECOACH_TURN_SERVER_PASS;
const startClaimUrl = process.env.START_CLAIM_URL;
const agencyTermsAndConditionsUrl = process.env.AGENCY_TERMS_CONDITIONS_URL;
const bancaTermsAndConditionsUrl = process.env.BANCA_TERMS_CONDITIONS_URL;

const schemeSuffix = build !== 'prd' ? `.${build}` : '';
const contentStackKey = process.env.CONTENT_STACK_KEY;
const contentStackDeliveryToken = process.env.CONTENT_STACK_DELIVERY_TOKEN;
const contentStackEnvironment = process.env.CONTENT_STACK_ENVIRONMENT;
const contentStackSettingEntryId = process.env.CONTENT_STACK_SETTING_ENTRY_ID;
const scheme = `${country}.com.fwd.cube${schemeSuffix}`;

const bundleIdentifier = {
  ios: `${country}.com.fwd.cube${schemeSuffix}`,
  android: `${country}.com.fwd.cube${schemeSuffix}`,
};

const googleServices =
  require('./firebaseConfig').googleServicesByCountry[country][build];

export default {
  expo: {
    jsEngine: 'hermes',
    owner: 'fwdgodevops-cube',
    name: 'Cube' + (build !== 'prd' ? `(${build})` : ''),
    slug: 'cube-mobile',
    scheme,
    version: version + '', // + '' is for version suffix concat in pipeline
    orientation: 'default',
    icon: `./assets/icon-${country}.png`, //TODO: add icon
    userInterfaceStyle: 'light',
    splash: {
      image: './assets/splash.png',
      resizeMode: 'contain',
      backgroundColor: '#ffffff',
    },
    assetBundlePatterns: ['**/*'],
    ios: {
      usesIcloudStorage: false,
      supportsTablet: true,
      requireFullScreen: true,
      bundleIdentifier: bundleIdentifier.ios,
      buildNumber: '1',
      infoPlist: {
        FirebaseAutomaticScreenReportingEnabled: false,
        LSApplicationQueriesSchemes: ['itms-apps', 'market', 'viber'],
        NSFaceIDUsageDescription:
          'This app uses the Face ID for biometric authentication',
        NSCameraUsageDescription:
          'This app uses the camera to do take profile picture',
        NSPhotoLibraryUsageDescription:
          'This app accesses the library to select profile picture',
      },
      googleServicesFile: googleServices.ios,
      // jsEngine: 'jsc',
    },
    android: {
      allowBackup: false,
      package: bundleIdentifier.android,
      adaptiveIcon: {
        foregroundImage: `./assets/adaptive-icon-${country}.png`, //TODO: add icon
        backgroundColor: '#ffffff',
      },
      googleServicesFile: googleServices.android,
      versionCode: 1,
      softwareKeyboardLayoutMode: 'pan',
      permissions: [
        'CAMERA',
        'RECORD_AUDIO',
        'ACCESS_NETWORK_STATE',
        'CHANGE_NETWORK_STATE',
        'MODIFY_AUDIO_SETTINGS',
        'INTERNET',
      ],
      enableProguardInReleaseBuilds: true,
      usesCleartextTraffic: false,
    },
    web: {
      favicon: './assets/favicon.png',
    },
    extra: {
      country,
      build,
      urls: {
        baseUrl,
        ocrUrl,
        irisUrl,
        smartUrl,
        surveyUrl,
        smartToken,
        smartUserToken,
        smartUserAgent,
        dragonPayToken,
        dragonPaySecretKey,
        ocrToken,
        eIrisUrl,
        salesConnectUrlForgotPassword,
        salesConnectUrlLogin,
        aiBotContentStackId,
        aiBotContentStackApiKey,
        aiBotContentStackDeliveryToken,
        aiBotContentStackEnvironment,
        ecoachBaseUrl,
        ecoachPublicConversationWebsocketUrl,
        ecoachPublicAvatarWebsocketUrl,
        ecoachPublicConversationApi,
        ecoachTurnServerUrl,
        ecoachTurnServerUser,
        ecoachTurnServerPass,
        startClaimUrl,
        agencyTermsAndConditionsUrl,
        bancaTermsAndConditionsUrl,
        fwdPintrAppleStoreUrl,
        fwdPintrGooglePlayStoreUrl,
      },
      idpClientId,
      idpClientSecret,
      contentStackKey,
      contentStackDeliveryToken,
      contentStackEnvironment,
      contentStackSettingEntryId,
      fullRemoteSellingEnabled,
      enhancedRemoteSellingEnabled,
      eas: {
        projectId: '6fe38399-de27-4ae8-89b0-507674908992',
      },
    },
    plugins: [
      './firebaseConfig/mods/withFirebaseMods',
      './plugins/disableDexingArtifactTransform',
      './plugins/customJavaHeapMemSize',
      './plugins/withCustomAndroidBuildGradle',
      './plugins/withLargeHeapAndroid',
      '@react-native-firebase/app',
      '@react-native-firebase/crashlytics',
      '@config-plugins/react-native-webrtc',
      [
        'expo-contacts',
        {
          contactsPermission:
            'The app accesses your contacts to add a new lead.',
        },
      ],
      [
        'expo-camera',
        {
          cameraPermission: 'Allow $(PRODUCT_NAME) to access your camera.',
          microphonePermission:
            'Allow $(PRODUCT_NAME) to access your microphone',
          recordAudioAndroid: true,
        },
      ],
      [
        'expo-media-library',
        {
          photosPermission: 'Allow $(PRODUCT_NAME) to access your photos.',
          savePhotosPermission: 'Allow $(PRODUCT_NAME) to save photos.',
          isAccessMediaLocationEnabled: true,
        },
      ],
      [
        'expo-notifications',
        {
          icon: './assets/images/notification-icon.png',
          color: '#E87722',
          defaultChannel: 'default',
          sounds: [],
        },
      ],
      [
        'expo-screen-orientation',
        {
          initialOrientation: 'DEFAULT',
        },
      ],
      [
        'expo-document-picker',
        {
          iCloudContainerEnvironment: 'Production',
        },
      ],
      [
        'expo-build-properties',
        // The version is required by the security team
        {
          android: {
            // update the file ./plugins/disableDexingArtifactTransform.js instead
            minSdkVersion: 31,
          },
          ios: {
            deploymentTarget: build === 'prd' ? '16.0' : '15.1',
          },
        },
      ],
      [
        'expo-local-authentication',
        {
          faceIDPermission: 'Allow $(PRODUCT_NAME) to use Face ID.',
        },
      ],
      [
        'expo-image-picker',
        {
          photosPermission:
            'The app accesses your photos to let you share them with your friends.',
        },
      ],
      ['expo-sensors'],
      'expo-font',
      'expo-secure-store',
      [
        'react-native-vision-camera',
        {
          cameraPermissionText: '$(PRODUCT_NAME) needs access to your Camera.',

          // optionally, if you want to record audio:
          enableMicrophonePermission: true,
          microphonePermissionText:
            '$(PRODUCT_NAME) needs access to your Microphone.',
        },
      ],
      ['expo-video'],
    ],
  },
};
