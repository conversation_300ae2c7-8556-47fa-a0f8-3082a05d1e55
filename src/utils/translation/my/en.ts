import coverageDetails from 'features/coverageDetails/translation/my/coverageDetails.en';
import eApp from 'features/eAppV2/my/translation/eApp.en';
import fna from 'features/fna/translation/my/fna.en';
import pdfViewer from 'features/pdfViewer/translation/pdfViewer.en';
import proposal from 'features/proposal/translation/my/en';
import teamManagement from 'features/teamManagement/translation/ph/en';
import ocr from 'features/ocr/translation/ocr.en';
import livenessCheck from 'features/livenessCheck/translations/livenessCheck.en';

// ******* After country-specific translations splitting
import document from 'features/Document/translation/my/documents.en';
import notification from 'features/notification/translation/my/notification.en';
import agentProfile from 'features/agentProfile/translation/my/agentProfile.en';
import aiBot from 'features/aiBot/translation/my/aiBot.en';
import birthday from 'features/birthday/translation/my/birthday.en';
import customerFactFind from 'features/customerFactFind/translations/my/customerFactFind.en';
import eRecruit from 'features/eRecruit/translation/my/eRecruit.en';
import ecoach from 'features/ecoach/translation/my/ecoach.en';
import news from 'features/fwdNews/translation/my/news.en';
import home from 'features/home/<USER>/my/home.en';
import customer from 'features/lead/translation/my/customer.en';
import lead from 'features/lead/translation/my/lead.en';
import leadProfile from 'features/lead/translation/my/leadProfile.en';
import performance from 'features/performance/translation/my/performance.en';
import policy from 'features/policy/translation/my/policy.en';
import navigation from 'navigation/translation/my/navigation.en';
import agentAssist from 'features/agentAssist/translation/my/agentAssist.en';
import reportGeneration from 'features/reportGeneration/translation/my/reportGeneration.en';

// Social Marketing translations
import socialMarketing from 'features/socialMarketing/translation/my/socialMarketing.en';

const en = {
  common: {
    'login.welcome':
      'Welcome! Login to start your new digital working experience.',
    'login.id': 'Agent ID',
    'login.password': 'Password',
    'login.footer': 'FWD Cube version {{appVersion}}',
    'login.error':
      'status: Invalid userid/password.  <NAME_EMAIL>',
    'login.login': 'Login',
    'login.error.username.password':
      'Invalid user ID or password.  If you have forgotten your password, please reset it on eiRIS.',
    'login.error.connection':
      'Connection issue.  Please check your connection and try again shortly.',
    'login.error.dueTo': 'Unable to login due to error: {{message}}',
    'login.error.onlyOneAgent':
      'Only 1 agent ID can be used to login on this device',
    'login.versionCheck.title': 'Version update',
    'login.versionCheck.message': 'Please download the latest version.',
    'login.versionCheck.download': 'Download',
    'login.versionCheck.askMeLater': 'Ask me Later',
    'login.toCube': 'Login to FWD Cube',
    'login.forgetPassword': 'Forget Password? Please reset your password ',
    'login.biometric.button': 'Log in with {{bioMeticType}}',
    'login.biometric.touchID': 'Touch ID',
    'login.biometric.faceID': 'Face ID',
    'login.biometric.iris': 'Iris',
    'login.biometric.biometric': 'Biometric',
    'login.biometric.prompt.question':
      'Do you want to enable "FWD Cube" to use {{bioMeticType}}?',
    'login.biometric.prompt.description':
      'Enable {{bioMeticType}} for faster log in. You can turn this feature on or off at any time under Settings.',
    'login.biometric.prompt.enable': 'Enable',
    'login.biometric.prompt.cancel': 'Cancel',
    'login.biometric.prompt.notNow': 'Not Now',
    'login.biometric.prompt.fallbackLabel': 'Try manual input',
    'login.biometric.error.failed': 'Biometric login failed',
    'login.biometric.error.noHardware':
      "Your device doesn't support Biometric login",
    'login.biometric.error.notEnrolled':
      "You haven't enroll any Biometric data on your device",
    'login.here': 'here',
    'home.welcome.hi': 'Hi, {{name}}!',
    'home.welcome.question': 'What would you like to do?',
    'validation.required': 'Required field',
    'navigation.tabScreen.MyLeads': 'My Leads',
    'navigation.tabScreen.SalesActivity': 'Sales activity',
    'navigation.tabScreen.Overview': 'Overview',
    'navigation.tabScreen.MyTasks': 'My tasks',
    'todayTasks.title': 'Today tasks',
    'form.invalidInput': 'Invalid field',
    'form.phoneNumberTooShort': `Minimum length should be {{length}}`,
    'form.phoneNumberTooLong': `Maximum length should be {{length}}`,
    'form.nameTooLong': 'Must not exceed 30 characters',
    'form.nameTooLongWithLength': 'Must not exceed {{length}} characters',
    'form.inputNumberOnly': 'Please input numbers only',
    'form.yearOfPassingLimitCharacters': 'Must be exactly 4 characters',
    'form.icNumberLimitCharacters': 'Must be exactly 12 characters',
    'form.taxNumberFormatError':
      'This field accepts only 2 alphabets followed by 10-11 numbers.',
    'form.invalidFormat': 'Invalid format',
    'form.nricError.dateOrGenderNotMatch':
      'NRIC number doesn’t match with Date of birth or Gender',
    'form.nricError.dateNotMatch':
      'NRIC number doesn’t match with Date of birth',
    'form.candidateAboveYearsOld': 'Candidate must be above 18 years old',
    'form.candidateBelowYearsOld': 'Candidate must be below 60 years old',
    'form.mustBeAtLeast': `Must be at least {{minPhoneNumberlength}} characters`,
    'form.leaderALCmaxNameLength': 'Must not exceed 50 characters',
    'form.leaderCodeNotExist': 'Leader code does not exist',
    'form.invalidEmail':
      'Email must contain a single @ and a valid domain portion',
    'form.invalidEmailFormat': 'Invalid email format',
    'form.mustHaveTakafulTBEFamilyCertificate':
      'Candidate must have Takaful TBE Family certificate',
    'form.invalidProfessionalQualiLength':
      'Must not be more than 30 characters',

    'form.invalidPhoneNumber': 'Invalid phone number',
    'forcedLogout.title': 'Your account has been SUSPENDED',
    'forcedLogout.message': `Should you need further clarification, please send an <NAME_EMAIL>.`,
    php: 'PHP',
    imLookingFor: "I'm looking for...",
    noResultsFound: 'No results found',
    loadingFail: 'Data retrieved failed. Please visit again later.',
    searchSuggestion:
      'Adjust keyword to get better result or try another search.',
    foundXResults: 'We found {{count}} results',
    view: 'View',
    done: 'Done',
    close: 'Close',
    more: 'More',
    add: 'Add',
    confirm: 'Confirm',
    details: 'Details',
    select: 'Select',
    compare: 'Compare',
    cancel: 'Cancel',
    search: 'Search',
    'search.leadName': 'Lead name',
    'search.companyName': 'Company name',
    'search.mobile': 'Mobile',
    'search.email': 'Email',
    searchResults: 'Search results ({{count}})',
    years: 'years',
    year: 'year',
    Year: 'Year',
    save: 'Save',
    delete: 'Delete',
    reset: 'Reset',
    exit: 'Exit',
    ok: 'OK',
    rm: 'RM',
    continue: 'Continue',
    next: 'Next',
    withCurrency: 'RM {{amount}}',
    currencySymbol: 'RM',
    withYears: '{{year}} year(s)',
    back: 'Back',
    tryAgain: 'Try again',
    'position.1': 'First',
    'position.2': 'Second',
    'position.3': 'Third',
    'position.4': 'Fourth',
    'position.5': 'Fifth',
    'position.6': 'Sixth',
    'position.7': 'Seventh',
    'position.8': 'Eighth',
    'position.9': 'Ninth',
    FWD: 'Financial Wealth Director',
    FWE: 'Financial Wealth Executive',
    FWC: 'Financial Wealth Consultant',
    TAAM: 'Takaful Agency Manager',
    TAUM: 'Takaful Unit Manager',
    TACA: 'Takaful Career Agent',
    TAAGT: 'Takaful Agent',
    TA: 'Takaful Agent',
    asOfDate: 'As of {{date}}',
    shortYearOld: 'y.o.',
    en: 'English',
    my: 'Bahasa',
    error: 'Error',
    backendError:
      'Error connecting to back-end system. Please try again later or contact support.',
    download: 'Download',
    share: 'Share',
    viewProfile: 'View profile',
    'searchExistingLeadModal.existingLeadTitle': 'Search for existing FWD lead',
    'searchExistingLeadModal.existingCompanyTitle':
      'Search for existing company',
    'searchExistingLeadModal.entityLeadPlaceholder':
      'Company name/ phone number/ email',
    'searchExistingLeadModal.individualLeadPlaceholder':
      'Lead name/ phone number/ email',
    'searchExistingLeadModal.emptyIndividual':
      'No pre-existing lead available. Please try another search.',
    'searchExistingLeadModal.emptyEntity':
      'No pre-existing company available. Please try another search.',
    days: 'days',
    yes: 'Yes',
    no: 'No',
    upTo: 'Up to',
    month: 'month',
    'autoComplete.noData': 'No matching options',
    // Contact
    'contact.title': 'Contact',
    'contact.message': 'FWD Cube',
    'contact.close': 'Close',
    'contact.call': 'Call',
    'contact.sms': 'SMS',
    'contact.email': 'Email',
    'contact.more': 'Others',
    'contact.phone.notProvide': 'Phone number and email are not provided',
    'contact.phone.notValid': 'Phone number is not valid',
    pdfDownload: 'Downloading',
    pdfDownloadedSuccessfully: 'Downloaded successfully',
    pdfFailToDownload: 'Document download has failed. Please try again.',
    pdfFailToShare: 'Fail to share. Please try again.',
    dateHint: 'DD/MM/YYYY',
    incompleteFields_other: '{{count}} incomplete fields',
    incompleteFields_one: '{{count}} incomplete field',
    goToTheField: 'Go to the field',
    application: 'Application',
    'ocr.upload.title': 'Upload ID card',
    'ocr.upload.title.mobile': 'Scan ID card',
    'ocr.upload.description': 'to pre-populate information below.',
    'ocr.upload.description.mobile': 'e.g. ID card or passport',
    'ocr.upload': 'Upload',
    'ocr.uploading': 'Document uploading...',
    'ocr.verifying': 'Verifying...',
    'ocr.scanID': 'Scan ID card',
    'ocr.scanID.example': 'e.g. ID card or passport',
    'ocr.scanID.info.title': 'Acceptant of ID list',
    'ocr.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
    'ocr.scanID.info.point.2': 'RP Passport',
    'ocr.scanID.info.point.3': "Driver's License",
    'ocr.scanID.info.point.4': 'PRC ID (Professional Regulation Commission ID)',
    'ocr.scanID.info.point.5': 'SSS (Social Security System ID)',
    'ocr.scanID.info.point.6': 'TIN (Bureau of Internal Revenue ID)',
    'ocr.instruction.takeA': 'Take a ',
    'ocr.instruction.closeUp': 'close up',
    'ocr.instruction.photo': ' photo of your ID card',
    'ocr.instruction.makeSure': 'Make sure your photo is:',
    'ocr.instruction.makeSure.clear': 'Clear',
    'ocr.instruction.makeSure.clear.1': ' visible and in focus',
    'ocr.instruction.makeSure.withoutFlash': 'Taken',
    'ocr.instruction.makeSure.withoutFlash.1': ' without flash',
    'ocr.picker.title': 'Upload profile image by',
    'ocr.re-upload': 'Re-upload',
    'ocr.verified': 'Verified',
    'birthDay.sendCard': 'Send birthday card',
    new: 'New',
    'upload.invalidFileSize':
      'The file you’re uploading exceeds the maximum file size allowed.  Please retry uploading a file less in size.',
    'upload.invalidFileExtension':
      'The file type you’re uploading is invalid.  Only {{fileTypes}} are accepted.',
  },
  smart: {
    'proposaldetails.totalpremium': 'Total premium/annually',
    'plan.sumAssured': 'Total sum assured',
    'proposaldetails.basepremium': 'Premium',
    'proposaldetails.basesumassured': 'Sum assured',
    'proposaldetails.paymentmode': 'Payment mode',
    'proposaldetails.paymenterm': 'Payment term',
    'personalDetails.title': 'Personal Details',
    'personalDetails.scanIDCard': 'Scan ID card',
    'personalDetails.scanIDCard.hint': 'e.g. ID card or passport',
    'personalDetails.name': 'Name',
    'personalDetails.customerType': 'Customer type',
    'personalDetails.salutation': 'Salutation/Title',
    'personalDetails.firstName': 'First name',
    'personalDetails.middleName': 'Middle name (optional)',
    'personalDetails.lastName': 'Last name',
    'personalDetails.extensionName': 'Extension name (optional)',
    'personalDetails.dateOfBirth': 'Date of birth',
    'personalDetails.occupation.question':
      'What best describes your occupation?',
    'personalDetails.smoking': 'Smoking Habit',
    'personalDetails.reviewPersonalHealthQuestion':
      'Review personal health question',
    'personalDetails.gender': 'Gender (optional)',
    'rider.sumAssured': 'Sum assured (PHP)',
    'rider.regularTopup': 'Top-up amount (PHP)',
    'rider.coverage': 'Coverage to',
    'rider.planOption': 'Plan option',
  },
  proposal,
  savedProposals: {
    title: 'Saved proposals',
    totalSavedProposal: 'Total saved proposals',
    filterBy: 'Filter by',
    filtered: 'Filtered',
    proposalStage: 'Proposal stage',
    clearAll: 'Reset',
    apply: 'Apply',
    'filter.FNA': 'CFF',
    'filter.CFF': 'CFF',
    'filter.QUICK_SI': 'Quick quote',
    'filter.FULL_SI': 'Benefit illustration',
    'filter.IN_APP': 'Application',
    'filter.INDIVIDUAL': 'Individual',
    'filter.ORGANISATION': 'Organisation',
    'filter.APP_SUBMITTED': 'Submitted',
    'filter.emptyRecord': 'Empty record',
    'filter.COVERAGE': 'Coverage',
    'filter.REMOTE_SELLING_COMPLETED': 'Remote selling completed',
    'filter.UNKNOWN': 'Unknown',
    'filter.REJECTED_BY_LEADER': 'Rejected by leader',
    'filter.PENDING_FOR_LEADER': 'Pending for leader',
    'filter.APPROVED_BY_LEADER': 'Approved by leader',
    'filter.EXPIRED_AFTER_15_DAYS': 'Expired after 15 days',
    fullTable: 'Full table',
    sortByTime: 'Sort by',
    newest: 'Newest',
    oldest: 'Oldest',
    showIn: 'Show in',
    inDays: '{{day}} days',
    noResultsFound: 'Oops! Empty saved proposals.',
    loadingProposalsMessage:
      'Please wait we are retrieving data from last {{day}} days ago',
    certificateOwner: 'Certificate Owner',
    proposalName: 'Proposal Name',
    insured: 'Person covered',
    insuredName: 'Insured name',
    status: 'Status',
    productName: 'Product Name',
    product: 'Product',
    premiumAmount: 'Premium Amount',
    modalPremium: 'Modal Contribution',
    sumAssured: 'Sum Covered',
    proposalNo: 'Proposal No',
    lastUpdate: 'Last update',
    lastUpdateDate: 'Last update',
    date: 'Date',
    showDataIn: 'Show data in',
    lastDays: 'Last {{day}} days',
    proposalPlaceholder: 'Proposal',
    searchHint:
      'e.g. Customer first name/ last name, product name or proposal name',
    expired: 'Expired',
    'paymentMode.EVERY_YEAR': 'annually',
    'paymentMode.EVERY_HALF_YEAR': 'semi-annually',
    'paymentMode.EVERY_QUARTER': 'quarterly',
    'paymentMode.EVERY_MONTH': 'monthly',
    'paymentMode.ONE_TIME': 'single premium',
    'search.noResult': 'No results found.',
    'search.adjustKeyword': 'Adjust keyword to get better result.',
    'search.result': 'Search proposals',
    'search.searchBar.placeholder': 'Search saved proposal',
    'search.description':
      'e.g. Customer first name/ last name, product name or proposal name',
    'search.dataFrom.period': 'Displaying data from last 90 days.',
    createNewProposal: 'Create new proposal',
    'popup.title': 'Note',
    'popup.text':
      'Your application has been Expired (>30 days). \nYou may resume with creation of new Benefit Illustration & continue with a fresh Application.',
    'popup.button.createBI': 'Create Benefit Illustration',
    'popup.button.cancel': 'Cancel',
    'popup.expired.title': 'Application expired',
    'popup.expired.text':
      'Your application has been incomplete for more than 30 days, or the certificate owner/person covered’s age has changed. This will affect the Benefit Illustration. Please create a new proposal to continue the sales process.',
  },
  product: {
    gotIt: 'Got it',
    invalidLicense: 'Agent is not licensed to sell {{productName}}',
    selectOneProduct: 'Select one product',
    yourGoalSummary: 'Your goal summary',
    recommended: 'Recommended',
    recommendedFor: 'Recommended for',
    details: 'Details',
    otherProducts: 'Other products',
    forYou: 'For you',
    'reason.title': 'Reason for selecting non recommended products:',
    'reason.otherReasonPlaceholder': 'Please specify the other reason',
    'reason.A':
      'Prefers a Takaful plan with returns if surrendered or reaches maturity',
    'reason.B':
      'Prefers to have some form of investment in a Takaful certificate',
    'reason.C': 'Recommended plan is too expensive',
    'reason.D': 'Prefers a shorter contribution term commitment',
    'reason.E':
      'Prefers higher coverage amount and /or longer coverage period plan with lower payment',
    'reason.F': 'Other plan features are more relevant to customer',
    'reason.G': 'Other reasons',
    'reason.G.placeholder': 'Please specify the reason',
    'validation.error.minLength10': 'Minimum length is 10',
    'disclaimer.title.en': "Customer's Acknowledgement",
    'disclaimer.title.my': 'Pengakuan Pelanggan',
    'disclaimer.1.en':
      'I/We understand that all information provided in this form have been given to allow the takaful agent to advise / recommend suitable takaful products to me and will be treated with strict confidence.\n\nI/We understand and agree that any personal information collected or held by FWD Takaful (whether contained in this form or otherwise obtained) may be used, processed, disclosed and shared by FWD Takaful to individuals/organisations related to and associated with FWD Takaful or any selected third party (within or outside of Malaysia, including retakaful and claims investigation companies and industry associations/federations) for the purpose of processing this form and providing subsequent services for this and other financial products and services to communicate with me/us for such purposes.\n\nI/We understand that I/we have the right to request access to my personal information held by FWD Takaful and to request correction of any personal information which is incorrect or to limit the processing of my personal information. I/We consent and hereby authorise FWD Takaful to charge a fee for processing and complying with such data access request or correction requests.',
    'disclaimer.1.en.short':
      'I/We understand that all information provided in this form have been given to allow the takaful agent to advise / recommend',
    'disclaimer.1.my':
      'Saya/Kami faham bahawa semua maklumat yang diberikan dalam borang ini telah diberikan untuk membenarkan ejen takaful memberi nasihat / mengesyorkan produk takaful yang sesuai kepada saya dan akan dianggap sulit.\n\nSaya/Kami memahami dan bersetuju bahawa mana-mana maklumat peribadi yang dikumpulkan atau disimpan oleh FWD Takaful (sama ada terkandung dalam borang ini atau diperolehi dengan cara lain) boleh digunakan, diproses, didedahkan dan dikongsi oleh FWD Takaful kepada individu/organisasi berhubung atau berkaitan dengan FWD Takaful atau mana-mana pihak ketiga yang terpilih (di dalam atau di luar Malaysia, termasuk retakaful dan syarikat penyiasatan tuntutan dan persatuan industri/persekutuan) bagi tujuan pemprosesan borang ini dan pemberian perkidmatan susulan untuk produk ini dan produk dan perkhidmatan lain dan untuk berhubung dengan saya/kami bagi tujuan tersebut.\n\nSaya/Kami memahami bahawa saya/kami mempunyai hak untuk mendapatkan akses kepada maklumat peribadi yang disimpan oleh FWD Takaful dan memohon pembetulan mana-mana maklumat peribadi yang salah atau menghadkan pemprosesan maklumat peribadi saya/kami. Saya/Kami mengizinkan dan dengan ini memberi kebenaran kepada FWD Takaful untuk mengenakan fi untuk memproseskan dan mematuhi permintaan akses data atau permintaan pembetulan.',
    'disclaimer.1.my.short':
      'Saya/Kami faham bahawa semua maklumat yang diberikan dalam borang ini telah diberikan untuk membenarkan ejen takaful memberi nasihat / mengesyorkan',
    'disclaimer.close': 'Close',
    'disclaimer.more': '...more',
    'disclaimer.accept.en':
      'I/We acknowledged that the takaful agent has provided me/us with a copy of the completed Customer Fact Find form or has shown us/we the content of the Customer Fact Find form in the form of physical or soft copy before the issuance of the certificate.',
    'disclaimer.accept.my':
      "Saya/Kami mengesahkan bahawa ejen takaful sudah memberikan saya/kami salinan borang 'Customer Fact Find' atau ada menunjukkan saya/kami isi kandungan borang tersebut dalam bentuk fizikal atau salinan lembut sebelum penerbitan polisi.",
    'disclaimer.accept2.en':
      'I/We acknowledge that the takaful agent has shown or provided me/us the web address of the Takaful Service Guide.',
    'disclaimer.accept2.my':
      'Saya/Kami mengakui bahawa ejen takaful telah menunjukkan atau meberikan saya/kami alamat web Panduan Perkhidmatan.',
    'disclaimer.acceptedOn': 'Accepted on',
    back: 'Back',
    continue: 'Continue',
    shareFNADoc: 'Share FNA doc',
    previewFNA: 'Preview CFF',
    done: 'Done',
    createSI: 'Create Benefit illustration',
    selectProduct: 'Please select a product',
    selectPackage: 'Please select package',
    productBrochure: 'Product Brochure',
    basePlan: 'Life Base Plan',
    previewCFF: 'Preview CFF',
    viewCFF: 'View CFF',
    'start.sale.illustration': 'Start Benefit illustration',
  },
  navigation,
  eApp,
  coverageDetails,
  teamManagement,
  policy,
  lead,
  customer,
  news,
  performance,
  fna,
  agentProfile,
  home,
  leadProfile,
  pdfViewer,
  customerFactFind,
  document,
  eRecruit,
  birthday,
  aiBot,
  ecoach,
  notification,
  agentAssist,
  ocr,
  livenessCheck,
  reportGeneration,
  socialMarketing,
  locale: {
    ph: 'Philippines',
    my: 'Malaysia',
  },
};

export default en;
