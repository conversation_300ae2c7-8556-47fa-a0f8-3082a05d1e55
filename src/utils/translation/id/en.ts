// --- TDC
import pdfViewer from 'features/pdfViewer/translation/pdfViewer.en';
import ocr from 'features/ocr/translation/ocr.en';
import livenessCheck from 'features/livenessCheck/translations/livenessCheck.en';

import birthday from 'features/birthday/translation/id/birthday.en';

// --- still in my
import ecoach from 'features/ecoach/translation/my/ecoach.en';
import customerFactFind from 'features/customerFactFind/translations/my/customerFactFind.en';
import aiBot from 'features/aiBot/translation/my/aiBot.en';

// --- still in ib
import affiliates from 'features/affiliates/translation/ib/affiliates.en';
import customer from 'features/lead/translation/ib/customer.en';
import teamManagement from 'features/teamManagement/translation/ib/en';
import agentAssist from 'features/agentAssist/translation/ib/agentAssist.en';

// ******* After IDN-specific translations splitting
import fna from 'features/fna/translation/id/fna.en';
import eApp from 'features/eAppV2/id/translation/eApp.en';
import coverageDetails from 'features/coverageDetails/translation/id/coverageDetails.en';
import proposal from 'features/proposal/translation/id/en';

import agentProfile from 'features/agentProfile/translation/id/agentProfile.en';
import eRecruit from 'features/eRecruit/translation/id/eRecruit.en';
import home from 'features/home/<USER>/id/home.en';
import performance from 'features/performance/translation/id/performance.en';
import policy from 'features/policy/translation/id/policy.en';
import navigation from 'navigation/translation/id/navigation.en';
import lead from 'features/lead/translation/id/lead.en';
import leadProfile from 'features/lead/translation/id/leadProfile.en';
import news from 'features/fwdNews/translation/id/news.en';
import notification from 'features/notification/translation/id/notification.en';

// Social Marketing translations
import socialMarketing from 'features/socialMarketing/translation/id/socialMarketing.en';

const en = {
  common: {
    'login.welcome':
      'Welcome! Login to start your new digital working experience.',
    'login.id': 'Agent ID',
    'login.password': 'Password',
    'login.footer': 'FWD Cube version {{appVersion}}',
    'login.error':
      'status: Invalid userid/password.  <NAME_EMAIL>',
    'login.login': 'Login',
    'login.error.username.password':
      'Invalid user ID or password.  If you have forgotten your password, please reset it on Sales Connect.',
    'login.error.banca.channel': `Oops, you're currently unauthorized to log in to FWD Cube on a mobile phone.  Please download and install FWD CUBE on a tablet.`,
    'login.error.connection':
      'Connection issue.  Please check your connection and try again shortly.',
    'login.error.dueTo': 'Unable to login due to error: {{message}}',
    'login.error.onlyOneAgent':
      'Only 1 agent ID can be used to login on this device',
    'login.versionCheck.title': 'Version update',
    'login.versionCheck.message': 'Please download the latest version.',
    'login.versionCheck.download': 'Download',
    'login.versionCheck.askMeLater': 'Ask me Later',
    'login.toCube': 'Login to FWD Cube',
    'login.forgetPassword': 'Forget Password? Please reset your password ',
    'login.forgetPasswordV2': 'Forget password?',
    'login.resetPassword': 'Please reset your password in ',
    'login.biometric.button': 'Log in with {{bioMeticType}}',
    'login.biometric.touchID': 'Touch ID',
    'login.biometric.faceID': 'Face ID',
    'login.biometric.iris': 'Iris',
    'login.biometric.biometric': 'Biometric',
    'login.biometric.prompt.question':
      'Do you want to enable "FWD Cube" to use {{bioMeticType}}?',
    'login.biometric.prompt.description':
      'Enable {{bioMeticType}} for faster log in. You can turn this feature on or off at any time under Settings.',
    'login.biometric.prompt.enable': 'Enable',
    'login.biometric.prompt.cancel': 'Cancel',
    'login.biometric.prompt.notNow': 'Not Now',
    'login.biometric.prompt.fallbackLabel': 'Try manual input',
    'login.biometric.error.failed': 'Biometric login failed',
    'login.biometric.error.noHardware':
      "Your device doesn't support Biometric login",
    'login.biometric.error.notEnrolled':
      "You haven't enroll any Biometric data on your device",
    'login.here': 'here',
    'home.welcome.hi': 'Hi, {{name}}',
    'home.welcome.question': 'What would you like to do?',
    'validation.required': 'Required field',
    'navigation.tabScreen.MyLeads': 'My Leads',
    'navigation.tabScreen.SalesActivity': 'Sales activity',
    'navigation.tabScreen.Overview': 'Overview',
    'navigation.tabScreen.MyTasks': 'My tasks',
    'todayTasks.title': 'Today tasks',
    'form.invalidInput': 'Invalid field',
    'form.invalidInputFormat': 'Invalid format',
    'form.phoneNumberTooShort': 'Phone number is too short',
    'form.phoneNumberTooLong': 'Phone number is too long',
    'form.phoneNumberTooShortWithLength': 'Minimum length should be {{length}}',
    'form.phoneNumberTooLongWithLength': 'Maximum length should be {{length}}',
    'form.nameTooLong': 'Must not exceed 30 characters',
    'form.nameTooLongWithLength': 'Must not exceed {{length}} characters',
    'form.taxNumberFormatError':
      'This field accepts only 2 alphabets followed by 10-11 numbers.',
    'form.inputNumberOnly': 'Please input numbers only',
    'form.yearOfPassingLimitCharacters': 'Must be exactly 4 characters',
    'form.icNumberLimitCharacters': 'Must be exactly 16 characters',
    'form.inputMustBeExactly': 'Must be exactly {{number}} characters',
    'form.invalidFormat': 'Invalid format',
    'form.candidateAboveYearsOld': 'Candidate must be above 18 years old',
    'form.candidateBelowYearsOld': 'Candidate must be below 60 years old',
    'form.mustBeAtLeast': `Must be at least {{minPhoneNumberlength}} characters`,
    'form.leaderALCmaxNameLength': 'Must not exceed 50 characters',
    'form.leaderCodeNotExist': 'Leader code does not exist',
    'form.invalidEmail':
      'Email must contain a single @ and a valid domain portion',
    'form.invalidEmailFormat': 'Invalid email format',
    'form.mustHaveTakafulTBEFamilyCertificate':
      'Candidate must have Takaful TBE Family certificate',
    'form.invalidProfessionalQualiLength':
      'Must not be more than 30 characters',

    'form.invalidPhoneNumber': 'Invalid phone number',
    'form.shouldNotExceedSomeNumber':
      'Input should not exceed {{number}} characters',
    'forcedLogout.title': 'Your account has been SUSPENDED',
    'forcedLogout.message': `Should you need further clarification, please send an <NAME_EMAIL>.`,
    imLookingFor: "I'm looking for...",
    noResultsFound: 'No results found',
    loadingFail: 'Data retrieved failed. Please visit again later.',
    searchSuggestion:
      'Adjust keyword to get better result or try another search.',
    foundXResults: 'We found {{count}} results',
    view: 'View',
    done: 'Done',
    close: 'Close',
    more: 'More',
    add: 'Add',
    confirm: 'Confirm',
    details: 'Details',
    select: 'Select',
    compare: 'Compare',
    cancel: 'Cancel',
    search: 'Search',
    'search.leadName': 'Lead name',
    'search.companyName': 'Company name',
    'search.mobile': 'Mobile',
    'search.email': 'Email',
    searchResults: 'Search results ({{count}})',
    years: 'years',
    year: 'year',
    save: 'Save',
    delete: 'Delete',
    reset: 'Reset',
    exit: 'Exit',
    ok: 'OK',
    rm: 'RM',
    continue: 'Continue',
    next: 'Next',
    withCurrency: 'IDR {{amount}}',
    currencySymbol: 'IDR',
    withYears: '{{year}} year(s)',
    back: 'Back',
    'position.1': 'First',
    'position.2': 'Second',
    'position.3': 'Third',
    'position.4': 'Fourth',
    'position.5': 'Fifth',
    'position.6': 'Sixth',
    'position.7': 'Seventh',
    'position.8': 'Eighth',
    'position.9': 'Ninth',
    FWD: 'Financial Wealth Director',
    FWE: 'Financial Wealth Executive',
    FWC: 'Financial Wealth Consultant',
    asOfDate: 'As of {{date}}',
    shortYearOld: 'y.o.',
    en: 'English',
    my: 'Bahasa',
    error: 'Error',
    backendError:
      'Error connecting to back-end system. Please try again later or contact support.',
    download: 'Download',
    share: 'Share',
    viewProfile: 'View profile',
    'searchExistingLeadModal.existingLeadTitle': 'Search for existing FWD lead',
    'searchExistingLeadModal.existingCompanyTitle':
      'Search for existing company',
    'searchExistingLeadModal.entityLeadPlaceholder':
      'Company name/ phone number/ email',
    'searchExistingLeadModal.individualLeadPlaceholder':
      'Lead name/ phone number/ email',
    'searchExistingLeadModal.emptyIndividual':
      'No pre-existing lead available. Please try another search.',
    'searchExistingLeadModal.emptyEntity':
      'No pre-existing company available. Please try another search.',
    days: 'days',
    date: 'Date',
    yes: 'Yes',
    no: 'No',
    'autoComplete.noData': 'No matching options',
    // Contact
    'contact.title': 'Contact',
    'contact.message': 'FWD Cube',
    'contact.close': 'Close',
    'contact.call': 'Call',
    'contact.sms': 'SMS',
    'contact.email': 'Email',
    'contact.more': 'Others',
    'contact.phone.notProvide': 'Phone number and email are not provided',
    'contact.phone.notValid': 'Phone number is not valid',
    pdfDownload: 'Document downloading...',
    pdfDownloadedSuccessfully: 'Document has been downloaded',
    pdfFailToDownload: 'Download failed',
    pdfFailToShare: 'File sharing failed: {{msg}}',
    dateHint: 'DD/MM/YYYY',
    incompleteFields_other: '{{count}} incomplete fields',
    incompleteFields_one: '{{count}} incomplete field',
    goToTheField: 'Go to the field',
    'ocr.upload.title': 'Upload ID card',
    'ocr.upload.title.mobile': 'Scan ID card',
    'ocr.upload.description':
      'Upload ID to skip manual verification and auto-populate your info below.',
    'ocr.upload.description.mobile': 'e.g. ID card or passport',
    'ocr.upload': 'Upload',
    'ocr.uploading': 'Document uploading...',
    'ocr.verifying': 'Verifying...',
    'ocr.scanID': 'Scan ID card',
    'ocr.scanID.example': 'e.g. ID card or passport',
    'ocr.scanID.info.title': 'Acceptant of ID list',
    'ocr.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
    'ocr.scanID.info.point.2': 'RP Passport',
    'ocr.scanID.info.point.3': "Driver's License",
    'ocr.scanID.info.point.4': 'PRC ID (Professional Regulation Commission ID)',
    'ocr.scanID.info.point.5': 'SSS (Social Security System ID)',
    'ocr.scanID.info.point.6': 'TIN (Bureau of Internal Revenue ID)',
    'ocr.instruction.takeA': 'Take a ',
    'ocr.instruction.closeUp': 'close up',
    'ocr.instruction.photo': ' photo of your ID card',
    'ocr.instruction.makeSure': 'Make sure your photo is:',
    'ocr.instruction.makeSure.clear': 'Clear',
    'ocr.instruction.makeSure.clear.1': ' visible and in focus',
    'ocr.instruction.makeSure.withoutFlash': 'Taken',
    'ocr.instruction.makeSure.withoutFlash.1': ' without flash',
    'ocr.picker.title': 'Upload profile image by',
    'ocr.re-upload': 'Re-upload',
    'ocr.verified': 'Verified',
    selectLanguage: 'Select Language',
    IDR: 'IDR',
    USD: 'USD',
    'birthDay.sendCard': 'Send birthday card',
    new: 'New',
    'upload.invalidFileSize': 'The file you’re uploading exceeds the maximum file size allowed.  Please retry uploading a file less in size.',
    'upload.invalidFileExtension':
      'The file type you’re uploading is invalid.  Only {{fileTypes}} are accepted.',
  },
  smart: {
    'proposaldetails.totalpremium': 'Total premium/annually',
    'plan.sumAssured': 'Total sum assured',
    'proposaldetails.basepremium': 'Premium',
    'proposaldetails.basesumassured': 'Sum assured',
    'proposaldetails.paymentmode': 'Payment mode',
    'proposaldetails.paymenterm': 'Payment term',
    'personalDetails.title': 'Personal Details',
    'personalDetails.scanIDCard': 'Scan ID card',
    'personalDetails.scanIDCard.hint': 'e.g. ID card or passport',
    'personalDetails.name': 'Name',
    'personalDetails.customerType': 'Customer type',
    'personalDetails.salutation': 'Salutation/Title',
    'personalDetails.firstName': 'First name',
    'personalDetails.middleName': 'Middle name (optional)',
    'personalDetails.lastName': 'Last name',
    'personalDetails.extensionName': 'Extension name (optional)',
    'personalDetails.dateOfBirth': 'Date of birth',
    'personalDetails.occupation.question':
      'What best describes your occupation?',
    'personalDetails.smoking': 'Smoking Habit',
    'personalDetails.reviewPersonalHealthQuestion':
      'Review personal health question',
    'personalDetails.gender': 'Gender (optional)',
    'rider.sumAssured': 'Sum assured (PHP)',
    'rider.regularTopup': 'Top-up amount (PHP)',
    'rider.coverage': 'Coverage to',
    'rider.planOption': 'Plan option',
  },
  proposal,
  savedProposals: {
    title: 'Saved proposals',
    totalSavedProposal: 'Total',
    filterBy: 'Filter by',
    filtered: 'Filtered by',
    proposalStage: 'Proposal stage',
    clearAll: 'Reset',
    apply: 'Apply',
    'filter.FNA': 'FNA',
    'filter.CFF': 'CFF',
    'filter.QUICK_SI': 'Quick quote',
    'filter.FULL_SI': 'SI',
    'filter.IN_APP': 'Application',
    'filter.INDIVIDUAL': 'Individual',
    'filter.ORGANISATION': 'Organisation',
    'filter.APP_SUBMITTED': 'Submitted',
    'filter.emptyRecord': 'Empty record',
    'filter.COVERAGE': 'Coverage',
    'filter.REMOTE_SELLING_COMPLETED': 'Remote selling completed',
    'filter.UNKNOWN': 'Unknown',
    'filter.REJECTED_BY_LEADER': 'Rejected by leader',
    'filter.PENDING_FOR_LEADER': 'Pending for leader',
    'filter.APPROVED_BY_LEADER': 'Approved by leader',
    'filter.EXPIRED_AFTER_15_DAYS': 'Expired after 15 days',
    fullTable: 'Full table',
    sortByTime: 'Sort by',
    newest: 'Newest',
    oldest: 'Oldest',
    showIn: 'Show in',
    inDays: '{{day}} days',
    noResultsFound: 'Oops! Empty saved proposals.',
    loadingProposalsMessage:
      'Please wait we are retrieving data from last {{day}} days ago',
    certificateOwner: 'Policy Owner',
    proposalName: 'Proposal name',
    insured: 'Insured',
    insuredName: 'Insured name',
    status: 'Status',
    productName: 'Product Name',
    product: 'Product',
    premiumAmount: 'Premium Amount',
    modalPremium: 'Modal premium',
    sumAssured: 'Sum assured',
    proposalNo: 'Proposal No',
    lastUpdate: 'Last update',
    lastUpdateDate: 'Last update',
    date: 'Date',
    showDataIn: 'Show data in',
    lastDays: 'Last {{day}} days',
    proposalPlaceholder: 'Proposal',
    searchHint:
      'e.g. Customer first name/ last name, product name or proposal name',
    expired: 'Expired',
    'paymentMode.EVERY_YEAR': 'annually',
    'paymentMode.EVERY_HALF_YEAR': 'semi-annually',
    'paymentMode.EVERY_QUARTER': 'quarterly',
    'paymentMode.EVERY_MONTH': 'monthly',
    'paymentMode.ONE_TIME': 'single premium',
    'search.noResult': 'No results found.',
    'search.adjustKeyword': 'Adjust keyword to get better result.',
    'search.result': 'Search result',
    'search.proposal': 'Search proposals',
    'search.searchBar.placeholder': 'Search saved proposal',
    'search.description':
      'e.g. Customer first name/ last name, product name or proposal name',
    'search.dataFrom.period': 'Displaying data from last 90 days.',
    createNewProposal: 'Create new proposal',
  },
  product: {
    gotIt: 'Got it',
    invalidLicense: 'Agent is not licensed to sell {{productName}}',
    selectOneProduct: 'Pick a product',
    yourGoalSummary: 'Your goal summary',
    recommended: 'Recommended',
    recommendedFor: 'Recommended for',
    details: 'Details',
    otherProducts: 'Other products',
    forYou: 'For you',
    'reason.title': 'Reason for selecting non recommended products:',
    'reason.otherReasonPlaceholder': 'Please specify the other reason',
    'reason.0': '',
    'reason.1': 'Client has existing FWD policy/ies with the same features',
    'reason.2':
      'Client has existing policy/ies with the same features with another insurance company',
    'reason.3': 'User selected product',
    'reason.4': 'The recommended product is not within my preference.',
    'reason.5': 'The recommended product does not meet my needs.',
    'reason.6': 'I have existing policy/ies with the same features.',
    'disclaimer.title': "Customer's Acknowledgement",
    'disclaimer.haveRead':
      'I have read and understand the following before submitting this application:',

    'disclaimer.point.1':
      'FWD Insurance Berhad’s assessment of my investment risk level and the resultant suitable product risk recommendation above;',
    'disclaimer.point.2':
      'The features of this plan and the investment strategy and goals of my nominated Investment Funds;',
    'disclaimer.point.3':
      'The benefits payable under this plan are linked to the performance of my nominated Investment Funds which will rise and fall.  Past performance of the fund is not an indication of its future performance. The investment risks under this plan are therefore borne solely by me.',

    'disclaimer.point.4':
      'I acknowledge that FWD Insurance Berhad and/or its agent / intermediary may recommend the most appropriate fund strategy suited to my financial needs, which I can use as a guide. If I select a fund strategy which is different from that which is recommended, I agree NOT to hold FWD Insurance Berhad, its principals, their representatives, and successor accountable and harmless in anyway including all liabilities, claims, opportunity cost and/or causes of action of whatever kind or nature, that may affect me as a result of or due to this choice.',

    'disclaimer.1':
      'I/We understand that all information provided in this form have been given to allow the agent to advise / recommend suitable products to me and will be treated with strict confidence.\n\nI/We understand and agree that any personal information collected or held by FWD (whether contained in this form or otherwise obtained) may be used, processed, disclosed and shared by FWD Insurance Berhad to individuals/organisations related to and associated with FWD Insurance Berhad or any selected third party (within or outside of Malaysia, including re and claims investigation companies and industry associations/federations) for the purpose of processing this form and providing subsequent services for this and other financial products and services to communicate with me/us for such purposes.\n\nI/We understand that I/we have the right to request access to my personal information held by FWD Insurance Berhad and to request correction of any personal information which is incorrect or to limit the processing of my personal information. I/We consent and hereby authorise FWD Insurance Berhad to charge a fee for processing and complying with such data access request or correction requests.',
    'disclaimer.1.short':
      'I/We understand that all information provided in this form have been given to allow the agent to advise / recommend',
    'disclaimer.2':
      'I am choosing to avail of a different product from that which is recommended by FWD Life Insurance Corporation based on my declared financial needs. I agree to hold FWD Life Insurance Corporation, its principals, their representatives, and successors in interest free and harmless from any and all liabilities, claims, opportunity cost and/or cause of action of whatever kind of nature, that may affect me as a result of or due to this choice.\nI, understand that the evaluation and recommendation are based on the information and data provided by me and are designed to help me assess my financial needs. I am aware that my financial needs may change over time depending on my personal situation and objectives. Therefore, any suggestions and recommendations provided here are general advice only based on my current status and are intended for reference only.',
    'disclaimer.2.short':
      'I am choosing to avail of a different product from that which is recommended by FWD Life Insurance Corporation based on',
    'disclaimer.3':
      'I/We understand that all information provided in this form have been given to allow the agent to advise / recommend suitable products to me and will be treated with strict confidence.\n\nI/We understand and agree that any personal information collected or held by FWD (whether contained in this form or otherwise obtained) may be used, processed, disclosed and shared by FWD Insurance Berhad to individuals/organisations related to and associated with FWD Insurance Berhad or any selected third party (within or outside of Malaysia, including re and claims investigation companies and industry associations/federations) for the purpose of processing this form and providing subsequent services for this and other financial products and services to communicate with me/us for such purposes.\n\nI/We understand that I/we have the right to request access to my personal information held by FWD Insurance Berhad and to request correction of any personal information which is incorrect or to limit the processing of my personal information. I/We consent and hereby authorise FWD Insurance Berhad to charge a fee for processing and complying with such data access request or correction requests.',
    'disclaimer.3.short':
      'I/We understand that all information provided in this form have been given to allow the agent to advise / recommend',
    'disclaimer.close': 'Close',
    'disclaimer.more': '...more',
    'disclaimer.accept':
      'I/We acknowledged that the agent has provided me/us with a copy of the completed Customer Fact Find form or has shown us/we the content of the Customer Fact Find form in the form of physical or soft copy before the issuance of the policy.',
    'disclaimer.accept2':
      'I/We acknowledge that the agent has shown or provided me/us the web address of the Service Guide.',
    'disclaimer.acceptedOn': 'Accepted on',
    back: 'Back',
    continue: 'Continue',
    shareFNADoc: 'Share FNA doc',
    previewFNA: 'Preview CFF',
    previewCFF: 'Preview CFF',
    viewCFF: 'View CFF',
    done: 'Done',
    createSI: 'Create Sales illustration',
    selectProduct: 'Please select a product',
    selectPackage: 'Please select package',
    productBrochure: 'Product Brochure',
    'start.sale.illustration': 'Start sales illustration',
    basePlan: 'Life Base Plan',
    'familySharePlan.title':
      'Are you purchasing a new plan, or joining an existing one?',
    'familySharePlan.plan.new': 'New plan',
    'familySharePlan.plan.existing': 'Existing plan',
    'familySharePlan.nricLabel': 'NRIC number of the policy owner',
    'familySharePlan.nricHint': 'YYMMDD-PB-###G',
    'familySharePlan.invalidNric': 'Invalid NRIC number',
    'familySharePlan.hasDeathClaim':
      'This policy owner has reported death claim',
    'familySharePlan.noPlan': 'No existing plan under this policy owner',
    'familySharePlan.choosePlan': 'Choose existing plan',
    'familySharePlan.primaryPolicyNumber': 'Primary policy number',
    'familySharePlan.insured': 'Life assured',
    'familySharePlan.availableSlots': 'Available slots',
    'maxQuoteError.title': 'Create new Sales illustration',
    'maxQuoteError.desc':
      'You have reached the limit of {{attempt}} to reselect a product in the same Sales illustration. Please create a new Sales illustration to continue.',
    'maxQuoteError.desc.attempt_one': '{{count}} attempt',
    'maxQuoteError.desc.attempt_other': '{{count}} attempts',
    backToHome: 'Back to home',
    startOver: 'Start over',
  },
  navigation,
  eApp,
  coverageDetails,
  teamManagement,
  policy,
  lead,
  customer,
  news,
  performance,
  fna,
  agentProfile,
  home,
  leadProfile,
  pdfViewer,
  customerFactFind,
  eRecruit,
  birthday,
  aiBot,
  ecoach,
  notification,
  affiliates,
  agentAssist,
  ocr,
  livenessCheck,
  socialMarketing,
  locale: {
    ph: 'Philippines',
    my: 'Malaysia',
    id: 'Indonesia',
    en: 'English',
  },
  language: {
    my: {
      local: 'Bahasa Malaysia',
      en: 'Bahasa Malaysia',
    },
    id: {
      local: 'Bahasa Indonesia',
      en: 'Bahasa Malaysia',
    },

    en: {
      local: 'English',
      en: 'English',
    },
    ph: {
      local: 'Filipino',
      en: 'Filipino',
    },
  },
};

export default en;
