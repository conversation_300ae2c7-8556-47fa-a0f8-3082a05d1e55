import coverageDetails from 'features/coverageDetails/translation/ph/coverageDetails.en';
import eApp from 'features/eAppV2/ph/translation/eApp.en';
import pdfViewer from 'features/pdfViewer/translation/pdfViewer.en';
import proposal from 'features/proposal/translation/ph/en';
import teamManagement from 'features/teamManagement/translation/ph/en';
import agentAssist from 'features/agentAssist/translation/ph/agentAssist.en';
import ocr from 'features/ocr/translation/ocr.en';
import livenessCheck from 'features/livenessCheck/translations/livenessCheck.en';

// ******* After country-specific translations splitting
import document from 'features/Document/translation/ph/documents.en';
import notification from 'features/notification/translation/ph/notification.en';
import agentProfile from 'features/agentProfile/translation/ph/agentProfile.en';
import aiBot from 'features/aiBot/translation/ph/aiBot.en';
import birthday from 'features/birthday/translation/ph/birthday.en';
import eRecruit from 'features/eRecruit/translation/ph/eRecruit.en';
import ecoach from 'features/ecoach/translation/ph/ecoach.en';
import fna from 'features/fna/translation/ph/fna.en';
import news from 'features/fwdNews/translation/ph/news.en';
import home from 'features/home/<USER>/ph/home.en';
import customer from 'features/lead/translation/ph/customer.en';
import lead from 'features/lead/translation/ph/lead.en';
import leadProfile from 'features/lead/translation/ph/leadProfile.en';
import performance from 'features/performance/translation/ph/performance.en';
import policy from 'features/policy/translation/ph/policy.en';
import recognition from 'features/recognition/translation/ph/recognition.en';
import reportGeneration from 'features/reportGeneration/translation/ph/reportGeneration.en';
import navigation from 'navigation/translation/ph/navigation.en';

// Social Marketing translations
import socialMarketing from 'features/socialMarketing/translation/ph/socialMarketing.en';

const en = {
  common: {
    'login.welcome':
      'Welcome! Login to start your new digital working experience.',
    'login.id': 'Agent ID',
    'login.password': 'Password',
    'login.footer': 'FWD Cube version {{appVersion}}',
    'login.error':
      'status: Invalid userid/password.  Please contact ITProdSupport',
    'login.login': 'Login',
    'login.error.username.password':
      'Invalid user ID or password.  If you have forgotten your password, please reset it on eiRIS.',
    'login.error.connection':
      'Connection issue.  Please check your connection and try again shortly.',
    'login.error.dueTo': 'Unable to login due to error: {{message}}',
    'login.error.onlyOneAgent':
      'Only 1 agent ID can be used to login on this device',
    'login.versionCheck.title': 'Version update',
    'login.versionCheck.message': 'Please download the latest version.',
    'login.versionCheck.download': 'Download',
    'login.versionCheck.askMeLater': 'Ask me Later',
    'login.toCube': 'Login to FWD Cube',
    'login.forgetPassword': 'Forget Password? Please reset your password in ',
    'login.biometric.button': 'Log in with {{bioMeticType}}',
    'login.biometric.touchID': 'Touch ID',
    'login.biometric.faceID': 'Face ID',
    'login.biometric.iris': 'Iris',
    'login.biometric.biometric': 'Biometric',
    'login.biometric.prompt.question':
      'Do you want to enable "FWD Cube" to use {{bioMeticType}}?',
    'login.biometric.prompt.description':
      'Enable {{bioMeticType}} for faster log in. You can turn this feature on or off at any time under Settings.',
    'login.biometric.prompt.enable': 'Enable',
    'login.biometric.prompt.cancel': 'Cancel',
    'login.biometric.prompt.notNow': 'Not Now',
    'login.biometric.prompt.fallbackLabel': 'Try manual input',
    'login.biometric.error.failed': 'Biometric login failed',
    'login.biometric.error.noHardware':
      "Your device doesn't support Biometric login",
    'login.biometric.error.notEnrolled':
      "You haven't enroll any Biometric data on your device",
    'login.here': 'here',
    'home.welcome.hi': 'Hi, {{name}}!',
    'home.welcome.question': 'What would you like to do?',
    'validation.required': 'Required field',
    'navigation.tabScreen.MyLeads': 'My Leads',
    'navigation.tabScreen.SalesActivity': 'Sales activity',
    'navigation.tabScreen.Overview': 'Overview',
    'navigation.tabScreen.MyTasks': 'My tasks',
    'todayTasks.title': 'Today tasks',
    'form.invalidInput': 'Invalid field',
    'form.invalidInputFormat': 'Invalid field',
    'form.phoneNumberTooShort': 'Phone number too short',
    'form.phoneNumberTooLong': 'Phone number too long',
    'form.invalidPhoneNumber': 'Invalid phone number',
    php: 'PHP',
    imLookingFor: "I'm looking for...",
    noResultsFound: 'No results found',
    loadingFail: 'Data retrieved failed. Please visit again later.',
    searchSuggestion:
      'Adjust keyword to get better result or try another search.',
    foundXResults: 'We found {{count}} results',
    view: 'View',
    done: 'Done',
    close: 'Close',
    add: 'Add',
    details: 'Details',
    select: 'Select',
    compare: 'Compare',
    cancel: 'Cancel',
    confirm: 'Confirm',
    search: 'Search',
    continue: 'Continue',
    next: 'Next',
    'search.leadName': 'Lead name',
    'search.companyName': 'Company name',
    'search.mobile': 'Mobile',
    'search.email': 'Email',
    searchResults: 'Search results ({{count}})',
    years: 'years',
    save: 'Save',
    reset: 'Reset',
    exit: 'Exit',
    ok: 'OK',
    yes: 'Yes',
    no: 'No',
    withCurrency: 'PHP {{amount}}',
    currencySymbol: 'PHP',
    'position.1': 'First',
    'position.2': 'Second',
    'position.3': 'Third',
    'position.4': 'Fourth',
    'position.5': 'Fifth',
    'position.6': 'Sixth',
    'position.7': 'Seventh',
    'position.8': 'Eighth',
    'position.9': 'Ninth',
    shortYearOld: 'y.o.',
    asOfDate: 'As of {{date}}',
    withYears: '{{year}} year(s)',
    en: 'English',
    ph: 'Filipino',
    error: 'Error',
    backendError:
      'Error connecting to back-end system. Please try again later or contact support.',
    download: 'Download',
    share: 'Share',
    viewProfile: 'View profile',
    'searchExistingLeadModal.existingLeadTitle': 'Search for existing FWD lead',
    'searchExistingLeadModal.existingCompanyTitle':
      'Search for existing company',
    'searchExistingLeadModal.entityLeadPlaceholder':
      'Company name/ phone number/ email',
    'searchExistingLeadModal.individualLeadPlaceholder':
      'Lead name/ phone number/ email',
    'searchExistingLeadModal.emptyIndividual':
      'No pre-existing lead available. Please try another search.',
    'searchExistingLeadModal.emptyEntity':
      'No pre-existing company available. Please try another search.',
    'autoComplete.noData': 'No matching options',
    pdfDownload: 'Document downloading...',
    pdfDownloadedSuccessfully: 'Document has been downloaded',
    pdfFailToDownload: 'Download failed',
    pdfFailToShare: 'File sharing failed: {{msg}}',
    dateHint: 'MM/DD/YYYY',
    incompleteFields_other: '{{count}} incomplete fields',
    incompleteFields_one: '{{count}} incomplete field',
    goToTheField: 'Go to the field',
    'ocr.upload.title': 'Upload ID card',
    'ocr.upload.title.mobile': 'Scan ID card',
    'ocr.upload.description':
      'Upload ID to skip manual verification and auto-populate your info below.',
    'ocr.upload.description.mobile': 'e.g. ID card or passport',
    'ocr.upload': 'Upload',
    'ocr.uploading': 'Document uploading...',
    'ocr.verifying': 'Verifying...',
    'ocr.scanID': 'Scan ID card',
    'ocr.scanID.example': 'e.g. ID card or passport',
    'ocr.scanID.info.title': 'Acceptant of ID list',
    'ocr.scanID.info.point.1': 'UMID (Unified Multi-Purpose ID)',
    'ocr.scanID.info.point.2': 'RP Passport',
    'ocr.scanID.info.point.3': "Driver's License",
    'ocr.scanID.info.point.4': 'PRC ID (Professional Regulation Commission ID)',
    'ocr.scanID.info.point.5': 'SSS (Social Security System ID)',
    'ocr.scanID.info.point.6': 'TIN (Bureau of Internal Revenue ID)',
    'ocr.instruction.takeA': 'Take a ',
    'ocr.instruction.closeUp': 'close up',
    'ocr.instruction.photo': ' photo of your ID card',
    'ocr.instruction.makeSure': 'Make sure your photo is:',
    'ocr.instruction.makeSure.clear': 'Clear',
    'ocr.instruction.makeSure.clear.1': ' visible and in focus',
    'ocr.instruction.makeSure.withoutFlash': 'Taken',
    'ocr.instruction.makeSure.withoutFlash.1': ' without flash',
    'ocr.picker.title': 'Upload profile image by',
    'ocr.re-upload': 'Re-upload',
    'ocr.verified': 'Verified',
    'birthDay.sendCard': 'Send birthday card',
    new: 'New',
    'upload.invalidFileSize': 'The file you’re uploading exceeds the maximum file size allowed.  Please retry uploading a file less in size.',
    'upload.invalidFileExtension':
      'The file type you’re uploading is invalid.  Only {{fileTypes}} are accepted.',
  },
  smart: {
    'proposaldetails.totalpremium': 'Total premium/annually',
    'plan.sumAssured': 'Total sum assured',
    'proposaldetails.basepremium': 'Premium',
    'proposaldetails.basesumassured': 'Sum assured',
    'proposaldetails.paymentmode': 'Payment mode',
    'proposaldetails.paymenterm': 'Payment term',
    'personalDetails.title': 'Personal Details',
    'personalDetails.scanIDCard': 'Scan ID card',
    'personalDetails.scanIDCard.hint': 'e.g. ID card or passport',
    'personalDetails.name': 'Name',
    'personalDetails.customerType': 'Customer type',
    'personalDetails.salutation': 'Salutation/Title',
    'personalDetails.firstName': 'First name',
    'personalDetails.middleName': 'Middle name (optional)',
    'personalDetails.lastName': 'Last name',
    'personalDetails.extensionName': 'Extension name (optional)',
    'personalDetails.dateOfBirth': 'Date of birth',
    'personalDetails.occupation.question':
      'What best describes your occupation?',
    'personalDetails.smoking': 'Smoking Habit',
    'personalDetails.reviewPersonalHealthQuestion':
      'Review personal health question',
    'personalDetails.gender': 'Gender (optional)',
    'rider.sumAssured': 'Sum assured (PHP)',
    'rider.regularTopup': 'Top-up amount (PHP)',
    'rider.coverage': 'Coverage to',
    'rider.planOption': 'Plan option',
  },
  proposal,
  savedProposals: {
    title: 'Saved proposals',
    totalSavedProposal: 'Total saved proposals',
    filterBy: 'Filter by',
    filtered: 'Filtered',
    proposalStage: 'Proposal stage',
    clearAll: 'Reset',
    apply: 'Apply',
    'filter.FNA': 'FNA',
    'filter.CFF': 'CFF',
    'filter.QUICK_SI': 'Quick quote',
    'filter.FULL_SI': 'SI',
    'filter.IN_APP': 'Application',
    'filter.APP_SUBMITTED': 'Submitted',
    'filter.emptyRecord': 'Empty record',
    'filter.COVERAGE': 'Coverage',
    'filter.REMOTE_SELLING_COMPLETED': 'Remote selling completed',
    'filter.UNKNOWN': 'Unknown',
    'filter.REJECTED_BY_LEADER': 'Rejected by leader',
    'filter.PENDING_FOR_LEADER': 'Pending for leader',
    'filter.APPROVED_BY_LEADER': 'Approved by leader',
    'filter.EXPIRED_AFTER_15_DAYS': 'Expired after 15 days',
    fullTable: 'Full table',
    sortByTime: 'Sort by',
    newest: 'Newest',
    oldest: 'Oldest',
    showIn: 'Show in',
    inDays: '{{day}} days',
    noResultsFound: 'Oops! Empty saved proposals.',
    loadingProposalsMessage:
      'Please wait we are retrieving data from last {{day}} days ago',
    proposalName: 'Proposal name',
    insured: 'Insured',
    insuredName: 'Insured name',
    status: 'Status',
    productName: 'Product Name',
    product: 'Product',
    premiumAmount: 'Premium Amount',
    modalPremium: 'Modal premium',
    sumAssured: 'Sum Assured',
    proposalNo: 'Proposal No',
    lastUpdate: 'Last update',
    lastUpdateDate: 'Last update date',
    date: 'Date',
    showDataIn: 'Show data in',
    lastDays: 'Last {{day}} days',
    proposalPlaceholder: 'Proposal',
    searchHint:
      'e.g. Customer first name/ last name, product name or proposal name',
    expired: 'Expired',
    'paymentMode.EVERY_YEAR': 'annually',
    'paymentMode.EVERY_HALF_YEAR': 'semi-annually',
    'paymentMode.EVERY_QUARTER': 'quarterly',
    'paymentMode.EVERY_MONTH': 'monthly',
    'paymentMode.ONE_TIME': 'single premium',
    'search.noResult': 'No results found.',
    'search.adjustKeyword': 'Adjust keyword to get better result.',
    'search.result': 'Search results',
    'search.searchBar.placeholder': 'Search saved proposal',
    'search.description':
      'e.g. Customer first name/ last name, product name or proposal name',
    'search.dataFrom.period': 'Displaying data from last 90 days.',
    currency: 'PHP/USD',
    createNewProposal: 'Create new proposal',
    pendingPOPI: 'Pending PO & PI remote selling',
    pendingPO: 'Pending PO remote selling',
    pendingPI: 'Pending PI remote selling',
  },
  product: {
    gotIt: 'Got it',
    invalidLicense: 'Agent is not licensed to sell {{productName}}',
    selectOneProduct: 'Select one product',
    yourGoalSummary: 'Your goal summary',
    recommended: 'Recommended',
    recommendedFor: 'Recommended for',
    details: 'Details',
    forYou: 'For you',
    'reason.title':
      'Please give a reason to select a product out of recommended list from FNA',
    'reason.title2': 'Please give a reason for selecting the product',
    'reason.0': '',
    'reason.1': 'Client has existing FWD policy/ies with the same features',
    'reason.2':
      'Client has existing policy/ies with the same features with another insurance company',
    'reason.3': 'User selected product',
    'disclaimer.title': 'Client waiver and client disclaimer',
    'disclaimer.1':
      'I acknowledge that FWD Life Insurance Corporation and/or its Financial Wealth Planner/ Financial Sales Consultant may recommend the most appropriate product/s suited to my financial needs, which I can use as a guide.  If I choose to avail a product different from that which is recommended, I agree to hold FWD Life Insurance Corporation, its principals, their representatives, and successors in interest free and harmless from any and all liabilities, claims, opportunity cost and/or causes of action of whatever kind or nature, that may affect me as a result of or due to this choice.\nI, understand that the evaluation and recommendation are based on the information and data provided by me and are designed to help me assess my financial needs. I am aware that my financial needs may change over time depending on my financial situation and financial objectives/goals. Therefore, any suggestions and recommendations provided here are general advices only based on my current status and are intended for reference only.',
    'disclaimer.1.short':
      'I acknowledge that FWD Life Insurance Corporation and/or its Financial Wealth Planner/ Financial Sales Consultant may',
    'disclaimer.2':
      'I am choosing to avail of a different product(s)/fund(s)/portfolio(s) from that which is(are) recommended by FWD Life Insurance Corporation based on my declared financial needs/risk profile, financial situation (e.g. budget) and financial objectives (e.g. quantum of coverage/required financial goals). I agree to hold FWD Life Insurance Corporation, its principals, their representatives, and successors in interest free and harmless from any and all liabilities, claims, opportunity cost and/or cause of action of whatever kind of nature, that may affect me as a result of or due to this choice.\n\nI, understand that the evaluation and recommendation are based on the information and data provided by me and are designed to help me assess my financial needs. I am aware that my financial needs may change over time depending on my financial situation and financial objectives/goals. Therefore, any suggestions and recommendations provided here are general advices only based on my current status and are intended for reference only.',
    'disclaimer.2.short':
      'I am choosing to avail of a different product(s)/fund(s)/portfolio(s) from that which is(are) recommended by FWD Life Insurance Corporation based on',
    'disclaimer.3':
      'I acknowledge that FWD Life Insurance Corporation and/or its Financial Wealth Planner/ Financial Sales Consultant may recommend the most appropriate product/s suited to my financial needs, which I can use as a guide.  If I choose to avail a product different from that which is recommended, I agree to hold FWD Life Insurance Corporation, its principals, their representatives, and successors in interest free and harmless from any and all liabilities, claims, opportunity cost and/or causes of action of whatever kind or nature, that may affect me as a result of or due to this choice.\nI, understand that the evaluation and recommendation are based on the information and data provided by me and are designed to help me assess my financial needs. I am aware that my financial needs may change over time depending on my personal situation and objectives. Therefore, any suggestions and recommendations provided here are general advice only based on my current status and are intended for reference only.',
    'disclaimer.3.short':
      'I acknowledge that FWD Life Insurance Corporation and/or its Financial Wealth Planner/ Financial Sales Consultant may',
    'disclaimer.close': 'Close',
    'disclaimer.more': '...more',
    'disclaimer.accept':
      'I have carefully read and understood the statement and that I accept it.',
    'disclaimer.acceptedOn': 'Accepted on',
    back: 'Back',
    continue: 'Continue',
    shareFNADoc: 'Share FNA doc',
    previewFNA: 'FNA Document',
    done: 'Done',
    basePlan: 'Life Base Plan',
  },
  navigation,
  eApp,
  coverageDetails,
  teamManagement,
  policy,
  lead,
  customer,
  news,
  performance,
  recognition,
  fna,
  agentProfile,
  home,
  leadProfile,
  pdfViewer,
  reportGeneration,
  document,
  eRecruit,
  birthday,
  aiBot,
  ecoach,
  notification,
  agentAssist,
  ocr,
  livenessCheck,
  socialMarketing,
  locale: {
    ph: 'Philippines',
    my: 'Malaysia',
  },
};

export default en;
