import * as FileSystem from 'expo-file-system';

export const DOCUMENT_DIR = 'documents/';

export const copyToAppFolder = async (
  appDir: string,
  srcUri: string,
  name?: string,
) => {
  if (srcUri.length === 0) return;
  await FileSystem.makeDirectoryAsync(FileSystem.documentDirectory + appDir, {
    intermediates: true,
  });
  await FileSystem.copyAsync({
    from: srcUri,
    to:
      FileSystem.documentDirectory +
      appDir +
      (name || srcUri.split('/').slice(-1)[0]),
  });
};

export const copyToAppDocumentFolder = async (
  srcUri: string,
  name?: string,
) => {
  return copyToAppFolder(DOCUMENT_DIR, srcUri, name);
};

export const saveFileToAppFolder = async (
  appDir: string,
  name: string,
  base64: string,
) => {
  await FileSystem.makeDirectoryAsync(FileSystem.documentDirectory + appDir, {
    intermediates: true,
  });
  await FileSystem.writeAsStringAsync(
    FileSystem.documentDirectory + appDir + name,
    base64,
    {
      encoding: FileSystem.EncodingType.Base64,
    },
  );
};

export const saveFileToDocumentFolder = async (
  name: string,
  base64: string,
) => {
  return saveFileToAppFolder(DOCUMENT_DIR, name, base64);
};

export const getAppFileUri = (appDir: string, fileName: string) => {
  if (fileName.length === 0) return '';
  return FileSystem.documentDirectory + appDir + fileName;
};

export const getDocumentUri = (fileName: string) => {
  return getAppFileUri(DOCUMENT_DIR, fileName);
};

export async function ensureDirectoryExists(fileUri: string) {
  const directory = fileUri.substring(0, fileUri.lastIndexOf('/'));
  const dirInfo = await FileSystem.getInfoAsync(directory);

  if (!dirInfo.exists) {
    await FileSystem.makeDirectoryAsync(directory, { intermediates: true });
  }
}
