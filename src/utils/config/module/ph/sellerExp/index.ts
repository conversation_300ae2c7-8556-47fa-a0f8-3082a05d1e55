import { SellerExpModuleConfig } from 'types/moduleConfig';

const sellerConfig: SellerExpModuleConfig = {
  isLoginActiveChecked: false,
  Home: true,
  Lead: true,
  Proposals: true,
  Policies: true,
  Performance: true,
  Document: true,
  Others: true,
  Customers: true,
  ReportGeneration: true,
  TeamManagement: true,
  SocialMarketing: true,
  ERecruit: true,
  myLMS: false,
  Merchandise: false,
  Affiliate: false,
  AgentAssist: true,
  dashboard: {
    TrainerGuruCard: {
      isShown: true,
    },
    FWDNewsCard: {
      isShown: true,
    },
    PerformanceCard: {
      isShown: true,
      isMdrtShown: true,
    },
    BusinessOpportunityCard: {
      isShown: true,
    },
    MarketingCard: {
      isShown: true,
    },
  },
  tasks: {
    AgencyConfidentialReportSection: true,
    BirthdaySection: true,
    ContactLeadsSection: true,
    PaymentRemindersSection: true,
    PolicyIssuesSection: true,
    showCount: true,
  },
  home: {
    businessOppCard: {
      isReminderVisible: true,
      hasViewAll: false,
    },
  },
  lead: {
    leadActivityModal: true,
    policyModal: false,
    today: {
      sortByNewest: true,
    },
  },
  performance: {
    tipYourPerformance: false,
    metricOne: 'APE',
    metricTwo: 'Case',
    TargetSetting: false,
    targetOneField: 'apeTarget',
    completionOneField: 'apeCompletion',
    submissionOneField: 'apeSubmission',
    submissionListField: 'apeSubmissionList',
    submissionItemSubField: 'apeSub',
    completionListField: 'apeCompletionList',
    completionItemSubField: 'ape',
    isRankingShown: true,
    isRecognitionShown: true,
    isPerformanceBSCShown: false,
  },
  isLoggedInUntilAppKilled: true,
  showPromptLibrary: true,
  contactOptions: ['call', 'sms', 'email', 'viber', 'more'],
};

export default sellerConfig;
