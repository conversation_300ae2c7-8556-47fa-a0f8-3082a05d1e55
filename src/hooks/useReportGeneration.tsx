import { useQuery } from '@tanstack/react-query';
import useBoundStore from './useBoundStore';
import {
  getReportListByReportStatement,
  getLapseReport,
  getPolicyAnniversaryReport,
  getPremiumReceivedReport,
  getUnsuccessfulAdaAcaReport,
  getCreditCardExpirationReport,
  getDueDateReport,
  getApplicationNotProceedReport,
  getInquiriesReport,
  getTransactionReport,
} from 'api/reportApi';
import {
  ReportStatement,
  Status,
  reportDownloadRequestObj,
} from 'types/report';
import * as FileSystem from 'expo-file-system';
import { baseUrl } from 'utils/context';
import { PermissionsAndroid, Platform } from 'react-native';
import * as Device from 'expo-device';
import * as Sharing from 'expo-sharing';
import { format } from 'date-fns';
import { addErrorToast } from 'cube-ui-components';

const QUERY_KEY = 'report';

const getAccessTokenBearer = () => {
  return 'Bearer ' + useBoundStore.getState().auth.authInfo?.accessToken;
};

export function useGetReportListByReportStatement(
  reportStatement: ReportStatement,
) {
  return useQuery({
    queryKey: [QUERY_KEY, reportStatement],
    queryFn: () => getReportListByReportStatement(reportStatement),
  });
}

export async function getPersistencyReport(
  reportDownloadRequestObj: reportDownloadRequestObj,
) {
  const reportUrl =
    baseUrl +
    '/api-gateway' +
    `/exp/api/report/download?` +
    `documentCode=${reportDownloadRequestObj.documentCode}&` +
    `uploadDate=${reportDownloadRequestObj.uploadDate}`;

  const strTimestamp = format(new Date(), 'MM-dd-yyyy-hh-mm-ss');
  const fileName =
    reportDownloadRequestObj.documentCode +
    reportDownloadRequestObj.uploadDate +
    strTimestamp +
    '.xlsx';
  // const downloadXLSXPath = `${FileSystem.documentDirectory}${fileName}`;
  const downloadXLSXPath =
    FileSystem.documentDirectory +
    reportDownloadRequestObj.documentCode +
    reportDownloadRequestObj.uploadDate +
    strTimestamp +
    '.xlsx';

  // Premission to Expo-File-System
  const directoryInfo = await FileSystem.getInfoAsync(downloadXLSXPath);
  if (!directoryInfo.exists) {
    await FileSystem.makeDirectoryAsync(downloadXLSXPath);
  }

  // Download to Expo-File-System
  try {
    const { uri } = await FileSystem.downloadAsync(
      reportUrl,
      downloadXLSXPath,
      {
        headers: {
          Authorization: getAccessTokenBearer() || '',
          tenant: reportDownloadRequestObj.tenant,
        },
      },
    );

    if (Platform.OS === 'android') {
      //   // Android
      await copyFileAndroid(uri, fileName);
    } else {
      // IOS: save in Share function
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    if (__DEV__) {
      console.log('Error in copy File: ', error);
      addErrorToast([{ message: `${error}` }]);
    }
    return error;
  }
}

export async function getReportAsync(
  reportDownloadRequestObj: reportDownloadRequestObj,
) {
  const reportUrl =
    baseUrl +
    '/api-gateway' +
    `/exp/api/report/download?` +
    `documentIndex=${reportDownloadRequestObj.documentIndex}&` +
    `documentCode=${reportDownloadRequestObj.documentCode}`;

  const strTimestamp = format(new Date(), 'MM-dd-yyyy-hh-mm-ss');
  const fileName =
    reportDownloadRequestObj.documentCode +
    reportDownloadRequestObj.documentIndex +
    strTimestamp +
    '.pdf';
  // const downloadPdfPath = `${FileSystem.documentDirectory}${fileName}`;
  const downloadPdfPath =
    FileSystem.documentDirectory +
    reportDownloadRequestObj.documentCode +
    reportDownloadRequestObj.documentIndex +
    strTimestamp +
    '.pdf';

  // Premission to Expo-File-System
  const directoryInfo = await FileSystem.getInfoAsync(downloadPdfPath);
  if (!directoryInfo.exists) {
    await FileSystem.makeDirectoryAsync(downloadPdfPath);
  }

  // Download to Expo-File-System
  try {
    const { uri } = await FileSystem.downloadAsync(reportUrl, downloadPdfPath, {
      headers: {
        Authorization: getAccessTokenBearer() || '',
        tenant: reportDownloadRequestObj.tenant,
      },
    });

    if (Platform.OS === 'android') {
      // Android
      await copyFileAndroid(uri, fileName);
    } else {
      // IOS: save in Share function
      await Sharing.shareAsync(uri);
    }
  } catch (error) {
    if (__DEV__) {
      console.log('Error in copy File: ', error);
      addErrorToast([{ message: `${error}` }]);
    }
    return error;
  }
}

const copyFileAndroid = async (orgFileUri: string, fileName: string) => {
  // TODO: use react-native-mime-types or other library to get mime type for more file types
  const fileExtName = fileName.split('.').pop();
  const mimeType =
    fileExtName === 'xlsx'
      ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      : 'application/pdf';

  const requestPermission = async () => {
    let premisstionType = 'WRITE_EXTERNAL_STORAGE';
    try {
      const osVersion = +(Device.osVersion || '').split('.')[0];
      if (osVersion >= 13) {
        premisstionType = 'READ_MEDIA_IMAGES';
      }

      const requestResult = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS[premisstionType],
      );

      if (requestResult !== 'granted') return null;

      const result =
        await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync(
          'content://com.android.externalstorage.documents/tree/primary%3ADownload',
        );
      return result;
    } catch (error) {
      return null;
    }
  };

  const toBase64 = async (uri: string) => {
    try {
      return await FileSystem.readAsStringAsync(uri, { encoding: 'base64' });
    } catch (error) {
      return '';
    }
  };

  const requestResult = await requestPermission();

  if (!requestResult || !requestResult.granted)
    throw new Error('Permission not granted');

  const uri = await FileSystem.StorageAccessFramework.createFileAsync(
    requestResult.directoryUri,
    fileName,
    mimeType,
  );

  const base64File = await toBase64(orgFileUri);

  await FileSystem.StorageAccessFramework.writeAsStringAsync(uri, base64File, {
    encoding: 'base64',
  });
};

/**
 * Lapsed policies report
 * Premium received report
 * Credit card expiration report
 * Unsuccessful ADA/ACA report
 * Policy anniversary
 */
const QUERY_KEY_INQUIRIES = '/exp/api/report/policy-report';
const QUERY_KEY_DUE_DATE = '/exp/api/report/due-date-report';
const QUERY_KEY_APP_NOT_PROCEED =
  '/exp/api/report/application-not-proceed-report';
const QUERY_KEY_TRANSACTION_REPORT =
  '/exp/api/report/policy-transaction-report';

const QUERY_KEY_LAPSE = '/exp/api/report/lapse-report';
const QUERY_KEY_PREMIUM_RECEIVED = '/exp/api/report/premium-received';
const QUERY_KEY_CREDIT_CARD_EXPIRATION_REPORT =
  '/exp/api/report/credit-card-expiration';
const QUERY_KEY_UNSUCCESSFUL_ADA_ACA = '/exp/api/report/unsuccessful-ada-aca';
const QUERY_KEY_POLICY_ANNIVERSARY = '/exp/api/report/policy-anniversary';

export function useGetInquiriesReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
  status,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  status: { code: string; meaning: string }[];
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_DUE_DATE,
      agentId,
      from,
      to,
      dueDateReportPolicyInfo,
      team,
      status,
    ],
    queryFn: () =>
      getInquiriesReport({
        agentId,
        from,
        to,
        dueDateReportPolicyInfo,
        team,
        status,
      }),
  });
}
export function useGetTransactionReport({
  agentId,
  from,
  to,
  transactionReportPolicyInfo,
  team,
  dateType = 'ISS', // ISS = Issue Date, EFF = Transaction Date
}: {
  agentId?: string;
  from: string;
  to: string;
  transactionReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  dateType: string;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_TRANSACTION_REPORT,
      agentId,
      from,
      to,
      transactionReportPolicyInfo,
      team,
      dateType,
    ],
    queryFn: () =>
      getTransactionReport({
        agentId,
        from,
        to,
        transactionReportPolicyInfo,
        team,
        dateType,
      }),
  });
}

export function useGetDueDateReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_DUE_DATE,
      agentId,
      from,
      to,
      dueDateReportPolicyInfo,
      team,
    ],
    queryFn: () =>
      getDueDateReport({
        agentId,
        from,
        to,
        dueDateReportPolicyInfo,
        team,
      }),
  });
}

export function useGetApplicationNotProceedReport({
  agentId,
  from,
  to,
  policyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  policyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [QUERY_KEY_APP_NOT_PROCEED, agentId, from, to, policyInfo, team],
    queryFn: () =>
      getApplicationNotProceedReport({
        agentId,
        from,
        to,
        policyInfo,
        team,
      }),
  });
}

export function useGetLapseReport({
  agentId,
  from,
  to,
  status,
  lapseReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from?: string;
  to?: string;
  status: Status;
  lapseReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_LAPSE,
      agentId,
      from,
      to,
      status,
      lapseReportPolicyInfo,
      team,
    ],
    queryFn: () =>
      getLapseReport({
        agentId,
        from,
        to,
        status,
        lapseReportPolicyInfo,
        team,
      }),
  });
}

export function useGetPremiumReceivedReport({
  agentId,
  from,
  to,
  premiumReceivedPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  premiumReceivedPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_PREMIUM_RECEIVED,
      agentId,
      from,
      to,
      premiumReceivedPolicyInfo,
      team,
    ],
    queryFn: () =>
      getPremiumReceivedReport({
        agentId,
        from,
        to,
        premiumReceivedPolicyInfo,
        team,
      }),
  });
}

export function useGetCreditCardExpirationReport({
  agentId,
  dateFilter,
  from,
  to,
  creditCardExpiationPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  creditCardExpiationPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_CREDIT_CARD_EXPIRATION_REPORT,
      agentId,
      dateFilter,
      from,
      to,
      creditCardExpiationPolicyInfo,
      team,
    ],
    queryFn: () =>
      getCreditCardExpirationReport({
        agentId,
        dateFilter,
        from,
        to,
        creditCardExpiationPolicyInfo,
        team,
      }),
  });
}

export function useGetUnsuccessfulAdaAcaReport({
  agentId,
  dateFilter,
  from,
  to,
  unsuccessfulAdaAcaPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  unsuccessfulAdaAcaPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  return useQuery({
    queryKey: [
      QUERY_KEY_UNSUCCESSFUL_ADA_ACA,
      agentId,
      dateFilter,
      from,
      to,
      unsuccessfulAdaAcaPolicyInfo,
      team,
    ],
    queryFn: () =>
      getUnsuccessfulAdaAcaReport({
        agentId,
        dateFilter,
        from,
        to,
        unsuccessfulAdaAcaPolicyInfo,
        team,
      }),
  });
}

export function useGetPolicyAnniversaryReport({
  from,
  to,
}: {
  from: string;
  to: string;
}) {
  return useQuery({
    queryKey: [QUERY_KEY_POLICY_ANNIVERSARY, from, to],
    queryFn: () => getPolicyAnniversaryReport({ from, to }),
  });
}
