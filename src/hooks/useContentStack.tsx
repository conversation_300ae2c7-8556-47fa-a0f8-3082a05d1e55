import {
  build,
  contentStackKey,
  contentStackDeliveryToken,
  contentStackEnvironment,
  country,
} from 'utils/context';
import ContentStack from 'contentstack';

export const CONTENT_TYPES = {
  VERSION_CHECK: 'cube_settings',
  DOCUMENT: 'cube_document_page',
  NEWS: 'cube_agent_news_post',
  RECRUITMENT_MATERIALS: 'cube_recruitment_materials',
  AGENT_REWARDS: 'cube_pop_up_modal',
  FEATURE_TOGGLE: 'cube_feature_toggle',
  IGNITE_TEMPLATE: 'cube_ignite_template',
} as const;

export const CONTENT_TYPES_ID = {
  VERSION_CHECK: 'blt4905468791c85852',
  DOCUMENT: '',
  NEWS: '',
  RECRUITMENT_MATERIALS: '',
} as Record<keyof typeof CONTENT_TYPES, string>;

const defaultContentStackConfig = {
  api_key: contentStackKey,
  delivery_token: contentStackDeliveryToken,
  environment: contentStackEnvironment,
};

const mysContentStackConfig = {
  ...defaultContentStackConfig,
  branch: 'capricorn',
};

export const contentStackConfig = ['my', 'ib'].includes(country)
  ? mysContentStackConfig
  : defaultContentStackConfig;

const contentStack = () => {
  const stack = ContentStack.Stack(contentStackConfig);
  return stack;
};

export default contentStack();

type MaintenanceSettings = {
  maintenance_status: boolean;
  message: string;
};

export type SurveySettings = {
  day_of_month?: number;
};

export type CubeSettingsContentStackItem = {
  _version: number;
  locale: string;
  uid: string;
  ACL: Record<string, unknown>;
  _in_progress: boolean;
  created_at: string;
  created_by: string;
  latest_app_version: LatestAppVersion[];
  maintenance_settings: MaintenanceSettings;
  survey_settings?: SurveySettings;
  tags: Array<unknown>;
  title: string;
  updated_at: string;
  updated_by: string;
  publish_details: PublishDetails;
};

type LatestAppVersion = {
  system_os: 'ios' | 'android';
  latest_app_version_number: string;
  app_download_url: string;
  force_user_to_upgrade: boolean;
  _metadata: Metadata;
  tag?: string;
};

type Metadata = {
  uid: string;
};

type PublishDetails = {
  environment: string;
  locale: string;
  time: string;
  user: string;
};
