import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getAgentProfileById } from 'api/authApi';
import useBoundStore, { getBoundStoreState } from './useBoundStore';
import { AgentProfile } from 'types/auth';
import { useCallback, useEffect } from 'react';

const QUERY_KEY = 'agentProfile';

export function getQueryKey(agentId: string | null | undefined) {
  const agentCode = agentId;
  return [QUERY_KEY, String(agentCode)];
}

export function useGetAgentProfile() {
  const agentCode = useBoundStore(state => state.auth.agentCode);

  return useQuery<AgentProfile>({
    queryKey: getQueryKey(agentCode),
    queryFn: () => getAgentProfileById(String(agentCode)),
    staleTime: 60000,
  });
}
export function useGetAgentProfileManually() {
  const agentCode = useBoundStore(state => state.auth.agentCode);

  const queryClient = useQueryClient();

  return useMutation<AgentProfile>({
    mutationFn: () => getAgentProfileById(String(agentCode)),
    onSuccess: data => {
      queryClient.setQueryData(getQueryKey(agentCode), data);
      queryClient.invalidateQueries(getQueryKey(agentCode));
    },
  });
}
export function useGetAgentInfoByAgentId(agentId: string | undefined) {
  return useQuery<AgentProfile>({
    queryKey: getQueryKey(agentId),
    queryFn: () => getAgentProfileById(String(agentId)),
    enabled: Boolean(agentId),
  });
}

export function useResetAgentProfileCache() {
  const agentCode = useBoundStore(state => state.auth.agentCode);
  const queryClient = useQueryClient();

  const resetAgentProfile = useCallback(async () => {
    await queryClient.resetQueries({
      queryKey: getQueryKey(agentCode),
    });
  }, [agentCode]);

  return {
    resetAgentProfile,
  };
}
