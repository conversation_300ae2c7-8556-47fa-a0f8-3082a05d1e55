import React from 'react';
import {
  Image,
  ImageSourcePropType,
  TouchableOpacity,
  View,
} from 'react-native';
import styled from '@emotion/native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { Body, H6, SmallLabel } from 'cube-ui-components';
import { Spacer } from 'features/lead/ph/tablet/components/LeadTableTitleRow';
import { cheveronRightInCircle } from 'features/ecoach/assets';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const Container = styled(TouchableOpacity)<{
  bgColor: string;
  isTabletMode: boolean;
}>(({ bgColor, isTabletMode }) => ({
  flex: 1,
  flexDirection: 'column',
  justifyContent: 'space-between',
  paddingHorizontal: isTabletMode ? sizes[6] : sizes[5],
  paddingVertical: isTabletMode ? sizes[8] : sizes[5],
  borderRadius: sizes[4],
  borderWidth: 0,
  backgroundColor: bgColor,
  minHeight: isTabletMode ? 240 : 140,
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: 4,
  },
  shadowOpacity: 0.15,
  shadowRadius: 8,
  elevation: 8,
}));

const TextContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    flex: 1,
    width: isTabletMode ? '75%' : '70%',
    zIndex: 2,
  }),
);

const CardImage = styled(Image)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    position: 'absolute',
    bottom: isTabletMode ? sizes[2] : sizes[1],
    right: isTabletMode ? sizes[4] : sizes[2],
    width: isTabletMode ? 100 : 85,
    height: isTabletMode ? 100 : 85,
    zIndex: 1,
    opacity: 0.9,
  }),
);

const Circle = styled(Image)<{ isTabletMode: boolean }>(({ isTabletMode }) => ({
  alignSelf: 'flex-start',
  marginTop: isTabletMode ? sizes[3] : sizes[2],
  width: isTabletMode ? sizes[10] : sizes[8],
  height: isTabletMode ? sizes[10] : sizes[8],
  zIndex: 2,
}));

const Tag = styled(View)(() => ({
  position: 'absolute',
  top: -sizes[2],
  left: sizes[4],
  paddingHorizontal: sizes[3],
  height: sizes[7],
  borderRadius: sizes[6],
  backgroundColor: colors.white,
  alignItems: 'center',
  justifyContent: 'center',
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.1,
  shadowRadius: 4,
  elevation: 4,
  zIndex: 3,
}));

type NavigationCardProps = {
  onPress: () => void;
  title: string;
  description: string;
  timeTag: string;
  timeDescription: string;
  imgSrc: ImageSourcePropType;
  bgColor: string;
};

const NavigationCard = ({
  title,
  description,
  timeTag,
  timeDescription,
  imgSrc,
  bgColor,
  onPress,
}: NavigationCardProps) => {
  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <Container isTabletMode={isTabletMode} bgColor={bgColor} onPress={onPress}>
      <Tag>
        <SmallLabel fontWeight="bold" color={colors.fwdAlternativeOrange[100]}>
          {timeTag}
        </SmallLabel>
      </Tag>
      <TextContainer isTabletMode={isTabletMode}>
        <H6
          fontWeight="bold"
          color={colors.white}
          style={{
            lineHeight: isTabletMode ? 28 : 24,
            marginBottom: sizes[2]
          }}
        >
          {title}
        </H6>
        <Body
          color={colors.white}
          style={{
            lineHeight: isTabletMode ? 22 : 20,
            opacity: 0.9
          }}
        >
          {description}
        </Body>
      </TextContainer>
      {isTabletMode && (
        <Circle isTabletMode={isTabletMode} source={cheveronRightInCircle} />
      )}
      <CardImage isTabletMode={isTabletMode} source={imgSrc} />
    </Container>
  );
};

export default NavigationCard;
