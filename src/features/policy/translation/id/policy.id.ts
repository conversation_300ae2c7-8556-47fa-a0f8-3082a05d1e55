export default {
  // New Business
  // Renamed as In Progress
  'newBusiness.pending': 'In progress',
  'newBusiness.issued': 'Policy Issued',
  'newBusiness.reviewSubmission': 'Submissions',
  'newBusiness.leaderReviewApplication': 'Review application',
  'newBusiness.certificateInsured': 'policy Insured',
  'newBusiness.certificateNo': 'Policy no.',
  'newBusiness.RegisterDate': 'Submission date',
  'newBusiness.submissionDate': 'Submission date',
  'newBusiness.policyNo': 'Policy no. ',
  'newBusiness.policyOwner': 'Policy owner',
  'newBusiness.product': 'Product',
  'newBusiness.owbStatus': 'Status',
  'newBusiness.submittedDate': 'Submitted date: ',
  'newBusiness.sentToReview': 'Sent to review',
  'newBusiness.outstandingItems': 'Pending requirement',
  'newBusiness.registerDate': 'Submission date',
  'newBusiness.registerDate.withColon': 'Submission date: ',
  'newBusiness.issuedDate': 'Issued date',
  'newBusiness.outstandingItem': 'Outstanding item',
  'newBusiness.policyIssuedDate': 'Issued date: ',
  'newBusiness.status': 'New business status',
  'newBusiness.totalCase': 'Total cases ({{count}})',
  'newBusiness.leaderReviewApplication.filter.pending':
    'Pending leader declaration',
  'newBusiness.leaderReviewApplication.filter.approved': 'Leader approved',
  'newBusiness.leaderReviewApplication.filter.rejected': 'Leader rejected',
  'newBusiness.leaderReviewApplication.filter.leaderNotResponded':
    'Leader not responded',
  'newBusiness.leaderReviewApplication.pendingReasons.rookieAgent':
    'Rookie agent',
  'newBusiness.leaderReviewApplication.vulnerableCustomer':
    'Vulnerable customer',
  'newBusiness.leaderReviewTable.modalPremium': 'Modal premium',

  'newBusiness.submission.table.header.policyNumber': 'Policy no.',
  'newBusiness.submission.table.header.owner': 'Policy owner',
  'newBusiness.submission.table.header.modalPremium': 'Modal premium',
  'newBusiness.submission.table.header.premium': 'premium',
  'newBusiness.submission.table.header.premiumWithDollar': 'Premium (IDR)',
  'newBusiness.submission.table.header.fromAgent': 'From',
  'newBusiness.submission.table.header.uwDecision': 'UW decision',
  'newBusiness.submission.table.header.status': 'Status',
  'newBusiness.submission.table.header.date': 'Submission date',

  'newBusiness.submission.filter.pendingLeaderDeclaration':
    'Pending leader declaration',
  'newBusiness.submission.filter.submitted': 'Submitted',
  'newBusiness.submission.filter.leaderRejected': 'Leader rejected',
  'newBusiness.submission.filter.leaderApproved': 'Leader approved',
  'newBusiness.submission.filter.leaderNotResponded': 'Leader not responded',
  'newBusiness.submission.filter.noRecord':
    'No results found, try another filter.',
  'newBusiness.submission.filter.noSearchResult':
    'No results found, try another search.',

  // NB Pending
  'newBusiness.pending.proposal': 'Proposal',
  'newBusiness.pending.NBPending': 'NB Pending',
  'newBusiness.pending.NBCounterOffer': 'NB Counter Offer',
  'newBusiness.pending.postponed': 'Postponed',
  'newBusiness.pending.notTakenUp': 'Not taken up',
  'newBusiness.pending.declined': 'Declined',
  'newBusiness.pending.withdrawn': 'Withdrawn',
  'newBusiness.pending.uwApproved': 'U/W Approved',

  'exitingPolicy.status.proposal': 'Proposal',
  'exitingPolicy.status.NBPending': 'NB Pending',
  'exitingPolicy.status.NBCounterOffer': 'NB Counter Offer',
  'exitingPolicy.status.postponed': 'Postponed',
  'exitingPolicy.status.notTakenUp': 'Not taken up',
  'exitingPolicy.status.declined': 'Declined',
  'exitingPolicy.status.withdrawn': 'Withdrawn',
  'exitingPolicy.status.uwApproved': 'U/W Approved',
  'exitingPolicy.status.cancelFromInception': 'Cancel from inception',
  'exitingPolicy.status.freelookCancellation': 'Free look cancellation',
  'exitingPolicy.status.Infore': 'Inforce',
  'exitingPolicy.status.lapsed': 'Lapsed',
  'exitingPolicy.status.surrendered': 'Surrendered',
  'exitingPolicy.status.registerForDeath': 'Register for Death',
  'exitingPolicy.status.uw': 'U/W',
  'exitingPolicy.status.deathClaim': 'Death Claim',
  'exitingPolicy.status.reverseToProposal': 'Reverse to Proposal',
  'exitingPolicy.status.voidProposal': 'Void Proposal',
  'exitingPolicy.status.componentChangeModifyPrp':
    'Component changes - modify prp',
  'exitingPolicy.status.componentChangeAddPrpsl':
    'Component chanage - Add prpsl',
  'exitingPolicy.status.notChecked': 'Not checked',
  'exitingPolicy.status.uwProcessing': 'U/W Processing',

  // POS
  'pos.due': 'Due',
  'pos.overdue': 'Overdue',
  'pos.freelookCancellation': 'Freelook cancellation',
  'pos.certificateNo': 'Policy no.',
  'pos.certificateInsured': 'policy Insured',
  'pos.dueIn': 'Due day',
  'pos.certificateDueDate': 'Due date',
  'pos.overdueDay': 'Overdue day',
  'pos.CancellationDate': 'Cancellation date',
  'pos.status': 'Policy servicing status',
  'pos.dueDate': 'Due date: ',
  'pos.freelookDate': 'Freelook date: ',
  'pos.policyNo': 'Policy no. ',

  //Issued
  'issued.policyNo': 'Policy no.',
  'issued.policyOwner': 'Policy owner',
  'issued.insured': 'Life assured',
  'issued.product': 'Product',
  'issued.modalPremium': 'Modal premium',
  'issued.issuedDate': 'Issued date',

  // Search
  'search.filterBy': 'Filter by',
  'search.filteredBy': 'Filtered by',
  'search.searchResult': 'Search result',
  'search.searchResultWithCount': 'Search Result ({{count}})',
  'search.submission': 'Submitted',
  'search.pending': 'Pending',
  'search.issued': 'Issued',
  'search.due': 'Due',
  'search.overdue': 'Overdue',
  'search.freelook': 'Freelook',
  'search.clearAll': 'Clear all',
  'search.reset': 'Reset',
  'search.apply': 'Apply',
  'search.placeholder': 'Search policy',
  'search.bar.hints': 'e.g. Policy owner name, policy number',
  'search.bar.hints.noAgent': 'e.g. Policy owner name or policy number',
  'search.nbLeaderReview.placeholder':
    'Look for policy owner name / Policy no.',
  'search.nbSubmission.placeholder':
    ' Look for policy owner name / policy no. / from agent',
  'search.posSearch.placeholder': ' Look for policy owner name / Policy no.',
  'search.resultCount.with30DaysLimit':
    'Total cases ({{count}})   |   Displaying data from last 30 days',
  'search.resultCount.with90DaysLimit':
    'Total cases ({{count}})   |   Displaying data from last 90 days',
  'search.resultCount.with90DaysLimit.review':
    'Total cases ({{count}})   |    Displaying data from last 90 days',
  'search.resultCount.with90DaysLimit.submitted':
    'Total cases ({{count}})   |    Displaying data from last 90 days',
  'search.resultCount.with90DaysLimit.pending':
    'Total cases ({{count}})   |    Displaying data from last 90 days',
  'search.resultCount.90DaysLimit': 'Displaying data from last 90 days.',
  // Policy Details
  'policyDetail.policy.applicationInfo': 'Application info',
  'policyDetail.policy.applicationNo': 'Application number',
  'policyDetail.policy.policyNo': 'Policy number',
  'policyDetail.policy.servicingAgent': 'Servicing agent',
  'policyDetail.policy.servicingGroup': 'Servicing group',
  'policyDetail.policy.supervisorAgent': 'Supervisor agent',
  'policyDetail.policy.policyHolderDetails': 'Policy holder details',
  'policyDetail.policy.name': 'Name',
  'policyDetail.policy.email': 'Email',
  'policyDetail.policy.mobileNumber': 'Mobile Number',
  'policyDetail.policy.mainInsuredDetails': 'Main insured details',
  'policyDetail.policy.policyInfo': 'Policy info',
  'policyDetail.policy.currency': 'Currency',
  'policyDetail.policy.currency.php': 'PHP',
  'policyDetail.policy.premiumHoliday': 'Premium holiday',
  'policyDetail.policy.virtualAccount': 'Virtual account',
  'policyDetail.policy.maturityDate': 'Maturity date',
  'policyDetail.policy.totalSumAsured': 'Total sum asured',
  'policyDetail.policy.fundValuesAsOf': 'Fund value as of ',
  'policyDetail.account.accountDebitInfo': 'Account debit information',
  'policyDetail.account.bankName': 'Bank name',
  'policyDetail.account.accountHolderName': 'Account holder name',
  'policyDetail.account.accountName': 'Account name',
  'policyDetail.account.ADAACAActivation': 'ADA/ACA Activation',
  'policyDetail.account.creditCardAccountInfo':
    'Credit card account information',
  'policyDetail.account.creditCardHolderName': 'Credit card holder name',
  'policyDetail.account.creditCardType': 'Credit card type',
  'policyDetail.account.creditCardNumber': 'Credit card number',
  'policyDetail.account.expiredDate': 'Expired date',
  'policyDetail.product.plan': 'Plan',
  'policyDetail.product.planName': 'Plan name',
  'policyDetail.product.insuredName': 'Person covered name',
  'policyDetail.product.ageAtEntry': 'Age at entry',
  'policyDetail.product.status': 'Status',
  'policyDetail.product.sumInsured': 'Sum insured',
  'policyDetail.product.premiumAmount': 'Premium Amount',
  'policyDetail.product.subPlan': 'Sub plan / Unit',
  'policyDetail.product.rcd': 'RCD',
  'policyDetail.beneficiary.name': 'Beneficiary name',
  'policyDetail.beneficiary.dateOfBirth': 'Date of birth',
  'policyDetail.beneficiary.noData': 'No data',
  'policyDetail.fundInfo.fundAllocation': 'Fund allocation',
  'policyDetail.fundInfo.noFundAllocation': 'No fund allocation',
  'policyDetail.fundInfo.fundvalue': 'Fund value',
  'policyDetail.fundInfo.asOf': 'As of',
  'policyDetail.history.paymentHistory': 'Payment history',
  'policyDetail.history.transactionDate': 'Transaction date',
  'policyDetail.history.paymentType': 'Payment type',
  'policyDetail.history.amount': 'Amount',
  'policyDetail.history.alterationHistory': 'Alteration history',
  'policyDetail.history.officialReceipts': 'Official Receipts',
  'policyDetail.history.lettersToCustomers': 'Letters to Customers',
  'policyDetail.history.description': 'Description',
  'policyDetail.history.file': 'File',
  'policyDetail.history.date': 'Date',
  'policyDetail.history.details': 'Details',
  'policyDetail.applicationTracker.received': 'Application received',
  'policyDetail.applicationTracker.screening': 'Screening - Data entry QA',
  'policyDetail.applicationTracker.assessment': 'Underwriting assessment',
  'policyDetail.applicationTracker.decision': 'Underwriting decision',
  'policyDetail.applicationTracker.issuance': 'Policy issuance',
  'policyDetail.applicationTracker.dispatch': 'Policy pack dispatch',
  'policyDetail.applicationTracker.completeDate': 'Completion date: ',
  'policyDetail.details.outstandingItems': 'Pending requirements',
  'detail.policyDetail': 'Policy',
  'detail.pdf': 'PDF',
  'detail.top': 'Top',
  'detail.modalPremium': 'Modal premium',
  'detail.basicPremium': 'Basic premium',
  'detail.paymentFrequency': 'Payment frequency',
  'detail.product': 'Product Name',
  'detail.paymentMethod': 'Payment method',
  'detail.overdueAmount': 'Overdue amount',
  'detail.overdue.urgent': 'Urgent',
  'detail.policyIssueDate': 'Policy issue date',
  'detail.dueDate': 'Due date',
  'detail.dueWarningText':
    'Please remind client to make the payment to prevent the policy from going to overdue status.',
  'detail.dueWarningEmphasis': 'Payment not received. ',
  'detail.overdueWarningText':
    'Please remind client to make the payment to prevent the policy from lapsing.',
  'detail.overdueWarningEmphasis': 'Payment overdue. ',
  'detail.freelook.description': 'Withdrawal requested by client',
  'detail.freelook.issuedDate': 'Policy issue date',
  'policyDetail.details.paymentDueDate': 'Payment due date: ',
  'detail.status.label.pending': 'New business pending',
  'detail.status.label.due.today': 'Due today',
  'detail.status.label.due': 'Due in {{dayCount}} day',
  'detail.status.label.overdue': 'Overdue {{dayCount}} day',
  'detail.status.label.due.plural': 'Due in {{dayCount}} days',
  'detail.status.label.overdue.plural': 'Overdue {{dayCount}} days',
  'detail.status.label.freelook': 'Freelook Cancellation',
  'detail.status.label.leaderApprovedToSubmit': 'Leader approved',
  'detail.status.label.nonRookieSubmitted': 'Submission',
  'detail.status.label.leaderNotResponded': 'Leader not responded',
  'detail.status.label.leaderRejected': 'Leader rejected',
  'detail.status.label.pendingLeaderDeclaration': 'Pending leader declaration',
  'detail.status.label.submission.failed': 'submission failed',
  'detail.status.label.submission.inprogress': 'in progress',
  'detail.status.label.issued': 'Policy Issued',
  'detail.status.pending': 'Pending leader declaration',
  'detail.status.approved.pending': 'Leader approved - in progress',
  'detail.status.approved.finished': 'Leader approved & submitted',
  'detail.status.approved.failed': 'Leader approved, system processing',
  'detail.status.rejected.pending': 'Leader rejected',
  'detail.status.rejected.finished': 'Leader rejected & closed',
  'detail.status.rejected.failed': 'Leader rejected, system processing',
  'detail.status.notResponded.pending': 'Processing auto rejection',
  'detail.status.notResponded.finished': 'Auto rejected & closed',
  'detail.status.notResponded.failed': 'Auto rejected, system processing',
  'detail.status.nonRookie.pending': 'Submission in progress',
  'detail.status.nonRookie.finished': 'Submitted',
  'detail.status.nonRookie.failed': 'System processing',
  'detail.owbPaymentMethod.biroAngkasa': 'Biro Angkasa',
  'detail.owbPaymentMethod.directBillingCash': 'Direct Billing (Cash)',
  'detail.owbPaymentMethod.directDebit': 'Direct Debit',
  'detail.owbPaymentMethod.groupBill': 'Group Billing',
  'detail.owbPaymentMethod.noBill': 'No Billing',
  'detail.owbPaymentMethod.pendingBiroAngkasa': 'Pending Biro Angkasa',
  'detail.owbPaymentMethod.creditCard': 'Credit Card',
  'detail.owbPaymentMethod.rmsCreditCard': 'RMS Credit Card',

  'detail.document.salesIllustration': 'Benefit illustration',
  'detail.document.CFF': 'Customer Fact Finding',
  'detail.document.applicationForm': 'Application form',

  'tabScreen.outstandingItemsCard': 'Outstanding items ({{count}})',
  'tabScreen.addFilesHint':
    'Accepted formats: jpg, pdf or png (up to 10MB each)',

  'outstandingItems.documentDesc': 'Document description',
  'outstandingItems.upload': 'Upload',
  'outstandingItems.submit': 'Submit',
  'outstandingItems.documentUploading': 'Document uploading...',
  'outstandingItems.viewPDF': 'View PDF',
  'outstandingItems.confirmSubmit': 'Confirm to submit',
  'outstandingItems.confirmSubmitDesc':
    'Once you click submit, you cannot edit. Underwriting team will follow-up your submission accordingly.',
  'outstandingItems.back': 'Back',
  'outstandingItems.delete': 'Delete the document',
  'outstandingItems.confirmDelete': 'Are you sure to delete the document?',
  'outstandingItems.submitted': 'Submitted',
  'outstandingItems.failed': 'Failed',
  'outstandingItems.uploadImageBy': '',

  // Other
  case: 'Case',
  searchResult: 'Search result ({{count}}) ',
  sortByTime: 'Sort by',
  newest: ' Newest',
  oldest: ' Oldest',
  closest: ' Closest',
  farthest: ' Farthest',
  allCase: 'All {{status}} case ({{count}})',
  submissionStaticText: 'Displaying data from last 90 days',
  pendingStaticText: 'Displaying data from last 180 days',
  issuedStaticText:
    'Displaying data from last 180 days. To view older issued policies, please proceed to the Reports module or IRIS Web',
  dueStaticText: 'Displaying data from last 90 days',
  overdueStaticText: 'Displaying data from last 90 days',
  freelookStaticText: 'Displaying all available data',
  dueToday: 'Due today',
  dueInDay: 'Due in {{days}} day',
  overdueInDay: 'Overdue {{days}} day',
  dueInDays: 'Due in {{days}} days',
  overdueInDays: 'Overdue {{days}} days',

  policyOwner: 'Policy owner',
  insuredName: 'Insured name',
  beneficiary: 'Beneficiary',
  product: 'Product',
  paymentMode: 'Payment mode',
  registerDate: 'Register date',
  nextPremiumPayDate: 'Next premium pay date',
  basicPremium: 'Basic Premium',
  singlePremium: 'Single premium',
  regularTopUp: 'Regular top up',
  modalPremium: 'Modal premium',
  riderPremium: 'Rider premium',
  basicPlan: 'Basic plan',
  sumInsured: 'Sum insured',
  issueDate: 'Issue date',
  policyStatus: 'Policy status',
  policy: 'Policy #',
  productName: 'Product name',
  riderName: 'Rider name',
  status: 'Status',
  pendingStatus: 'O',
  clientName: 'Client name',
  lastUpdateDate: 'Last update date:',
  month: '/ month',
  policyNo: 'Policy no. {{policyNo}}',

  contactAndSupport: 'Contact and support',
  contactPolicyOwner: 'Contact policy owner',
  callPolicyOwner: 'Call policy owner',
  smsPolicyOwner: 'SMS policy owner',
  support: 'Support',
  emailFWDSupport: 'Email FWD support',
  'email.back': 'Back',
  'email.enquire': 'What do you want to enquire us ?',
  lookForCertificateOwnerName: ' Look for Policy owner name / Policy no.',
  displayingDateForPolicesToBeDueWithIn30Days:
    'Displaying data for policies to be due within 30 days',
  displayingDataFromLast30Days: 'Displaying data from last 30 days',
  displayingDataFromLastDays: 'Displaying data from last {{days}} days',
  sumCovered: 'Sum assured',
  modalContribution: 'Modal premium',
  notApplicable: 'Not Applicable',
  insuredInformationV2: 'Life assured information',
  insuredInformation: "Policy owner's information",
  policyOwnerInformation: "Policy owner's information",
  mobile: 'Mobile',
  certificationType: 'Policy type',
  certNo: 'Policy #',
  submissionDate: 'Submission date',
  registrationDate: 'Register date',
  certOwner: 'Policy owner',
  maturityDate: 'Maturity date',
  personCovered: 'Life assured',
  nextContribution: 'Next premium due date',
  certOverview: 'Policy overview',
  coverage: 'Coverage',
  basePlan: 'Base plan',
  emptyRecord: 'Empty Record',

  policyOverview: 'Policy Overview',
};
