import React, { Fragment, useState } from 'react';
import { Theme, useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { SortDirectionKeys } from 'types/eRecruit';
import { Icon, XView } from 'cube-ui-components';
import { View } from 'react-native';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';

import {
  IconTouchable,
  TableTitleRow,
  TitleContainer,
  TitleSeparatorLine,
  TitleStyle,
} from './NewBusinessStyle';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';

export default function NBTableTitleRow({
  sortOrder,
  setSortOrder,
}: {
  sortOrder: string;
  setSortOrder: React.Dispatch<React.SetStateAction<SortDirectionKeys>>;
}) {
  const { t } = useTranslation('policy');
  const { colors } = useTheme();

  const [currentIcon, setCurrentIcon] = useState<React.ReactNode>(
    <Icon.ArrowDown size={sizes[4]} fill={colors.palette.white} />,
  );

  const handleIconPress = () => {
    setSortOrder(prev => (prev == 'oldest' ? 'newest' : 'oldest'));
  };

  return (
    <View style={{ backgroundColor: colors.palette.fwdGrey[50] }}>
      <TableTitleRow>
        <XView>
          {pendingTableConfig[country].certificatePending.listHeader.map(
            (item, index) => (
              <Fragment key={'pendingTableConfig' + index}>
                <View style={{ width: item.width, alignItems: 'flex-start' }}>
                  <TitleContainer>
                    <TitleStyle fontWeight="medium">
                      {t(`newBusiness.${item.type}`)}
                    </TitleStyle>
                    {index ===
                      pendingTableConfig[country].certificatePending.listHeader
                        .length -
                        1 && (
                      <IconTouchable onPress={handleIconPress}>
                        {sortOrder === 'oldest' ? (
                          <Icon.ArrowUp
                            size={sizes[4]}
                            fill={colors.palette.white}
                          />
                        ) : (
                          <Icon.ArrowDown
                            size={sizes[4]}
                            fill={colors.palette.white}
                          />
                        )}
                      </IconTouchable>
                    )}
                  </TitleContainer>
                </View>
                {index <
                  pendingTableConfig[country].certificatePending.listHeader
                    .length -
                    1 && <TitleSeparatorLine />}
              </Fragment>
            ),
          )}
        </XView>
      </TableTitleRow>
    </View>
  );
}

export const pendingTableConfig = {
  my: {
    certificatePending: {
      listHeader: [
        {
          type: 'policyNo',
          name: 'Policy no.',
          width: '15%',
        },
        {
          type: 'policyOwner',
          name: 'Policy owner',
          // width: '45%',
          width: '27%',
        },
        {
          type: 'owbStatus',
          // name: 'newBusiness.OutstandingItem',
          name: 'Status',
          width: '20%',
        },
        {
          type: 'outstandingItem',
          name: 'Outstanding item',
          width: '18%',
          // width: '20%',
        },
        {
          type: 'submissionDate',
          name: 'Submission date',
          width: '20%',
        },
      ],
      listHeaderDate: {
        dateTitle: 'submissionDate',
        width: '15%',
      },
    },
  },
  ib: {
    certificatePending: {
      listHeader: [
        {
          type: 'policyNo',
          name: 'Policy no.',
          width: '15%',
        },
        {
          type: 'policyOwner',
          name: 'Policy owner',
          // width: '45%',
          width: '27%',
        },
        {
          type: 'owbStatus',
          name: 'Status',
          width: '20%',
        },
        {
          type: 'outstandingItem',
          name: 'Outstanding item',
          width: '18%',
          // width: '20%',
        },
        {
          type: 'submissionDate',
          name: 'Submission date',
          width: '20%',
        },
      ],
      listHeaderDate: {
        dateTitle: 'submissionDate',
        width: '15%',
      },
    },
  },
  ph: {
    certificatePending: {
      listHeader: [
        {
          type: 'certificateNo',
          name: 'Policy no.',
          width: '15%',
        },
        {
          type: 'certificateInsured',
          name: 'Policy owner',
          width: '45%',
          // width: '27%',
        },
        // {
        //   type: 'owbStatus',
        //   name: 'Status',
        //   width: '20%',
        // },
        {
          type: 'outstandingItem',
          name: 'Outstanding item',
          width: '20%',
          // width: '18%',
        },
        {
          type: 'RegisterDate',
          name: 'newBusiness.RegisterDate',
          width: '20%',
        },
      ],
      listHeaderDate: {
        dateTitle: 'RegisterDate',
        width: '15%',
      },
    },
  },
  id: {
    certificatePending: {
      listHeader: [
        {
          type: 'policyNo',
          name: 'Policy no.',
          width: '16%',
        },
        {
          type: 'policyOwner',
          name: 'Policy owner',
          width: '32%',
        },
        {
          type: 'outstandingItem',
          name: 'Outstanding item',
          width: '32%',
        },
        {
          type: 'submissionDate',
          name: 'Submission date',
          width: '20%',
        },
      ],
      listHeaderDate: {
        dateTitle: 'submissionDate',
        width: '15%',
      },
    },
  },
} as const;
