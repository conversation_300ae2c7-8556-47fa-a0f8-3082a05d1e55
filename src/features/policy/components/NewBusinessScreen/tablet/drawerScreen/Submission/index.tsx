import { TouchableOpacity } from 'react-native';
import React, { useMemo, useState } from 'react';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import { useTheme } from '@emotion/react';
import {
  Box,
  Icon,
  LoadingIndicator,
  Row,
  Typography,
} from 'cube-ui-components';
import {
  NBPolicyLeaderReviewStatus,
  NBSubmissionForReviewStatus,
} from 'types/policy';
import { SortDirectionKeys } from 'types/eRecruit';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { SubmissionDataList } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission/config';
import SubmissionTableSet from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission/SubmissionTable';
import SearchSection from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission/SearchSection';
import { useTranslation } from 'react-i18next';
import { CaseStatus } from 'types/case';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import { PartyRole } from 'types/party';
import { casePaymentModeToPremiumMatcher } from 'features/policy/utils/policyDetailUtil';
import { allSubmissionCaseStatusFilterSet } from 'features/policy/components/NewBusinessScreen/tablet/constant';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useCheckRookieAgent } from 'hooks/useCheckRookieAgent';
import SubmissionFiltersSection from './SubmissionFilterSection';
import { country } from 'utils/context';
import TableEmptyRecord from 'components/EmptyRecord';
import SearchButton from 'features/policy/components/NewBusinessScreen/tablet/SearchButton';
import { CaseStatusToNBSubmissionForReviewStatusMap } from 'features/policy/hooks/useGetCaseObjInfo';

export default function NewBusinessSubmissionScreen() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('policy');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const [isSearching, setIsSearching] = useState(false);
  // const [submissionStatus, setSubmissionStatus] = useState<
  //   NBSubmissionForReviewStatus[]
  // >([]);

  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [statusFilterParams, setStatusFilterParams] = useState<CaseStatus[]>([
    ...allSubmissionCaseStatusFilterSet,
  ]);

  const { data: agentProfile, isLoading: isAgentLoading } =
    useGetAgentProfile();
  const {
    isLoading: isSavedProposalsLoading,
    isFetchingNextPage,
    isRefetching,
    refetch,
    hasNextPage,
    fetchNextPage,
    data,
  } = useGetSavedProposals({
    status: statusFilterParams,
    sort_by: order === 'oldest' ? '+updatedAt' : '-updatedAt',
    q: undefined,
  });
  const policyCasesToBeReviewed = useMemo(() => {
    if (!data) {
      return [];
    }
    return data?.pages.map(page => page.data).flat();
  }, [data]);

  const submissionCount = policyCasesToBeReviewed.length;

  const submissionCaseDataList = useMemo(() => {
    // already sorted by backend
    return policyCasesToBeReviewed.map(item => {
      const policyNo = item.application?.policyNum ?? '--';
      const policyOwner = item.parties.find(party =>
        party.roles.includes(PartyRole.PROPOSER),
      );
      const policyOwnerNameObj = policyOwner?.person?.name;

      const latestStatus = item.latestStatus;
      const frontendKey =
        latestStatus in CaseStatusToNBSubmissionForReviewStatusMap
          ? CaseStatusToNBSubmissionForReviewStatusMap[latestStatus] ??
            'leaderNotResponded'
          : 'leaderNotResponded';
      const policyPaymentMode =
        item.quotation?.basicInfo?.paymentMode ??
        item.quotation?.plans?.find(item => item?.paymentMode)?.paymentMode;

      const policyPremium = casePaymentModeToPremiumMatcher({
        paymentMode: policyPaymentMode,
        premiumSet: item.quotation?.summary,
      });

      const pendingReason = item.pendingReasons;

      const isPolicyOwnerEntity = policyOwner?.clientType === 'ENTITY';
      const individualPolicyOwner = policyOwnerNameObj
        ? policyOwnerNameObj?.firstName ??
          '' + policyOwnerNameObj?.lastName ??
          ''
        : '--';
      const entityPolicyOwner = policyOwner?.entity?.name ?? '';
      const policyOwnerName = isPolicyOwnerEntity
        ? entityPolicyOwner
        : individualPolicyOwner;

      return {
        caseId: item.caseId,
        policyNo: policyNo.replaceAll(/[^a-zA-Z0-9]/g, ''),
        policyOwner: policyOwnerName,
        premium: policyPremium,
        paymentMode: policyPaymentMode,
        fromAgent:
          item.agent?.fullName ?? agentProfile?.person?.fullName ?? '--',
        UWDecision: policyOwner?.uw?.result?.scenario ?? '--',
        status: frontendKey ?? 'leaderNotResponded',
        submissionStatus: item.submissionResult?.status ?? '',
        submissionDate: item.submissionResult?.submittedAt
          ? item.submissionResult?.submittedAt
          : item.updatedAt ?? '--',
        pendingReasons: pendingReason ?? [],
      };
    }) satisfies SubmissionDataList;
  }, [order, policyCasesToBeReviewed, statusFilterParams, agentProfile]);

  const isRookieAgent = useCheckRookieAgent();
  const isLeader = agentProfile?.isLeader ?? false;

  // * controlling the table UI for rookie agents(isDefaultConfig=false), OR non-rookie agents(isDefaultConfig=true)
  // * for ib, it will apply all rookie agents views (headers and columns) to all agents
  // * not just rookie agents but also vulnerable customers
  const isDefaultConfig = !isRookieAgent && country !== 'ib';

  if (isSearching) {
    return (
      <SearchSection
        isDefaultConfig={isDefaultConfig}
        setIsSearching={setIsSearching}
        data={submissionCaseDataList}
      />
    );
  }

  return (
    <AnimatedViewWrapper
      style={{
        flex: 1,
        paddingHorizontal: space[6],
        backgroundColor: colors.palette.fwdGrey[50],
      }}>
      <Row alignItems="center" minH={space[11]} justifyContent="space-between">
        <Typography.H6 fontWeight="bold">
          {t('newBusiness.reviewSubmission')}
        </Typography.H6>
        <SearchButton setIsSearching={setIsSearching} />
      </Row>
      {isAgentLoading ? (
        <Box
          minHeight={40}
          justifyContent="center"
          alignItems="center"
          marginBottom={space[6]}>
          <Box h={space[5]} width={space[5]}>
            <LoadingIndicator size={space[5]} />
          </Box>
        </Box>
      ) : (isDefaultConfig || isLeader) && country !== 'ib' ? (
        <Box h={space[3]} />
      ) : (
        <SubmissionFiltersSection
          statusFilterParams={statusFilterParams}
          setStatusFilterParams={setStatusFilterParams}
        />
      )}
      {/* <SubmissionFiltersSection */}
      {/*   statusFilterParams={statusFilterParams} */}
      {/*   setStatusFilterParams={setStatusFilterParams} */}
      {/* /> */}
      <>
        <Typography.Body color={colors.palette.fwdGreyDarkest}>
          {t('search.resultCount.with90DaysLimit.submitted', {
            count: submissionCount ?? 0,
            // * Temp handling for removing the limit
          })}
        </Typography.Body>
        <Box h={space[3]} />
      </>

      <SubmissionTableSet.Table
        ListEmptyComponent={() => <TableEmptyRecord />}
        type="refresh-able"
        isRefreshing={isRefetching}
        onRefresh={refetch}
        isLoading={isSavedProposalsLoading}
        HeaderComponent={() => (
          <SubmissionTableSet.Header
            sortOrder={order}
            setSortOrder={setOrder}
            isDefaultConfig={isDefaultConfig}
          />
        )}
        data={submissionCaseDataList}
        isDefaultConfig={isDefaultConfig}
      />
    </AnimatedViewWrapper>
  );
}
