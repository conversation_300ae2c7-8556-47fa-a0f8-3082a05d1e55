import React, { Fragment, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  Box,
  Column,
  LoadingIndicator,
  Row,
  Typography,
} from 'cube-ui-components';
import Table from 'components/Table';
import { SortDirectionKeys } from 'types/eRecruit';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlashList } from '@shopify/flash-list';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import PolicyStatusForApproval from 'features/policy/components/PolicyDetails/tablet/PolicyStatusForApprovalFlagLabel';
import { useTranslation } from 'react-i18next';
import {
  DefaultSubmissionTableConfig,
  RookieAgentSubmissionTableConfig,
  SubmissionDataList,
} from './config';
import { PaymentMode } from 'types/proposal';
import { RefreshControl } from 'react-native';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { PaymentModeLabelMap } from 'features/policy/utils/policyDetailUtil';
import { country } from 'utils/context';
import styled from '@emotion/native';

function SubmissionTable({
  type,
  HeaderComponent,
  ListEmptyComponent,
  onPressItem,
  isDefaultConfig,
  data,
  isLoading = false,
  ...rest
}:
  | {
      type?: 'default';
      HeaderComponent: React.ComponentType<any>;
      ListEmptyComponent?: React.ComponentType<any>;
      onPressItem?: (data: SubmissionDataList[number]) => void;
      data: SubmissionDataList;
      isLoading?: boolean;
      isDefaultConfig: boolean;
    }
  | {
      type: 'refresh-able';
      HeaderComponent: React.ComponentType<any>;
      ListEmptyComponent?: React.ComponentType<any>;
      onPressItem?: (data: SubmissionDataList[number]) => void;
      onRefresh: () => void;
      isRefreshing: boolean;
      data: SubmissionDataList;
      isLoading?: boolean;
      isDefaultConfig: boolean;
    }) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const onRefresh =
    type === 'refresh-able' && 'onRefresh' in rest ? rest.onRefresh : undefined;
  const isRefreshing =
    type === 'refresh-able' && 'isRefreshing' in rest
      ? rest.isRefreshing
      : undefined;

  const refreshControl =
    type === 'refresh-able' ? (
      <RefreshControl
        refreshing={isRefreshing ?? false}
        onRefresh={onRefresh}
      />
    ) : undefined;

  const bounces = type === 'refresh-able' ? true : false;
  return (
    <>
      <HeaderComponent />
      <FlashList
        keyExtractor={item =>
          item.caseId + '_' + item.policyNo + `_${isDefaultConfig}`
        }
        bounces={bounces}
        estimatedItemSize={60}
        refreshControl={refreshControl}
        refreshing={isRefreshing}
        onRefresh={onRefresh}
        contentContainerStyle={{ paddingBottom: bottom + space[4] }}
        showsHorizontalScrollIndicator={false}
        data={data}
        ListEmptyComponent={
          isLoading ? (
            <Box
              backgroundColor={colors.background}
              minHeight={120}
              justifyContent="center"
              alignItems="center"
              borderBottomRadius={borderRadius.large}>
              <Box h={space[5]} width={space[5]}>
                <LoadingIndicator size={space[5]} />
              </Box>
            </Box>
          ) : ListEmptyComponent ? (
            <ListEmptyComponent />
          ) : (
            <Table.ListEmptyRecord width={space[25]} height={space[21]} />
          )
        }
        // ListFooterComponent={<SimpleLoadingFooter />}
        ItemSeparatorComponent={() => <Table.RowSeparator />}
        renderItem={({ item, index }) => (
          <SubmissionTableContentRow
            data={item}
            itemIndex={index}
            dataListLength={data.length ?? 0}
            isDefaultConfig={isDefaultConfig}
            onPress={() => {
              navigation.navigate('ExistingPolicyDetail', {
                type: 'case',
                caseId: item.caseId,
                reviewStatus: item.status,
                submissionStatus: item.submissionStatus,
                fromFrontendTab: 'submission',
                submissionDate: item.submissionDate,
              });
              onPressItem && onPressItem(item);
            }}
          />
        )}
      />
    </>
  );
}

function SubmissionTableHeader({
  sortOrder,
  setSortOrder,
  isDefaultConfig,
}: {
  sortOrder: string;
  setSortOrder: (sortOrder: SortDirectionKeys) => void;
  isDefaultConfig: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { data: agentProfile } = useGetAgentProfile();

  const submissionTableConfig = isDefaultConfig
    ? DefaultSubmissionTableConfig[country]
    : RookieAgentSubmissionTableConfig[country];
  return (
    <Table.ListHeaderStyleWrapper>
      {submissionTableConfig.listHeader.map(({ name, width }, idx) => (
        <Fragment key={name}>
          <Table.ListHeaderTitle
            type="width"
            wrapperStyle={{
              paddingVertical: space[1],
            }}
            isSmallerLineHeight
            name={name}
            width={width}
          />
          <Table.ColumnSeparator height={18} />
        </Fragment>
      ))}
      <Table.ListHeaderDate
        type="width"
        isSmallerLineHeight
        dateTitle={submissionTableConfig.listHeaderDate.dateTitle}
        width={submissionTableConfig.listHeaderDate.width ?? '10%'}
        onPress={() =>
          setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest')
        }
        // * if true, showing ArrowDownSVG
        isSortDateDesc={sortOrder === 'newest'}
        containerStyle={{ paddingRight: space[5] }}
      />
    </Table.ListHeaderStyleWrapper>
  );
}

function SubmissionTableContentRow({
  itemIndex,
  dataListLength,
  data,
  onPress,
  isDefaultConfig,
}: {
  itemIndex: number;
  dataListLength: number;
  data: SubmissionDataList[number];
  onPress?: (data: SubmissionDataList[number]) => void;
  isDefaultConfig: boolean;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['policy', 'common']);
  const { data: agentProfile } = useGetAgentProfile();

  const SubmissionTableConfig = isDefaultConfig
    ? DefaultSubmissionTableConfig[country]
    : RookieAgentSubmissionTableConfig[country];

  const dataHandler = (
    data: SubmissionDataList[number],
    type: (typeof SubmissionTableConfig.listHeader)[number]['type'],
  ) => {
    switch (type) {
      case 'policyNumber':
        return country === 'my' ? data.policyNo : '#' + data.policyNo;
      case 'owner':
        return data.policyOwner;
      case 'premium': {
        const modeLabel =
          data?.paymentMode && data.paymentMode in PaymentModeLabelMap
            ? PaymentModeLabelMap[data.paymentMode].noun
            : '';
        return (
          t('common:currencySymbol') +
          ' ' +
          (data.premium ? numberToThousandsFormat(data.premium) : '--') +
          (isDefaultConfig ? `/` : country === 'my' ? `/` : '/') +
          modeLabel
        );
      }

      case 'fromAgent':
        return data.fromAgent;
      case 'UWDecision':
        return data.UWDecision;
      case 'status':
        return data.status;
      default:
        return '--';
    }
  };

  return (
    <Table.ListItemStyleWrapper
      onPress={onPress}
      index={itemIndex}
      dataCount={dataListLength}
      wrapperStyle={
        isDefaultConfig
          ? { minHeight: space[12] }
          : { paddingVertical: space[2] - 2 }
      }>
      {SubmissionTableConfig.listHeader.map(({ type, width }, idx) => {
        return (
          <Fragment key={type}>
            {type === 'status' ? (
              <Box width={width} justifyContent="center" px={space[4]}>
                <PolicyStatusForApproval
                  status={data?.status}
                  submissionStatus={data?.submissionStatus}
                />
              </Box>
            ) : (
              <Row
                px={space[4]}
                width={width}
                alignItems={'center'}
                justifyContent="space-between">
                <Column width={'100%'}>
                  {type == 'owner' &&
                    data.pendingReasons.includes('vulnerableCustomer') && (
                      <PendingReasonLabel>
                        {t(
                          'policy:newBusiness.leaderReviewApplication.vulnerableCustomer',
                        )}
                      </PendingReasonLabel>
                    )}
                  <Typography.Label color={colors.onBackground}>
                    {dataHandler(data, type)}
                  </Typography.Label>

                  {/* <Table.ListItemText */}
                  {/*   containerStyle={{ paddingVertical: space[3] }} */}
                  {/*   width={width} */}
                  {/*   text={dataHandler(data, type)} */}
                  {/*   isShorterLineHeight */}
                  {/* /> */}
                </Column>
              </Row>
            )}
            <Table.ColumnSeparator
              height={18}
              color={colors.palette.fwdGrey[100]}
            />
          </Fragment>
        );
      })}
      <Table.ListItemDate
        width={SubmissionTableConfig.listHeaderDate.width ?? '10%'}
        date={data.submissionDate}
        iconSize={sizes[6]}
        isShorterLineHeight
      />
    </Table.ListItemStyleWrapper>
  );
}

const SubmissionTableSet = {
  Table: SubmissionTable,
  Header: SubmissionTableHeader,
  Row: SubmissionTableContentRow,
};

const PendingReasonLabel = styled(Typography.SmallLabel)(
  ({ theme: { colors, space } }) => ({
    color: colors.palette.fwdDarkGreen[50],
    paddingBottom: space[1],
  }),
);

export default SubmissionTableSet;
