import { t } from 'i18next';
import { BuildCountry } from 'types';
import { CaseStatus } from 'types/case';
import {
  NBPolicyLeaderReviewSubmissionStatus,
  NBSubmissionForReviewStatus,
} from 'types/policy';
import { PaymentMode } from 'types/proposal';
import { country } from 'utils/context';

export const filterSubmissionStatus: Array<{
  type: NBSubmissionForReviewStatus;
  label: string;
  caseStatus: CaseStatus[];
}> = [
  {
    type: 'pendingLeaderDeclaration' satisfies NBSubmissionForReviewStatus as NBSubmissionForReviewStatus,
    caseStatus: [CaseStatus.PENDING_FOR_LEADER],
    label: 'newBusiness.submission.filter.pendingLeaderDeclaration',
  },
  {
    type: 'leaderRejected' satisfies NBSubmissionForReviewStatus as NBSubmissionForReviewStatus,
    caseStatus: [CaseStatus.REJECTED_BY_LEADER],
    label: 'newBusiness.submission.filter.leaderRejected',
  },
  {
    type: 'leaderApprovedToSubmit' satisfies NBSubmissionForReviewStatus as NBSubmissionForReviewStatus,
    caseStatus: [
      CaseStatus.APPROVED_BY_LEADER,
      CaseStatus.APP_SUBMITTED,
    ].splice(0, country === 'my' ? 2 : 1),
    label: 'newBusiness.submission.filter.leaderApproved',
  },
  {
    type: 'leaderNotResponded' satisfies NBSubmissionForReviewStatus as NBSubmissionForReviewStatus,
    caseStatus: [CaseStatus.EXPIRED_AFTER_15_DAYS],
    label: 'newBusiness.submission.filter.leaderNotResponded',
  },
  {
    type: 'nonRookieSubmitted' satisfies NBSubmissionForReviewStatus as NBSubmissionForReviewStatus,
    caseStatus: [CaseStatus.APP_SUBMITTED],
    label: 'newBusiness.submission.filter.submitted',
  },
].splice(0, country === 'my' ? 4 : 5);

export type SubmissionDataList = Array<{
  policyNo: string;
  caseId: string;
  policyOwner: string;
  premium: number | undefined;
  paymentMode: PaymentMode | undefined;
  fromAgent: string;
  UWDecision: string;
  status: NBSubmissionForReviewStatus;
  submissionStatus: NBPolicyLeaderReviewSubmissionStatus | '';
  submissionDate: string;
  pendingReasons: string[];
}>;

export const NBSubmissionCaseKeyToCaseStatusMap: Record<
  NBSubmissionForReviewStatus,
  CaseStatus
> = {
  pendingLeaderDeclaration: CaseStatus.PENDING_FOR_LEADER,
  leaderRejected: CaseStatus.REJECTED_BY_LEADER,
  leaderApprovedToSubmit: CaseStatus.APP_SUBMITTED,
  leaderNotResponded: CaseStatus.EXPIRED_AFTER_15_DAYS,
  nonRookieSubmitted: CaseStatus.APP_SUBMITTED,
};

type ListHeaderItem = {
  type:
    | 'policyNumber'
    | 'owner'
    | 'premium'
    | 'fromAgent'
    | 'UWDecision'
    | 'status'
    | 'product';
  name: string;
  width: string;
};

type ListHeaderDate = {
  dateTitle: string;
  width: string;
};

type SubmissionTableConfig = Record<
  BuildCountry,
  { listHeader: ListHeaderItem[]; listHeaderDate: ListHeaderDate }
>;

export const RookieAgentSubmissionTableConfig: SubmissionTableConfig = {
  my: {
    listHeader: [
      { type: 'policyNumber', name: 'Certificate no.', width: '14%' },
      {
        type: 'owner',
        name: 'Certificate owner',
        width: '14%',
      },
      // { type: 'premium', name: 'Contribution (RM)', width: '17.5%' },
      { type: 'premium', name: 'Contribution (RM)', width: '28%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      // {
      //   type: 'UWDecision',
      //   name: 'UW decision',
      //   width: '11.3%',
      // },
      { type: 'status', name: 'Status', width: '28%' },
    ],
    listHeaderDate: {
      dateTitle: 'Submission date',
      width: '16%',
    },
  },
  ib: {
    listHeader: [
      {
        type: 'policyNumber',
        name: t('policy:newBusiness.submission.table.header.policyNumber'),
        width: '14%',
      },
      {
        type: 'owner',
        name: t('policy:newBusiness.submission.table.header.owner'),
        width: '17%',
      },
      {
        type: 'premium',
        name: t('policy:newBusiness.submission.table.header.modalPremium'),
        width: '21.5%',
      },
      // { type: 'premium', name: 'Contribution (RM)', width: '29%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      {
        type: 'UWDecision',
        name: t('policy:newBusiness.submission.table.header.uwDecision'),
        width: '13.3%',
      },
      {
        type: 'status',
        name: t('policy:newBusiness.submission.table.header.status'),
        width: '18%',
      },
    ],
    listHeaderDate: {
      dateTitle: t('policy:newBusiness.submission.table.header.date'),
      width: '16%',
    },
  },
  ph: {
    listHeader: [
      { type: 'policyNumber', name: 'Policy no.', width: '14%' },
      {
        type: 'owner',
        name: 'Policy owner',
        width: '12%',
      },
      { type: 'premium', name: 'Premium (PHP)', width: '17.5%' },
      // { type: 'premium', name: 'Contribution (RM)', width: '29%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      {
        type: 'UWDecision',
        name: 'UW decision',
        width: '11.3%',
      },
      { type: 'status', name: 'Status', width: '29%' },
    ],
    listHeaderDate: {
      dateTitle: 'Submission date',
      width: '16%',
    },
  },
  id: {
    listHeader: [
      {
        type: 'policyNumber',
        name: t('policy:newBusiness.submission.table.header.policyNumber'),
        width: '14%',
      },
      {
        type: 'owner',
        name: t('policy:newBusiness.submission.table.header.owner'),
        width: '12%',
      },
      {
        type: 'premium',
        name: t('policy:newBusiness.submission.table.header.premiumWithDollar'),
        width: '17.5%',
      },
      // { type: 'premium', name: 'Contribution (RM)', width: '29%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      {
        type: 'UWDecision',
        name: t('policy:newBusiness.submission.table.header.uwDecision'),
        width: '11.3%',
      },
      {
        type: 'status',
        name: t('policy:newBusiness.submission.table.header.status'),
        width: '29%',
      },
    ],
    listHeaderDate: {
      dateTitle: t('policy:newBusiness.submission.table.header.date'),
      width: '16%',
    },
  },
} as const;

export const DefaultSubmissionTableConfig: SubmissionTableConfig = {
  my: {
    listHeader: [
      { type: 'policyNumber', name: 'Certificate no.', width: '14%' },
      {
        type: 'owner',
        name: 'Certificate owner',
        width: '22%',
      },
      { type: 'premium', name: 'Contribution (RM)', width: '23%' },
      { type: 'status', name: 'Status', width: '21%' },
    ],
    listHeaderDate: {
      dateTitle: 'Submission date',
      width: '20%',
    },
  },
  ib: {
    listHeader: [
      {
        type: 'policyNumber',
        name: t('policy:newBusiness.submission.table.header.policyNumber'),
        width: '16%',
      },
      {
        type: 'owner',
        name: t('policy:newBusiness.submission.table.header.owner'),
        width: '20%',
      },
      {
        type: 'premium',
        name: t('policy:newBusiness.submission.table.header.modalPremium'),
        width: '28%',
      },
      // { type: 'premium', name: 'Modal premium', width: '44%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      {
        type: 'UWDecision',
        name: t('policy:newBusiness.submission.table.header.uwDecision'),
        width: '16%',
      },
      // { type: 'status', name: 'Status', width: '17%' },
    ],
    listHeaderDate: {
      dateTitle: t('policy:newBusiness.submission.table.header.date'),
      width: '20%',
    },
  },
  ph: {
    listHeader: [
      { type: 'policyNumber', name: 'Policy no.', width: '16%' },
      {
        type: 'owner',
        name: 'Policy owner',
        width: '20%',
      },
      { type: 'premium', name: 'Premium (PHP)', width: '28%' },
      // { type: 'premium', name: 'Contribution (RM)', width: '44%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      {
        type: 'UWDecision',
        name: 'UW decision',
        width: '16%',
      },
      // { type: 'status', name: 'Status', width: '17%' },
    ],
    listHeaderDate: {
      dateTitle: 'Submission date',
      width: '20%',
    },
  },
  id: {
    listHeader: [
      { type: 'policyNumber', name: 'Policy no.', width: '20%' },
      {
        type: 'owner',
        name: 'Policy owner',
        width: '30%',
      },
      { type: 'product', name: 'Product', width: '30%' },
      // { type: 'premium', name: 'Contribution (RM)', width: '44%' },
      // {
      //   type: 'fromAgent',
      //   name: 'From',
      //   width: '12.2%',
      // },
      // ! Temporarily hide UWDecision till data available
      // {
      //   type: 'UWDecision',
      //   name: 'UW decision',
      //   width: '16%',
      // },
      // { type: 'status', name: 'Status', width: '17%' },
    ],
    listHeaderDate: {
      dateTitle: t('policy:newBusiness.submission.table.header.date'),
      width: '20%',
    },
  },
} as const;
