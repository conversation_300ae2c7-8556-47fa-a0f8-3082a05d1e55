import React, { Fragment, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Box, Icon, LoadingIndicator } from 'cube-ui-components';
import TableEmptyRecord from 'components/EmptyRecord';
import { InfoText, RowSeparator, SeparatorLine } from './NewBusinessStyle';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { BuildCountry, RootStackParamList } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { NBIssued, sortOrder } from 'types/policy';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlashList } from '@shopify/flash-list';
import { country } from 'utils/context';
import { RefreshControl } from 'react-native';
import Table from 'components/Table';
import { useTranslation } from 'react-i18next';
import { SortDirectionKeys } from 'features/savedProposals/types';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { paymentModeLabelMapAsNoun } from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview/LeaderReviewTable';
import { PaymentMode } from 'types/proposal';
import { sortDateHandler } from 'utils/helper/dateUtil';
import { TFuncKey } from 'i18next';

export default function IssuedTable({
  data,
  isLoading,
  isRefreshing,
  onRefresh,
}: {
  data: NBIssued[];
  onRefresh: () => void;
  isRefreshing: boolean;
  isLoading: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const [sortOrder, setSortOrder] = useState<sortOrder>('newest');
  const { t } = useTranslation('policy');

  const sortedData = useMemo(() => {
    return data.sort((a, b) =>
      sortDateHandler({
        aDate: a.policyIssuedDate,
        bDate: b.policyIssuedDate,
        sortOrder: sortOrder,
      }),
    );
  }, [data, sortOrder]);

  return (
    <>
      <IssuedTableTitleRow sortOrder={sortOrder} setSortOrder={setSortOrder} />
      <FlashList
        showsHorizontalScrollIndicator={false}
        estimatedItemSize={80}
        refreshControl={
          <RefreshControl refreshing={isRefreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={{ paddingBottom: bottom + space[5] }}
        ListEmptyComponent={
          isLoading ? (
            <Box
              p={space[5]}
              alignItems="center"
              bgColor={colors.background}
              borderBottomRadius={borderRadius['x-large']}>
              <LoadingIndicator />
            </Box>
          ) : (
            <TableEmptyRecord />
          )
        }
        data={sortedData}
        renderItem={({ item, index }) => (
          <TableContent
            item={item}
            index={index}
            dataLength={sortedData?.length ?? 0}
          />
        )}
      />
    </>
  );
}

type TableColumnConfig = {
  type: string;
  flex: number;
};

function getIssuedTableConfig(country: BuildCountry): TableColumnConfig[] {
  switch (country) {
    case 'id':
      return [
        { type: 'policyNo', flex: 0.8 },
        { type: 'policyOwner', flex: 3 },
        { type: 'product', flex: 1.5 },
        { type: 'issuedDate', flex: 1.2 },
      ];
    default:
      return [
        { type: 'policyNo', flex: 0.8 },
        { type: 'policyOwner', flex: 3 },
        { type: 'modalPremium', flex: 1.5 },
        { type: 'issuedDate', flex: 1.2 },
      ];
  }
}

const issuedTableConfig = getIssuedTableConfig(country);

function IssuedTableTitleRow({
  sortOrder,
  setSortOrder,
}: {
  sortOrder: string;
  setSortOrder: (sortOrder: SortDirectionKeys) => void;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('policy');

  return (
    <Table.ListHeaderStyleWrapper
      wrapperStyle={{ height: 50 }}
      backgroundColor={colors.palette.whiteTransparent}>
      {issuedTableConfig.map(({ flex, type }, idx) => {
        switch (type) {
          case 'policyNo':
            return (
              <Fragment key={type}>
                <Table.ListHeaderTitle
                  type="flex"
                  name={t(`issued.${type}`)}
                  flex={flex}
                  isSmallerLineHeight
                />
                <Table.ColumnSeparator height={18} />
              </Fragment>
            );

          case 'policyOwner':
          case 'modalPremium':
          case 'product':
            return <HeaderTextLabel key={type} type={type} flex={flex} />;
          case 'issuedDate':
            return (
              <Table.ListHeaderDate
                key={type}
                type="flex"
                dateTitle={t(`issued.issuedDate`)}
                flex={flex}
                onPress={() =>
                  setSortOrder(sortOrder === 'newest' ? 'oldest' : 'newest')
                }
                // * if true, showing ArrowDownSVG
                isSortDateDesc={sortOrder === 'newest'}
              />
            );

          default: {
            console.log('--type:', type, '-- not handled');
            return <Fragment key={type}></Fragment>;
          }
        }
      })}
    </Table.ListHeaderStyleWrapper>
  );
}

function HeaderTextLabel({ type, flex }: TableColumnConfig) {
  const { t } = useTranslation('policy');

  return (
    <>
      <Table.ListHeaderTitle
        type="flex"
        name={t(`issued.${type}` as TFuncKey<'policy'>)}
        flex={flex}
      />
      <Table.ColumnSeparator height={18} />
    </>
  );
}

function TableContent({
  item,
  index,
  dataLength,
  isDisabled,
}: {
  item: NBIssued;
  index: number;
  dataLength: number;
  isDisabled?: boolean;
}) {
  const { colors, borderRadius, space } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  index == 0 && console.log('item :', item);

  const { t } = useTranslation(['home', 'common']);
  return (
    <Box>
      <RowSeparator
        style={[
          {
            backgroundColor:
              index % 2 === 0
                ? colors.palette.white
                : colors.palette.fwdGrey[20],
            borderBottomLeftRadius:
              index === dataLength - 1 ? borderRadius.large : 0,
            borderBottomRightRadius:
              index === dataLength - 1 ? borderRadius.large : 0,
          },
          index === dataLength - 1 && { borderBottomWidth: 0 },
        ]}
        disabled={isDisabled}
        onPress={() => {
          navigation.navigate('ExistingPolicyDetail', {
            type: 'policy',
            policyId: item.policyNo,
            status: 'issued',
            policyInfo: {
              phoneMobile: item.phoneMobile as string,
              policyIssuedDate: (item.policyIssuedDate as string) || '--',
            },
          });
        }}>
        {/* <Row style={{ alignItems: 'center' }}> */}
        {issuedTableConfig?.map(({ type, flex }, i) => {
          const paymentMode =
            'paymentMode' in item
              ? item?.paymentMode &&
                item?.paymentMode in paymentModeLabelMapAsNoun
                ? paymentModeLabelMapAsNoun?.[item?.paymentMode as PaymentMode]
                : item?.paymentMode
              : undefined;

          const dataMap: Record<
            (typeof issuedTableConfig)[number]['type'],
            string | number | undefined
          > = {
            policyOwner: item.displayName.en,
            // insured: item.displayName.en,
            modalPremium: `${t('common:currencySymbol')} ${
              'modalPremium' in item && item?.modalPremium != null
                ? numberToThousandsFormat(item?.modalPremium)
                : '--'
            } / ${paymentMode ?? '--'}`,
            policyNo: (country == 'ib' ? '#' : '') + item.policyNo,
            issuedDate: item.policyIssuedDate
              ? dateFormatUtil(item.policyIssuedDate)
              : '--',
            product: item?.product ?? '--',
          };
          return (
            <Fragment key={type}>
              <InfoText
                numberOfLines={2}
                style={{
                  flex: flex,
                }}>
                {dataMap?.[type] ?? '--'}
              </InfoText>
              {type == 'issuedDate' ? null : <SeparatorLine />}
            </Fragment>
          );
        })}
        <Box style={{ position: 'absolute', right: space[2] }}>
          {isDisabled ? null : <Icon.ChevronRight />}
        </Box>
        {/* </Row> */}
      </RowSeparator>
    </Box>
  );
}
