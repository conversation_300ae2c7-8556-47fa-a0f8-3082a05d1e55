import React, { useEffect, useMemo, useState } from 'react';
import { BuildCountry, NBDrawerParamList } from 'types';
import { useTheme } from '@emotion/react';
import {
  DrawerContentComponentProps,
  DrawerContentScrollView,
  createDrawerNavigator,
} from '@react-navigation/drawer';
import { View, useWindowDimensions } from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { ButtonStyle } from './NewBusinessStyle';
import { Box, Icon, SmallLabel, SvgIconProps } from 'cube-ui-components/';
import { useTranslation } from 'react-i18next';
import useBoundStore from 'hooks/useBoundStore';
import { useGetNewBusinessPolicyByAgentId } from 'hooks/useGetPolicyList';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { CaseStatus } from 'types/case';
import { build, country } from 'utils/context';

// Common Screens
import NewBusinessLeaderReviewScreen from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessLeaderReview';
import NewBusinessSubmissionScreen from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Submission';
import NotFoundScreen from 'screens/NotFoundScreen';

// FIB Screens
// seperate Isseued Screen on both FIB and Takaful.
// TBC if they can be merged into one common screen with only translation diff/ minor conditional handling
import FIBNewBusinessIssuedScreen from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/Issued';
import FIBNewBusinessPendingScreen from 'features/policy/components/NewBusinessScreen/tablet/drawerScreen/NewBusinessPendingScreen';

// Takaful Screens
import TakafulNewBusinessPendingScreen from 'features/policy/my/tablet/NewBusiness/Pending';
import TakafulNewBusinessCertificateIssuedScreen from 'features/policy/my/tablet/NewBusiness/issue';

const NBDrawer = createDrawerNavigator<NBDrawerParamList>();

const IBNavigationScreenConfig: Record<
  keyof NBDrawerParamList,
  () => React.JSX.Element
> = {
  nbPending: FIBNewBusinessPendingScreen,
  nbLeaderReview: NewBusinessLeaderReviewScreen,
  nbSubmissionForReview: NewBusinessSubmissionScreen,
  issued: FIBNewBusinessIssuedScreen,
};

const MyTakafulNavigationScreenConfig: Record<
  keyof NBDrawerParamList,
  () => React.JSX.Element
> = {
  nbPending: TakafulNewBusinessPendingScreen,
  nbSubmissionForReview: NewBusinessSubmissionScreen,
  nbLeaderReview: NewBusinessLeaderReviewScreen,
  issued: FIBNewBusinessIssuedScreen,
};

const IDNavigationScreenConfig: Record<
  keyof NBDrawerParamList,
  () => React.JSX.Element
> = {
  nbPending: FIBNewBusinessPendingScreen,
  nbLeaderReview: NewBusinessLeaderReviewScreen,
  nbSubmissionForReview: NewBusinessSubmissionScreen,
  issued: FIBNewBusinessIssuedScreen,
};

const countryNBScreenMap: Record<
  BuildCountry,
  Record<keyof NBDrawerParamList, () => React.JSX.Element> | undefined
> = {
  ib: IBNavigationScreenConfig,
  my: MyTakafulNavigationScreenConfig,
  ph: undefined,
  id: IDNavigationScreenConfig,
};

export default function NewBusinessScreenTablet() {
  const { colors } = useTheme();
  const dimensions = useWindowDimensions();

  return (
    <NBDrawer.Navigator
      initialRouteName={
        country === 'id' ? 'nbSubmissionForReview' : 'nbPending'
      }
      drawerContent={props => <NBDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerType: dimensions.width >= 500 ? 'permanent' : 'front',
        drawerStyle: {
          backgroundColor: colors.palette.fwdGrey[50],
          width: 120,
          borderRightWidth: 0,
        },
      }}>
      <NBDrawer.Screen
        name="nbPending"
        component={countryNBScreenMap[country]?.nbPending ?? NotFoundScreen}
      />
      <NBDrawer.Screen
        name="nbLeaderReview"
        component={
          countryNBScreenMap[country]?.nbLeaderReview ?? NotFoundScreen
        }
      />
      <NBDrawer.Screen
        name="nbSubmissionForReview"
        component={
          countryNBScreenMap[country]?.nbSubmissionForReview ?? NotFoundScreen
        }
      />
      <NBDrawer.Screen
        name="issued"
        component={countryNBScreenMap[country]?.issued ?? NotFoundScreen}
      />
    </NBDrawer.Navigator>
  );
}

function NBDrawerContent(props: DrawerContentComponentProps) {
  const { t } = useTranslation('policy');
  const { colors, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<NBDrawerParamList>>(); // TODO: Add type
  const route = useRoute<RouteProp<NBDrawerParamList>>(); // TODO: Add type
  const [focusedTab, setFocusedTab] = useState<keyof NBDrawerParamList>();
  const loginAgentCode = useBoundStore(state => state.auth.agentCode);
  const { data: agentProfile } = useGetAgentProfile();

  const { data: newBusinessPolicyData, isLoading } =
    useGetNewBusinessPolicyByAgentId(loginAgentCode || '');

  const [statusFilterParams, setsStatusFilterParams] = useState<CaseStatus[]>([
    CaseStatus.APP_SUBMITTED,
    CaseStatus.PENDING_FOR_LEADER,
    CaseStatus.EXPIRED_AFTER_15_DAYS,
    CaseStatus.REJECTED_BY_LEADER,
    CaseStatus.APPROVED_BY_LEADER,
  ]);

  // * -- Leader Review Policy Count------
  const { isLoading: isPolicyForLeaderReviewLoading, data: leaderReviewData } =
    useGetSavedProposals({
      // based on commit 6cc719134c244739d4d9a1ade6e8fb9f2353ee86 for release fix
      status: statusFilterParams.filter(
        caseStatus => caseStatus !== CaseStatus.APP_SUBMITTED,
      ),
      sort_by: '+updatedAt',
      q: undefined,
      downlineOnly: true,
    });

  const leaderReviewPolicyList = useMemo(() => {
    if (!leaderReviewData) {
      return [];
    }
    return leaderReviewData?.pages.map(page => page.data).flat();
  }, [leaderReviewData]);
  const leaderReviewPolicyListCount = leaderReviewPolicyList.length;

  // * -- Submission Policy Count------
  const { isLoading: isSubmittedPolicyLoading, data: submittedPolicyData } =
    useGetSavedProposals({
      status: statusFilterParams,
      sort_by: '+updatedAt',
      q: undefined,
    });
  const submittedPolicyList = useMemo(() => {
    if (!submittedPolicyData) {
      return [];
    }
    return submittedPolicyData?.pages.map(page => page.data).flat();
  }, [submittedPolicyData]);
  const submittedPolicyListCount = submittedPolicyList.length;

  // * -- newBusinessPolicy Count------
  const pendingCaseCount = newBusinessPolicyData?.pendings?.length ?? 0;
  const issuedsCount = newBusinessPolicyData?.issueds?.length ?? 0;

  // * -- Show review application tab function------
  const toShowLeaderReviewTab = () => {
    if (country == 'id') return false;
    return agentProfile?.isApprover || false;
  };

  const isReviewApplicationTabVisible = toShowLeaderReviewTab();

  const NB_DRAWER_TABS = [
    {
      label: 'newBusiness.leaderReviewApplication',
      type: 'nbLeaderReview',
      Icon: Icon.Document,
      alertIcon: (
        <Icon.WarningFill size={sizes[4]} fill={colors.palette.alertRed} />
      ),
      count: leaderReviewPolicyListCount,
      toBeShown: isReviewApplicationTabVisible,
    },

    {
      label: 'newBusiness.reviewSubmission',
      type: 'nbSubmissionForReview',
      Icon: Icon.DocumentCopy,
      alertIcon: null,
      count: submittedPolicyListCount,
      toBeShown: true,
    },
    {
      label: 'newBusiness.pending',
      type: 'nbPending',
      Icon: Icon.Warning,
      alertIcon: (
        <Icon.WarningFill size={sizes[4]} fill={colors.palette.alertRed} />
      ),
      count: pendingCaseCount,
      toBeShown: true,
    },
    {
      label: 'newBusiness.issued',
      type: 'issued',
      Icon: country === 'id' ? Icon.Document : Icon.TickCircle,
      alertIcon: null,
      count: issuedsCount,
      // to hide the issued tab if the feature is not ready
      toBeShown: true,
    },
  ] satisfies Array<{
    label:
      | 'newBusiness.pending'
      | 'newBusiness.leaderReviewApplication'
      | 'newBusiness.reviewSubmission'
      | 'newBusiness.issued';
    type: keyof NBDrawerParamList;
    Icon: (props: SvgIconProps) => JSX.Element;
    alertIcon: React.JSX.Element | null;
    count: number;
    toBeShown: boolean;
  }>;

  useEffect(() => {
    setFocusedTab(
      props.state.routeNames[props.state.index] as keyof NBDrawerParamList,
    );
  }, [props]);

  return (
    <DrawerContentScrollView
      {...props}
      contentContainerStyle={{
        paddingTop: 0,
      }}>
      <Box
        gap={sizes[3]}
        bgColor={colors.palette.fwdGrey[50]}
        alignItems="flex-end">
        {NB_DRAWER_TABS.map(
          ({ label, type, Icon, count, alertIcon, toBeShown }) =>
            toBeShown && (
              <ButtonStyle
                key={'nb_drawer_tab_' + type}
                isFocused={focusedTab === type}
                onPress={() => {
                  navigation.navigate(type);
                  setFocusedTab(type);
                }}>
                <View style={{ top: 10, right: 16, position: 'absolute' }}>
                  {/* // * Show for Ib and Id only,
                    till feature implemented with api integration about
                    outstanding item or other regions*/}
                  {(country === 'ib' || country === 'id') &&
                    count > 0 &&
                    alertIcon &&
                    alertIcon}
                </View>
                <Icon
                  size={sizes[8]}
                  fill={
                    focusedTab === type
                      ? colors.primary
                      : colors.palette.fwdOrange[50]
                  }
                />
                <SmallLabel
                  fontWeight={focusedTab === type ? 'bold' : 'normal'}
                  style={{
                    color:
                      focusedTab === type
                        ? colors.primary
                        : colors.palette.fwdGreyDarkest,
                    textAlign: 'center',
                    paddingTop: sizes[1],
                    // maxWidth: sizes[17],
                  }}>
                  {t(label)} ({count})
                </SmallLabel>
              </ButtonStyle>
            ),
        )}
      </Box>
    </DrawerContentScrollView>
  );
}
