import React, { Fragment, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Box, H8, Icon, LoadingIndicator, Row } from 'cube-ui-components';
import NBTableTitleRow, { pendingTableConfig } from './NBTableTitleRow';
import TableEmptyRecord from 'components/EmptyRecord';
import { InfoText, RowSeparator, SeparatorLine } from './NewBusinessStyle';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { NBPending, owbCodeToLabelStatus, sortOrder } from 'types/policy';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { FlashList } from '@shopify/flash-list';
import { country } from 'utils/context';
import { RefreshControl } from 'react-native';
import { useTranslation } from 'react-i18next';
import FlagLabel from 'components/FlagLabel';
import { sortDateHandler } from 'utils/helper/dateUtil';
import { pendingDocCountOnListHandler } from 'features/policy/utils/pendingDocHandler';

export default function NBPendingTable({
  data,
  isLoading,
  isItemPressable = true,
  ...rest
}: {
  data: NBPending[];
  onRefresh: () => void;
  isRefreshing: boolean;
  isLoading: boolean;
  isItemPressable?: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const [sortOrder, setSortOrder] = useState<sortOrder>('newest');

  const sortedData = useMemo(() => {
    return data.sort((a, b) =>
      sortDateHandler({
        aDate: a.submissionDate,
        bDate: b.submissionDate,
        sortOrder: sortOrder,
      }),
    );
  }, [data, sortOrder]);

  return (
    <Box flex={1}>
      <NBTableTitleRow sortOrder={sortOrder} setSortOrder={setSortOrder} />
      <FlashList
        // keyExtractor={item => item.policyNo + item.registerDate}
        showsHorizontalScrollIndicator={false}
        estimatedItemSize={80}
        refreshControl={
          <RefreshControl
            refreshing={'isRefreshing' in rest && rest?.isRefreshing}
            onRefresh={'onRefresh' in rest ? rest?.onRefresh : undefined}
          />
        }
        contentContainerStyle={{ paddingBottom: bottom + space[5] }}
        ListEmptyComponent={
          isLoading ? (
            <Box
              p={space[5]}
              alignItems="center"
              bgColor={colors.background}
              borderBottomRadius={borderRadius['x-large']}>
              <LoadingIndicator />
            </Box>
          ) : (
            <TableEmptyRecord />
          )
        }
        data={sortedData}
        renderItem={({ item, index }) => (
          <PendingTableContent
            item={item}
            index={index}
            dataLength={sortedData?.length ?? 0}
            isItemPressable={isItemPressable}
          />
        )}
      />
    </Box>
  );
}

function PendingTableContent({
  item,
  index,
  dataLength,
  isItemPressable,
}: {
  item: NBPending;
  index: number;
  dataLength: number;
  isItemPressable: boolean;
}) {
  const { colors, borderRadius, space } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { t } = useTranslation('policy');

  const owbStatusLabel =
    item.policyStatus && item.policyStatus in owbCodeToLabelStatus
      ? owbCodeToLabelStatus[item.policyStatus]
      : undefined;

  return (
    <Box>
      <RowSeparator
        disabled={!isItemPressable}
        style={[
          {
            backgroundColor:
              index % 2 === 0
                ? colors.palette.white
                : colors.palette.fwdGrey[20],
            borderBottomLeftRadius:
              index === dataLength - 1 ? borderRadius.large : 0,
            borderBottomRightRadius:
              index === dataLength - 1 ? borderRadius.large : 0,
          },
          index === dataLength - 1 && { borderBottomWidth: 0 },
        ]}
        // disabled={country == 'ib'}
        onPress={() => {
          navigation.navigate('ExistingPolicyDetail', {
            type: 'policy',
            policyId: item.policyNo,
            status:
              item.policyStatus && item.policyStatus in owbCodeToLabelStatus
                ? owbCodeToLabelStatus[item.policyStatus]
                : // : item.policyStatus ?? 'pending',
                  'pending',
            policyInfo: {
              phoneMobile: item.phoneMobile,
              submissionDate: item.submissionDate,
            },
            fromFrontendTab: 'pending',
          });
        }}>
        {pendingTableConfig[country].certificatePending?.listHeader?.map(
          ({ type, width }) => {
            if (
              type == 'policyNo' ||
              type == 'submissionDate' ||
              type == 'policyOwner'
            ) {
              return (
                <Fragment key={type}>
                  <InfoText
                    style={{
                      width: width,
                    }}>
                    {type == 'policyNo'
                      ? (country == 'ib' ? '#' : '') + item.policyNo
                      : type == 'policyOwner'
                      ? item.displayName.en
                      : type == 'submissionDate' && item.submissionDate
                      ? dateFormatUtil(item.submissionDate)
                      : '--'}
                  </InfoText>
                  {['registerDate', 'submissionDate'].includes(type) ? null : (
                    <SeparatorLine />
                  )}
                </Fragment>
              );
            }

            if (type == 'owbStatus') {
              return (
                <Fragment key={type}>
                  <Box pl={space[4]} w={width}>
                    <FlagLabel
                      type={
                        owbStatusLabel &&
                        (owbStatusLabel == 'NBCounterOffer' ||
                          owbStatusLabel == 'NBPending')
                          ? 'alertMild_lightRed'
                          : 'primary_orange'
                      }
                      content={
                        item.policyStatus &&
                        item.policyStatus in owbCodeToLabelStatus
                          ? t(
                              `newBusiness.pending.${
                                owbCodeToLabelStatus[item.policyStatus]
                              }`,
                            )
                          : item.policyStatus ?? '--'
                      }
                    />
                  </Box>
                  <SeparatorLine />
                </Fragment>
              );
            }

            if (type == 'outstandingItem') {
              const count = pendingDocCountOnListHandler(item);
              return (
                <Fragment key={type}>
                  <Row
                    paddingX={space[4]}
                    paddingY={13}
                    alignItems="center"
                    gap={space[1]}
                    width={width}>
                    {count ? (
                      <Icon.Warning fill={colors.palette.alertRed} size={18} />
                    ) : null}
                    <H8
                      color={count ? colors.palette.alertRed : undefined}
                      fontWeight="bold">
                      {count ?? '--'}
                    </H8>
                  </Row>
                  <SeparatorLine />
                </Fragment>
              );
            }
          },
        )}

        <Box style={{ position: 'absolute', right: space[2] }}>
          {isItemPressable ? <Icon.ChevronRight /> : null}
        </Box>
      </RowSeparator>
    </Box>
  );
}
