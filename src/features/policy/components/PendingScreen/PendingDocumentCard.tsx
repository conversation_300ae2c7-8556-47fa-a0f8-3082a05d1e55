import { useTheme } from '@emotion/react';
import {
  Box,
  Icon,
  Row,
  Button,
  addToast,
  addErrorBottomToast,
  Typography,
  TextProps as UICompTextProps,
} from 'cube-ui-components';
import {
  QUERY_KEY_DOCUMENTS_LIST,
  useGetDocumentList,
  useSubmitV2Document,
  useBatchUploadPendingDocument,
} from 'hooks/useGetPolicyDetail';
import { TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import ImagePicker from 'components/ImagePicker';
import React, { useState } from 'react';
import LoadingIndicator from 'components/LoadingIndicator';
import { GetDocumentsListParams, PendingDocuments } from 'types/policy';
import { useQueryClient } from '@tanstack/react-query';
import SubmitConfirmDialog from './SubmitConfirmDialog';
import { ImageTitle, SectionTitle, UploadItemBox, Wrapper } from './StyledComp';
import DeleteConfirmDialog from './DeleteConfirmDialog';
import UploadProgressBar from './UploadProgressBar';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import { UploadedDocument } from 'api/policyDetailApi';
import { CubeResponse, UIMode } from 'types';
import { ImagePickerFile } from 'components/ImagePicker/utils';

export default function PendingDocumentCard({
  mode = 'tablet',
  pendDocData,
  hasSubmitConfirmDialogBefore,
  setHasSubmitConfirmDialogBefore,
}: {
  mode?: UIMode;
  pendDocData: PendingDocuments;
  hasSubmitConfirmDialogBefore: boolean;
  setHasSubmitConfirmDialogBefore: React.Dispatch<
    React.SetStateAction<boolean>
  >;
}) {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['policy', 'common']);
  // STATES
  const [isUploadLoading, setIsUploadLoading] = useState(false);
  const [isOpenImagePicker, setIsOpenImagePicker] = useState(false);

  const queryClient = useQueryClient();
  const {
    mutateAsync: uploadV2PendingDocument,
    isLoading: isUploadingDocument,
  } = useBatchUploadPendingDocument();

  const documentListParams = {
    policyNumber: pendDocData.policyNumber,
    pendingTransactionId: pendDocData.pendingTransactionId,
    pendMemo: pendDocData.pendMemo,
    documentClass: pendDocData.documentClass,
    documentId: pendDocData.documentId,
  } satisfies GetDocumentsListParams;

  const { data: documentsListData, isLoading: isDocumentsListDataLoading } =
    useGetDocumentList(documentListParams);

  const { mutateAsync: submitV2DocumentToOWB, isLoading: isSubmittingToOWB } =
    useSubmitV2Document();

  // const [uploadedDocuments, setIsUploadedDocument] =
  //   useState<GetDocumentsListResponse>(
  //     documentsListData?.map(
  //       item =>
  //         ({
  //           ...item,
  //           status: 'pendingUpload',
  //         } ?? []),
  //     ),
  //   );

  const [isShowSubmitImageConfirmDialog, setIsShowSubmitImageConfirmDialog] =
    useState(false);

  const [submitFailCount, setSubmitFailCount] = useState(0);

  // PICKER and LOADING
  const onCompletedImagePicker = (file: ImagePickerFile) => {
    setIsUploadLoading(true);
    const newFile = Object.keys(file).reduce((acc, cur) => {
      const key = cur as keyof typeof file;
      if (key == 'name') {
        const fileType = getFileType(file?.['uri']);
        const isExtensionInName = file?.[key]?.endsWith('.' + fileType);

        return {
          ...acc,
          [key]: isExtensionInName ? file?.[key] : file?.[key] + '.' + fileType,
        };
      }
      return {
        ...acc,
        [key]: file?.[key],
      };
    }, {} as ImagePickerFile);

    const errorHandler = (
      error: unknown,
      res?: CubeResponse<MemoUploadResponse>,
    ) => {
      console.log('~~~~ onCompletedImagePicker ~~ error', error);
      const toastMessages: { message: string }[] = [];

      if (res?.status === '413') {
        toastMessages.push({
          message: t('common:upload.invalidFileSize'),
        });
      } else if (
        res?.responseData &&
        res?.responseData.some(res =>
          res.message?.includes('File type not match pattern'),
        )
      ) {
        toastMessages.push({
          message: t('common:upload.invalidFileExtension', {
            fileTypes: '.jpg, .png, .bmp, and .pdf ',
          }),
        });
      } else {
        toastMessages.push({
          message: 'Please try again later.',
        });
      }

      addErrorBottomToast(toastMessages);
      setIsUploadLoading(false);
    };

    uploadV2PendingDocument(
      {
        body: {
          files: newFile,
          documentClass: pendDocData.documentClass,
          documentId: pendDocData.documentId,
          policyNumber: pendDocData.policyNumber,
          pendMemo: pendDocData.pendMemo,
          pendingTransactionId: pendDocData.pendingTransactionId,
        },
      },
      {
        onSuccess: async res => {
          try {
            const parsedRes = JSON.parse(
              res.body,
            ) as CubeResponse<MemoUploadResponse>;
            if (
              parsedRes?.success == false ||
              (parsedRes?.success == true &&
                parsedRes?.responseData &&
                parsedRes?.responseData?.length > 0 &&
                parsedRes?.responseData.some(res => res.status === 'FAILED'))
            ) {
              errorHandler(new Error(), parsedRes);
              return;
            }

            await queryClient.invalidateQueries({
              queryKey: [QUERY_KEY_DOCUMENTS_LIST, documentListParams],
            }),
              setSubmitFailCount(0);
            setIsUploadLoading(false);
          } catch (error) {
            console.log('error', error);
          }
        },
        onError: e => {
          errorHandler(e);
        },
      },
    );
  };

  const onConfirmSubmit = async () => {
    try {
      await submitV2DocumentToOWB(documentListParams, {
        onSuccess: res => {
          console.log('success ==== res: ', res);
          addToast([
            {
              message: 'Submit successfully.',
              IconLeft: <Icon.Tick fill="#FFFFFF" />,
            },
          ]);
        },
        onError: error => {
          console.log('------- error', error);
          addErrorBottomToast([
            {
              message: 'Please try again later.',
            },
          ]);
          setSubmitFailCount(count => count + 1);
        },
      });
      setIsShowSubmitImageConfirmDialog(false);
    } catch (error) {
      console.log('error', error);
    }
  };

  const isPendDocActive = pendDocData.acceptanceStatus === 'Active';
  const isPendDocSubmitted = pendDocData.acceptanceStatus === 'Submitted';
  const isPendDocResolved = pendDocData.acceptanceStatus === 'Resolved';
  const isPendDocWavied = pendDocData.acceptanceStatus === 'Waived';

  const MapIcon = () =>
    isPendDocResolved ? (
      <Icon.TickCircle
        fill={colors.palette.alertGreen}
        size={mode == 'phone' ? space[5] : space[6]}
      />
    ) : isPendDocSubmitted ? (
      <Icon.Progress
        fill={colors.palette.fwdBlue[100]}
        size={mode == 'phone' ? space[6] : space[7]}
      />
    ) : isPendDocWavied ? (
      <Icon.CloseCircle
        fill={colors.palette.fwdGreyDarker}
        size={mode == 'phone' ? space[5] : space[6]}
      />
    ) : (
      <></>
    );

  const mapStatusTextColor = isPendDocResolved
    ? colors.palette.alertGreen
    : isPendDocSubmitted
    ? colors.palette.fwdBlue[100]
    : isPendDocWavied
    ? colors.palette.fwdGreyDarker
    : undefined;

  const isPhone = mode === 'phone';

  const isAlerted = isPendDocActive || isPendDocSubmitted;

  return (
    <Box
      gap={space[4]}
      p={space[4]}
      borderRadius={space[4]}
      backgroundColor={colors.palette.fwdGrey[20]}>
      <ImagePicker
        visible={isOpenImagePicker}
        attachmentEnabled
        maxSizeInMB={10}
        title={t('policy:outstandingItems.uploadImageBy')}
        onDismiss={() => setIsOpenImagePicker(false)}
        onDone={({ file, failed }) => {
          // for HARDY: when the quil API is ready, here we do the post API and send the uploaded image to the CUBE mongoDB.
          // can refernce to erecruitV2/ib/tablet/ApplidationCheck/Documents.tsx
          // there has a part which i did the formdata on the onDone of imagePicker.
          setIsUploadLoading(true);
          if (file) {
            onCompletedImagePicker(file);
          }
          if (failed) {
            setIsUploadLoading(false);
          }
        }}
      />
      {/* ---- Header  */}
      <Row
        justifyContent="space-between"
        alignItems="center"
        gap={space[2]}
        minH={space[10]}>
        {isAlerted ? (
          <Icon.Warning
            size={mode == 'phone' ? space[5] : space[6]}
            fill={colors.palette.alertRed}
          />
        ) : null}
        <Row alignItems="center" gap={space[2]} flex={1}>
          <HeaderText
            mode={mode}
            color={isAlerted ? colors.palette.alertRed : undefined}
            numberOfLines={2}
            fontWeight="bold">
            {pendDocData.remark}
          </HeaderText>
        </Row>
        {isPendDocActive ? null : (
          <Row gap={space[1]} alignItems="center">
            <MapIcon />
            <PendDocStatusText mode={mode} color={mapStatusTextColor}>
              {pendDocData?.acceptanceStatus ?? '--'}
            </PendDocStatusText>
          </Row>
        )}
      </Row>

      {/* ---- Content  */}
      {!isPendDocActive ? (
        // Document List when the status is not Active: no User action
        <Box gap={space[3]}>
          <Wrapper isPhone={isPhone}>
            {documentsListData
              // .filter(item => item.status == 'pendingUpload')
              ?.map((doc, docIndex) => (
                <Row
                  key={doc?.createdAt + doc?.originalFilename}
                  w={isPhone ? '100%' : '50%'}
                  alignItems={isPhone ? undefined : 'center'}
                  borderLeft={isPhone ? undefined : docIndex % 2 !== 0 ? 1 : 0}
                  borderLeftColor={
                    isPhone ? undefined : colors.palette.fwdGrey[100]
                  }
                  pr={isPhone ? undefined : docIndex % 2 === 0 ? space[7] : 0}
                  pl={isPhone ? undefined : docIndex % 2 === 0 ? 0 : space[7]}>
                  <ImageTitle
                    numberOfLines={isPhone ? 3 : 1}
                    style={{
                      width: undefined,
                      flex: 1,
                    }}>
                    {doc.originalFilename}
                  </ImageTitle>
                </Row>
              ))}
          </Wrapper>
        </Box>
      ) : isUploadLoading ? (
        <UploadProgressBar isUploadLoading />
      ) : documentsListData?.length ? (
        // Document List when the status is Active and there are updated files
        // User action: upload files, or delete updated files
        <Box gap={space[2]}>
          <UploadItemBox
            style={[
              submitFailCount > 0
                ? {
                    borderColor: colors.palette.alertRed,
                  }
                : {},
              { marginBottom: 0 },
            ]}>
            <Wrapper isPhone={isPhone}>
              {documentsListData?.map((doc, docIndex) => (
                <Row
                  key={doc?.createdAt + doc?.originalFilename}
                  w={isPhone ? '100%' : '50%'}
                  alignItems="center"
                  gap={space[2]}
                  borderLeft={isPhone ? undefined : docIndex % 2 !== 0 ? 1 : 0}
                  borderLeftColor={
                    isPhone ? undefined : colors.palette.fwdGrey[100]
                  }
                  pr={isPhone ? undefined : docIndex % 2 === 0 ? space[7] : 0}
                  pl={isPhone ? undefined : docIndex % 2 === 0 ? 0 : space[7]}>
                  <ImageTitle
                    numberOfLines={3}
                    style={{
                      width: undefined,
                      flex: 1,
                    }}>
                    {doc.originalFilename}
                  </ImageTitle>
                  {isUploadingDocument ? (
                    <Row>
                      <LoadingIndicator />
                    </Row>
                  ) : (
                    <DeleteButtonWithModal
                      isPhone={isPhone}
                      doc={doc}
                      setSubmitFailCount={setSubmitFailCount}
                    />
                  )}
                </Row>
              ))}
            </Wrapper>
          </UploadItemBox>
          {submitFailCount === 0 ? null : (
            <Row gap={space[1]} alignItems="center">
              <Icon.CloseCircle
                fill={colors.palette.alertRed}
                size={space[5]}
              />
              <Typography.LargeBody color={colors.palette.alertRed}>
                {submitFailCount >= 3
                  ? 'System error, please contact your local support team.'
                  : 'Submission failed, please try again.'}
              </Typography.LargeBody>
            </Row>
          )}
        </Box>
      ) : // Document List when the status is Active and there are NO updated files
      null}

      {/*  -------- Upload and Submit buttons, and Uploading Bar*/}
      {isPendDocActive ? (
        <Row justifyContent="center" gap={space[4]}>
          <Button
            // disabled={uploadedDocuments?.length > 0}
            variant="secondary"
            text={t('policy:outstandingItems.upload')}
            icon={<Icon.Upload size={18} />}
            mini={true}
            onPress={() => {
              setIsOpenImagePicker(true);
            }}
            contentStyle={{
              paddingHorizontal: space[2],
              width: isPhone ? '100%' : space[50],
            }}
            style={{ flex: mode == 'phone' ? 1 : undefined }}
          />
          {documentsListData?.length ? (
            <>
              <Button
                variant="primary"
                text={t('policy:outstandingItems.submit')}
                mini={true}
                disabled={isUploadLoading || isSubmittingToOWB}
                onPress={() => {
                  if (hasSubmitConfirmDialogBefore) {
                    // has shown the confirm dialog before, and submit directly
                    onConfirmSubmit();
                  } else {
                    setHasSubmitConfirmDialogBefore(true);
                    setIsShowSubmitImageConfirmDialog(true);
                  }
                }}
                contentStyle={{
                  paddingHorizontal: space[2],
                  width: isPhone ? '100%' : space[50],
                }}
                style={{ flex: isPhone ? 1 : undefined }}
              />
              <SubmitConfirmDialog
                isPhone={isPhone}
                isSubmitting={isSubmittingToOWB}
                isShowSubmitImageConfirmDialog={isShowSubmitImageConfirmDialog}
                setIsShowSubmitImageConfirmDialog={
                  setIsShowSubmitImageConfirmDialog
                }
                onConfirmSubmit={onConfirmSubmit}
              />
            </>
          ) : null}
        </Row>
      ) : null}
    </Box>
  );
}

function DeleteButtonWithModal({
  doc,
  setSubmitFailCount,
  isPhone,
}: {
  doc: UploadedDocument;
  isPhone: boolean;
  setSubmitFailCount: React.Dispatch<React.SetStateAction<number>>;
}) {
  const { space, colors } = useTheme();

  const [isShownDeleteImageConfirmDialog, setIsShowDeleteImageConfirmDialog] =
    useState(false);
  return (
    <>
      <TouchableOpacity
        onPress={() => {
          setIsShowDeleteImageConfirmDialog(true);
        }}>
        <Icon.Delete fill={colors.palette.black} />
      </TouchableOpacity>
      <DeleteConfirmDialog
        isPhone={isPhone}
        isShownDeleteImageConfirmDialog={isShownDeleteImageConfirmDialog}
        setIsShowDeleteImageConfirmDialog={setIsShowDeleteImageConfirmDialog}
        item={doc}
        onSuccess={() => setSubmitFailCount(0)}
      />
    </>
  );
}

const HeaderText = (props: UICompTextProps & { mode: UIMode }) => {
  const mode: UIMode = props?.mode ?? 'tablet';

  if (mode == 'phone') {
    return <Typography.H7 {...props} />;
  }

  return <Typography.H6 {...props} />;
};

const PendDocStatusText = (props: UICompTextProps & { mode: UIMode }) => {
  const mode: UIMode = props?.mode ?? 'tablet';

  if (mode == 'phone') {
    return <Typography.Body {...props} />;
  }

  return <Typography.LargeBody {...props} />;
};

type MemoUploadResponse = {
  status: 'SUCCESS' | 'FAILED';
  fileName: string;
  originalFilename: string;
  fileType: string;
  message: string;
}[];
