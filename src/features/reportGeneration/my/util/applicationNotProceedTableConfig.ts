/**
 * This file contains the configuration for the data grid in report generation - Lapse report.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Certificate number/\nCertificate owner',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'registrationDate',
    title: 'Registration date',
  },
  {
    type: 'decisionDate',
    title: 'Decision date',
  },
  {
    type: 'status',
    title: 'Status',
  },
  {
    type: 'currency',
    title: 'Currency',
  },
  {
    type: 'modalPremium',
    title: 'Modal contribution',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'ace',
    title: 'ACE',
  },
  {
    type: 'remark',
    title: 'Remark',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
