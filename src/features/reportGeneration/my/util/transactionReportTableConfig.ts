/**
 * This file contains the configuration for the data grid in report generation - certificate transaction report.
 */

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: 'Certificate number/\nCertificate owner',
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: 'Agent code',
  },
  {
    type: 'agentName',
    title: 'Agent name',
  },
  {
    type: 'product',
    title: 'Product',
  },
  {
    type: 'issueDate',
    title: 'Issue date',
  },
  {
    type: 'transactionDate',
    title: 'Transaction date',
  },
  {
    type: 'frequency',
    title: 'Frequency',
  },
  {
    type: 'paymentMethod',
    title: 'Payment method',
  },
  {
    type: 'mandateStatus',
    title: 'Mandate status',
  },
  {
    type: 'modalPremium',
    title: 'Modal contribution',
  },

  {
    type: 'ace',
    title: 'ACE',
  },
  {
    type: 'description',
    title: 'Description',
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
