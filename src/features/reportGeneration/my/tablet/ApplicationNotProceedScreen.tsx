import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  Column,
  Icon,
  Label,
  LargeLabel,
  Row,
  SmallBody,
  Typography,
} from 'cube-ui-components';
import {
  MemberInfo,
  flattenTeamHierarchy,
} from 'features/reportGeneration/utils/reportUtils';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetTeamHierarchy } from 'hooks/useGetTeam';
import _ from 'lodash';
import React, { useContext, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import TabletScreenHeader from 'navigation/components/ScreenHeader/tablet';
import {
  useGetApplicationNotProceedReport,
  useGetDueDateReport,
} from 'hooks/useReportGeneration';
import ReportDataGrid from 'features/reportGeneration/ph/components/DataGrid/ReportDataGrid';
import Animated, { LinearTransition } from 'react-native-reanimated';
import {
  CELL_RENDER_ORDER,
  FREEZE_HEADER,
  HEADERS,
} from '../util/applicationNotProceedTableConfig';
import { TouchableOpacity } from 'react-native';
import { dateFormatUtil } from 'agent-guru';
import styled from '@emotion/native';
import useCheckIsLeader from 'features/reportGeneration/hooks/useCheckIsLeader';
import DateSelectModal from '../components/DateSelectModal';
import SelectAgentModal from '../components/SelectAgentModal';
import PolicyInfoActionPanel from 'features/reportGeneration/ph/components/ActionPanel/PolicyInfoActionPanel';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';

/**
 * For both mobile and tablet
 */
export default function ApplicationNotProceedScreen(props: any) {
  const { agent, to, from, team } = props.route.params;
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedAgent, setSelectedAgent] = useState<MemberInfo | null>(null);
  const [showSelectedAgentList, setShowSelectedAgentList] = useState(false);
  const [isTeamSelected, setIsTeamSelected] = useState(false);
  const [showDateSelectModal, setShowDateSelectModal] = useState(false);
  const [policyInfo, updatePolicyInfo] = useState({
    searchType: 'all',
    focusedChip: 'policyNumber',
    policyHolderName: '',
    policyNumber: '',
  });
  const [openPolicyHolderPanel, setOpenPolicyHolderPanel] = useState(false);
  const [dateRange, setDateRange] = useState({
    from: from,
    to: to,
    datePeriodType: 'DUEDATE',
  });

  console.log('policyInfo', policyInfo);

  const handleDateRangeChange = ({
    to,
    from,
  }: {
    to: string;
    from: string;
  }) => {
    setDateRange({
      from: from,
      to: to,
      datePeriodType: 'DUEDATE',
    });
  };

  const selfAgentCode = useBoundStore(state => state.auth.agentCode);

  const { data: agentProfile } = useGetAgentProfile();
  const isLeader = agentProfile?.isLeader;
  const { data: teamData, isLoading: isTeamLoading } = useGetTeamHierarchy();
  const memberInfoList = flattenTeamHierarchy(teamData);
  const hasNoDownline =
    _.isEmpty(teamData?.members) && _.isEmpty(teamData?.subteams);

  // console.log('memberInfoList', memberInfoList);
  // console.log('isLeader', isLeader);
  // console.log('hasNoDownline', hasNoDownline);

  const DATEPERIOD_CONFIG = [
    {
      title: t('last2months'),
      value: 'last2months',
    },
    {
      title: t('last30days'),
      value: 'last30days',
    },
    {
      title: t('last180days'),
      value: 'last180days',
    },
    {
      title: t('customise'),
      value: 'customise',
    },
  ];

  const {
    data: dueDateReportData,
    isLoading: isDueDateReportLoading,
    isError,
  } = useGetApplicationNotProceedReport({
    agentId: isTeamSelected ? '' : selectedAgent?.agentCode,
    team: isTeamSelected,
    from: dateRange.from,
    to: dateRange.to,
    policyInfo: {
      policyHolderName: policyInfo.policyHolderName,
      policyNumber: policyInfo.policyNumber,
    },
  });

  useMemo(() => {
    setSelectedAgent(agent);
    setIsTeamSelected(team);
  }, [agent, team]);

  const onCloseShowSelectedAgentList = () => {
    setShowSelectedAgentList(false);
  };

  const handleSelectAgent = (agent: MemberInfo) => {
    setSelectedAgent(agent);
  };

  return (
    <>
      <PolicyInfoActionPanel
        visible={openPolicyHolderPanel}
        handleClose={() => setOpenPolicyHolderPanel(false)}
        contextValue={policyInfo}
        updateContextValue={updatePolicyInfo}
      />
      <SelectAgentModal
        visible={showSelectedAgentList}
        onClose={onCloseShowSelectedAgentList}
        data={memberInfoList}
        handleSelectAgent={handleSelectAgent}
      />
      <DateSelectModal
        visible={showDateSelectModal}
        handleClose={() => {
          setShowDateSelectModal(false);
        }}
        datePeriodConfig={DATEPERIOD_CONFIG}
        title={t('registrationDate')}
        defaultDateRange={{
          datePeriodType: 'DUEDATE',
          from: dateRange.from,
          to: dateRange.to,
        }}
        handleDateRangeChange={handleDateRangeChange}
      />
      <TabletScreenHeader
        route={'ApplicationNotProceedScreen'}
        customTitle={t('applicationNotProceed')}
        isLeftArrowBackShown
        showBottomSeparator={false}
        rightChildren={
          <TouchableOpacity>
            <Row alignItems="center" gap={space[1]}>
              <Icon.Download size={24} fill={colors.onBackground} />
              <Typography.H7 fontWeight="bold">{t('export')}</Typography.H7>
            </Row>
          </TouchableOpacity>
        }
      />
      <Box padding={space[4]} gap={space[2]}>
        <Row gap={space[1]}>
          {isLeader && (
            <TouchableOpacity
              onPress={() => {
                setShowSelectedAgentList(true);
              }}>
              <FilterView>
                <SmallBody>{t('toolbar.selectedAgent')}</SmallBody>
                <Row gap={space[1]} alignItems="center">
                  <Label fontWeight="medium">
                    {selectedAgent?.agentName ?? '--'}
                  </Label>
                  <Icon.ChevronDown
                    size={sizes[4]}
                    fill={colors.onBackground}
                  />
                </Row>
              </FilterView>
            </TouchableOpacity>
          )}
          <TouchableOpacity
            onPress={() => {
              setShowDateSelectModal(true);
            }}>
            <FilterView>
              <SmallBody>{t('dueDate')}</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">{`${formatDate(
                  dateRange.from,
                )} - ${formatDate(dateRange.to)}`}</Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setOpenPolicyHolderPanel(true);
            }}>
            <FilterView
              style={{
                backgroundColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[5]
                    : colors.background,
                borderColor:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? colors.palette.fwdOrange[100]
                    : colors.palette.fwdGrey[100],
                borderWidth:
                  policyInfo.policyHolderName || policyInfo.policyNumber
                    ? 2
                    : 1,
              }}>
              <SmallBody>Certificate owner/number</SmallBody>
              <Row gap={space[1]} alignItems="center">
                <Label fontWeight="medium">
                  {policyInfo.policyHolderName ||
                    policyInfo.policyNumber ||
                    'All'}
                </Label>
                <Icon.ChevronDown size={sizes[4]} fill={colors.onBackground} />
              </Row>
            </FilterView>
          </TouchableOpacity>
          {isLeader && (
            <FilterView>
              <Checkbox
                value={isTeamSelected}
                onChange={() => {
                  setIsTeamSelected(!isTeamSelected);
                }}
              />
              <Label>{t('toolbar.team')}</Label>
            </FilterView>
          )}
        </Row>
        <Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {t('totalResult')}{' '}
          </Typography.Body>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            {`(${dueDateReportData?.summary.caseCount})  |  ${t(
              'displaying4500',
            )}`}
          </Typography.Body>
        </Row>
      </Box>
      <Animated.View layout={LinearTransition.delay(50)} style={{ flex: 1 }}>
        <ReportDataGrid
          darkMode={false}
          isLoading={isDueDateReportLoading}
          isError={isError}
          data={dueDateReportData?.data}
          freezeHeader={FREEZE_HEADER}
          headers={HEADERS}
          cellRenderOrder={CELL_RENDER_ORDER}
        />
      </Animated.View>
    </>
  );
}

const FilterView = styled.View(({ theme }) => {
  const { colors, space, sizes } = theme;
  return {
    display: 'flex',
    flexDirection: 'row',
    gap: space[2],
    backgroundColor: colors.background,
    borderRadius: sizes[2],
    paddingHorizontal: space[3],
    paddingVertical: space[2],
    borderWidth: 1,
    borderColor: colors.palette.fwdGrey[100],
  };
});
