import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFooter,
  BottomSheetFooterProps,
  BottomSheetModal,
} from '@gorhom/bottom-sheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import LoadingIndicator from 'components/LoadingIndicator';
import {
  Box,
  Button,
  DropdownPanel,
  H6,
  Icon,
  Label,
  Row,
} from 'cube-ui-components';
import { AnimatedScrollDownPdf } from 'features/pdfViewer/components/AnimatedScrollDownPdf';
import type { InternalPdfViewerProps } from 'features/pdfViewer/components/PdfViewer';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import {
  BackHandler,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import Pdf from 'react-native-pdf';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import EmailSharing from './EmailSharing';

const HIT_SLOP = 12;

const SPdf = styled(Pdf)(({ theme: { sizes, colors } }) => ({
  flex: 1,
  backgroundColor: '#525558',
  borderRadius: sizes[3],
}));

const PdfPlaceholder = styled.View(({ theme: { sizes, colors } }) => ({
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: '#525558',
  borderRadius: sizes[3],
}));

const LanguagePickerContainer = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: theme.space[3],
  paddingVertical: theme.space[3] / 2,
  borderRadius: theme.borderRadius.full,
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[50],
  columnGap: theme.space[1],
}));

const FooterContainer = styled.View(({ theme }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    paddingBottom: bottomInset + theme.space[4],
    paddingTop: theme.space[4],
    paddingHorizontal: theme.space[isNarrowScreen ? 3 : 4],
    backgroundColor: theme.colors.background,
  };
});

const PdfViewerPhone = ({
  visible,
  title,
  language,
  setLanguage,
  isLoading,
  pageData,
  showEmailSharing,
  handleOnScroll,
  modalTitle,
  pdf,
  onSavePdf,
  onSharePdf,
  onClose,
  downloadable,
  pdfOption,
  actionOption,
  sharable,
  shareType,
  mailConfig,
  attachments,
  emailSharingVisible,
  hideEmailSharing,
  closable = true,
  headerContainerStyle,
}: InternalPdfViewerProps) => {
  const { height: windowHeight } = useWindowDimensions();
  const { top: topInset, bottom: bottomInset } = useSafeAreaInsets();
  const bottomSheetRef = useRef<BottomSheetModal>(null);
  const { space, sizes, colors, borderRadius } = useTheme();
  const selectedLanguageItem = useMemo(
    () =>
      Array.isArray(title) ? title.find(o => o.value === language) : undefined,
    [language, title],
  );
  const [
    languageSelectionVisible,
    showLanguageSelection,
    hideLanguageSelection,
  ] = useToggle();

  useEffect(() => {
    if (visible) {
      bottomSheetRef.current?.present();
    } else {
      bottomSheetRef.current?.dismiss();
    }
  }, [language, visible]);

  const { t } = useTranslation(['product', 'common', 'pdfViewer']);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => {
      return React.createElement(BottomSheetBackdrop, {
        ...props,
        appearsOnIndex: 0,
        disappearsOnIndex: -1,
        pressBehavior: closable ? 'close' : 'none',
      });
    },
    [closable],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const backgroundStyle = useMemo(
    () => ({
      borderRadius: borderRadius.large,
    }),
    [borderRadius],
  );
  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [],
  );
  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      if (actionOption) {
        let text = '';
        let subtext: string | undefined = undefined;
        let onPress;
        switch (actionOption.actionMode) {
          case 'default':
            text = actionOption.text;
            subtext = actionOption.subtext;
            onPress = actionOption.onPress;
            break;
          case 'send-email':
            text = actionOption.text;
            subtext = actionOption.subtext;
            onPress = showEmailSharing;
            break;
        }
        return (
          <BottomSheetFooter {...props}>
            <FooterContainer>
              <Button
                disabled={
                  isLoading ||
                  (actionOption.activeMode === 'end-of-file' &&
                    (pageData.currentPage !== pageData.totalPages ||
                      (actionOption.disabledMode === 'exclude-zero' &&
                        pageData.totalPages === 0))) ||
                  (!pdf?.base64 && !pdf?.url)
                }
                text={text}
                subtext={subtext}
                onPress={onPress}
              />
            </FooterContainer>
          </BottomSheetFooter>
        );
      }
      return null;
    },
    [
      actionOption,
      isLoading,
      pageData.currentPage,
      pageData.totalPages,
      pdf?.base64,
      pdf?.url,
      showEmailSharing,
    ],
  );

  useEffect(() => {
    const backAction = () => {
      bottomSheetRef?.current?.dismiss();
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, []);

  const snapPoints = useMemo(
    () => [windowHeight - topInset - sizes[11]],
    [sizes, topInset, windowHeight],
  );

  const noHeader =
    !modalTitle &&
    !downloadable &&
    !sharable &&
    !(Array.isArray(title) && title.length > 1);

  const renderHandle = useCallback(() => {
    if (modalTitle) {
      return <Box h={space[6]} />;
    }
    if (noHeader) {
      return <Box h={space[8]} />;
    }
    return <Box h={space[6]} />;
  }, [modalTitle, noHeader, space]);

  return (
    <>
      <BottomSheetModal
        ref={bottomSheetRef}
        stackBehavior="push"
        onDismiss={onClose}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        handleIndicatorStyle={handleIndicatorStyle}
        handleStyle={handleStyle}
        enableContentPanningGesture={false}
        backgroundStyle={backgroundStyle}
        footerComponent={renderFooter}
        handleComponent={closable ? undefined : renderHandle}
        enableHandlePanningGesture={closable}
        enablePanDownToClose={closable}>
        <Box
          flex={1}
          px={space[isNarrowScreen ? 3 : 4]}
          pb={actionOption ? 0 : bottomInset || space[4]}>
          {!noHeader && (
            <Row
              alignItems="center"
              justifyContent="space-between"
              paddingBottom={space[6]}
              style={headerContainerStyle}>
              {modalTitle && <H6 fontWeight="bold">{modalTitle}</H6>}
              <Row alignItems="center" columnGap={space[4]}>
                {Array.isArray(title) && title.length > 1 && (
                  <LanguagePickerContainer onPress={showLanguageSelection}>
                    <Label>{language.toUpperCase()}</Label>
                    <Icon.ChevronDown size={sizes[5]} />
                  </LanguagePickerContainer>
                )}
                {downloadable && (
                  <TouchableOpacity hitSlop={HIT_SLOP} onPress={onSavePdf}>
                    <Box
                      alignItems="center"
                      justifyContent="center"
                      gap={space[1]}>
                      <Icon.Download size={space[6]} />
                    </Box>
                  </TouchableOpacity>
                )}
                {sharable &&
                  ((shareType === 'email' &&
                    actionOption?.actionMode !== 'send-email') ||
                    shareType === 'social-media') && (
                    <TouchableOpacity hitSlop={HIT_SLOP} onPress={onSharePdf}>
                      <Box
                        alignItems="center"
                        justifyContent="center"
                        gap={space[1]}>
                        <Icon.Share size={space[6]} />
                      </Box>
                    </TouchableOpacity>
                  )}
              </Row>
            </Row>
          )}
          {!isLoading ? (
            <Box
              flex={1}
              borderWidth={1}
              borderColor={colors.palette.fwdGrey[100]}
              borderRadius={sizes[3]}
              overflow={'hidden'}>
              <SPdf
                source={{
                  uri: pdf?.url
                    ? pdf.url
                    : 'data:application/pdf;base64,' + pdf?.base64,
                  ...(pdf?.headers && { headers: pdf?.headers }),
                }}
                onError={e => {
                  console.error(
                    'Fail to load the PDF in PDF Viewer (tablet)',
                    e,
                  );
                }}
                onPageChanged={handleOnScroll}
                trustAllCerts={false}
                scale={0.9}
                minScale={0.9}
                maxScale={5.0}
                password={pdf?.password}
                renderActivityIndicator={() => (
                  <LoadingIndicator size={space[25]} />
                )}
                {...pdfOption}
              />
              {(pdf?.base64 || pdf?.url) &&
                actionOption?.activeMode === 'end-of-file' && (
                  <AnimatedScrollDownPdf pageData={pageData} />
                )}
            </Box>
          ) : (
            <PdfPlaceholder>
              <LoadingIndicator size={space[25]} />
            </PdfPlaceholder>
          )}
          <BottomSheetFooterSpace />
        </Box>
      </BottomSheetModal>
      {Array.isArray(title) && (
        <DropdownPanel
          visible={languageSelectionVisible}
          title={t('pdfViewer:language')}
          data={title}
          getItemLabel={item => item.option}
          getItemValue={item => item.value}
          actionLabel={t('pdfViewer:done')}
          selectedItem={selectedLanguageItem}
          onDone={item => setLanguage(item.value)}
          onDismiss={hideLanguageSelection}
        />
      )}
      {sharable && shareType === 'email' && mailConfig && (
        <EmailSharing
          visible={emailSharingVisible}
          title={mailConfig.title}
          onDismiss={hideEmailSharing}
          agentEmail={mailConfig.mailCc}
          customerEmail={mailConfig.mailTo}
          subject={mailConfig.subject}
          content={mailConfig.content}
          html={mailConfig.html}
          attachments={attachments}
          onSent={payload => {
            if (actionOption?.actionMode === 'send-email') {
              actionOption.onEmailSent(payload);
            }
            onClose();
          }}
        />
      )}
    </>
  );
};

export default PdfViewerPhone;
