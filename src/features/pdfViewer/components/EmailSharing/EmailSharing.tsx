import { SendEmailBody } from 'api/emailApi';
import DeviceBasedRendering from 'components/DeviceBasedRendering';
import { addErrorBottomToast, addToast } from 'cube-ui-components';
import { useSendEmail } from 'hooks/useSendEmail';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';
import useSavePdf from '../../hooks/useSavePdf';
import EmailSharingPhone from './EmailSharing.phone';
import EmailSharingTablet from './EmailSharing.tablet';
import { emailAddressRegex } from './emailAddressRegex';
import { useSendIllustrationEmail } from 'hooks/useSendIllustrationEmail';

export interface InternalEmailSharingProps
  extends Pick<Props, 'onDismiss' | 'attachments' | 'title'> {
  isSendingEmail?: boolean;
  subject: string;
  content: string;
  mailTo: string[];
  additionalMailToList: string[];
  setMailTo: (value: string[]) => void;
  setAdditionalMailToList: (value: string[]) => void;
  mailToError: string;
  fixedMailCc: string[];
  additionalMailCcList: string[];
  setAdditionalMailCcList: (value: string[]) => void;
  mailCcError: string;
  onSend?: () => void;
  onToggleAttachment?: (attachment: Attachment) => void;
}

export type Attachment = {
  attachmentUrl?: string;
  attachmentBase64?: string;
  attachmentName: string;
  required?: boolean;
  selected?: boolean;
};

type Props = {
  visible?: boolean;
  title?: string;
  onDismiss?: () => void;
  onSent?: (args: Omit<SendEmailBody, 'emailBody'>) => void;
  agentEmail: string;
  customerEmail?: string;
  subject: string;
  content: string;
  html: string;
  attachments: Attachment[];
  templateName?: string;
  extra?: Record<string, string>;
};

export default function EmailSharing({
  visible,
  title,
  onDismiss,
  agentEmail,
  customerEmail,
  subject,
  content,
  html,
  attachments,
  onSent,
  templateName,
  extra,
}: Props) {
  const { t } = useTranslation(['pdfViewer']);
  const [mailTo, setMailTo] = useState(() =>
    customerEmail ? [customerEmail] : [],
  );
  const fixedMailCc = useState(() => (agentEmail ? [agentEmail] : []))[0];
  const [additionalMailToList, setAdditionalMailToList] = useState<string[]>(
    () => [],
  );
  const [additionalMailCcList, setAdditionalMailCcList] = useState<string[]>(
    () => [],
  );

  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [localAttachments, setLocalAttachments] =
    useState<Attachment[]>(attachments);

  const { pdfToBase64, pdfToLocalUri } = useSavePdf();
  const { mutateAsync: sendEmail } = useSendEmail();
  const { mutateAsync: sendIllustrationEmail } = useSendIllustrationEmail();

  const convertPdfToBase64 = async ({
    attachmentBase64,
    attachmentUrl,
    attachmentName,
  }: {
    attachmentBase64?: string;
    attachmentUrl?: string;
    attachmentName: string;
  }) => {
    if (country === 'id') {
      const fileUri = await pdfToLocalUri({
        url: attachmentUrl,
        base64: attachmentBase64,
        defaultName: attachmentName,
      });

      return {
        fileName: `${attachmentName}.pdf`,
        base64: fileUri,
      };
    } else {
      let base64Pdf: string | null | undefined = attachmentBase64;

      if (!attachmentBase64 && attachmentUrl) {
        base64Pdf = await pdfToBase64({
          url: attachmentUrl,
          defaultName: attachmentName,
        });
      }

      if (base64Pdf) {
        return {
          fileName: `${attachmentName}.pdf`,
          base64: base64Pdf,
        };
      } else throw new Error();
    }
  };

  const onSend = async () => {
    setIsSendingEmail(true);

    const convertPromises = localAttachments
      .filter(att => att.selected)
      .map(convertPdfToBase64);

    try {
      const emailToRecipients = [...mailTo, ...(additionalMailToList ?? [])];
      const emailCcRecipients = [
        ...fixedMailCc,
        ...(additionalMailCcList ?? []),
      ];
      const base64Pdfs = await Promise.all(convertPromises);
      if (!Array.isArray(base64Pdfs) || base64Pdfs?.length === 0) {
        addErrorBottomToast([
          {
            message: t('pdfViewer:attachmentNotFound'),
          },
        ]);
        return;
      }

      if (emailToRecipients.length === 0) {
        addErrorBottomToast([
          {
            message: t('pdfViewer:recipientNotFound'),
          },
        ]);
        return;
      }

      const attachmentObj = base64Pdfs.reduce(
        (obj, { fileName, base64 }) => ({ ...obj, [fileName]: base64 }),
        {},
      );

      const payload: SendEmailBody = {
        attachments: attachmentObj,
        emailToRecipients,
        emailCcRecipients,
        emailBccRecipients: [],
        emailTitle: templateName ?? subject,
        emailBody: html,
        isHtmlText: true,
        isFIB: country === 'ib' ? 'Y' : 'N',
        extra,
      };

      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { emailBody, ...restPayload } = payload;

      const sendEmailFn = country === 'id' ? sendIllustrationEmail : sendEmail;

      const { status } = await sendEmailFn(payload);

      if (['200 OK', '200', null].includes(status)) {
        addToast([
          {
            message: t('pdfViewer:emailHasBeenSent'),
          },
        ]);
        onSent?.({
          ...restPayload,
        });
        onDismiss?.();
      } else {
        throw new Error(t('pdfViewer:failedToSendEmail'));
      }
    } catch (e) {
      addErrorBottomToast([
        {
          message: t('pdfViewer:unableToSendEmail'),
        },
      ]);
    } finally {
      setIsSendingEmail(false);
    }
  };

  const onToggleAttachment = (attachment: Attachment) => {
    setLocalAttachments(prev =>
      prev.map(item => {
        if (item.attachmentName === attachment.attachmentName)
          return { ...item, selected: !item.selected };
        return item;
      }),
    );
  };

  const mailToError = useMemo(() => {
    const emailToRecipients = [...mailTo, ...(additionalMailToList ?? [])];
    if (emailToRecipients.length > 0) {
      if (emailToRecipients.some(email => !emailAddressRegex.test(email))) {
        return t('pdfViewer:invalidEmail');
      }
      return '';
    }
    return t('pdfViewer:emptyError');
  }, [mailTo, additionalMailToList, t]);

  const mailCcError = useMemo(() => {
    const emailCcRecipients = [...fixedMailCc, ...(additionalMailCcList ?? [])];
    if (emailCcRecipients.length > 0) {
      if (emailCcRecipients.some(email => !emailAddressRegex.test(email))) {
        return t('pdfViewer:invalidEmail');
      }
    }
    return '';
  }, [fixedMailCc, additionalMailCcList, t]);

  useEffect(() => {
    if (visible) {
      setLocalAttachments(attachments);
    }
  }, [attachments, visible]);

  if (!visible) return;

  return (
    <DeviceBasedRendering
      phone={
        <EmailSharingPhone
          title={title}
          isSendingEmail={isSendingEmail}
          subject={subject}
          content={content}
          mailTo={mailTo}
          additionalMailToList={additionalMailToList}
          setAdditionalMailToList={setAdditionalMailToList}
          mailToError={mailToError}
          fixedMailCc={fixedMailCc}
          additionalMailCcList={additionalMailCcList}
          setAdditionalMailCcList={setAdditionalMailCcList}
          setMailTo={setMailTo}
          mailCcError={mailCcError}
          onSend={onSend}
          onDismiss={onDismiss}
          attachments={localAttachments}
          onToggleAttachment={onToggleAttachment}
        />
      }
      tablet={
        <EmailSharingTablet
          title={title}
          isSendingEmail={isSendingEmail}
          subject={subject}
          content={content}
          mailTo={mailTo}
          additionalMailToList={additionalMailToList}
          setAdditionalMailToList={setAdditionalMailToList}
          setMailTo={setMailTo}
          mailToError={mailToError}
          fixedMailCc={fixedMailCc}
          additionalMailCcList={additionalMailCcList}
          setAdditionalMailCcList={setAdditionalMailCcList}
          mailCcError={mailCcError}
          onSend={onSend}
          onDismiss={onDismiss}
          attachments={localAttachments}
          onToggleAttachment={onToggleAttachment}
        />
      }
    />
  );
}
