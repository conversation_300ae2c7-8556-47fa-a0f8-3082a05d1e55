import { SendEmailBody } from 'api/emailApi';
import PdfViewerPhone from 'features/pdfViewer/components/PdfViewer.phone';
import PdfViewerTablet from 'features/pdfViewer/components/PdfViewer.tablet';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useLatest from 'hooks/useLatest';
import useToggle from 'hooks/useToggle';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { PdfProps } from 'react-native-pdf';
import { LanguagesKeys } from 'utils/translation';
import useSavePdf from '../hooks/useSavePdf';
import useSharePdf from '../hooks/useSharePdf';
import { StyleProp, ViewStyle } from 'react-native';

export type PdfGeneratorResult = {
  fileName: string;
  attachmentName?: string;
  password?: string;
} & (
  | {
      base64: string;
      url?: never;
      headers?: {
        Authorization: string;
        'x-agent-id'?: string;
      };
    }
  | {
      url: string;
      base64?: never;
      headers?: {
        Authorization: string;
        'x-agent-id'?: string;
      };
    }
);

export type PdfGenerator = (
  lang?: LanguagesKeys,
) => Promise<PdfGeneratorResult | undefined>;

type ActionActiveMode = {
  activeMode?: 'end-of-file' | 'default';
  disabledMode?: 'exclude-zero' | 'default';
};

type DefaultAction = {
  actionMode: 'default';
  text: string;
  subtext?: string;
  onPress: () => void;
};

type SendEmailAction = {
  actionMode: 'send-email';
  text: string;
  subtext?: string;
  onEmailSent: (args: Omit<SendEmailBody, 'emailBody'>) => void;
};

export type PdfActionOption = ActionActiveMode &
  (DefaultAction | SendEmailAction);

export type Attachment = {
  attachmentUrl?: string;
  attachmentBase64?: string;
  attachmentName: string;
  required?: boolean;
  selected?: boolean;
};

export type MailConfig = {
  title?: string;
  mailTo?: string;
  mailCc: string;
  subject: string;
  content: string;
  html: string;
  additionalAttachments?: Attachment[];
  templateName?: string;
};

type Sharable = {
  sharable?: boolean;
} & (
  | {
      shareType: 'email';
      mailConfig?: MailConfig;
    }
  | {
      shareType: 'social-media';
      mailConfig?: undefined;
    }
);

interface NonSharable {
  sharable?: undefined | false;
  shareType?: undefined;
  mailConfig?: undefined;
}

export type PdfViewerProps = {
  visible: boolean;
  onClose: () => void;
  pdfGenerator: PdfGenerator;
  downloadable?: boolean;
  title: string | { title: string; option: string; value: LanguagesKeys }[];
  fallbackTitle?: string;
  initialLanguage?: LanguagesKeys;
  actionOption?: PdfActionOption;
  pdfOption?: Partial<PdfProps>;
  closable?: boolean;
  extra?: Record<string, string>;
  headerContainerStyle?: StyleProp<ViewStyle>;
} & (Sharable | NonSharable);

export type InternalPdfViewerProps = {
  visible: boolean;
  language: LanguagesKeys;
  setLanguage: (language: LanguagesKeys) => void;
  isLoading: boolean;
  pageData: {
    currentPage: number;
    totalPages: number;
  };
  showEmailSharing: () => void;
  handleOnScroll: (currentPage: number, totalPages: number) => void;
  modalTitle: string;
  pdf?: PdfGeneratorResult;
  onSavePdf: () => void;
  onSharePdf: () => void;
  emailSharingVisible: boolean;
  hideEmailSharing: () => void;
  attachments: Attachment[];
  emailTitle?: string;
  closable?: boolean;
  extra?: Record<string, string>;
  headerContainerStyle?: StyleProp<ViewStyle>;
} & Pick<
  PdfViewerProps,
  | 'title'
  | 'onClose'
  | 'downloadable'
  | 'actionOption'
  | 'pdfOption'
  | 'sharable'
  | 'shareType'
  | 'mailConfig'
>;

const PdfViewer = ({
  visible,
  onClose,
  pdfGenerator,
  downloadable,
  title,
  fallbackTitle = '',
  actionOption,
  initialLanguage,
  pdfOption = {},
  sharable,
  shareType,
  mailConfig,
  closable,
  extra,
  headerContainerStyle,
}: PdfViewerProps) => {
  const [language, setLanguage] = useState<LanguagesKeys>(
    initialLanguage ?? 'en',
  );
  useEffect(() => {
    if (visible) {
      setLanguage(initialLanguage ?? 'en');
    }
  }, [initialLanguage, visible]);

  const [emailSharingVisible, showEmailSharing, hideEmailSharing] = useToggle();
  const [pdf, setPdf] = useState<PdfGeneratorResult>();
  const [isLoading, setLoading] = useState(false);
  const [pageData, setPageData] = useState<{
    currentPage: number;
    totalPages: number;
  }>({
    currentPage: 0,
    totalPages: 0,
  });

  const pdfGeneratorRef = useLatest(pdfGenerator);
  const fetchPdf = useCallback(async () => {
    try {
      setLoading(true);
      const result = await pdfGeneratorRef.current(language);
      setPdf(result);
    } finally {
      setLoading(false);
    }
  }, [language, pdfGeneratorRef]);

  useEffect(() => {
    if (visible) {
      fetchPdf();
    }
  }, [fetchPdf, language, visible]);

  useEffect(() => {
    if (initialLanguage) setLanguage(initialLanguage);
  }, [initialLanguage]);

  const handleOnScroll = (currentPage: number, totalPages: number) => {
    if (pageData.currentPage !== totalPages) {
      setPageData({ currentPage, totalPages });
    }
  };

  const handleOnClose = () => {
    onClose();
    // Reset the state after closing the modal
    setPageData({ currentPage: 0, totalPages: 0 });
    setPdf(undefined);
    setLoading(false);
  };

  const modalTitle = useMemo(() => {
    return typeof title === 'string'
      ? title
      : title.find(i => i.value === language)?.title ?? fallbackTitle;
  }, [title, fallbackTitle, language]);

  const { savePdf } = useSavePdf();
  const onSavePdf = () => {
    savePdf({
      url: pdf?.url,
      base64: pdf?.base64,
      defaultName: pdf?.fileName || String(new Date().getTime()),
      showToast: true,
    });
  };

  const { sharePdf } = useSharePdf();
  const onSharePdf = () => {
    if (shareType === 'email') {
      showEmailSharing();
    } else if (shareType === 'social-media') {
      sharePdf({
        url: pdf?.url,
        base64: pdf?.base64,
        defaultName: pdf?.fileName || String(new Date().getTime()),
      });
    }
  };

  const attachments = useMemo<Attachment[]>(() => {
    return [
      {
        attachmentName: pdf?.attachmentName ?? pdf?.fileName ?? '',
        attachmentBase64: pdf?.base64,
        attachmentUrl: pdf?.url,
        selected: true,
        required: true,
      },
      ...(mailConfig?.additionalAttachments || []),
    ];
  }, [
    mailConfig?.additionalAttachments,
    pdf?.attachmentName,
    pdf?.base64,
    pdf?.fileName,
    pdf?.url,
  ]);

  const { isTabletMode } = useLayoutAdoptionCheck();
  const Component = isTabletMode ? PdfViewerTablet : PdfViewerPhone;

  return (
    <Fragment>
      <Component
        visible={visible}
        title={title}
        language={language}
        setLanguage={setLanguage}
        isLoading={isLoading}
        pageData={pageData}
        showEmailSharing={showEmailSharing}
        handleOnScroll={handleOnScroll}
        modalTitle={modalTitle}
        pdf={pdf}
        onSavePdf={onSavePdf}
        onSharePdf={onSharePdf}
        onClose={handleOnClose}
        downloadable={downloadable}
        pdfOption={pdfOption}
        actionOption={actionOption}
        sharable={sharable}
        shareType={shareType}
        mailConfig={mailConfig}
        attachments={attachments}
        emailSharingVisible={emailSharingVisible}
        hideEmailSharing={hideEmailSharing}
        closable={closable}
        extra={extra}
        headerContainerStyle={headerContainerStyle}
      />
    </Fragment>
  );
};

export default PdfViewer;
