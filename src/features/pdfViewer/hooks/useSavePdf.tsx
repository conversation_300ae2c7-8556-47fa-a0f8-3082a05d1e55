import { format } from 'date-fns';
import { useCallback, useRef } from 'react';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import * as Device from 'expo-device';
import {
  addToast,
  LoadingIndicator,
  Toast,
  Icon,
  addErrorBottomToast,
} from 'cube-ui-components';
import RootSiblings from 'react-native-root-siblings';
import { PermissionsAndroid, Platform } from 'react-native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';

type DownloadParam = {
  url?: string;
  base64?: string;
  defaultName: string;
  showToast?: boolean;
};

type CopyParam = {
  fullName: string;
  downloadedFile: string;
  isBase64: boolean;
};

const useSavePdf = () => {
  const toastInstance = useRef<RootSiblings>();
  const { colors } = useTheme();
  const { t } = useTranslation();
  const showLoadingToast = useCallback(() => {
    toastInstance.current = Toast.show(
      [
        {
          message: t('pdfDownload'),
          IconLeft: LoadingIndicator,
        },
      ],
      {
        // duration: Toast.durations.SHORT,
        type: 'success',
      },
    );
  }, []);

  const hideLoadingToast = useCallback(() => {
    Toast.hide(toastInstance.current);
  }, []);

  const downloadPdf = useCallback(
    async ({
      url,
      base64,
      defaultName = 'Brochures',
    }: DownloadParam): Promise<CopyParam> => {
      const pdfNameSuffix = url?.split('/')?.pop() || '';
      const strTimestamp = format(new Date(), 'MM-dd-yyyy-hh-mm-ss');
      let pdfName = defaultName + '_' + pdfNameSuffix;
      pdfName = pdfName?.replace('.pdf', '');
      pdfName += '_' + strTimestamp;
      pdfName += '.pdf';
      const fullName =
        (FileSystem.documentDirectory || '') + encodeURIComponent(pdfName);

      let downloadedFile = '';
      let isBase64 = false;
      if (typeof url === 'string' && url !== '') {
        downloadedFile = await downloadFile({ fileName: fullName, url });
      } else if (typeof base64 === 'string' && base64 !== '') {
        downloadedFile = base64;
        isBase64 = true;
      } else {
        throw new Error(
          'Invalid download parameter: url or base64 is required',
        );
      }

      return { fullName, downloadedFile, isBase64 };
    },
    [],
  );

  const copyPdfToPublicFolder = useCallback(
    async ({ fullName, downloadedFile, isBase64 }: CopyParam) => {
      if (Platform.OS === 'android') {
        await copyFile({
          downloadedFile,
          isBase64,
          fileName: fullName.split('/').slice(-1)[0].split('.')[0], // '/path/to/file.pdf' => 'file'
        });
      } else {
        if (isBase64) {
          await FileSystem.writeAsStringAsync(fullName, downloadedFile, {
            encoding: 'base64',
          });
        }
        await Sharing.shareAsync(fullName);
      }
    },
    [],
  );

  const savePdf = useCallback(
    async (params: DownloadParam) => {
      const showToast = params?.showToast ?? Platform.OS === 'android';
      try {
        if (showToast) showLoadingToast();

        const copyParam = await downloadPdf(params);

        if (showToast) hideLoadingToast();

        await copyPdfToPublicFolder(copyParam);

        if (showToast)
          addToast([
            {
              message: t('pdfDownloadedSuccessfully'),
              IconLeft: <Icon.Tick fill={colors.background} />,
            },
          ]);
      } catch {
        if (showToast) hideLoadingToast();

        addErrorBottomToast([
          {
            message: t('pdfFailToDownload'),
            IconLeft: <Icon.AlarmAlert fill={colors.error} />,
          },
        ]);
      }
    },
    [
      showLoadingToast,
      downloadPdf,
      hideLoadingToast,
      copyPdfToPublicFolder,
      t,
      colors.background,
      colors.error,
    ],
  );

  const pdfToBase64 = useCallback(
    async (params: Omit<DownloadParam, 'base64'>): Promise<string | null> => {
      try {
        const { downloadedFile } = await downloadPdf(params);
        return await FileSystem.readAsStringAsync(downloadedFile, {
          encoding: FileSystem.EncodingType.Base64,
        });
      } catch {
        return null;
      }
    },
    [downloadPdf, copyPdfToPublicFolder],
  );

  const pdfToLocalUri = useCallback(
    async ({ url, base64, defaultName }: DownloadParam): Promise<string> => {
      const pdfNameSuffix = url?.split('/')?.pop() || '';
      const strTimestamp = format(new Date(), 'MM-dd-yyyy-hh-mm-ss');
      let pdfName = defaultName + '_' + pdfNameSuffix;
      pdfName = pdfName?.replace('.pdf', '');
      pdfName += '_' + strTimestamp;
      pdfName += '.pdf';
      const fullName =
        (FileSystem.documentDirectory || '') + encodeURIComponent(pdfName);

      if (typeof url === 'string' && url !== '') {
        // Download and return local URI
        return await downloadFile({ fileName: fullName, url });
      } else if (typeof base64 === 'string' && base64 !== '') {
        // Save base64 to file and return local URI
        await FileSystem.writeAsStringAsync(fullName, base64, {
          encoding: 'base64',
        });
        return fullName;
      } else {
        throw new Error(
          'Invalid download parameter: url or base64 is required',
        );
      }
    },
    [],
  );

  return { savePdf, pdfToBase64, pdfToLocalUri };
};

const copyFile = async ({
  downloadedFile,
  fileName,
  isBase64,
}: {
  downloadedFile: string;
  fileName: string;
  isBase64: boolean;
}) => {
  const requestPermission = async () => {
    let premisstionType = 'WRITE_EXTERNAL_STORAGE';
    try {
      const osVersion = +(Device.osVersion || '').split('.')[0];
      if (osVersion >= 13) {
        premisstionType = 'READ_MEDIA_IMAGES';
      }
      const requestResult = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS[premisstionType],
      );

      if (requestResult !== 'granted') return null;
      const result =
        await FileSystem.StorageAccessFramework.requestDirectoryPermissionsAsync(
          'content://com.android.externalstorage.documents/tree/primary%3ADownload',
        );
      return result;
    } catch (error) {
      return null;
    }
  };

  const toBase64 = async (uri: string) => {
    try {
      return await FileSystem.readAsStringAsync(uri, { encoding: 'base64' });
    } catch (error) {
      return '';
    }
  };

  if (Platform.OS !== 'android')
    throw new Error(`Unsupported platform: ${Platform.OS}`);

  const requestResult = await requestPermission();

  if (!requestResult || !requestResult.granted)
    throw new Error('Permission not granted');

  const uri = await FileSystem.StorageAccessFramework.createFileAsync(
    requestResult.directoryUri,
    fileName,
    'application/pdf',
  );
  let base64File = '';
  if (!isBase64) {
    base64File = await toBase64(downloadedFile);
  } else {
    base64File = downloadedFile;
  }

  await FileSystem.StorageAccessFramework.writeAsStringAsync(uri, base64File, {
    encoding: 'base64',
  });
};

const downloadFile = async ({
  fileName,
  url,
}: {
  fileName: string;
  url: string;
}) => {
  const directoryInfo = await FileSystem.getInfoAsync(fileName);
  if (!directoryInfo.exists) {
    await FileSystem.makeDirectoryAsync(fileName, {
      intermediates: true,
    });
  }
  const { uri } = await FileSystem.downloadAsync(url, fileName);
  return uri;
};

export default useSavePdf;
