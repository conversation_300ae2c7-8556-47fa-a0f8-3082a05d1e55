import {
  <PERSON>,
  H6,
  <PERSON>um<PERSON>,
  <PERSON>iew,
  H7,
  LargeBody,
  Body,
  H8,
  Box,
} from 'cube-ui-components';
import { View } from 'react-native';

import { useTheme } from '@emotion/react';
import { formatDate } from 'features/eRecruit/ib/phone/components/utils/FormatDate';
import NewDocForFormIcon from 'features/eRecruit/ib/tablet/asset/NewDocForFormIcon';
import { useTranslation } from 'react-i18next';
import useBoundStore from 'hooks/useBoundStore';
import React, { useMemo } from 'react';
import { getQuestionsMap } from 'features/eRecruit/ib/tablet/NewApplicationForm/DeclarationOfCOI';
import { country } from 'utils/context';
import { ConflictsOfInterestSection } from 'types/eRecruit';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { InfoField } from '../../ApplicationCheck/ReviewInformationDetails/components/InfoField';
export type CoflictOfInterestProps =
  | ConflictsOfInterestSection['conflictOfInterest']
  | undefined;

export function DeclarationCOTInfo({ dCOT }: { dCOT: CoflictOfInterestProps }) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();
  const currentLanguage = useBoundStore(state => state.language);

  const questionsMap = useMemo(
    () => getQuestionsMap(country, currentLanguage),
    [currentLanguage],
  );

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const Question = isTabletMode ? H7 : H8;
  const Text = isTabletMode ? LargeBody : H8;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <NewDocForFormIcon />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.declarationOfCOI')}
        </Title>
      </Row>
      <Column gap={isTabletMode ? space[5] : space[4]}>
        <View>
          <Question
            style={{ paddingBottom: isTabletMode ? space[3] : space[2] }}
            fontWeight="bold">
            Q1 - {questionsMap['ownershipInterest'].title}
          </Question>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }
            style={{ paddingBottom: isTabletMode ? space[1] : space[2] }}>
            {questionsMap['ownershipInterest'].qBody}
          </Text>
          <Text>
            {dCOT?.ownershipInterest
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
          <OwnershipInterestsInfo dCOT={dCOT} />
        </View>
        <View>
          <Question
            style={{ paddingBottom: isTabletMode ? space[3] : space[2] }}
            fontWeight="bold">
            Q2 - {questionsMap['externalEmployment'].title}
          </Question>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }
            style={{ paddingBottom: isTabletMode ? space[1] : space[2] }}>
            {questionsMap['externalEmployment'].qBody}
          </Text>
          <Text>
            {dCOT?.externalEmployment
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
          <ExternalEmploymentsInfo dCOT={dCOT} />
        </View>
        <View>
          <Question
            style={{ paddingBottom: isTabletMode ? space[3] : space[2] }}
            fontWeight="bold">
            Q3 - {questionsMap['businessAffiliationInterest'].title}
          </Question>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }
            style={{ paddingBottom: isTabletMode ? space[1] : space[2] }}>
            {questionsMap['businessAffiliationInterest'].qBody}
          </Text>
          <Text>
            {dCOT?.businessAffiliationInterest
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
          <BusinessAffiliationInterestsInfo dCOT={dCOT} />
        </View>
        <View>
          <Question
            style={{ paddingBottom: isTabletMode ? space[3] : space[2] }}
            fontWeight="bold">
            Q4 - {questionsMap['relationshipGovernmentOfficial'].title}
          </Question>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }
            style={{ paddingBottom: isTabletMode ? space[1] : space[2] }}>
            {questionsMap['relationshipGovernmentOfficial'].qBody}
          </Text>
          <Text>
            {dCOT?.relationshipGovernmentOfficial
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
          <RelationshipGovernmentOfficialsInfo dCOT={dCOT} />
        </View>
        <View>
          <Question
            style={{ paddingBottom: isTabletMode ? space[3] : space[2] }}
            fontWeight="bold">
            Q5 - {questionsMap['otherInterest'].title}
          </Question>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }
            style={{ paddingBottom: isTabletMode ? space[1] : space[2] }}>
            {questionsMap['otherInterest'].qBody}
          </Text>
          <Text>
            {dCOT?.otherInterest
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
          <OtherInterestsInfo dCOT={dCOT} />
        </View>
      </Column>
    </View>
  );
}

const OwnershipInterestsInfo = ({ dCOT }: { dCOT: CoflictOfInterestProps }) => {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const ownershipInterests = dCOT?.ownershipInterests ?? [];
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Title = isTabletMode ? H7 : H8;

  const InfoFieldContainer = isTabletMode ? XView : React.Fragment;

  return ownershipInterests.map((ownershipInterest: any, index: number) => {
    return (
      <View
        key={index}
        style={{
          paddingTop: isTabletMode ? space[2] : space[4],
        }}>
        <Title
          style={{ paddingBottom: isTabletMode ? 5 : space[2] }}
          fontWeight="bold">
          {t('application.COI.record', {
            number: index + 1,
          })}
        </Title>
        <Box gap={space[2]}>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
              data={ownershipInterest.nameOfBusiness}
            />
            <InfoField
              label={t('application.COI.natureOfBusiness')}
              data={ownershipInterest.natureOfBusiness}
            />
          </InfoFieldContainer>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.nameOfOwnerAndRelationship')}
              data={ownershipInterest.nameOfOwner}
            />
            <InfoField
              label={t('application.COI.percentageOfOwnership')}
              data={ownershipInterest.percentageOfOwnership + '%'}
            />
          </InfoFieldContainer>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.dateAcquired')}
              data={formatDate(ownershipInterest.dateAcquired)}
            />
            {isTabletMode && <InfoField label={''} data={''} />}
          </InfoFieldContainer>
        </Box>
      </View>
    );
  });
};

const ExternalEmploymentsInfo = ({
  dCOT,
}: {
  dCOT: CoflictOfInterestProps;
}) => {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const externalEmployments = dCOT?.externalEmployments ?? [];
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H7 : H8;

  const InfoFieldContainer = isTabletMode ? XView : React.Fragment;

  return externalEmployments.map((externalEmployment: any, index: number) => {
    return (
      <View key={index} style={{ paddingTop: space[2], gap: space[2] }}>
        <Title
          style={{ paddingBottom: isTabletMode ? 5 : space[2] }}
          fontWeight="bold">
          {t('application.COI.record', {
            number: index + 1,
          })}
        </Title>
        <InfoFieldContainer>
          <InfoField
            label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
            data={externalEmployment.nameOfBusiness}
          />
          <InfoField
            label={t('application.COI.natureOfBusiness')}
            data={externalEmployment.natureOfBusiness}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('application.COI.position')}
            data={externalEmployment.position}
          />
          <InfoField
            label={t('application.COI.details')}
            data={externalEmployment.details}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('application.COI.compensationReceived')}
            data={
              externalEmployment.compensationReceived
                ? t('eRecruit.application.personalDetails.question.yes')
                : t('eRecruit.application.personalDetails.question.no')
            }
          />
          {isTabletMode && <InfoField label={''} data={''} />}
        </InfoFieldContainer>
      </View>
    );
  });
};

const BusinessAffiliationInterestsInfo = ({
  dCOT,
}: {
  dCOT: CoflictOfInterestProps;
}) => {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const businessAffiliationInterests = dCOT?.businessAffiliationInterests ?? [];
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H7 : H8;

  const InfoFieldContainer = isTabletMode ? XView : React.Fragment;

  return businessAffiliationInterests.map(
    (businessAffiliationInterest: any, index: number) => {
      return (
        <View key={index} style={{ paddingTop: space[2], gap: space[2] }}>
          <Title
            style={{ paddingBottom: isTabletMode ? 5 : space[2] }}
            fontWeight="bold">
            {t('application.COI.record', {
              number: index + 1,
            })}
          </Title>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.nameOfBusinessEnterpriseOrEntity')}
              data={businessAffiliationInterest.nameOfBusiness}
            />
            <InfoField
              label={t('application.COI.natureOfBusiness')}
              data={businessAffiliationInterest.natureOfBusiness}
            />
          </InfoFieldContainer>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.nameOfFamilyMemberAndRelationship')}
              data={businessAffiliationInterest.nameOfFamilyMember}
            />
            <InfoField
              label={t('application.COI.positionDepartment')}
              data={businessAffiliationInterest.positionDepartment}
            />
          </InfoFieldContainer>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.dateCommencementEmployment')}
              data={formatDate(
                businessAffiliationInterest.dateCommencementEmployment,
              )}
            />
            {isTabletMode && <InfoField label={''} data={''} />}
          </InfoFieldContainer>
        </View>
      );
    },
  );
};

const RelationshipGovernmentOfficialsInfo = ({
  dCOT,
}: {
  dCOT: CoflictOfInterestProps;
}) => {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const relationshipGovernmentOfficials =
    dCOT?.relationshipGovernmentOfficials ?? [];
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H7 : H8;

  const InfoFieldContainer = isTabletMode ? XView : React.Fragment;

  return relationshipGovernmentOfficials.map(
    (relationshipGovernmentOfficial: any, index: number) => {
      return (
        <View key={index} style={{ paddingTop: space[2], gap: space[2] }}>
          <Title
            style={{ paddingBottom: isTabletMode ? 5 : space[2] }}
            fontWeight="bold">
            {t('application.COI.record', {
              number: index + 1,
            })}
          </Title>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.nameOfGovernment')}
              data={relationshipGovernmentOfficial.nameOfGovernment}
            />
            <InfoField
              label={t('application.COI.positionDepartment')}
              data={relationshipGovernmentOfficial.positionDepartment}
            />
          </InfoFieldContainer>
          <InfoFieldContainer>
            <InfoField
              label={t('application.COI.relationshipWithGovOfficials')}
              data={relationshipGovernmentOfficial.relationship}
            />
            {isTabletMode && <InfoField label={''} data={''} />}
          </InfoFieldContainer>
        </View>
      );
    },
  );
};

const OtherInterestsInfo = ({ dCOT }: { dCOT: CoflictOfInterestProps }) => {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const otherInterests = dCOT?.otherInterests ?? [];
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H7 : H8;

  const InfoFieldContainer = isTabletMode ? XView : React.Fragment;

  return otherInterests.map((otherInterest: any, index: number) => {
    return (
      <View key={index} style={{ paddingTop: space[2], gap: space[2] }}>
        <Title
          style={{ paddingBottom: isTabletMode ? 5 : space[2] }}
          fontWeight="bold">
          {t('application.COI.record', {
            number: index + 1,
          })}
        </Title>
        <InfoFieldContainer>
          <InfoField
            label={t('application.COI.details')}
            data={otherInterest.details}
          />
          {isTabletMode && <InfoField label={''} data={''} />}
        </InfoFieldContainer>
      </View>
    );
  });
};
