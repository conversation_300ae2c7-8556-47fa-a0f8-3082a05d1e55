import { useTheme } from '@emotion/react';
import { IdentityDetails } from '../../ApplicationCheck/ReviewInformationDetails/IdentityDetails';
import { ContactDetails } from '../../ApplicationCheck/ReviewInformationDetails/ContactDetails';
import { useTranslation } from 'react-i18next';
import { useGetERecruitConfig } from 'features/eRecruit/hooks/useGetERecruitConfig';
import { Box, Typography } from 'cube-ui-components';
import { AddressInfo } from '../../ApplicationCheck/ReviewInformationDetails/AddressInfo';
import { BankInfo } from '../../ApplicationCheck/ReviewInformationDetails/BankInfo';
import { SupervisorAndCandidateInfo } from '../../ApplicationCheck/ReviewInformationDetails/SupervisorCandidateInfo';
import styled from '@emotion/native';
import { View } from 'react-native';
import { useGetReviewAgentsApplicationDetails } from 'features/eRecruit/hooks/useGetReviewAgentsApplicationDetails';
import {
  CoflictOfInterestProps,
  DeclarationCOTInfo,
} from './DeclarationCOTInfo';
import { InsuranceExperience } from '../../ApplicationCheck/ReviewInformationDetails/InsuranceExperience';
import { EmergencyContact } from '../../ApplicationCheck/ReviewInformationDetails/EmergencyContact';
import { FinancialCondition } from '../../ApplicationCheck/ReviewInformationDetails/FinancialCondition';
import { ReputationRecords } from '../../ApplicationCheck/ReviewInformationDetails/ReputationRecords';
import useBoundStore from 'hooks/useBoundStore';

import { ApplicationFormResponds } from 'types/eRecruit';
import { convertSavedApplicationForHookForm } from 'features/eRecruit/id/utils/otherDetailsFuntions';
import Remark from '../../ApplicationCheck/ReviewInformationDetails/Remark';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export function ReviewInformation({
  applicationId,
}: {
  applicationId: number | null;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { data: applicationData } = useGetReviewAgentsApplicationDetails(
    applicationId ? applicationId : 0,
  ) as { data: ApplicationFormResponds };
  const { data: configList } = useGetERecruitConfig();
  const identity = applicationData?.identity;
  const spouseInformation = applicationData?.spouseInformation;
  const contact = applicationData?.contact;
  const qualifications = applicationData?.qualifications;
  const workingExperiences = applicationData?.workingExperiences;
  const position = applicationData?.position;
  const comments = applicationData?.approvalComments;
  const regulatorys = applicationData?.regulatorys;
  const approvalComments = applicationData?.approvalComments;

  const agentCode = useBoundStore(state => state.auth.agentCode);

  const cityList = configList?.cityList || [];
  const convertedApplicationData = applicationData
    ? convertSavedApplicationForHookForm({
        parsedObj: applicationData,
        cityList,
        currentAgentCode: agentCode,
      })
    : undefined;

  const remark = approvalComments?.[0]?.comment;

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? Typography.H7 : Typography.H8;
  return (
    <>
      <GreenWhiteCard>
        <GreenHeader>
          <Title fontWeight="bold" color={colors.background}>
            {t('eRecruit.progressBar.personalDetails')}
          </Title>
        </GreenHeader>
        <WhiteContent isTabletMode={isTabletMode}>
          <IdentityDetails
            identity={identity}
            spouseInformation={spouseInformation}
            qualifications={qualifications}
            workingExperiences={workingExperiences}
            configList={configList}
          />
          <ContactDetails contact={contact} />
          <InsuranceExperience workingExperiences={workingExperiences} />
        </WhiteContent>
      </GreenWhiteCard>
      <Box h={space[4]} />
      <GreenWhiteCard>
        <GreenHeader>
          <Typography.H7 fontWeight="bold" color={colors.background}>
            {t('eRecruit.progressBar.otherDetails')}
          </Typography.H7>
        </GreenHeader>
        <WhiteContent isTabletMode={isTabletMode}>
          <AddressInfo contact={contact} configList={configList} />
          <BankInfo contact={contact} configList={configList} />
          <SupervisorAndCandidateInfo
            position={position}
            comments={comments}
            configList={configList}
          />
          <EmergencyContact contact={contact} configList={configList} />
          <FinancialCondition
            regulatorys={regulatorys}
            configList={configList}
          />
          <ReputationRecords
            regulatorys={regulatorys}
            configList={configList}
          />
          <DeclarationCOTInfo
            dCOT={
              convertedApplicationData?.conflictOfInterest as CoflictOfInterestProps
            }
          />
          {typeof remark === 'string' && remark.length > 0 && (
            <>
              <Remark remark={remark} />
            </>
          )}
        </WhiteContent>
      </GreenWhiteCard>
    </>
  );
}

export const GreenWhiteCard = styled(View)(({}) => ({
  gap: 0,
  width: '100%',
}));

export const GreenHeader = styled(View)(({ theme: { colors, space } }) => ({
  backgroundColor: colors.palette.fwdDarkGreen[100],
  paddingHorizontal: space[6],
  paddingVertical: space[3],
  borderTopRightRadius: space[4],
  borderTopLeftRadius: space[4],
}));

export const WhiteContent = styled(View)<{ isTabletMode?: boolean }>(
  ({ theme: { colors, space }, isTabletMode = true }) => ({
    backgroundColor: colors.background,
    paddingHorizontal: isTabletMode ? space[6] : space[4],
    paddingTop: isTabletMode ? space[5] : space[3],
    paddingBottom: isTabletMode ? space[5] : space[4],
    borderBottomRightRadius: space[4],
    borderBottomLeftRadius: space[4],
    gap: isTabletMode ? space[8] : space[4],
  }),
);
