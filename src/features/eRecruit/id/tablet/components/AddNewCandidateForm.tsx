import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import {
  Box,
  Button,
  Dropdown,
  H6,
  <PERSON><PERSON>,
  Picker,
  Row,
  TextField,
  addToast,
} from 'cube-ui-components';
import React, { useMemo } from 'react';
import {
  SubmitErrorHandler,
  SubmitHandler,
  useForm,
  UseFormHandleSubmit,
} from 'react-hook-form';
import { country } from 'utils/context';
import { useValidationYupResolver } from 'utils/validation';

import { DATA_COUNTRY_CODE } from 'features/lead/components/AddLeadForm/countryCodeList';
import { useTranslation } from 'react-i18next';
import { CountryCode } from 'types/optionList';
import { usePostNewCandidate } from 'features/eRecruit/hooks/usePostNewCandidate';
import { GenderKeys, NewCandidateRequestBody } from 'types/eRecruit';
import { RECRUIT_ENDPOINT } from 'api/eRecruitApi';
import {
  addNewCandidateSchema,
  initialCandidateData,
  NewCandidateFormValues,
} from 'features/eRecruit/id/validations/addNewCandidateSchema';
import PhoneField from 'components/PhoneField';
import { defaultCountryCode } from 'features/eRecruit/config';

type AddNewCandidateFormProps = {
  onClose: () => void;
};

const BUTTON_WIDTH = 200;

export default function AddNewCandidateForm({
  onClose,
}: AddNewCandidateFormProps) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const candidate = 'eRecruit.candidate';

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { errors, isValid },
  } = useForm<NewCandidateFormValues>({
    mode: 'onChange',
    defaultValues: {
      ...initialCandidateData,
      countryCode: defaultCountryCode,
    },
    resolver: useValidationYupResolver(addNewCandidateSchema),
  });

  const { mutateAsync, isLoading } = usePostNewCandidate();
  const queryClient = useQueryClient();

  const onValidSubmit = (values: NewCandidateFormValues) => {
    const newCandidate: NewCandidateRequestBody = {
      applicationId: null,
      stage: 'NEW_APPLICATION',
      identity: {
        fullname: values.fullName as string,
        gender: values.gender as GenderKeys,
      },
      contact: {
        email: values.emailAddress,
        phones: [
          {
            countryCode: values.countryCode,
            number: values.phoneNumber,
            type: 'MOBILE',
          },
        ],
      },
    };

    mutateAsync(newCandidate, {
      onSuccess: () => {
        cancelHandler();
        setTimeout(
          () => {
            (country === 'ib' || country === 'id') &&
              addToast([
                {
                  IconLeft: Icon.Tick,
                  message: 'Candidate is added.',
                },
              ]);
          },
          country === 'id' ? 1000 : 0,
        );
        queryClient.invalidateQueries({
          queryKey: [RECRUIT_ENDPOINT],
        });
      },
      onError: error => {
        if (country === 'id') {
          addToast([
            {
              message:
                error instanceof Error
                  ? error.message
                  : t('eRecruit.pleaseTryAgainLater'),
            },
          ]);
        }
      },
    });
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    console.log(
      '🚀 ~ file: AddNewCandidate ~ AddNewCandidateForm ~ error:',
      error,
    );
  };

  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;

  const cancelHandler = () => {
    onClose();
    reset();
  };

  const countryCodeOptions = useMemo(
    () =>
      DATA_COUNTRY_CODE.map(({ label, value }) => ({
        label,
        value,
      })),
    [],
  );
  const hasErrors = Object.keys(errors).length > 0;

  return (
    <>
      <Row paddingBottom={space[5]}>
        <H6 fontWeight="bold">{t(`${candidate}.addNewCandidate`)}</H6>
      </Row>

      <Row paddingBottom={space[4]} gap={space[5]}>
        <Input
          control={control}
          as={TextField}
          name="fullName"
          style={{ flex: 1 }}
          label={t(`${candidate}.fullName`)}
          error={errors?.fullName?.message}
        />
        <Input
          control={control}
          as={Picker}
          name="gender"
          type="text"
          label={t(`${candidate}.gender`)}
          style={{ flex: 1 }}
          error={errors?.gender?.message}
          items={[
            {
              value: 'M',
              text: t(`${candidate}.male`),
            },
            {
              value: 'F',
              text: t(`${candidate}.female`),
            },
          ]}
        />
      </Row>

      <Row paddingTop={space[2]} paddingBottom={space[6]} gap={space[5]}>
        <Row flex={1} gap={space[3]}>
          <Input
            control={control}
            as={Dropdown<CountryCode, string>}
            name="countryCode"
            label={t(`${candidate}.code`)}
            modalTitle={'Country code'}
            data={countryCodeOptions}
            style={{ flex: 0.5 }}
            getItemValue={item => item.value}
            getItemLabel={item => item.label}
            getDisplayedLabel={item => getCountryCodeValue(item)}
            keyExtractor={item => item.value + item.label}
            disabled
          />

          <Input
            control={control}
            as={PhoneField}
            name="phoneNumber"
            label={t(`${candidate}.phoneNumber`)}
            style={{ flex: 1 }}
            keyboardType="numeric"
            error={errors?.phoneNumber?.message}
            size="large"
          />
        </Row>
        <Input
          autoCapitalize="none"
          control={control}
          as={TextField}
          name="emailAddress"
          label={t(`${candidate}.email`)}
          style={{ flex: 1 }}
          error={errors?.emailAddress?.message}
        />
      </Row>

      {/* <Row paddingTop={space[2]} paddingBottom={space[6]} gap={space[5]}>
        <Input
          control={control}
          as={TextField}
          name="emailAddress"
          label={t(`${candidate}.email`)}
          style={{ flex: 1 }}
          error={errors?.emailAddress?.message}
        />
        <Box flex={1} />
      </Row> */}

      <Row gap={space[6]} justifyContent="center" alignItems="center">
        <Button
          text={t(`${candidate}.cancel`)}
          variant="secondary"
          onPress={cancelHandler}
          style={{ width: BUTTON_WIDTH }}
        />
        <Button
          text={t(`${candidate}.save`)}
          disabled={!isValid || isLoading || hasErrors}
          variant="primary"
          style={{ width: BUTTON_WIDTH }}
          loading={isLoading}
          onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
        />
      </Row>
    </>
  );
}

const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
