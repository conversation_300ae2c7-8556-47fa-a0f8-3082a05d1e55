// import { useRoute } from '@react-navigation/native';
import { useTheme } from '@emotion/react';
// import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen.tablet';
import styled from '@emotion/native';
import { View } from 'react-native';
import {
  H8,
  H7,
  H6,
  Row,
  Icon,
  LargeBody,
  Column,
  Box,
  Typography,
  Body,
} from 'cube-ui-components';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTranslation } from 'react-i18next';
import { CandidateProfileResponds, RejectedAgentDetails } from 'types/eRecruit';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CandidateStatusProgressBar from 'features/eRecruit/components/CandidateProfile/CandidateStatusProgressBar';
import { useMemo } from 'react';
import { t } from 'i18next';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentInfoByAgentId } from 'hooks/useGetAgentProfile';
type Props = Partial<CandidateProfileResponds>;

export default function CandidateApplicationStatus(props: Props) {
  const { t } = useTranslation('eRecruit');
  const { top } = useSafeAreaInsets();
  const { colors, space } = useTheme();
  const agentCode = useBoundStore(state => state.auth.agentCode);

  const remarkMatch =
    props?.rejectedReason && props?.rejectedReason.match(/Remark:(.*)/);

  const remark = remarkMatch ? remarkMatch[1] : null;

  const approvedRemarkMatch =
    props?.approveReason && props?.approveReason.match(/Remark:(.*)/);

  const approvedRemark = approvedRemarkMatch ? approvedRemarkMatch[1] : null;

  const applicationSubmittedLabel = useMemo(() => {
    if (props?.submissionDate) {
      return t(`eRecruit.candidateProfile.applicationSubmitted`);
    }
    return t(`eRecruit.candidateProfile.applicationInProgress`);
  }, [props?.submissionDate, t]);

  const candidateSignatureSubmittedLabel = useMemo(() => {
    if (props?.signatureDate) {
      return t(`eRecruit.candidateProfile.candidateSignatureSubmitted`);
    }
    if (props?.cubeStatus === 'REMOTE_SIGNATURE') {
      return t(`eRecruit.candidateProfile.awaitingCandidateSignature`);
    }

    return t(`eRecruit.candidateProfile.candidateSignature`);
  }, [props?.signatureDate, props?.cubeStatus, t]);

  const progressHandledResult = progressHandler({
    cubeStatus: props?.cubeStatus ?? null,
    licenseActivatedDate: props?.licenseActivatedDate ?? null,
    codeIssuedDate: props?.codeIssuedDate ?? null,
    rejectedDate: props?.rejectedDate ?? null,
  });

  const filteredApproveComments = props?.comments?.filter(
    comment =>
      comment.action === 'APPROVE' && comment.approverAgentCode !== agentCode,
  );
  const filteredRejectComments = props?.comments?.filter(
    comment =>
      comment.action === 'REJECT' && comment.approverAgentCode !== agentCode,
  );

  const lastApproveComment = lastCommentHandler(filteredApproveComments);
  const lastRejectComment = lastCommentHandler(filteredRejectComments);

  const { data: agentLeftLastApproveComment } = useGetAgentInfoByAgentId(
    lastApproveComment?.approverAgentCode,
  );
  const { data: agentLeftLastRejectComment } = useGetAgentInfoByAgentId(
    lastRejectComment?.approverAgentCode,
  );

  return (
    <InfoContainer style={{ paddingTop: top + space[5] }}>
      <H6 fontWeight="bold">
        {t(`eRecruit.candidateProfile.candidateStatus`)}
      </H6>

      <InfoRowCard>
        <Row gap={space[6]}>
          <Column gap={space[3]} flex={395}>
            {/* ---- Visited */}
            <InfoRowStyle>
              <Row gap={space[2]}>
                <RenderTickCircle checked={Boolean(props?.createdDate)} />
                <H7>{t(`eRecruit.candidateProfile.visited`)}</H7>
              </Row>
              <DateStyle>
                {props?.createdDate ? dateFormatUtil(props?.createdDate) : ''}
              </DateStyle>
            </InfoRowStyle>

            {/* ---- Application Completed */}
            <InfoRowStyle>
              <Row gap={space[2]}>
                <RenderTickCircle checked={Boolean(props?.submissionDate)} />
                <H7>{applicationSubmittedLabel}</H7>
              </Row>
              <DateStyle>
                {props?.submissionDate
                  ? dateFormatUtil(props?.submissionDate)
                  : ''}
              </DateStyle>
            </InfoRowStyle>

            {/* ---- Candidate Signature  Submitted*/}
            <InfoRowStyle>
              <Row style={{ gap: space[2] }}>
                <RenderTickCircle checked={Boolean(props?.signatureDate)} />
                <H7>{candidateSignatureSubmittedLabel}</H7>
              </Row>
              <DateStyle>
                {props?.signatureDate
                  ? dateFormatUtil(props?.signatureDate)
                  : ''}
              </DateStyle>
            </InfoRowStyle>

            {/* --- Leader Approved or Rejected */}
            <LeaderApprovalStatus
              approvedDate={props?.approvedDate}
              rejectedDate={props?.rejectedDate}
            />
          </Column>

          <Column flex={313}>
            <Box paddingY={6}>
              <CandidateStatusProgressBar
                isDeclined={props?.cubeStatus === 'REJECTED'}
                progress={progressHandledResult.progress}
                height={44}
                isDone={!!props?.licenseActivatedDate}
                label={progressHandledResult.text}
              />
            </Box>
            <Box flex={1} alignItems="center" paddingTop={space[2]}>
              <Typography.Body style={{ textAlign: 'center' }}>
                {progressHandledResult.label}
              </Typography.Body>
            </Box>
          </Column>
        </Row>
        <DynamicStatusAndContentSection
          // For review comments
          rejectedReason={lastRejectComment?.comment ?? props?.rejectedReason}
          rejectedAgent={{
            agentName: agentLeftLastRejectComment?.person?.fullName ?? '--',
            // agentLeftLastRejectComment?.person?.fullName ??
            // props?.rejectedAgent?.agentName,
            agentCode:
              lastRejectComment?.approverAgentCode ??
              props?.rejectedAgent?.agentCode,
          }}
          rejectedDate={props?.rejectedDate}
          approveReason={lastApproveComment?.comment ?? props?.approveReason}
          approvedAgent={{
            agentName: agentLeftLastApproveComment?.person?.fullName ?? '--',
            agentCode:
              lastApproveComment?.approverAgentCode ??
              props?.approvedAgent?.agentCode,
          }}
          approvedDate={props?.approvedDate}
          // ---------------------------------
          // For license info, and agent code
          codeIssuedDate={props?.codeIssuedDate}
          agentCode={props?.agentCode}
          licenseActivatedDate={props?.licenseActivatedDate}
          rejectedRemark={remark}
          approvedRemark={approvedRemark}
        />
      </InfoRowCard>
    </InfoContainer>
  );
}

const InfoContainer = styled(View)(({ theme: { sizes } }) => ({
  padding: sizes[5],
  gap: sizes[5],
}));

const InfoRowCard = styled(Column)(
  ({ theme: { sizes, colors, space, getElevation } }) => ({
    ...getElevation(5),
    backgroundColor: colors.background,
    padding: sizes[5],
    gap: sizes[3],
    borderRadius: space[4],
  }),
);

const DateStyle = styled(Body)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
}));

const InfoRowStyle = styled(Row)(() => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const ReviewContainer = styled(Column)(
  ({ theme: { borderRadius, colors, space } }) => ({
    backgroundColor: colors.palette.fwdOrange[5],
    borderRadius: borderRadius.large,
    padding: space[3],
    marginVertical: space[1],
  }),
);

const progressHandler = ({
  cubeStatus,
  licenseActivatedDate,
  codeIssuedDate,
  rejectedDate,
}: {
  cubeStatus: CandidateProfileResponds['cubeStatus'];
  licenseActivatedDate: CandidateProfileResponds['licenseActivatedDate'];
  codeIssuedDate: CandidateProfileResponds['codeIssuedDate'];
  rejectedDate: CandidateProfileResponds['rejectedDate'];
}) => {
  switch (cubeStatus) {
    case 'POTENTIAL_CANDIDATE':
      return {
        progress: 14,
        label: t(`eRecruit:eRecruit.candidateProfile.progressBar.await`),
      };
    case 'RESUME_APPLICATION':
      return {
        progress: 14,
        label: t(`eRecruit:eRecruit.candidateProfile.progressBar.await`),
      };
    case 'REMOTE_SIGNATURE':
      return {
        progress: 28,
        label: t(
          `eRecruit:eRecruit.candidateProfile.progressBar.pendingSignature`,
        ),
      };
    case 'PENDING_LEADER_APPROVAL':
      return {
        progress: 42,
        label: t(
          `eRecruit:eRecruit.candidateProfile.progressBar.pendingApproval`,
        ),
      };
    case 'APPROVED': {
      //LICENSE_ACTIVATED
      if (licenseActivatedDate) {
        return {
          progress: 100,
          label: t(`eRecruit:eRecruit.candidateProfile.progressBar.readyToGo`),
          text: t(
            `eRecruit:eRecruit.candidateProfile.progressBar.activeAccount`,
          ),
        };
      }
      //CODE_ISSUED
      if (codeIssuedDate) {
        return {
          progress: 79,
          label: t(
            `eRecruit:eRecruit.candidateProfile.progressBar.licensingCompletion`,
          ),
          text: t(
            `eRecruit:eRecruit.candidateProfile.progressBar.inactiveAccount`,
          ),
        };
      }

      //APPROVED
      return {
        progress: 57,
        label: t(
          `eRecruit:eRecruit.candidateProfile.progressBar.licensingCompletion`,
        ),
        text: t(
          `eRecruit:eRecruit.candidateProfile.progressBar.inactiveAccount`,
        ),
      };
    }
    case 'REJECTED':
      return {
        progress: 0,
        label: t(`eRecruit:eRecruit.candidateProfile.autoDeclinedOn`, {
          date: rejectedDate ? dateFormatUtil(rejectedDate) : '',
        }),
      };
    default:
      return { progress: 0, label: '' };
  }
};

function RenderTickCircle({ checked }: { checked: boolean }) {
  const { colors, space } = useTheme();

  return checked ? (
    <Icon.TickCircle fill={colors.palette.alertGreen} />
  ) : (
    <Icon.TickCircle fill={colors.palette.fwdGrey[100]} />
  );
}

function LeaderApprovalStatus(props: {
  rejectedDate?: string | null;
  approvedDate?: string | null;
}) {
  const { colors, space } = useTheme();

  const getStatusDetails = () => {
    if (props?.rejectedDate) {
      return {
        icon: <Icon.CloseCircle fill={colors.palette.alertRed} />,
        textKey: t('eRecruit:eRecruit.candidateProfile.leaderRejected'),
        date: props.rejectedDate,
      };
    }
    if (props?.approvedDate) {
      return {
        icon: <Icon.TickCircle fill={colors.palette.alertGreen} />,
        textKey: t('eRecruit:eRecruit.candidateProfile.leaderApproved'),
        date: props.approvedDate,
      };
    }
    // Default: Pending approval
    return {
      icon: <Icon.TickCircle fill={colors.palette.fwdGrey[100]} />,
      textKey: t('eRecruit:eRecruit.candidateProfile.leaderApproval'),
      date: null,
    };
  };

  const { icon, textKey, date } = getStatusDetails();

  return (
    <InfoRowStyle>
      <Row style={{ gap: space[2] }}>
        {icon}
        <H7>{textKey}</H7>
      </Row>
      <DateStyle>{date ? dateFormatUtil(date) : ''}</DateStyle>
    </InfoRowStyle>
  );
}

type Agent = {
  agentName: string | null | undefined;
  agentCode: string | null | undefined;
};
type ReviewCommentProps = {
  statusLabel: string;
  agent: Agent;
  reason: string;
};

export const ReviewComment = ({
  statusLabel,
  agent,
  reason,
}: ReviewCommentProps) => {
  const { colors, space } = useTheme();

  return (
    <ReviewContainer gap={space[4]}>
      <H8 fontWeight="bold">
        {t(`eRecruit:eRecruit.candidateProfile.reviewerComment`)}
      </H8>
      <Box gap={space[3]}>
        <Body>
          {statusLabel}:{' '}
          <Body fontWeight="medium">
            {agent.agentName} ({agent.agentCode})
          </Body>
        </Body>
        <LargeBody>{reason}</LargeBody>
      </Box>
    </ReviewContainer>
  );
};

function DynamicStatusAndContentSection(props: {
  rejectedReason: string | null | undefined;
  rejectedAgent: Agent | null | undefined;
  approveReason: string | null | undefined;
  approvedAgent: Agent | null | undefined;
  approvedDate: string | null | undefined;
  rejectedDate: string | null | undefined;
  codeIssuedDate: string | null | undefined;
  agentCode: string | null | undefined;
  licenseActivatedDate: string | null | undefined;
  rejectedRemark?: string | null | undefined;
  approvedRemark?: string | null | undefined;
}) {
  const { colors, space } = useTheme();

  const {
    rejectedReason,
    rejectedAgent,
    approveReason,
    approvedAgent,
    approvedDate,
    rejectedDate,
    codeIssuedDate,
    agentCode,
    licenseActivatedDate,
    rejectedRemark,
    approvedRemark,
  } = props;

  const isRejected = rejectedReason && rejectedAgent;
  const isApproved = approveReason && approvedAgent && !isRejected;
  const showLicenseInfo = approvedDate && !rejectedDate;

  return (
    <>
      {isRejected && (
        <ReviewComment
          statusLabel={t(`eRecruit:eRecruit.candidateProfile.rejectedBy`)}
          agent={rejectedAgent}
          reason={rejectedRemark ?? rejectedReason}
        />
      )}

      {isApproved && (
        <ReviewComment
          statusLabel={t(`eRecruit:eRecruit.candidateProfile.approvedBy`)}
          agent={approvedAgent}
          reason={approvedRemark ?? approveReason}
        />
      )}

      {showLicenseInfo && (
        <Row gap={space[6]}>
          {/* ... license info JSX remains the same ... */}
          <Column gap={space[3]} flex={395}>
            <Row justifyContent="space-between">
              <Row style={{ gap: space[2] }}>
                <RenderTickCircle checked={Boolean(codeIssuedDate)} />
                <Column gap={space[2]}>
                  <Box mt={2}>
                    <H7>
                      {t(`eRecruit:eRecruit.candidateProfile.agentCodeIssued`)}
                    </H7>
                  </Box>
                  {codeIssuedDate && (
                    <Row alignItems="center" gap={space[2]}>
                      <H7>
                        {t(`eRecruit:eRecruit.candidateProfile.agentCode`) +
                          ':'}
                      </H7>
                      {!!agentCode && (
                        <Typography.ExtraLargeBody fontWeight="medium">
                          {agentCode}
                        </Typography.ExtraLargeBody>
                      )}
                    </Row>
                  )}
                </Column>
              </Row>
              <DateStyle>
                {codeIssuedDate && dateFormatUtil(codeIssuedDate)}
              </DateStyle>
            </Row>
            <InfoRowStyle>
              <Row style={{ gap: space[2] }}>
                <RenderTickCircle checked={Boolean(licenseActivatedDate)} />
                <H7>
                  {t(`eRecruit:eRecruit.candidateProfile.licenseActivated`)}
                </H7>
              </Row>
              <DateStyle>
                {licenseActivatedDate && dateFormatUtil(licenseActivatedDate)}
              </DateStyle>
            </InfoRowStyle>
          </Column>
          <Box flex={313} />
        </Row>
      )}
    </>
  );
}

const lastCommentHandler = (comments: CandidateProfileResponds['comments']) => {
  if (!comments || comments.length === 0) {
    return null;
  }

  const lastComment = comments[comments.length - 1];
  return lastComment;
};
