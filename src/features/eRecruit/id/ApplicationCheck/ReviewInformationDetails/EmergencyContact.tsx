import {
  Row,
  PictogramIcon,
  H6,
  Column,
  XView,
  H7,
  Body,
  LargeBody,
  CubePictogramIcon,
} from 'cube-ui-components';
import { View } from 'react-native';

import { useTheme } from '@emotion/react';
import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';
import { InfoFieldContainer as TabletInfoFieldContainer } from 'features/eRecruit/components/InfoField';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { InfoField } from './components/InfoField';

type ContactProps = ApplicationFormResponds['contact'] | undefined;

export function EmergencyContact({
  contact,
  configList,
}: {
  contact: ContactProps;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, sizes, colors } = useTheme();
  const emergencyContact = contact?.emergencyContact;

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const InfoFieldContainer = isTabletMode
    ? TabletInfoFieldContainer
    : React.Fragment;

  const address = `${
    emergencyContact?.address ? `${emergencyContact?.address},` : ''
  }${
    emergencyContact?.state
      ? ` ${
          configList?.provinceList?.find(
            item => item.itemCode === emergencyContact?.state,
          )?.longDesc.en
        },`
      : ''
  }${emergencyContact?.city ? ` ${emergencyContact?.city}` : ''}${
    emergencyContact?.postCode ? ` ${emergencyContact?.postCode}` : ''
  }`;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <CubePictogramIcon.Alarm size={sizes[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.emergencyContact')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t(`eRecruit.application.personalDetails.fullName`)}
            data={
              emergencyContact?.fullName ? emergencyContact?.fullName : 'N/A'
            }
          />
          <InfoField
            label={t(`eRecruit.application.otherDetails.residenceNumber`)}
            data={
              emergencyContact?.residentNumber
                ? emergencyContact?.residentNumber
                : 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t(`eRecruit.application.personalDetails.phoneNumber`)}
            data={
              emergencyContact?.mobile
                ? `${
                    emergencyContact?.mobile.countryCode
                      ? `${emergencyContact?.mobile.countryCode} `
                      : ''
                  }${emergencyContact?.mobile.number}`
                : 'N/A'
            }
          />
          {isTabletMode && <InfoField label={''} data={''} />}
        </InfoFieldContainer>
        {isTabletMode ? (
          <Column gap={space[1]}>
            <LabelStyle>
              {t('eRecruit.application.otherDetails.addressLine')}
            </LabelStyle>
            <DataStyle>{address ? address : 'N/A'}</DataStyle>
          </Column>
        ) : (
          <InfoFieldContainer>
            <InfoField
              label={t(`eRecruit.application.otherDetails.addressLine`)}
              data={address ? address : 'N/A'}
            />
          </InfoFieldContainer>
        )}
      </Column>
    </View>
  );
}

const DataStyle = styled(LargeBody)(({ theme: { colors } }) => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const LabelStyle = styled(LargeBody)(
  ({ theme: { colors, space, borderRadius } }) => ({
    color: colors.palette.fwdGreyDarker,
    flex: 1,
  }),
);
