import { useTheme } from '@emotion/react';
import { Column, H6, H7, Pictogram<PERSON><PERSON>, Row, XView } from 'cube-ui-components';
import { InfoFieldContainer as TabletInfoFieldContainer } from 'features/eRecruit/components/InfoField';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { View } from 'react-native';
import React from 'react';
import { InfoField } from './components/InfoField';

export function ContactDetails({ contact }: { contact: any }) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const countryCode = contact?.phones.find(
    tel => tel.type === 'MOBILE',
  )?.countryCode;
  const officeCountryCode = contact?.phones.find(
    tel => tel.type === 'WORK',
  )?.countryCode;

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;
  const InfoFieldContainer = isTabletMode
    ? TabletInfoFieldContainer
    : React.Fragment;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <PictogramIcon.ActivePhoneCall size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.personalDetails.contactDetails')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t(`eRecruit.application.personalDetails.email`)}
            data={contact?.email ?? 'N/A'}
          />
          <InfoField
            label={t(`eRecruit.application.personalDetails.mobileNumber`)}
            data={
              contact?.phones.find(tel => tel.type === 'MOBILE')
                ? `${countryCode ? `${countryCode} ` : ''}${
                    contact.phones.find(tel => tel.type === 'MOBILE')?.number
                  }`
                : 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.officePhone')}
            data={
              contact?.phones.find(tel => tel.type === 'WORK')
                ? `${officeCountryCode ? `${officeCountryCode} ` : ''}${
                    contact.phones.find(tel => tel.type === 'WORK')?.number
                  }`
                : 'N/A'
            }
          />
          <InfoField label={''} data={''} />
        </InfoFieldContainer>
      </Column>
    </View>
  );
}
