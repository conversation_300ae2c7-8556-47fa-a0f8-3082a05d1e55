import { useTheme } from '@emotion/react';
import { Column, H6, H7, PictogramIcon, Row } from 'cube-ui-components';
import { ApplicationFormResponds } from 'types/eRecruit';
import { View } from 'react-native';
import { GetERecruitConfigResponse } from 'types/eRecruit';
import { InfoFieldContainer as TabletInfoFieldContainer } from 'features/eRecruit/components/InfoField';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { InfoField } from './components/InfoField';
type PositionProps = ApplicationFormResponds['position'];

export function SupervisorAndCandidateInfo({
  position,
  comments,
  configList,
}: {
  position: PositionProps | undefined;
  comments: ApplicationFormResponds['approvalComments'] | undefined;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const InfoFieldContainer = isTabletMode
    ? TabletInfoFieldContainer
    : React.Fragment;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <PictogramIcon.ManWithShield size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.candidateInformation')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t(
              'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition',
            )}
            data={
              configList?.positionList?.find(
                item => item.itemCode === position?.position,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('review.table.salesOffice')}
            data={
              configList?.salesOfficeList.find(
                item => item.itemCode === position?.salesOffice,
              )?.longDesc.en ?? 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.otherDetails.domicile')}
            data={
              configList?.domicileList.find(
                item => item.itemCode === position?.domicile,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.otherDetails.areaManager')}
            data={position?.osAreaManager ?? 'N/A'}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.otherDetails.supervisor')}
            data={
              configList?.superiorAgentCodeList.find(
                item => item.itemCode === position?.superiorAgentCode,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.otherDetails.ref')}
            data={
              configList?.refList.find(item => item.itemCode === position?.ref)
                ?.longDesc.en ?? 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.otherDetails.financingProgram')}
            data={
              position?.financingProgram
                ? t('eRecruit.application.personalDetails.question.yes')
                : t('eRecruit.application.personalDetails.question.no')
            }
          />
          {isTabletMode && <InfoField label={''} data={''} />}
        </InfoFieldContainer>
      </Column>
    </View>
  );
}
