import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Row,
  PictogramIcon,
  H6,
  Column,
  LargeBody,
  Box,
  H7,
  Body,
  Label,
} from 'cube-ui-components';
import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';
import React from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export type ContactInfo = {
  email: string;
  phones:
    | {
        countryCode: string;
        number: string;
        type: string;
      }[]
    | null[];
  address: {
    line1: string | null;
    line2: string | null;
    city: string | null;
    cityDesc: string | null;
    state: string | null;
    stateDesc: string | null;
    postCode: string | null;
  };
  businessAddress: {
    line1: string | null;
    line2: string | null;
    city: string | null;
    cityDesc: string | null;
    state: string | null;
    stateDesc: string | null;
    postCode: string | null;
  };
  bankInformation: {
    accountNumber: string | null;
    icNumber: string | null;
    bankName: string | null;
  };
};

type ContactDetailsProps = ApplicationFormResponds['contact'] | undefined;

export function AddressInfo({
  contact,
  configList,
}: {
  contact: ContactDetailsProps;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const Text = isTabletMode ? LargeBody : Label;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <PictogramIcon.House2 size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.addressInformation')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <Box gap={isTabletMode ? space[1] : space[2]}>
          <Text
            color={
              isTabletMode
                ? colors.palette.fwdGreyDarker
                : colors.palette.fwdGreyDarkest
            }>
            {t('eRecruit.application.otherDetails.residentialAddress')}
          </Text>
          <Text>
            {contact?.address.line1 ? `${contact?.address.line1},` : ''}
            {contact?.address.line2 ? ` ${contact?.address.line2},` : ''}
            {contact?.address.subDistrict
              ? ` ${contact?.address.subDistrict},`
              : ''}
            {contact?.address.district ? ` ${contact?.address.district},` : ''}
            {contact?.address.state
              ? ` ${
                  configList?.provinceList?.find(
                    item => item.itemCode === contact?.address.state,
                  )?.longDesc.en
                },`
              : ''}
            {contact?.address.city ? ` ${contact?.address.city}` : ''}
            {contact?.address.postCode ? ` ${contact?.address.postCode}` : ''}
          </Text>
        </Box>
        {contact?.businessAddress.line1 !== null && (
          <Box gap={isTabletMode ? space[1] : space[2]}>
            <Text
              color={
                isTabletMode
                  ? colors.palette.fwdGreyDarker
                  : colors.palette.fwdGreyDarkest
              }>
              {t('eRecruit.application.otherDetails.nationalIdAddress')}
            </Text>
            <Text>
              {contact?.businessAddress.line1
                ? `${contact?.businessAddress.line1},`
                : ''}
              {contact?.businessAddress.line2
                ? ` ${contact?.businessAddress.line2},`
                : ''}
              {contact?.businessAddress.subDistrict
                ? ` ${contact?.businessAddress.subDistrict},`
                : ''}
              {contact?.businessAddress.district
                ? ` ${contact?.businessAddress.district},`
                : ''}
              {contact?.businessAddress.state
                ? ` ${
                    configList?.provinceList?.find(
                      item => item.itemCode === contact?.businessAddress.state,
                    )?.longDesc.en
                  },`
                : ''}
              {contact?.businessAddress.city
                ? ` ${contact?.businessAddress.city}`
                : ''}
              {contact?.businessAddress.postCode
                ? ` ${contact?.businessAddress.postCode}`
                : ''}
            </Text>
          </Box>
        )}
      </Column>
    </View>
  );
}

const DataStyle = styled(LargeBody)(() => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const LabelStyle = styled(LargeBody)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
  flex: 1,
}));
