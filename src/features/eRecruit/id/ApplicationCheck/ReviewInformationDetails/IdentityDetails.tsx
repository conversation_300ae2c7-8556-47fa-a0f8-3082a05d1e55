import { useTheme } from '@emotion/react';
import {
  Column,
  H6,
  CubePictogramIcon,
  Row,
  XView,
  H7,
} from 'cube-ui-components';
import { View } from 'react-native';
import { dateFormatUtil } from 'utils/helper/formatUtil';

import { GetERecruitConfigResponse } from 'types/eRecruit';
import { ApplicationFormResponds } from 'types/eRecruit';
import { InfoFieldContainer as TabletInfoFieldContainer } from 'features/eRecruit/components/InfoField';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { InfoField } from './components/InfoField';

type IdentityDetailsProps = ApplicationFormResponds['identity'] | undefined;
type SpouseInformationProps =
  | ApplicationFormResponds['spouseInformation']
  | undefined;

export function IdentityDetails({
  identity,
  spouseInformation,
  qualifications,
  workingExperiences,
  configList,
}: {
  identity: IdentityDetailsProps;
  spouseInformation: SpouseInformationProps;
  qualifications: ApplicationFormResponds['qualifications'] | undefined;
  workingExperiences: ApplicationFormResponds['workingExperiences'] | undefined;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();

  const dateOfBirth = identity?.dateOfBirth ?? 'N/A';
  const formattedDate = dateFormatUtil(dateOfBirth);
  const calculateAge = (dateString: string | Date) => {
    if (dateString) {
      const dobDate = new Date(dateString);
      const today = new Date();
      let age = today.getFullYear() - dobDate.getFullYear();
      const monthDiff = today.getMonth() - dobDate.getMonth();
      if (
        monthDiff < 0 ||
        (monthDiff === 0 && today.getDate() < dobDate.getDate())
      ) {
        age--;
      }
      return age;
    }
    return 0;
  };
  const age = calculateAge(dateOfBirth);

  const firstName = identity?.fullname?.split(' ')[0] ?? 'N/A';
  const lastName = identity?.fullname?.split(' ')[1] ?? 'N/A';

  const { isTabletMode } = useLayoutAdoptionCheck();

  const InfoFieldContainer = isTabletMode
    ? TabletInfoFieldContainer
    : React.Fragment;

  const Title = isTabletMode ? H6 : H7;
  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <CubePictogramIcon.Man size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.personalDetails.identityDetails')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.firstName')}
            data={firstName}
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.lastName')}
            data={lastName}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.gender')}
            data={
              configList?.genderList.find(
                item => item.itemCode === identity?.gender,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.dateOfBirth')}
            data={`${formattedDate} ${age} y.o`}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.identity')}
            data={
              identity?.registration.find(reg => reg.type === 'NRIC')
                ?.rawType ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.icNumber')}
            data={
              identity?.registration.find(reg => reg.type === 'NRIC')?.number ??
              'N/A'
            }
          />
        </InfoFieldContainer>

        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.birthPlace')}
            data={identity?.birthPlace ?? 'N/A'}
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.religion')}
            data={
              configList?.religionList.find(
                item => item.itemCode === identity?.religion,
              )?.longDesc.en ?? 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.maritalStatus')}
            data={
              configList?.maritalList.find(
                item => item.itemCode === identity?.maritalStatus,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.numberOfDependents')}
            data={spouseInformation?.numberOfDependence ?? 'N/A'}
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.education')}
            data={qualifications?.academic?.name ?? 'N/A'}
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.industry')}
            data={
              configList?.industryList.find(
                item => item.itemCode === workingExperiences?.[0].industry,
              )?.longDesc.en ?? 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.personalDetails.presentOccupation')}
            data={
              configList?.occupationList.find(
                item =>
                  item.itemCode === workingExperiences?.[0].presentOccupation,
              )?.longDesc.en ?? 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.personalDetails.npwp')}
            data={
              identity?.registration?.find(reg => reg.type === 'INCOME_TAX')
                ?.number ?? 'N/A'
            }
          />
        </InfoFieldContainer>
      </Column>
    </View>
  );
}
