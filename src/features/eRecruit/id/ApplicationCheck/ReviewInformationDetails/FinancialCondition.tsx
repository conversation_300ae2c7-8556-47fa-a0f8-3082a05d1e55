import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  CubePictogramIcon,
  H6,
  H7,
  Label,
  LargeBody,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';

import { View } from 'react-native';

import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
  IDNRegulatorysQuestionsOnly,
} from 'types/eRecruit';
import { renderLabelByLanguage } from 'utils/helper/translation';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export function FinancialCondition({
  regulatorys,
  configList,
}: {
  regulatorys: ApplicationFormResponds['regulatorys'] | undefined;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { sizes, space, colors } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const financialInfoObj = configList?.regulatoryList?.find(
    i => i.section == 'S-1',
  );

  const Text = isTabletMode ? LargeBody : Body;
  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <CubePictogramIcon.MoneyBag size={space[10]} />
        <Title
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.financialCondition')}
        </Title>
      </Row>

      <Box gap={space[2]}>
        {financialInfoObj?.regulatoryList?.map((item, index) => {
          const fieldKey = item.key as keyof IDNRegulatorysQuestionsOnly;
          return (
            <Row gap={space[1]} key={fieldKey}>
              <Text
                color={colors.palette.fwdGreyDarker}
                style={{ width: sizes[4] }}>
                {`${index + 1}.`}
              </Text>
              <Box gap={space[1]}>
                <Text color={colors.palette.fwdGreyDarker}>
                  {renderLabelByLanguage(item?.longDesc)}
                </Text>
                <Text>
                  {regulatorys?.[fieldKey]?.checked
                    ? t('eRecruit.application.personalDetails.question.yes')
                    : t('eRecruit.application.personalDetails.question.no')}
                </Text>
                {typeof regulatorys?.[fieldKey]?.detail === 'string' && (
                  <Text>{regulatorys?.[fieldKey]?.detail}</Text>
                )}
              </Box>
            </Row>
          );
        })}
      </Box>
    </View>
  );
}
