import { useTheme } from '@emotion/react';
import { CubePictogramIcon, H6, H7, LargeBody, Row } from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
const Remark = memo(({ remark }: { remark: string }) => {
  const { space, colors } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <CubePictogramIcon.Notebook size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.remark')}
        </Title>
      </Row>
      <LargeBody>{remark}</LargeBody>
    </View>
  );
});

export default Remark;
