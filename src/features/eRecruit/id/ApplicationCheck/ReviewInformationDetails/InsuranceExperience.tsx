import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  CubePictogramIcon,
  H6,
  H7,
  LargeBody,
  Row,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';

import { View } from 'react-native';

import { ApplicationFormResponds } from 'types/eRecruit';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export function InsuranceExperience({
  workingExperiences,
}: {
  workingExperiences: ApplicationFormResponds['workingExperiences'] | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { sizes, space, colors } = useTheme();

  const answer1 = workingExperiences?.[0]?.isHaveExpLifeInsurance;
  const answer2 = workingExperiences?.[0]?.isHaveExpGeneInsurance;

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const Text = isTabletMode ? LargeBody : Body;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <CubePictogramIcon.Notebook size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.personalDetails.insuranceExperience')}
        </Title>
      </Row>

      <Row gap={space[1]}>
        <Text color={colors.palette.fwdGreyDarker} style={{ width: sizes[4] }}>
          1.
        </Text>
        <Box gap={space[1]}>
          <Text color={colors.palette.fwdGreyDarker}>
            {t('eRecruit.application.personalDetails.questionOne')}
          </Text>
          <Text>
            {answer1
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
        </Box>
      </Row>
      <Box h={space[2]} />
      <Row gap={space[1]}>
        <Text color={colors.palette.fwdGreyDarker} style={{ width: sizes[4] }}>
          2.
        </Text>
        <Box gap={space[1]}>
          <Text color={colors.palette.fwdGreyDarker}>
            {t('eRecruit.application.personalDetails.questionTwo')}
          </Text>
          <Text>
            {answer2
              ? t('eRecruit.application.personalDetails.question.yes')
              : t('eRecruit.application.personalDetails.question.no')}
          </Text>
        </Box>
      </Row>
    </View>
  );
}
