import { useTheme } from '@emotion/react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { default as DefaultInfoField } from 'features/eRecruit/components/InfoField';

export const InfoField = ({ label, data }: { label: string; data: string }) => {
  const { colors } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  return (
    <DefaultInfoField
      label={label}
      data={data}
      labelStyle={
        isTabletMode ? undefined : { color: colors.palette.fwdGreyDarkest }
      }
    />
  );
};
