import { useTheme } from '@emotion/react';
import { Row, PictogramIcon, H6, Column, H7 } from 'cube-ui-components';
import { View } from 'react-native';
import { InfoFieldContainer as TabletInfoFieldContainer } from 'features/eRecruit/components/InfoField';
import {
  ApplicationFormResponds,
  GetERecruitConfigResponse,
} from 'types/eRecruit';
import { useTranslation } from 'react-i18next';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React from 'react';
import { InfoField } from './components/InfoField';

type ContactDetailsProps = ApplicationFormResponds['contact'] | undefined;

export function BankInfo({
  contact,
  configList,
}: {
  contact: ContactDetailsProps;
  configList: GetERecruitConfigResponse | undefined;
}) {
  const { t } = useTranslation('eRecruit');
  const { colors, space } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? H6 : H7;

  const InfoFieldContainer = isTabletMode
    ? TabletInfoFieldContainer
    : React.Fragment;

  return (
    <View>
      <Row
        gap={space[2]}
        style={{
          alignItems: 'center',
          paddingBottom: isTabletMode ? space[5] : space[4],
        }}>
        <PictogramIcon.Bank3 size={space[10]} />
        <Title
          style={{ flex: 1 }}
          fontWeight="bold"
          color={isTabletMode ? '#333' : colors.secondary}>
          {t('eRecruit.application.otherDetails.bankAccountInformation')}
        </Title>
      </Row>
      <Column gap={space[2]}>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.otherDetails.bankName')}
            data={
              contact?.bankInformation.bankName
                ? configList?.bankList?.find(
                    item => item.itemCode === contact?.bankInformation.bankName,
                  )?.longDesc.en ?? 'N/A'
                : 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.application.otherDetails.branch')}
            data={
              contact?.bankInformation.branchName
                ? configList?.branchList?.find(
                    item =>
                      item.itemCode === contact?.bankInformation.branchName,
                  )?.longDesc.en ?? 'N/A'
                : 'N/A'
            }
          />
        </InfoFieldContainer>
        <InfoFieldContainer>
          <InfoField
            label={t('eRecruit.application.otherDetails.accountNumber')}
            data={
              contact?.bankInformation.accountNumber
                ? contact?.bankInformation.accountNumber
                : 'N/A'
            }
          />
          {isTabletMode && <InfoField label={''} data={''} />}
        </InfoFieldContainer>
      </Column>
    </View>
  );
}
