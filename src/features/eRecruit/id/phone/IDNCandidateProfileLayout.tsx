import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Column, H8, Icon, Row } from 'cube-ui-components';
import useBoundStore from 'hooks/useBoundStore';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { Animated, ScrollView, TouchableOpacity, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  ERecruitCandidateProfileParamList,
  RootStackParamListMap,
} from 'types';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useGetERCandidateProfile } from 'features/eRecruit/hooks/useGetERCandidateProfile';
import CandidateApplicationStatus from './CandidateApplicationStatus';
import CandidatePersonalInfo from 'features/eRecruit/ib/phone/components/CandidatesStatus/CandidatePersonalInfo';
import CandidateBanner from 'features/eRecruit/ib/phone/components/CandidatesStatus/CandidateBanner';
import { ContactButton } from 'features/eRecruit/ib/phone/components/utils/ContactButton';
import RemoveCandidateButton from 'features/eRecruit/ib/phone/components/utils/RemoveCandidateButton';
import ShareLinkButton from 'features/eRecruit/ib/phone/components/utils/ShareLinkButton';
import { RemoteCheckingLinkModal } from 'features/eRecruit/ib/phone/components/utils/RemoteCheckingLinkModal';
import { CandidateProfileResponds } from 'types/eRecruit';
import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen';

export type CandidatePersonalInfoSource = 'I' | 'C' | 'E' | 'R' | 'A';

export default function IDNCandidateProfileLayout(props: ERAppStatusProps) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes } = useTheme();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();
  const { cubeStatus, registrationId, registrationStagingId } = props.route
    .params as ERecruitCandidateProfileParamList;
  const [isShowModal, setIsShowModal] = useState(false);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const haveFooter = (CandidateProfile: CandidateProfileResponds): boolean =>
    CandidateProfile?.submissionDate == null;

  const { isLoading: isCPDetailsLoading, data: candidateProfileDetails } =
    useGetERCandidateProfile({
      registrationId: registrationId,
      registrationStagingId: registrationStagingId,
    });

  useEffect(() => {
    if (isCPDetailsLoading) {
      setAppLoading();
    } else setAppIdle();
  }, [isCPDetailsLoading, setAppIdle, setAppLoading]);

  const isRejected = cubeStatus === 'REJECTED';
  const isSubmissionDateNull = candidateProfileDetails?.submissionDate == null;
  const isNotRemoteSignature = cubeStatus !== 'REMOTE_SIGNATURE';

  const shouldShowRemoveButton = isSubmissionDateNull && isNotRemoteSignature;

  const shouldShowFooter =
    candidateProfileDetails?.submissionDate == null ||
    cubeStatus === 'REMOTE_SIGNATURE';
  const isRemoteSignature = cubeStatus === 'REMOTE_SIGNATURE';

  const getTriggerApplicationButtonLabel = () => {
    if (!candidateProfileDetails?.stage) return '--';
    return candidateProfileDetails.stage === 'NEW_APPLICATION'
      ? t('eRecruit.candidateProfile.startApplication')
      : t('eRecruit.candidateProfile.resumeApplication');
  };

  const handleTriggerApplicationButtonPress = useCallback(() => {
    if (!candidateProfileDetails?.stage) {
      console.log('No candidateProfileDetails?.stage found, Not navigation');
      return;
    }
    const currentApplicationId =
      typeof candidateProfileDetails?.registrationStagingId === 'number'
        ? String(candidateProfileDetails?.registrationStagingId)
        : '';

    const isNewApplication =
      candidateProfileDetails?.stage == 'NEW_APPLICATION' &&
      !currentApplicationId;

    navigate(
      'ERecruitApplication',
      isNewApplication
        ? undefined
        : {
            registrationStagingId: currentApplicationId,
          },
    );
  }, [candidateProfileDetails, navigate]);

  const isResumeApplication = cubeStatus === 'RESUME_APPLICATION';

  const warningBannerDefaultHeight = sizes[15];
  const warningBannerHeight = useRef(
    new Animated.Value(warningBannerDefaultHeight),
  ).current;
  const warningBannerPositionY = useRef(new Animated.Value(0)).current;

  const removeWarningBanner = () => {
    Animated.parallel([
      Animated.timing(warningBannerPositionY, {
        toValue: -60,
        duration: 500,
        useNativeDriver: false,
      }),
      Animated.timing(warningBannerHeight, {
        toValue: 0,
        duration: 500,
        useNativeDriver: false,
      }),
    ]).start();
  };
  const WarningBanner = TouchableOpacity;

  const warningMessage = useMemo(() => {
    switch (cubeStatus) {
      case 'REMOTE_SIGNATURE':
        return t('eRecruit.candidateProfile.remoteSignatureWarning');
      case 'RESUME_APPLICATION':
        return t('eRecruit.candidateProfile.resumeApplicationWarning');
      default:
        return undefined;
    }
  }, [cubeStatus, t]);

  return (
    <>
      <ScreenHeader
        route={'ERecruitCandidateProfile'}
        isLeftArrowBackShown
        customTitle={'Candidate Profile'}
        showBottomSeparator={false}
      />
      <RemoteCheckingLinkModal
        isShowModal={isShowModal}
        setIsShowModal={setIsShowModal}
        shareLink={candidateProfileDetails?.link}
        shareMessage={
          cubeStatus == 'PENDING_PAYMENT'
            ? candidateProfileDetails?.sharePaymentUrl
            : candidateProfileDetails?.shareLink
        }
        mobilePhone={candidateProfileDetails?.mobilePhone}
        cubeStatus={cubeStatus}
      />
      {!!warningMessage && (
        <WarningBanner
          style={{ overflow: 'hidden' }}
          onPress={() => {
            removeWarningBanner();
          }}>
          <Animated.View
            style={{
              height: warningBannerHeight,
              top: warningBannerPositionY,
            }}>
            <Row
              backgroundColor={colors.palette.alertRedLight}
              gap={space[2]}
              height={warningBannerDefaultHeight}
              alignItems="center"
              paddingX={space[3]}>
              <Icon.Warning size={sizes[5]} fill={colors.palette.alertRed} />
              <Box flex={1}>
                <H8 color={colors.palette.alertRed}>{warningMessage}</H8>
              </Box>
            </Row>
          </Animated.View>
        </WarningBanner>
      )}
      <ScrollView contentContainerStyle={{ paddingBottom: space[40] }}>
        <Column flex={1} backgroundColor={colors.surface} gap={space[4]}>
          <CandidateBanner
            candidateProfileDetails={candidateProfileDetails}
            cubeStatus={cubeStatus}
          />
          <CandidateApplicationStatus {...candidateProfileDetails} />
          <CandidatePersonalInfo
            fullName={candidateProfileDetails?.name}
            mobilePhone={candidateProfileDetails?.mobilePhone}
            email={candidateProfileDetails?.email}
            gender={candidateProfileDetails?.gender}
            source={
              candidateProfileDetails?.source as CandidatePersonalInfoSource
            }
            candidateNo={candidateProfileDetails?.candidateRefId}
          />
        </Column>
        {(isRejected || shouldShowRemoveButton) && (
          <RemoveCandidateButton
            registrationId={registrationId}
            registrationStagingId={registrationStagingId}
          />
        )}
      </ScrollView>

      {shouldShowFooter && (
        <>
          <Footer>
            <BottomStyle>
              {isRemoteSignature ? (
                <ShareLinkButton
                  candidateProfileDetails={candidateProfileDetails}
                  setIsShowModal={setIsShowModal}
                />
              ) : (
                <Button
                  onPress={handleTriggerApplicationButtonPress}
                  style={{ flex: 1 }}
                  text={getTriggerApplicationButtonLabel()}
                  textStyle={{
                    color: colors.palette.white,
                  }}
                />
              )}
            </BottomStyle>
          </Footer>
        </>
      )}

      <ContactButton
        haveFooter={
          candidateProfileDetails ? haveFooter(candidateProfileDetails) : false
        }
        email={candidateProfileDetails?.email ?? ''}
        phoneNumber={candidateProfileDetails?.mobilePhone ?? ''}
      />
    </>
  );
}

const Footer = styled(View)(({ theme: { space, colors, sizes } }) => {
  const { bottom: bottomInset } = useSafeAreaInsets();
  return {
    maxHeight: sizes[50],
    width: '100%',
    paddingHorizontal: space[4],
    borderWidth: 1,
    borderColor: colors.background,
    borderTopColor: colors.palette.fwdGrey[100],
    paddingTop: space[4],
    paddingBottom: space[4] + bottomInset,
    backgroundColor: colors.background,
    position: 'absolute',
    bottom: 0,
  };
});

const BottomStyle = styled(Row)(({ theme: { space } }) => ({
  gap: space[4],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
}));
