import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { View } from 'react-native';
import {
  H8,
  H7,
  H6,
  Row,
  Icon,
  LargeBody,
  Column,
  Box,
  Typography,
  ExtraLargeBody,
} from 'cube-ui-components';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { useTranslation } from 'react-i18next';
import { CandidateProfileResponds, RejectedAgentDetails } from 'types/eRecruit';
import CandidateStatusProgressBar from 'features/eRecruit/components/CandidateProfile/CandidateStatusProgressBar';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useMemo } from 'react';

type Props = Partial<CandidateProfileResponds>;

export default function CandidateApplicationStatus(props: Props) {
  const { t } = useTranslation('eRecruit');
  const { space } = useTheme();
  const eRecruit = 'eRecruit.candidateProfile';

  const remarkMatch =
    props?.rejectedReason && props?.rejectedReason.match(/Remark:(.*)/);

  const remark = remarkMatch ? remarkMatch[1] : null;

  const approvedRemarkMatch =
    props?.approveReason && props?.approveReason.match(/Remark:(.*)/);

  const approvedRemark = approvedRemarkMatch ? approvedRemarkMatch[1] : null;

  const { data: agentProfile, isLoading: isAgentLoading } =
    useGetAgentProfile();
  const recruiterAgencyType = agentProfile?.agentType;

  const progressHandler = () => {
    switch (props?.cubeStatus) {
      case 'POTENTIAL_CANDIDATE':
        return {
          progress: 14,
          label: t(`eRecruit.candidateProfile.progressBar.await`),
        };
      case 'RESUME_APPLICATION':
        return {
          progress: 14,
          label: t(`eRecruit.candidateProfile.progressBar.await`),
        };
      case 'REMOTE_SIGNATURE':
        return {
          progress: 28,
          label: t(`eRecruit.candidateProfile.progressBar.pendingSignature`),
        };
      case 'PENDING_LEADER_APPROVAL':
        return {
          progress: 42,
          label: t(`eRecruit.candidateProfile.progressBar.pendingApproval`),
        };
      case 'APPROVED': {
        //LICENSE_ACTIVATED
        if (props?.licenseActivatedDate) {
          return {
            progress: 100,
            label: t(`eRecruit.candidateProfile.progressBar.readyToGo`),
            text: t(`eRecruit.candidateProfile.progressBar.activeAccount`),
          };
        }
        //CODE_ISSUED
        if (props?.codeIssuedDate) {
          return {
            progress: 79,
            label: t(
              `eRecruit.candidateProfile.progressBar.licensingCompletion`,
            ),
            text: t(`eRecruit.candidateProfile.progressBar.inactiveAccount`),
          };
        }

        //APPROVED
        return {
          progress: 57,
          label: t(`eRecruit.candidateProfile.progressBar.licensingCompletion`),
          text: t(`eRecruit.candidateProfile.progressBar.inactiveAccount`),
        };
      }
      case 'REJECTED':
        return {
          progress: 0,
          label: t(`eRecruit.candidateProfile.autoDeclinedOn`, {
            date: props?.rejectedDate
              ? dateFormatUtil(props?.rejectedDate)
              : '',
          }),
        };
      default:
        return { progress: 0, label: '' };
    }
  };

  const applicationSubmittedLabel = useMemo(() => {
    if (props?.submissionDate) {
      return t(`eRecruit.candidateProfile.applicationSubmitted`);
    }
    return t(`eRecruit.candidateProfile.applicationInProgress`);
  }, [props?.submissionDate, t]);

  const candidateSignatureSubmittedLabel = useMemo(() => {
    if (props?.signatureDate) {
      return t(`eRecruit.candidateProfile.candidateSignatureSubmitted`);
    }
    if (props?.cubeStatus === 'REMOTE_SIGNATURE') {
      return t(`eRecruit.candidateProfile.awaitingCandidateSignature`);
    }

    return t(`eRecruit.candidateProfile.candidateSignature`);
  }, [props?.signatureDate, props?.cubeStatus, t]);

  return (
    <Box paddingX={space[4]}>
      <InfoRowCard>
        <H6 fontWeight="bold">
          {t(`eRecruit.candidateProfile.candidateStatus`)}
        </H6>

        <Column gap={space[5]}>
          <Column>
            <Box paddingY={6}>
              <CandidateStatusProgressBar
                isDeclined={props?.cubeStatus === 'REJECTED'}
                progress={progressHandler().progress}
                height={44}
                isDone={!!props?.licenseActivatedDate}
                label={progressHandler().text}
              />
            </Box>
            <Box flex={1} alignItems="center" paddingTop={space[2]}>
              <Typography.Body>{progressHandler().label}</Typography.Body>
            </Box>
          </Column>

          <Column gap={space[3]}>
            <InfoRow
              tick={Boolean(props?.createdDate)}
              label={t(`eRecruit.candidateProfile.visited`)}
              date={
                props?.createdDate ? dateFormatUtil(props?.createdDate) : ''
              }
            />
            <InfoRow
              tick={Boolean(props?.submissionDate)}
              label={applicationSubmittedLabel}
              date={
                props?.submissionDate
                  ? dateFormatUtil(props?.submissionDate)
                  : ''
              }
            />
            <InfoRow
              tick={Boolean(props?.signatureDate)}
              label={candidateSignatureSubmittedLabel}
              date={
                props?.signatureDate ? dateFormatUtil(props?.signatureDate) : ''
              }
            />
            {props?.rejectedDate ? (
              <InfoRow
                tick={false}
                label={t(`eRecruit.candidateProfile.leaderRejected`)}
                date={
                  props?.rejectedDate ? dateFormatUtil(props?.rejectedDate) : ''
                }
                CloseCircle={true}
              />
            ) : (
              <>
                {props?.approvedDate ? (
                  <InfoRow
                    tick={true}
                    label={t(`eRecruit.candidateProfile.leaderApproved`)}
                    date={
                      props?.approvedDate
                        ? dateFormatUtil(props?.approvedDate)
                        : ''
                    }
                  />
                ) : (
                  <InfoRow
                    tick={false}
                    label={t(`eRecruit.candidateProfile.leaderApproval`)}
                    date={''}
                  />
                )}
              </>
            )}
            {props?.approvedDate && !props?.rejectedDate && (
              <>
                <InfoRow
                  tick={props?.codeIssuedDate ? true : false}
                  label={t(`eRecruit.candidateProfile.agentCodeIssued`)}
                  date={
                    props?.codeIssuedDate
                      ? dateFormatUtil(props?.codeIssuedDate)
                      : ''
                  }
                  subLabel={
                    props?.codeIssuedDate
                      ? t(`eRecruit.candidateProfile.agentCode`)
                      : undefined
                  }
                  subLabelValue={
                    props?.codeIssuedDate ? props?.agentCode ?? '' : undefined
                  }
                />
                <InfoRow
                  tick={props?.licenseActivatedDate ? true : false}
                  label={t(`eRecruit.candidateProfile.licenseActivated`)}
                  date={
                    props?.licenseActivatedDate
                      ? dateFormatUtil(props?.licenseActivatedDate)
                      : ''
                  }
                />
              </>
            )}
          </Column>
        </Column>

        {props?.rejectedReason && props?.rejectedAgent && (
          <>
            <LineBreaker />
            <H7 fontWeight="bold">
              {t(`eRecruit.candidateProfile.reviewerComment`)}
            </H7>
            <View style={{ gap: space[2] }}>
              <LargeBody>{remark ?? props?.rejectedReason}</LargeBody>
              <LargeBody>
                {t(`eRecruit.candidateProfile.rejectedBy`)}:{' '}
                <LargeBody fontWeight="medium">
                  {props?.rejectedAgent?.agentName} (
                  {props?.rejectedAgent?.designate})
                </LargeBody>
              </LargeBody>
            </View>
          </>
        )}
        {props?.approveReason &&
          props?.approvedAgent &&
          !(props?.rejectedReason && props?.rejectedAgent) && (
            <>
              <LineBreaker />
              <H7 fontWeight="bold">
                {t(`eRecruit.candidateProfile.reviewerComment`)}
              </H7>
              <View style={{ gap: space[2] }}>
                <LargeBody>{approvedRemark ?? props?.approveReason}</LargeBody>
                <LargeBody>
                  {t(`eRecruit.candidateProfile.approvedBy`)}:{' '}
                  <LargeBody fontWeight="medium">
                    {props?.approvedAgent?.agentName} (
                    {props?.approvedAgent?.designate})
                  </LargeBody>
                </LargeBody>
              </View>
            </>
          )}
      </InfoRowCard>
    </Box>
  );
}

const LineBreaker = styled(View)(({ theme: { colors } }) => ({
  width: '100%',
  height: 1,
  borderBottomColor: colors.palette.fwdGrey[100],
  borderBottomWidth: 1,
}));

const InfoRowCard = styled(Column)(({ theme: { sizes, colors, space } }) => ({
  backgroundColor: colors.background,
  padding: sizes[4],
  gap: sizes[4],
  borderRadius: space[4],
}));

const DateStyle = styled(H8)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
}));

const InfoRow = ({
  tick,
  label,
  date,
  CloseCircle,
  subLabel,
  subLabelValue,
}: {
  tick: boolean;
  label: string;
  date: string;
  CloseCircle?: boolean;
  subLabel?: string;
  subLabelValue?: string;
}) => {
  const { colors, space } = useTheme();
  return (
    <Box gap={space[1]}>
      <Row alignItems="center" justifyContent="space-between" gap={space[3]}>
        <Row style={{ flex: 1, gap: space[2] }} alignItems="center">
          {CloseCircle && <Icon.CloseCircle fill={colors.palette.alertRed} />}
          {CloseCircle ? (
            <></>
          ) : !CloseCircle && tick ? (
            <Icon.TickCircle fill={colors.palette.alertGreen} />
          ) : (
            <Icon.TickCircle fill={colors.palette.fwdGrey[100]} />
          )}
          <H7 style={{ flex: 1 }}>{label}</H7>
        </Row>
        <DateStyle>{date}</DateStyle>
      </Row>
      {subLabel && subLabelValue && (
        <Row ml={space[8]} alignItems="center" gap={space[3]}>
          <H7>{subLabel}:</H7>
          <ExtraLargeBody fontWeight="bold">{subLabelValue}</ExtraLargeBody>
        </Row>
      )}
    </Box>
  );
};
