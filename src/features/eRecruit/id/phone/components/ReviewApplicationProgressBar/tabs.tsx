import { ERecruitTabs } from 'features/eRecruit/hooks/useGetERecruitTab';
import { IBReviewApplicationCombinedRouteKey } from 'features/eRecruit/types/progressBarTypes';
import PersonalDetailSection from '../ReviewApplication/PersonalDetailSection';
import DocumentSection from 'features/eRecruit/ib/phone/components/ReviewApplication/DocumentSection';

export const IDReviewTabs: ERecruitTabs<IBReviewApplicationCombinedRouteKey> = {
  phone: [
    {
      route: 'personalDetails--',
      component: PersonalDetailSection,
    },
    {
      route: 'documentUpload--',
      component: DocumentSection,
    },
  ],
  tablet: [],
};
