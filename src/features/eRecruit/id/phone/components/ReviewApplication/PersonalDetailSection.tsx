import styled from '@emotion/native';
import { Box } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React from 'react';
import { ScrollView } from 'react-native';
import { ReviewAgentApplicationResponds } from 'types/eRecruit';
import { useTheme } from '@emotion/react';
import { useRoute } from '@react-navigation/native';
import ERecruitFooter from 'features/eRecruit/ib/phone/components/utils/ERecruitFooter';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';
import { shallow } from 'zustand/shallow';
import { ReviewInformation } from 'features/eRecruit/id/ReviewAgentsApplication/ReviewInformation';
import { TrackerCard } from 'features/eRecruit/ib/components/TrackerCard';

export default function PersonalDetailSection() {
  const { space } = useTheme();
  const route = useRoute();

  const { applicationId } = route.params as ReviewAgentApplicationResponds;

  const { setProgressBarState } = useERecruitReviewApplicationProgressBarStore(
    state => ({
      completedMap: state.completedMap,
      setProgressBarState: state.setProgressBarState,
      groups: state.groups,
      groupKey: state.groupKey,
      subgroupKey: state.subgroupKey,
      itemKey: state.itemKey,
    }),
    shallow,
  );

  return (
    <>
      <ScrollView>
        <Container>
          <Box paddingY={space[4]}>
            <TrackerCard applicationId={applicationId as number} />
            <ReviewInformation applicationId={applicationId as number} />
          </Box>
        </Container>
      </ScrollView>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={false}
        onPrimaryPress={() =>
          setProgressBarState({
            groupKey: 'documentUpload',
            subgroupKey: 'documentUpload',
            itemKey: 'documentUpload',
          })
        }
        primarySubLabel="Review documents"
      />
    </>
  );
}
const Container = styled.View(({ theme: { space, borderRadius } }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    borderRadius: borderRadius.large,
    paddingVertical: space[2],
    marginHorizontal: space[isNarrowScreen ? 3 : 4],
    overflow: 'hidden',
  };
});
