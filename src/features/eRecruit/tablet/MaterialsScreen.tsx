import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Chip,
  Column,
  H7,
  Icon,
  Label,
  LargeLabel,
  Row,
  SmallBody,
} from 'cube-ui-components';
import EmptyRecordSVG from 'features/eRecruit/assets/EmptyRecordSVG';
import useSearchRecruitmentMaterials from 'features/eRecruit/hooks/useSearchRecruitmentMaterials';
import { useGetRecruitmentMaterials } from 'hooks/useGetRecruitmentMaterials';
import ScreenHeader from 'navigation/components/ScreenHeader/tablet';
import { useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  StyleProp,
  TextInput,
  TouchableOpacity,
  useWindowDimensions,
  View,
  ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CategoryType, SortType } from 'types/recruitmentMaterials';
import MaterialCard from '../components/tablet/MaterialCard';
import EmptyFilterCaseSvg from 'features/eRecruit/assets/EmptyFilterCaseSVG';

const SCROLL_AREA_PADDING = 16;
const COLUMN_GAP = 24;
const ROW_GAP = 40;

const CHIP_FILTERS = [
  { type: 'recruitment', label: 'materials.filter.label.recruitment' },
  { type: 'gyb', label: 'materials.filter.label.gyb' },
  { type: 'agent_to_agent', label: 'materials.filter.label.agentToAgent' },
] as const;

export default function MaterialsScreen() {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes } = useTheme();
  const { width } = useWindowDimensions();
  const { bottom } = useSafeAreaInsets();

  const ref = useRef<TextInput>(null);

  const [listWidth, setListWidth] = useState(0);
  const [isFocused, setIsFocused] = useState(false);
  const [inSearchMode, setInSearchMode] = useState(false);

  // Sorting
  const [sortType, setSortType] = useState<SortType>('newest');

  const handleSortPress = () => {
    setSortType(prev => (prev === 'newest' ? 'oldest' : 'newest'));
  };

  // Data
  const { data } = useGetRecruitmentMaterials({ sortType });

  const {
    isSearching,
    setIsSearching,
    query,
    setQuery,
    searchResults,
    categoryFilter,
    setCategoryFilter,
  } = useSearchRecruitmentMaterials(data ?? []);

  const handleBackPress = () => {
    setQuery('');
    ref.current?.clear();
    ref.current?.blur();
    setIsSearching(false);
    setInSearchMode(false);
    setCategoryFilter([]);
  };

  return (
    <>
      <ScreenHeader
        route={'Materials'}
        customTitle={'Materials'}
        isLeftArrowBackShown
      />

      {inSearchMode ? (
        <Column paddingTop={space[6]} paddingX={width * 0.1}>
          <Row alignItems="center" justifyContent="center" gap={space[2]}>
            <TouchableOpacity onPress={() => handleBackPress()}>
              <Icon.ArrowLeft fill={colors.palette.fwdDarkGreen[100]} />
            </TouchableOpacity>
            <SearchInputContainer isFocused={isFocused}>
              <SearchInput
                ref={ref}
                placeholder={t('materials.search.placeholder')}
                onChangeText={text => setQuery(text)}
                autoCapitalize={'none'}
                enablesReturnKeyAutomatically={true}
                returnKeyType={'search'}
                autoComplete={'off'}
                autoCorrect={false}
                autoFocus
                onFocus={() => {
                  setIsFocused(true);
                  setIsSearching(true);
                }}
                onBlur={() => {
                  setIsFocused(false);
                  setIsSearching(false);
                }}
                selectionColor={colors.primary}
                placeholderTextColor={colors.palette.fwdGreyDark}
              />
              {query != '' && (
                <TouchableOpacity
                  onPress={() => {
                    setQuery('');
                    ref.current?.clear();
                    setIsSearching(true);
                    setCategoryFilter([]);
                  }}>
                  <Icon.CloseCircle
                    fill={colors.onBackground}
                    size={sizes[5]}
                  />
                </TouchableOpacity>
              )}
            </SearchInputContainer>
          </Row>

          {isSearching ? (
            <></>
          ) : // query + has search result
          Boolean(query) && searchResults?.length > 0 ? (
            <Column>
              <H7
                fontWeight="bold"
                children={`${t('materials.search.result')} (${
                  searchResults?.length
                })`}
                style={{ paddingVertical: space[3] }}
              />

              <Row alignItems="center" paddingBottom={space[6]} gap={space[1]}>
                {CHIP_FILTERS.map(({ type, label }) => (
                  <Chip
                    key={type}
                    focus={categoryFilter?.includes(type)}
                    label={t(label)}
                    onPress={() => {
                      setCategoryFilter((prev: CategoryType[]) => {
                        return categoryFilter?.includes(type)
                          ? prev.filter((item: CategoryType) => item !== type)
                          : [...prev, type];
                      });
                    }}
                  />
                ))}
              </Row>

              <FlatList
                data={searchResults}
                renderItem={({ item }) => (
                  <MaterialCard
                    item={item}
                    itemWidth={(listWidth - 2 * ROW_GAP) / 3}
                  />
                )}
                keyExtractor={item => item?.uid}
                numColumns={3}
                columnWrapperStyle={{ gap: ROW_GAP }} // columnWrapperStyle is not yet supported in FlashList, so FlatList is used.
                contentContainerStyle={{
                  gap: COLUMN_GAP,
                  paddingBottom: bottom + SCROLL_AREA_PADDING,
                  paddingTop: space[4],
                }}
                //
                onLayout={event => {
                  const { width } = event.nativeEvent.layout;
                  setListWidth(width);
                }}
              />
            </Column>
          ) : // query + applied category filter + no search result
          Boolean(query) &&
            searchResults.length === 0 &&
            categoryFilter?.length > 0 ? (
            <Column>
              <H7
                fontWeight="bold"
                children={`${t('materials.search.searchResult')} (${
                  searchResults?.length
                })`}
                style={{ paddingVertical: space[3] }}
              />

              <Row alignItems="center" paddingBottom={space[6]} gap={space[1]}>
                {CHIP_FILTERS.map(({ type, label }) => (
                  <Chip
                    key={type}
                    focus={categoryFilter?.includes(type)}
                    label={t(label)}
                    onPress={() => {
                      setCategoryFilter((prev: CategoryType[]) => {
                        return categoryFilter?.includes(type)
                          ? prev.filter((item: CategoryType) => item !== type)
                          : [...prev, type];
                      });
                    }}
                  />
                ))}
              </Row>

              <Column paddingTop={space[20]} alignItems="center">
                <EmptyFilterCaseSvg />
                <LargeLabel
                  children={t('materials.search.noResult')}
                  style={{ color: colors.palette.fwdGreyDarker }}
                />
              </Column>
            </Column>
          ) : (
            // query + no search result
            <Column>
              <H7
                fontWeight="bold"
                children={`${t('materials.search.searchResult')} (${
                  searchResults?.length
                })`}
                style={{ paddingVertical: space[3] }}
              />
              <Column paddingTop={space[20]} alignItems="center">
                <EmptyRecordSVG />
                <LargeLabel
                  children={t('materials.search.noResult')}
                  style={{ color: colors.palette.fwdGreyDarker }}
                />
              </Column>
            </Column>
          )}
        </Column>
      ) : (
        <>
          <Column paddingTop={space[6]} paddingX={width * 0.1}>
            <Row paddingBottom={space[3]} justifyContent="space-between">
              <Row alignItems="center" gap={space[1]}>
                <SmallBody
                  children={t('materials.filter')}
                  style={{ color: colors.palette.fwdGreyDarker }}
                />

                {CHIP_FILTERS.map(({ type, label }) => (
                  <Chip
                    key={type}
                    focus={categoryFilter?.includes(type)}
                    label={t(label)}
                    onPress={() => {
                      setCategoryFilter((prev: CategoryType[]) => {
                        return categoryFilter?.includes(type)
                          ? prev.filter((item: CategoryType) => item !== type)
                          : [...prev, type];
                      });
                    }}
                  />
                ))}
              </Row>

              <SearchMaterialsButton
                onPress={() => {
                  setInSearchMode(true);
                  setIsSearching(true);
                  setCategoryFilter([]);
                }}
              />
            </Row>

            <SortBtnContainer onPress={() => handleSortPress()}>
              <SmallBody
                children={`${t('materials.search.total')} (${
                  searchResults?.length ?? '--'
                })`}
                style={{
                  color: colors.palette.fwdGreyDarkest,
                }}
              />
              <Row style={{ alignItems: 'center' }}>
                <Label
                  fontWeight="bold"
                  color={colors.palette.fwdAlternativeOrange[100]}
                  children={
                    sortType === 'newest'
                      ? t('materials.sort.newest')
                      : t('materials.sort.oldest')
                  }
                />
                {sortType === 'newest' ? (
                  <Icon.ArrowDown
                    fill={colors.palette.fwdAlternativeOrange[100]}
                    size={sizes[4]}
                  />
                ) : (
                  <Icon.ArrowUp
                    fill={colors.palette.fwdAlternativeOrange[100]}
                    size={sizes[4]}
                  />
                )}
              </Row>
            </SortBtnContainer>
          </Column>

          <Column flex={1} paddingX={width * 0.1}>
            <FlatList
              data={searchResults}
              renderItem={({ item }) => (
                <MaterialCard
                  item={item}
                  itemWidth={(listWidth - 2 * ROW_GAP) / 3}
                />
              )}
              keyExtractor={item => item?.uid}
              numColumns={3}
              columnWrapperStyle={{ gap: ROW_GAP }} // columnWrapperStyle is not yet supported in FlashList, so FlatList is used.
              contentContainerStyle={{
                gap: COLUMN_GAP,
                paddingBottom: bottom + SCROLL_AREA_PADDING,
                paddingTop: space[4],
              }}
              //
              onLayout={event => {
                const { width } = event.nativeEvent.layout;
                setListWidth(width);
              }}
            />
          </Column>
        </>
      )}
    </>
  );
}

function SearchMaterialsButton({ onPress }: { onPress: () => void }) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, borderRadius } = useTheme();

  return (
    <TouchableOpacity onPress={onPress}>
      <Row
        bgColor={colors.palette.white}
        borderWidth={2}
        borderColor={colors.palette.fwdOrange[50]}
        borderRadius={borderRadius.full}
        py={space[2]}
        pl={space[5]}
        pr={space[6]}
        mr={space[3]}
        gap={space[2]}
        alignItems="center">
        <Icon.Search />
        <LargeLabel
          fontWeight="medium"
          children={t('materials.search.placeholder')}
          color={colors.primary}
        />
      </Row>
    </TouchableOpacity>
  );
}

const SortBtnContainer = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  paddingBottom: theme.space[4],
  alignSelf: 'flex-start',
  alignItems: 'center',
  gap: theme.space[4],
  paddingVertical: theme.space[2],
}));

// search bar
interface SearchBarContainerProps {
  show?: boolean;
  active?: boolean;
  isFocused?: boolean;
  style?: StyleProp<ViewStyle>;
}

const SearchInputContainer = styled.View<SearchBarContainerProps>(
  ({ theme, active, isFocused }) => {
    return {
      flex: 1,
      borderRadius: theme.borderRadius.full,
      borderColor:
        active || isFocused
          ? theme.colors.primary
          : theme.colors.palette.fwdDarkGreen[20],
      borderWidth: 1,
      backgroundColor: theme.colors.background,
      paddingHorizontal: theme.space[4],
      flexDirection: 'row',
      minHeight: theme.sizes[12],
      alignItems: 'center',
    };
  },
);

const SearchInput = styled.TextInput<SearchBarContainerProps>(({ theme }) => ({
  flex: 1,
  color: theme.colors.onSurface,
  fontFamily: 'FWDCircularTT-Book',
  placeholderTextColor: theme.colors.palette.fwdGreyDark,
}));
