import styled from '@emotion/native';
import { View } from 'react-native';
import {
  Box,
  Column,
  H6,
  H7,
  Icon,
  Label,
  LargeBody,
  Row,
  XView,
} from 'cube-ui-components';
import React from 'react';
import { useTheme } from '@emotion/react';
import { TextProps } from 'react-native';
import { ViewStyle } from 'react-native';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export default function InfoField({
  label,
  data,
  labelStyle,
  dataStyle,
  labelContainerStyle,
}: {
  label: string;
  data: string | number;
  labelStyle?: TextProps['style'];
  labelContainerStyle?: ViewStyle;
  dataStyle?: TextProps['style'];
}) {
  const { isTabletMode } = useLayoutAdoptionCheck();

  const Label = isTabletMode ? LabelStyle : PhoneLabelStyle;
  const Data = isTabletMode ? DataStyle : PhoneDataStyle;

  return (
    <XView
      style={{ flex: 1, gap: country === 'id' && !isTabletMode ? 16 : 20 }}>
      <Box
        style={{
          flex: country === 'id' && isTabletMode ? 1.228 : 1.5,
          ...labelContainerStyle,
        }}>
        <Label style={labelStyle && labelStyle}>{label}</Label>
      </Box>
      <Data style={dataStyle && dataStyle}>{data}</Data>
    </XView>
  );
}

export function ColumnField({ label, data }: { label: string; data: string }) {
  const { space } = useTheme();
  return (
    <Column style={{ flex: 1, gap: space[1] }}>
      <LabelStyle>{label}</LabelStyle>
      <DataStyle>{data}</DataStyle>
    </Column>
  );
}

export function InfoFieldQualification({
  label,
  data,
}: {
  label: string;
  data: (string | undefined)[];
}) {
  return (
    <Column gap={8}>
      <LabelStyleQualification>{label}</LabelStyleQualification>
      <Row
        gap={4}
        style={{
          paddingBottom: 12,
          paddingRight: 12,
        }}>
        {data &&
          data.map(
            (item, index) =>
              item && (
                <React.Fragment key={index}>
                  <Icon.Tick />
                  <DataStyleQualification>{item}</DataStyleQualification>
                </React.Fragment>
              ),
          )}
      </Row>
    </Column>
  );
}

export function OtherInsuranceQualifications({
  label,
  data,
}: {
  label: string;
  data: { name: string; description?: string | null }[];
}) {
  const { space, sizes } = useTheme();
  return (
    <Column gap={space[2]}>
      <LabelStyle>{label}</LabelStyle>
      <Column>
        {data &&
          data.map((item, index) => {
            return item !== undefined ? (
              <Column key={index}>
                <Row>
                  <React.Fragment>
                    <IconContainer>
                      <Icon.Tick width={sizes[5]} height={sizes[5]} />
                    </IconContainer>
                    <DataStyle>{item?.name}</DataStyle>
                  </React.Fragment>
                </Row>
                <DescriptionStyle>{item?.description}</DescriptionStyle>
              </Column>
            ) : null;
          })}
      </Column>
    </Column>
  );
}
const LabelStyleQualification = styled(H7)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
}));

const DataStyleQualification = styled(H7)(() => ({
  justifyContent: 'flex-start',
  width: 160,
}));

export const LabelStyle = styled(LargeBody)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
}));

export const DataStyle = styled(LargeBody)(() => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

export const PhoneLabelStyle = styled(Label)(({ theme: { colors } }) => ({
  color: colors.palette.fwdGreyDarker,
}));

export const PhoneDataStyle = styled(Label)(() => ({
  justifyContent: 'flex-start',
  flex: 2,
}));

const DescriptionStyle = styled(DataStyle)(({ theme: { space } }) => ({
  marginTop: space[2],
  marginLeft: space[6],
}));

const IconContainer = styled(View)(({ theme: { space } }) => ({
  marginRight: space[1],
}));

export const SectionTitle = styled(H6)(() => ({
  fontFamily: 'FWDCircularTT-Bold',
  color: '#333333',
}));

export const InfoFieldContainer = styled(XView)(({ theme: { space } }) => ({
  gap: space[10],
}));
