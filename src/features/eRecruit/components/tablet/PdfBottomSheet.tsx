import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import LoadingIndicator from 'components/LoadingIndicator';
import CustomModal from 'components/Modal/CustomModal';
import { Box, H6, Icon, Row } from 'cube-ui-components';
import { useCustomWindowDimensions } from 'hooks/useCustomWindowDimensions';
import React from 'react';
import { StyleProp, TouchableOpacity, ViewStyle } from 'react-native';
import Pdf from 'react-native-pdf';
import { RootSiblingParent } from 'react-native-root-siblings';
import {
  SafeAreaView,
  SafeAreaViewProps,
} from 'react-native-safe-area-context';
import WebView from 'react-native-webview';

export type PdfViewerOptions = {
  uid: string;
  _version: number;
  created_by: string;
  updated_by: string;
  created_at: string;
  updated_at: string;
  content_type: string;
  file_size: string;
  filename: string;
  title: string;
  ACL: any;
  parent_uid: string;
  is_dir: boolean;
  tags: string[];
  publish_details: {
    time: string;
    user: string;
    environment: string;
    locale: string;
  };
  url: string;
};

type Props = {
  visible: boolean;
  onClose: () => void;
  pdf?: PdfViewerOptions;
  customTitle?: string;
  scale?: number;
};

const RenderContent = ({ pdf }: { pdf?: PdfViewerOptions }) => {
  const isPdf = pdf?.content_type === 'application/pdf';
  const { sizes, borderRadius } = useTheme();

  const style: StyleProp<ViewStyle> = {
    flex: 1,
    borderRadius: borderRadius.large,
    overflow: 'hidden',
    backgroundColor: '#525558', // value from Figma
  };

  if (isPdf) {
    return (
      <Pdf
        source={{ uri: pdf?.url }}
        // eslint-disable-next-line react-native/no-color-literals
        style={style}
        onError={e => {
          console.error('Fail to load the PDF in PDF Bottom Sheet', e);
        }}
        trustAllCerts={false}
        scale={1.4}
        renderActivityIndicator={() => (
          <PdfPlaceholder>
            <LoadingIndicator size={sizes[25]} />
          </PdfPlaceholder>
        )}
      />
    );
  }

  // Added webview to support a PPT & PPTX file
  return (
    <WebView
      style={style}
      source={{ uri: pdf?.url ?? '' }}
      onError={e => {
        console.error('Fail to load the PPT in PDF Bottom Sheet', e);
      }}
      renderLoading={() => (
        <PdfPlaceholder>
          <LoadingIndicator size={sizes[25]} />
        </PdfPlaceholder>
      )}
    />
  );
};

export default function PdfBottomSheet({
  visible,
  onClose,
  pdf,
  customTitle,
}: Props) {
  const { space, sizes, colors } = useTheme();
  const { height: screenHeight } = useCustomWindowDimensions();

  return (
    <CustomModal
      isVisible={visible}
      style={{ margin: 0, justifyContent: 'flex-end' }}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      hideModalContentWhileAnimating>
      <Container height={screenHeight}>
        <RootSiblingParent>
          <Content>
            <Row paddingBottom={space[6]} paddingX={space[3]}>
              <Row flex={1}>
                <Box marginRight={space[4]}>
                  <TouchableOpacity onPress={onClose}>
                    <Icon.Close
                      size={sizes[6]}
                      fill={colors.palette.fwdDarkGreen[100]}
                    />
                  </TouchableOpacity>
                </Box>
                <Box flexGrow={1}>
                  <H6 fontWeight="bold" children={customTitle} />
                </Box>
              </Row>
            </Row>
            <RenderContent pdf={pdf} />
          </Content>
        </RootSiblingParent>
      </Container>
    </CustomModal>
  );
}

const Container = styled(SafeAreaView)<SafeAreaViewProps & { height: number }>(
  ({ theme, height }) => ({
    backgroundColor: theme.colors.primary,
    height: height * 0.95,
    flexGrow: 0,
    flexShrink: 0,
    paddingTop: 5,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
  }),
);

const Content = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.background,
  flex: 1,
  paddingHorizontal: theme.space[4],
  paddingTop: theme.space[4],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const PdfPlaceholder = styled.View(() => ({
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
}));
