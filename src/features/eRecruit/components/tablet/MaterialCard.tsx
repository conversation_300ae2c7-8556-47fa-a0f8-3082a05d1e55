import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  Box,
  Column,
  Icon,
  LargeBody,
  SmallLabel,
  Body,
} from 'cube-ui-components';
import { Image } from 'expo-image';
import PlayMediaSVG from 'features/eRecruit/assets/PlayMediaSVG';
import _ from 'lodash';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { RootStackParamList } from 'types';
import { RecruitmentMaterialItem } from 'types/recruitmentMaterials';
import PdfBottomSheet, { PdfViewerOptions } from './PdfBottomSheet';

export default function MaterialCard({
  item,
  itemWidth,
}: {
  item: RecruitmentMaterialItem;
  itemWidth?: number;
}) {
  const { uid, category, display_title, thumbnail_image, pdf, video } = item;

  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const [pdfSheetVisible, setPdfSheetVisible] = useState(false);

  const isVideo = !pdf && !_.isEmpty(video);
  const isShareable = category === 'recruitment';

  const handleCardPress = () => {
    isVideo
      ? navigation.navigate('MaterialDetails', { id: uid ?? '' })
      : setPdfSheetVisible(true);
  };

  return (
    <TouchableOpacity
      onPress={() => handleCardPress()}
      style={{
        flex: 1,
        maxWidth: itemWidth,
        borderRadius: borderRadius.large,
      }}>
      <Column
        borderRadius={borderRadius.medium}
        overflow="hidden"
        mb={space[2]}>
        <Image
          source={{ uri: thumbnail_image?.url }}
          style={{ width: 'auto', height: 200 }}
          contentFit="cover"
        />

        {isVideo && <PlayMediaSvgContainer children={<PlayMediaSVG />} />}

        {isShareable && (
          <ShareIconContainer
            children={<Icon.Share size={sizes[5]} fill={colors.onPrimary} />}
          />
        )}

        <FileTypeText
          fontWeight="bold"
          children={
            isVideo
              ? t('materials.fileType.video')
              : t('materials.fileType.pdf')
          }
        />
      </Column>
      <Box h={space[18]}>
        <LargeBody
          numberOfLines={3}
          ellipsizeMode="tail"
          children={display_title ?? '--'}
          color={colors.palette.fwdDarkGreen[100]}
        />
      </Box>

      <Body
        fontWeight="bold"
        children={t(`materials.category.${category}`)}
        numberOfLines={1}
        style={{ color: colors.palette.fwdGreyDarker }}
      />

      <PdfBottomSheet
        visible={pdfSheetVisible}
        onClose={() => setPdfSheetVisible(false)}
        pdf={pdf as PdfViewerOptions}
        scale={1.5}
        customTitle={display_title ?? '--'}
      />
    </TouchableOpacity>
  );
}

const PlayMediaSvgContainer = styled.View(() => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: 'center',
  alignItems: 'center',
}));

const ShareIconContainer = styled.View(() => ({
  position: 'absolute',
  right: 12,
  top: 12,
}));

const FileTypeText = styled(SmallLabel)(({ theme }) => ({
  color: theme.colors.onPrimary,
  position: 'absolute',
  left: 12,
  bottom: 12,
}));
