export type RecruitActionConfig = Array<{
  key:
    | 'startApplication'
    | 'shareRemoteLink'
    | 'trackMyCandidateStatus'
    | 'reviewAgentSubmission'
    | 'materials';
  icon: JSX.Element;
  label: string;
  count: number;
  nav: () => void;
  isHidden?: boolean;
}>;

export type COISectionKeys =
  | 'ownershipInterest'
  | 'externalEmployment'
  | 'businessAffiliationInterest'
  | 'relationshipGovernmentOfficial'
  | 'otherInterest';

type Question = {
  key: COISectionKeys;
  title: string;
  qBody: string[];
  tooltipContentList: string[];
};

export type QuestionsMap = Record<string, Question>;
