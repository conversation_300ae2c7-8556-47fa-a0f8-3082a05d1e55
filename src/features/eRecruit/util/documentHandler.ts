import { DocumentFileKeyType } from 'features/eRecruit/my/type';
import {
  ApplicationFormResponds,
  DocumentFile,
  DocumentFileValue,
} from 'types/eRecruit';
import { cloneDeep } from 'utils/helper/objectUtil';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import { ImagePickerFile } from 'components/ImagePicker/utils';
import {
  allImageFileSubtypes,
  allImageFullMimeSubtypes,
  nonImgFilesToMimeTypes,
} from 'components/ImagePicker/config';

export const formSectionToFilesMap = ({
  files,
  sectionName,
  fileFromRes,
}: {
  files: DocumentFile;
  fileFromRes: DocumentFileValue;
  sectionName: DocumentFileKeyType;
}) => {
  if (!files) {
    console.log('== addFileToSection == files is null');
    return null;
  }

  const copyOfFiles = cloneDeep(files);

  copyOfFiles[sectionName] = copyOfFiles[sectionName] || [];
  copyOfFiles[sectionName].push(fileFromRes);

  return copyOfFiles;
};

export const insertDocumentFiles = ({
  applicationFormData,
  documentFiles,
}: {
  applicationFormData: ApplicationFormResponds | undefined;
  documentFiles: Record<DocumentFileKeyType, DocumentFileValue[]> | null;
}) => {
  const copyOfData = cloneDeep(applicationFormData);

  if (!copyOfData) {
    console.log('copyOfData from applicationFormData is null');
    return;
  }
  if (!copyOfData.documentFiles) {
    console.log('copyOfData.documentFiles is null');
    return;
  }
  if (!documentFiles) {
    console.log('copyOfFiles is null');
    return;
  }

  copyOfData.documentFiles = documentFiles;

  return copyOfData;
};

export const handleFormDataForUpload = ({
  asset,
  sectionName,
}: {
  asset: ImagePickerFile;
  sectionName: DocumentFileKeyType;
}) => {
  const { uri, name: documentName } = asset;

  const form = new FormData();
  const fileType = getFileType(uri);
  console.log('documentName', documentName);
  console.log('------------ ----- ------ fileType: ', fileType);

  const isImageBasedOnMime = allImageFullMimeSubtypes?.includes(fileType);
  const isImageBasedOnFileType = allImageFileSubtypes?.includes(fileType);

  const fileData =
    isImageBasedOnMime || isImageBasedOnFileType
      ? ({
          uri: uri,
          name: 'photo' + '.' + fileType,
          type: 'image/' + fileType,
        } as unknown as File)
      : ({
          uri: uri,
          name: 'document.' + fileType,
          type: nonImgFilesToMimeTypes?.[fileType] ?? 'application/' + fileType,
        } as unknown as File);

  if (!(fileType in nonImgFilesToMimeTypes)) {
    console.log(
      '====handleFormDataForUpload==== fileType not found in nonImgFilesToMimeTypes',
    );
  }

  form.append('file', fileData);
  form.append('fileKey', sectionName);

  return form;
};
