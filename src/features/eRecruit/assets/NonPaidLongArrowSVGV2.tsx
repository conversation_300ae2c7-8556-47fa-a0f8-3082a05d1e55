import * as React from 'react';
import Svg, { Path, SvgProps } from 'react-native-svg';

function NonPaidLongArrowSVGV2(props: SvgProps) {
  return (
    <Svg width={66} height={103} viewBox="0 0 66 103" fill="none" {...props}>
      <Path
        stroke="#F3BB90"
        strokeWidth={2.4}
        d="M17.424 2A19.48 19.48 0 0 0 2 21.052v54.344C2 86.224 10.777 95 21.604 95H59"
      />
      <Path
        fill="#F3BB90"
        stroke="#F3BB90"
        strokeWidth={0.4}
        d="M64.673 94.147a.507.507 0 0 1 0 .78l-6.026 5.018a.507.507 0 0 1-.832-.39V89.52c0-.43.501-.665.832-.39l6.026 5.018Z"
      />
    </Svg>
  );
}

export default NonPaidLongArrowSVGV2;
