import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  ButtonProps,
  Column,
  H8,
  Icon,
  Label,
  LargeBody,
  LoadingIndicator,
  Row,
  Typography,
  XView,
  addErrorBottomToast,
} from 'cube-ui-components';
import React, { useMemo, useState } from 'react';
import {
  ScrollView,
  View,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
  Image as ReactNativeImage,
  ViewProps,
} from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { NewApplicationFormParmaList, RootStackParamListMap } from 'types';
import { SubmitErrorHandler, useForm } from 'react-hook-form';
import { DocumentFileKeyType } from 'features/eRecruit/my/type';
import TabletFooter from 'components/Footer/TabletFooter';
import { tabletStyles } from 'features/coverageDetails/utils/common/customStyles';
import DeleteUploadedFileModal from './DeleteUploadedFileModal';
import ZoomableImageViewer from './ZoomableImageViewer';
import {
  ApplicationFormResponds,
  DocumentFile,
  DocumentListDetail,
  NewApplicationFormValues,
  SaveApplicationFormResponse,
} from 'types/eRecruit';
import { useGetApplicationData } from 'features/eRecruit/hooks/useGetERecruitApplicationForm';
import { useGetDocumentList } from 'features/eRecruit/hooks/useGetDocumentList';
import { UseMutateAsyncFunction } from '@tanstack/react-query';
import { usePostDocumentUpload } from 'features/eRecruit/hooks/usePostDocumentUpload';
import useBoundStore from 'hooks/useBoundStore';
import { baseUrl, build, country } from 'utils/context';
import MYSProgressStepBar from 'features/eRecruit/my/tablet/components/NewApplicationForm/ProgressStepBar';

import CommonProgressStepBar from 'features/eRecruit/components/tablet/ProgressStepBar';
import { formatBytes } from 'features/eRecruit/my/utils';
import { useTranslation } from 'react-i18next';
import { parsingDocumentData } from 'features/eRecruit/my/utils/documentsFuntions';
import { useDeleteERecruitDocument } from 'features/eRecruit/hooks/useDeleteERecruitDocument';
import { useSaveERecruitApplicationForm } from 'features/eRecruit/hooks/useSaveERecruitApplicationForm';
import ImagePicker from 'components/ImagePicker';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';
import {
  ImagePickerOnDoneProps,
  ImagePickerProps,
} from 'components/ImagePicker/utils';
import {
  handleFormDataForUpload,
  formSectionToFilesMap,
  insertDocumentFiles,
} from 'features/eRecruit/util/documentHandler';

import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import FormWithTickSVG from 'features/eRecruit/assets/icon/FormWithTickSVG';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import {
  checkMatchedNonImage,
  getKeyByNonImgMimeTypesValue,
  imageFileTypeFromConfig,
} from 'components/ImagePicker/config';
import { AxiosError } from 'axios';

export function DocumentsTab() {
  const { handleSubmit } = useForm<NewApplicationFormValues>();

  const route =
    useRoute<RouteProp<RootStackParamListMap['ib'], 'ERecruitApplication'>>();

  const registrationStagingId = route.params?.registrationStagingId ?? '';

  const { data: backendEndAppData } = useGetApplicationData(
    registrationStagingId,
  );

  const { t } = useTranslation('eRecruit');

  const { space, colors, borderRadius } = useTheme();

  const { mutateAsync: saveERApplication, isLoading: saveAppLoading } =
    useSaveERecruitApplicationForm();

  const { data: documentList, isLoading: isDocListLoading } =
    useGetDocumentList({
      agentType: backendEndAppData?.position?.agencyType ?? '',
    });

  const navigation =
    useNavigation<NavigationProp<NewApplicationFormParmaList>>();

  const onValidSubmit = () => {
    // * parsingDocumentData is ONLY to update the stage in the object
    // so data from the hookForm is not used
    const updateData = parsingDocumentData(backendEndAppData);
    saveERApplication(updateData);
    navigation.navigate('newApplicationConsent', { registrationStagingId });
  };

  const onInvalidSubmit: SubmitErrorHandler<NewApplicationFormValues> = error =>
    console.log('🚀 ~ file: DocumentsTab ~ error:', error);

  const checkMandatoryDocument = () =>
    documentList
      ?.filter(item => item.mandatory)
      ?.every(item =>
        backendEndAppData?.documentFiles?.[item.fileKey]
          ? backendEndAppData?.documentFiles?.[item.fileKey]?.length > 0
          : false,
      ) ?? false;

  const totalIncompleteRequiredFields = documentList?.filter(
    doc =>
      doc.mandatory && !backendEndAppData?.documentFiles?.[doc.fileKey]?.length,
  ).length;

  const [deleteUploadedFileModalVisible, setDeleteUploadedFileModalVisible] =
    useState(false);
  const [disableAllButton, setDisableAllButton] = useState(false);

  const ProgressStepBar =
    country == 'id' ? CommonProgressStepBar : MYSProgressStepBar;

  const parsedDocumentList = useMemo(() => {
    if (!documentList) return [];

    const mandatory: typeof documentList = [];
    const optional: typeof documentList = [];

    documentList.forEach(item => {
      if (item.mandatory) {
        mandatory.push(item);
      } else {
        optional.push(item);
      }
    });

    return [...mandatory, ...optional];
  }, [documentList]);

  return (
    <>
      <ProgressStepBar
        isTabGreyWhenDisabled={country == 'id'}
        type="triggerValidationOnly"
        triggerValidation={() =>
          new Promise<boolean>((resolve, reject) => {
            resolve(checkMandatoryDocument());
          })
        }
        onPressValid={() => {
          onValidSubmit();
        }}
        onPressInValid={() => console.log('onPressInValid')}
      />

      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: space[8],
          paddingTop: space[4],
          paddingBottom: space[7],
        }}>
        <Box
          p={space[6]}
          gap={space[2]}
          backgroundColor={colors.background}
          borderRadius={borderRadius['large']}>
          <Row gap={space[2]} alignItems="center">
            <FormWithTickSVG size={space[10]} />
            <Typography.H6 fontWeight="bold">
              {t(`eRecruit.application.documents.doc`)}
            </Typography.H6>
          </Row>
          <H8 color={colors.placeholder}>
            {t(`eRecruit.application.documents.fileSize`)}
          </H8>
          <Column gap={20} />
          {/* ***content section*** */}
          <View>
            {isDocListLoading ? (
              <Box
                marginY={space[2]}
                w={space[6]}
                h={space[6]}
                alignSelf="center">
                <LoadingIndicator size={space[6]} />
              </Box>
            ) : (
              parsedDocumentList?.map((item, index) => (
                <React.Fragment key={`application-${item.fileKey}-${index}`}>
                  <UploadSection
                    disableAllButton={disableAllButton}
                    setDisableAllButton={setDisableAllButton}
                    sectionName={item.fileKey}
                    documentListDetails={item}
                    registrationStagingId={
                      registrationStagingId
                        ? parseInt(registrationStagingId)
                        : null
                    }
                    files={backendEndAppData?.documentFiles}
                    saveERApplication={saveERApplication}
                    applicationFormData={backendEndAppData}
                    saveAppLoading={saveAppLoading}
                  />
                  {parsedDocumentList &&
                    parsedDocumentList.length - 1 !== index && <Separator />}
                </React.Fragment>
              ))
            )}
          </View>
        </Box>
      </ScrollView>

      <View>
        <TabletFooter
          style={{ flexDirection: 'row', justifyContent: 'space-between' }}>
          {country == 'id' ? (
            <Box />
          ) : (
            <XView style={{ justifyContent: 'flex-start', gap: space[5] }}>
              {totalIncompleteRequiredFields !== undefined &&
                totalIncompleteRequiredFields > 0 && (
                  <Row ml={space[1]} alignSelf="center" alignItems="center">
                    <Icon.Warning size={space[6]} />
                    <Box width={sizes[2]} />
                    <LargeBody>
                      {t(
                        'eRecruit.application.personalDetails.incompleteFields',
                        { total: totalIncompleteRequiredFields },
                      )}
                    </LargeBody>
                    <Box width={sizes[4]} />
                    <TouchableOpacity
                      hitSlop={ICON_HIT_SLOP}
                      // onPress={}
                    >
                      <Row alignItems="center">
                        <Label
                          fontWeight="medium"
                          color={colors.palette.fwdAlternativeOrange[100]}>
                          {t(
                            'eRecruit.application.personalDetails.goToTheField',
                          )}
                        </Label>
                        <Icon.ChevronDown
                          fill={colors.palette.fwdAlternativeOrange[100]}
                          size={space[4]}
                        />
                      </Row>
                    </TouchableOpacity>
                  </Row>
                )}
            </XView>
          )}
          <Button
            variant={'primary'}
            text={t(`eRecruit.application.otherDetails.next`)}
            subtext={t(`eRecruit.application.documents.consent`)}
            onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
            disabled={!checkMandatoryDocument()}
            // disabled={!isMandatoryFieldsFilled}
            loading={saveAppLoading}
            style={{ width: space[40], alignSelf: 'flex-end' }}
            textStyle={{ ...tabletStyles.mainButtonText }}
            contentStyle={{ minHeight: space[13] }}
          />
        </TabletFooter>
        <DeleteUploadedFileModal
          visible={deleteUploadedFileModalVisible}
          onCancel={() => setDeleteUploadedFileModalVisible(false)}
          onRemove={() => {
            setDeleteUploadedFileModalVisible(false);
          }}
        />
      </View>
    </>
  );
}

function UploadSection({
  setDisableAllButton,
  disableAllButton,
  sectionName,
  registrationStagingId,
  files,
  applicationFormData,
  documentListDetails,
  saveAppLoading,
  saveERApplication,
}: {
  disableAllButton: boolean;
  setDisableAllButton: React.Dispatch<React.SetStateAction<boolean>>;
  sectionName: DocumentFileKeyType;
  registrationStagingId: number | null;
  files: DocumentFile;
  saveERApplication: UseMutateAsyncFunction<
    SaveApplicationFormResponse,
    unknown,
    ApplicationFormResponds,
    unknown
  >;
  applicationFormData: ApplicationFormResponds | undefined;
  saveAppLoading: boolean;
  documentListDetails: DocumentListDetail;
}) {
  const { t } = useTranslation(['eRecruit', 'common']);
  const { colors, space, borderRadius } = useTheme();
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [deleteUploadedFileModalVisible, setDeleteUploadedFileModalVisible] =
    useState(false);
  const [onSelectItem, setOnSelectItem] = useState({
    fileId: 0,
    fileSize: 0,
    fileType: '',
  });
  const token = useBoundStore(state => state.auth.authInfo.accessToken);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const getImageHeader = {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    Authorization: 'Bearer ' + token,
  };
  const { mutate, isLoading } = usePostDocumentUpload();
  const { mutateAsync: deleteDocument, isLoading: deleteLoading } =
    useDeleteERecruitDocument(registrationStagingId);

  const disabledButton =
    (files?.[sectionName]?.length &&
      files?.[sectionName]?.length >= documentListDetails.imageUploadLimit) ||
    isLoading ||
    saveAppLoading ||
    disableAllButton;

  const isIdnAndBlockUploadForKTP = !(
    country == 'id' && (sectionName as any) == 'agt-200001'
  );
  return (
    <View style={{ gap: space[1] }}>
      <TitleRow>
        <Typography.LargeLabel fontWeight="medium">
          {documentListDetails.enDesc +
            (documentListDetails.mandatory ? '' : ` (optional)`)}
        </Typography.LargeLabel>
        <UploadButton
          selectByLibraryEnabled={isIdnAndBlockUploadForKTP}
          attachmentEnabled={isIdnAndBlockUploadForKTP}
          setDisableAllButton={setDisableAllButton}
          disabled={disabledButton}
          disabledButton={disabledButton}
          isLoading={isLoading}
          text={
            disableAllButton
              ? 'Loading'
              : t(`eRecruit:eRecruit.application.documents.upload`)
          }
          onPress={() => setDisableAllButton(true)}
          pickerOnDone={async ({ file: asset }) => {
            if (!asset) {
              console.error('--- pickerOnDone --- asset is null');
              return;
            }
            const { uri, base64, name: documentName } = asset;

            const b64 = base64.split(',')[1] || base64;
            const sizeInMB = (b64.length * 3) / 4 / (1024 * 1024);
            console.log('local size in mb', sizeInMB);
            if (sizeInMB > 2) {
              Alert.alert(
                `File size is too large. Current file size: ${
                  Math.round(sizeInMB * 10) / 10
                }MB`,
              );
              return;
            }

            if (country === 'id') {
              const isImage = checkMatchedNonImage(documentName) == false;
              const currentFiles = files?.[sectionName] || [];
              const imageCount =
                currentFiles?.filter(f => f.fileType?.startsWith('image/'))
                  ?.length ?? 0;
              const fileCount = (currentFiles?.length ?? 0) - imageCount;

              if (isImage && imageCount >= 5) {
                addErrorBottomToast([
                  {
                    message: t(
                      'eRecruit:eRecruit.application.documents.image.uploadLimit',
                    ),
                  },
                ]);
                return;
              }
              if (!isImage && fileCount >= 1) {
                addErrorBottomToast([
                  {
                    message: t(
                      'eRecruit:eRecruit.application.documents.file.uploadLimit',
                    ),
                  },
                ]);
                return;
              }
            }

            const parsedForm = handleFormDataForUpload({
              asset,
              sectionName,
            });

            mutate(parsedForm, {
              onError: e => {
                console.log('this is the errror', e);
                const errorMessage: { message: string }[] = [];
                if (e instanceof AxiosError && e?.response?.status === 413) {
                  errorMessage.push({
                    message: t('common:upload.invalidFileSize'),
                  });
                } else if (
                  e instanceof AxiosError &&
                  e?.response?.status === 500 &&
                  e?.response?.data?.messageList?.some(msg =>
                    msg.content.includes('File type not match pattern'),
                  )
                ) {
                  errorMessage.push({
                    message: t('common:upload.invalidFileExtension', {
                      fileTypes: '.jpg, .png, .bmp, and .pdf',
                    }),
                  });
                } else {
                  errorMessage.push({
                    message:
                      t('eRecruit:eRecruit.pleaseTryAgainLater') +
                      (build == 'dev' || build == 'sit'
                        ? '---fileType for debug: ' +
                          (getFileType(asset?.uri) ?? '--')
                        : ''),
                  });
                }
                addErrorBottomToast(errorMessage);
                setDisableAllButton(false);
              },
              onSuccess: res => {
                const parsedFiles = formSectionToFilesMap({
                  files,
                  sectionName,
                  fileFromRes: {
                    fileId: res.fileId,
                    fileSize: res.fileSize,
                    fileType: res.fileType,
                  },
                });

                const parsedData = insertDocumentFiles({
                  applicationFormData,
                  documentFiles: parsedFiles,
                });

                if (!parsedData) {
                  console.log(
                    'parsedData from updateApplicationFormData is null',
                  );
                  return;
                }

                saveERApplication(parsedData, {
                  onSuccess: res => {
                    console.log('successful to saveERApplication');
                  },
                });
                setTimeout(() => {
                  setDisableAllButton(false);
                }, 1000);
              },
            });
          }}
        />
      </TitleRow>
      <Row gap={space[6]}>
        {files?.[sectionName]?.map((item, index) => {
          const uploadedUri = `${baseUrl}/api-gateway/proc/recruitment/document/file?registrationStagingId=${registrationStagingId}&fileId=${item.fileId}`;
          const isPdf = item.fileType == 'application/pdf';
          const docTitle = documentListDetails.enDesc;
          const isImage = imageFileTypeFromConfig.includes(
            item.fileType as (typeof imageFileTypeFromConfig)[number],
          );
          return (
            <Box
              alignItems="center"
              flexDirection={isPdf ? 'row' : 'column'}
              gap={isImage ? space[1] : undefined}
              key={item.fileId + item.fileType}>
              <TouchableOpacity
                disabled={!isPdf && !isImage}
                onPress={() => {
                  if (isPdf) {
                    setShowPdfViewer(true);
                  }
                  if (isImage) {
                    setShowImageViewer(true);
                  }
                }}>
                {isImage == false ? (
                  <Row gap={space[1]}>
                    <Icon.Attachment
                      width={space[6]}
                      height={space[6]}
                      fill={colors.palette.fwdDarkGreen[100]}
                    />
                    <Typography.Body>
                      {item.fileId}.
                      {getKeyByNonImgMimeTypesValue(item.fileType)}
                    </Typography.Body>
                  </Row>
                ) : (
                  <ImageContainer>
                    <ReactNativeImage
                      style={{ width: 80, height: 80 }}
                      source={{
                        uri: uploadedUri,
                        method: 'GET',
                        headers: getImageHeader,
                      }}
                    />
                  </ImageContainer>
                )}
              </TouchableOpacity>
              {/* View image modal */}
              <FileInfoAndDeleteRow
                fileSize={item.fileSize}
                isLoading={deleteLoading}
                onDelete={() => {
                  setOnSelectItem(item);
                  setDeleteUploadedFileModalVisible(true);
                }}
                style={
                  isPdf
                    ? {
                        alignItems: 'center',
                        paddingHorizontal: space[5],
                        gap: space[1],
                      }
                    : {
                        justifyContent: 'space-between',
                        width: 81,
                      }
                }
              />
              {showImageViewer && (
                <ZoomableImageViewer
                  uri={uploadedUri}
                  isBase64={false}
                  onDismiss={() => setShowImageViewer(false)}
                />
              )}
              {showPdfViewer && (
                <PdfViewer
                  visible={showPdfViewer}
                  pdfGenerator={() => {
                    return new Promise((resolve, reject) => {
                      resolve({
                        url: uploadedUri,
                        fileName: '',
                        headers: {
                          Authorization: `Bearer ${token}`,
                          'x-agent-id': agentId ?? '',
                        },
                      });
                    });
                  }}
                  onClose={() => setShowPdfViewer(false)}
                  title={docTitle ? `${docTitle}.pdf` : ''}
                />
              )}
            </Box>
          );
        })}
        {isLoading && (
          <ImageContainer>
            <ActivityIndicator size={80} />
          </ImageContainer>
        )}
      </Row>
      <DeleteUploadedFileModal
        visible={deleteUploadedFileModalVisible}
        onCancel={() => setDeleteUploadedFileModalVisible(false)}
        onRemove={() => {
          deleteDocument(onSelectItem.fileId, {
            onSuccess: res => {
              if (res?.result === 'SUCCESS') {
                setDeleteUploadedFileModalVisible(false);
              }
            },
          });
        }}
      />
    </View>
  );
}

function FileInfoAndDeleteRow({
  onDelete,
  isLoading,
  fileSize,
  style,
}: {
  onDelete: () => void;
  isLoading: boolean;
  fileSize: number;
  style?: ViewProps['style'];
}) {
  const { colors, space } = useTheme();

  return (
    <Row style={style}>
      <Typography.SmallBody color={colors.palette.fwdGreyDarkest[100]}>
        {formatBytes(fileSize)}
      </Typography.SmallBody>

      <TouchableOpacity onPress={onDelete}>
        {isLoading ? (
          <LoadingIndicator size={18} />
        ) : (
          <Icon.Delete
            width={18}
            height={18}
            fill={colors.palette.fwdDarkGreen[100]}
          />
        )}
      </TouchableOpacity>
    </Row>
  );
}

function UploadButton(
  props: ButtonProps & {
    setDisableAllButton: React.Dispatch<React.SetStateAction<boolean>>;
    disabledButton: boolean;
    isLoading: boolean;
    pickerOnDone: (result: ImagePickerOnDoneProps) => void;
  } & Pick<ImagePickerProps, 'selectByLibraryEnabled' | 'attachmentEnabled'>,
) {
  const { colors, space, borderRadius } = useTheme();
  const [visibleDocumentUpload, setVisibleDocumentPicker] = useState(false);

  const [buttonPressColor, setButtonPressColor] = useState(colors.primary);
  const { disabledButton, isLoading, pickerOnDone, ...rest } = props;

  return (
    <>
      <Button
        mini
        size="medium"
        icon={() =>
          isLoading ? (
            <LoadingIndicator size={18} color={colors.palette.fwdOrange[50]} />
          ) : (
            <Icon.Upload
              size={18}
              fill={
                // unknown boolean
                disabledButton ? colors.palette.fwdOrange[50] : buttonPressColor
              }
            />
          )
        }
        onPressIn={() => setButtonPressColor(colors.background)}
        onPressOut={() => setButtonPressColor(colors.primary)}
        contentStyle={{
          paddingRight: 0,
          paddingLeft: space[2],
        }}
        textStyle={{ fontSize: sizes[4] }}
        variant="secondary"
        {...rest}
        onPress={e => {
          setVisibleDocumentPicker(true);

          rest?.onPress?.(e);
        }}
      />
      <ImagePicker
        isAllowedToPickerMsftOffice={country == 'id'}
        selectByLibraryEnabled={props.selectByLibraryEnabled}
        attachmentEnabled={props.attachmentEnabled ?? true}
        visible={visibleDocumentUpload}
        maxSizeInMB={2}
        onDismiss={() => {
          setVisibleDocumentPicker(false);
          setTimeout(() => {
            props.setDisableAllButton(false);
          }, 3500);
        }}
        config={{
          compression: 0.5,
          maxHeight: 720,
          maxWidth: 720,
          cameraQuality: 0.7,
        }}
        onDone={pickerOnDone}
      />
    </>
  );
}

const TitleRow = styled(Row)(({ theme }) => ({
  justifyContent: 'space-between',
  alignItems: 'center',
}));

const Separator = styled(View)(({ theme }) => ({
  width: '100%',
  height: 1,
  backgroundColor: '#C4C4C4',
  marginVertical: theme.space[4],
}));

const ImageContainer = styled.View(({ theme }) => ({
  width: theme.sizes[20],
  height: theme.sizes[20],
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.small,
  overflow: 'hidden',
  justifyContent: 'center',
}));
