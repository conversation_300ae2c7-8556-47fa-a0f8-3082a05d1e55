import { View, Text } from 'react-native';
import React from 'react';
import { Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

function Frame({ children }: { children?: React.ReactNode }) {
  const { colors, space, borderRadius } = useTheme();

  return (
    <View
      style={{
        borderWidth: 1,
        padding: space[5],
        gap: space[5],
        borderRadius: borderRadius['large'],
        borderColor: colors.palette.fwdGrey[100],
      }}>
      {children}
    </View>
  );
}

function Title({ children }: { children?: React.ReactNode }) {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const Text =
    country === 'id' && !isTabletMode ? Typography.H7 : Typography.H6;
  return <Text fontWeight="bold">{children}</Text>;
}

function Date({ children }: { children?: React.ReactNode }) {
  const { colors } = useTheme();

  return (
    <Typography.Body fontWeight="normal" color={colors.palette.fwdGreyDarker}>
      {children}
    </Typography.Body>
  );
}

function HeaderNote({ children }: { children?: React.ReactNode }) {
  return <Typography.Body fontWeight="normal">{children}</Typography.Body>;
}

const RecruitmentProgressCard = {
  Frame,
  Title,
  Date,
  HeaderNote,
};

export default RecruitmentProgressCard;
