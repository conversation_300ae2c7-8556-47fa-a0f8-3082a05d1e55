import { Dimensions } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { Box } from 'cube-ui-components';
import RecruitmentProgressCard from 'features/eRecruit/my/tablet/components/RecruitmentProgress/RecruitmentProgressCard';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import Bar<PERSON>hart, {
  BarStyle,
  TargetLineStyle,
  calculateYAxisMax,
  yIntervalHandler,
} from 'components/Chart/BarChart';
import { useTranslation } from 'react-i18next';
import { useGetERecruitStat } from 'features/eRecruit/hooks/useGetERecruitStat';
import { TimeSectionKeys } from 'types/eRecruit';

export default function RecruitmentTarget({
  timeSection,
  isLoading,
}: {
  timeSection: TimeSectionKeys;
  isLoading?: boolean;
}) {
  const timePeriodLabel = timeSection ?? '-';
  const formattedDate = dateFormatUtil(new Date());
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  const { data } = useGetERecruitStat();
  const { width: windowWidth } = Dimensions.get('window');

  const conversionData = data?.performance?.[timeSection]?.conversion;
  const targetData = data?.performance?.[timeSection]?.target;

  const yAxisMax: number = useMemo(() => {
    if (conversionData && targetData) {
      const keysOfConversionData = Object.keys(conversionData) as Array<
        keyof typeof conversionData
      >;
      const keysOfTargetData = Object.keys(targetData) as Array<
        keyof typeof targetData
      >;
      return calculateYAxisMax(
        keysOfConversionData.map(key => conversionData[key]),
        keysOfTargetData.map(key => targetData[key]),
      );
    }
    return 0;
  }, [conversionData, targetData]);

  const chartDataOptions = {
    x: (d: any, i: number) => i,
    y: (d: any) => d.value,
    title: (d: any) => d.name,
    bottomPadding: space[2],
    leftPadding: space[2] + space[2],
    width: windowWidth / 2.5 + space[2],
    height: windowWidth / 5,
    yInterval: yIntervalHandler(yAxisMax),
    xPadding: 0,
    color: colors.palette.fwdOrange[100],
    internalPadding: space[7],
  };

  const barChartData = [
    {
      name: t('eRecruit.candidate'),
      value: conversionData?.candidate ?? 0,
      color: colors.palette.fwdOrange[20],
      target: targetData?.candidate ?? 0,
    },
    {
      name: t('eRecruit.submitted'),
      value: conversionData?.submitted ?? 0,
      color: colors.palette.fwdOrange[50],
      target: targetData?.submitted ?? 0,
    },
    {
      name: t('eRecruit.approved'),
      value: conversionData?.approve ?? 0,
      color: colors.palette.fwdOrange[100],
      target: targetData?.approve ?? 0,
    },
  ];

  const targetLabel = `- ${
    timeSection === 'week'
      ? 'Weekly'
      : timeSection === 'month'
      ? 'Monthly'
      : '--'
  } Target`;

  return (
    <RecruitmentProgressCard.Frame>
      <Box>
        <RecruitmentProgressCard.Title>
          {t('eRecruit.targetVsActualNumber', {
            timePeriodLabel: timePeriodLabel,
          })}
        </RecruitmentProgressCard.Title>
        <RecruitmentProgressCard.Date>
          {t('eRecruit.asOf')} {formattedDate}
        </RecruitmentProgressCard.Date>
        <RecruitmentProgressCard.HeaderNote>
          {targetLabel}
        </RecruitmentProgressCard.HeaderNote>
      </Box>

      <BarChart
        data={barChartData}
        dataOptions={chartDataOptions}
        xlabelWidth={70}
        barWidthInput={space[13]}
        barTopBorderStyle={BarStyle.curved}
        targetLineStyle={TargetLineStyle.dashed}
        isWidthOffset={false}
        isLoading={isLoading}
      />
    </RecruitmentProgressCard.Frame>
  );
}
