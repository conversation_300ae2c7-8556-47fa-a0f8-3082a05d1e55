export default {
  filterBy: 'Filter by',
  total: 'Total',
  newest: 'Newest',
  oldest: 'Oldest',
  review: 'Review',
  reject: 'Reject',
  approve: 'Approve',
  reason: 'Reason',
  emptyRecord: 'Empty record',
  confirm: 'Confirm',
  cancel: 'Cancel',
  status: 'Status',
  optional: 'Optional',
  //
  'banner.title': 'Recruitment',
  'banner.slogan': 'Build a strong team',
  //
  'title.overview': 'Overview',
  'title.candidates': 'Candidates',
  'title.materials': 'Materials',
  'title.share': 'Share recruitment link',
  //
  searchCandidate: 'Search candidate',
  recentSearchTitle: 'Recent search',
  //
  'overview.mtd': 'MTD',
  'overview.mtd.statusTitle': 'Month to date applicant status',
  'overview.mtd.chartTitle': 'Month to month new recruit count',
  'overview.ytd': 'YTD',
  'overview.ytd.statusTitle': 'Year to date applicant status',
  'overview.ytd.chartTitle': 'Year to year new recruit count',
  'overview.status.gybAttendees': 'GYB attendees',
  'overview.status.examPassers': 'Exam passers',
  'overview.status.examRegistered': 'Exam registered',
  'overview.status.docCompletion': 'Document completion',
  'overview.status.training': 'Training',
  'overview.status.inactive': 'Inactive',
  'overview.chart.data.codedAgent': 'Coded agent',
  'overview.chart.data.toGoAgent': 'To-go agent',
  //
  'candidates.toDo': 'To-do',
  'candidates.approved': 'Approved',
  'candidates.declined': 'Declined',
  'candidates.pendingLicense': 'Pending license',
  'candidates.pendingLicenseType': 'Pending license type',
  'candidates.pendingLicenseType.dual': 'Dual',
  'candidates.pendingLicenseType.traditional': 'Traditional',
  'candidates.pendingLicenseType.variable': 'Variable',
  'candidates.allStatusCompleted': 'All status completed',
  'candidates.displayDataDuration': 'Displaying data from last 4 months',
  'candidates.recruitBy': 'Recruit by',
  'candidates.dueOnAt': 'Due on {{dueDate}} at {{dueTime}}',
  'candidates.dueTodayAt': 'Due today at {{dueTime}}',
  'candidates.profile.title': 'Candidate profile',
  'candidates.profile.candidateStatus': 'Candidate status',
  'candidates.review.title': 'Review application',
  'candidates.review.personalInfo': 'Personal information',
  'candidates.review.personalInfo2': 'Personal info',
  'candidates.profile.completed': 'completed',
  'candidates.profile.declined': 'Declined',
  'candidates.profile.declinedOn': 'Declined on {{declinedDate}}',
  'candidates.profile.declinedReason': 'Declined reason',
  'candidates.review.fullName': 'Full name',
  'candidates.review.gender': 'Gender',
  'candidates.review.dob': 'Date of birth',
  'candidates.review.contactNumber': 'Contact no.',
  'candidates.review.email': 'Email',
  'candidates.review.recruiter': 'Recruiter',
  'candidates.approve.title': 'Approve application',
  'candidates.approve.recruitSource': 'Recruit source',
  'candidates.approve.selectOneOfTheFollowing':
    'Please select one of the following',
  'candidates.approve.agentType': 'Agent type',
  'candidates.approve.designation': 'Designation',
  'candidates.approve.successToastMessage': 'Application marked as approved.',
  'candidates.reject.title': 'Reject application',
  'candidates.reject.subTitle': 'Please tell us the reason to reject',
  'candidates.reject.successToastMessage': 'Application is Declined.',
  //
  'materials.category.recruitment': 'Recruitment',
  'materials.category.gyb': 'GYB Presenter',
  'materials.category.agent_to_agent': 'Agent to agent Presenter',
  'materials.fileType.video': 'Video',
  'materials.fileType.pdf': 'PDF',
  'materials.share': 'Share',
  'materials.search.placeholder': 'Search materials',
  'materials.search.total': 'Total',
  'materials.search.result': 'Result',
  'materials.search.searchResult': 'Search result',
  'materials.sort.newest': 'Newest',
  'materials.sort.oldest': 'Oldest',
  'materials.filter': 'Filter by',
  'materials.filter.type': 'Material type',
  'materials.filter.label.recruitment': 'Recruitment',
  'materials.filter.label.gyb': 'GYB Presenter',
  'materials.filter.label.agentToAgent': 'Agent to agent Presenter',
  'materials.filter.reset': 'Reset',
  'materials.filter.apply': 'Apply',
  'materials.networkAlert':
    'No WiFi Connection. You are not currently connected to a WiFi network. Please be aware that streaming video may consume significant mobile data.',
  'materials.search.noResult': 'No results found, try another filter',
  //
  'GYBAttendees.attendee': 'Attendee',
  'GYBAttendees.recruiterLeader': 'Recruiter, Leader',
  'GYBAttendees.attendedDate': 'Date attended',
  'GYBAttendees.recruiter': 'Recruiter',
  'GYBAttendees.leader': 'Leader',
  'GYBAttendees.export': 'Export',
  'GYBAttendees.MTD': 'MTD',
  'GYBAttendees.YTD': 'YTD',
  //
  'recruitmentLink.title': 'Share your recruitment link',
  'recruitmentLink.copyLink': 'Copy link',
  'recruitmentLink.copied': 'Copied',
  'recruitmentLink.linkCopied': 'Link copied',
  'recruitmentLink.share': 'Share',
};
