export default {
  'eRecruit.home': 'Home',
  'eRecruit.searchCandidate': 'Search candidate',
  'eRecruit.recruitment': 'Recruitment',
  'eRecruit.application.shareLink': 'Share remote link',
  'eRecruit.application.startApplication': 'Start a new application',
  'eRecruit.application.applicationStatus': 'Application status',
  'eRecruit.application.viewAll': 'View all',
  'eRecruit.application.remote': 'Remote',
  'eRecruit.application.newApplication': 'New application',
  'eRecruit.application.shareApplicationLink': 'Share application link',
  'eRecruit.application.shareApplicationLink.modal.': 'Share application link',
  'eRecruit.remote.modal.title': 'Share application link',
  'eRecruit.remote.modal.description':
    'To ensure the recruitment is under you or your team, please share this unique application link to your candidate.',
  'eRecruit.remote.linkCopied': 'Link copied.',
  'eRecruit.application.sms': 'SMS',
  'eRecruit.application.copyLink': 'Copy Link',
  'eRecruit.application.others': 'More',

  'eRecruit.recruitmentProgress': 'Recruitment progress',
  'eRecruit.week': 'This week',
  'eRecruit.month': 'This month',
  'eRecruit.candidate': 'Candidate',
  'eRecruit.candidates': 'Candidates',
  'eRecruit.submitted': 'Submitted',
  'eRecruit.approved': 'Approved',
  'eRecruit.recruitmentConversionThis': 'Recruitment Conversion This',
  'eRecruit.asOf': 'As of',
  'eRecruit.candidatesInProgress': 'Candidate in progress',
  'eRecruit.targetVsActualNumber':
    'This {{timePeriodLabel}} target VS actual number',

  'eRecruit.targetOfThis': 'Target of this',
  'eRecruit.this': 'This',
  'eRecruit.operationData': 'Oops! Empty operation data.',
  'eRecruit.noTarget': 'No target has been set up yet.',
  'eRecruit.edit': 'Edit',
  'eRecruit.thisCycle': 'This {{timeSection}} cycle',
  'eRecruit.pleaseTryAgainLater': 'Please try again later',
  'eRecruit.ok': 'Ok',

  'eRecruit.candidate.filterBy': 'Filter by',
  'eRecruit.candidate.sortBy': 'Sort by',
  'eRecruit.candidate.latestUpdated': 'Latest updated',
  'eRecruit.candidate.latestCreated': 'Latest created',
  'eRecruit.candidate.candidates': 'Candidates',
  'eRecruit.candidate.pendingPayment': 'Pending payment',
  'eRecruit.candidate.pendingLeaderApproval': 'Pending leader approval',
  'eRecruit.candidate.approved': 'Approved',
  'eRecruit.candidate.rejected': 'Rejected',
  'eRecruit.candidate.declined': 'Declined',
  'eRecruit.candidate.remoteChecking': 'Remote checking',
  'eRecruit.candidate.remoteSignature': 'Remote signature',
  'eRecruit.candidate.addNewCandidate': 'Add new candidate',
  'eRecruit.candidate.firstName': 'First Name',
  'eRecruit.candidate.lastName': 'Last Name',
  'eRecruit.candidate.fullName': 'Full Name',
  'eRecruit.candidate.gender': 'Gender',
  'eRecruit.candidate.male': 'Male',
  'eRecruit.candidate.female': 'Female',
  'eRecruit.candidate.code': 'Code',
  'eRecruit.candidate.phoneNumber': 'Phone Number',
  'eRecruit.candidate.email': 'Email',
  'eRecruit.candidate.cancel': 'Cancel',
  'eRecruit.candidate.save': 'Save',
  'candidates.profile.declined': 'Declined',
  'eRecruit.candidateProfile': 'Candidate Profile',
  'eRecruit.candidateProfile.profile': 'Profile',
  'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided':
    'Phone number and email are not provided',
  'eRecruit.candidateProfile.personalInfo': 'Personal info',

  'eRecruit.candidateProfile.name': 'Name',
  'eRecruit.candidateProfile.gender': 'Gender',
  'eRecruit.candidateProfile.contactNo': 'Contact no.',
  'eRecruit.candidateProfile.email': 'Email',
  'eRecruit.candidateProfile.source': 'Source',
  'eRecruit.candidateProfile.candiateNo': 'Candidate no.',
  'eRecruit.candidateProfile.paymentLink': 'Payment Link',
  'eRecruit.candidateProfile.remoteSignatureLink': 'Remote signature link',
  'eRecruit.candidateProfile.shareLinkToComplete': 'Share link to complete',

  'eRecruit.shareRemoteLink.sms': 'SMS',
  'eRecruit.shareRemoteLink.email': 'Email',
  'eRecruit.shareRemoteLink.more': 'More',
  'eRecruit.shareRemoteLink.copyLink': 'Copy link',
  'eRecruit.shareRemoteLink.whatsapp': 'WhatsApp',
  'eRecruit.shareRemoteLink.shareLinkToComplete': 'Share link to complete',
  'eRecruit.shareRemoteLink.shareLinkToComplete.desc':
    'Please share this remote signature link to your candidate.',
  'eRecruit.shareRemoteLink.linkIsNotAvailable':
    'Link is not available. Please try again later.',
  'eRecruit.shareRemoteLink.linkCopied': 'Link Copied',
  'eRecruit.shareRemoteLink.msg':
    'Please share this remote signature link to your candidate.',
  'eRecruit.shareRemoteLink.msg,ifNotReceive':
    'Please send this link to your candidates if they cannot receive it.',

  'eRecruit.candidateProfile.createDate': 'Create date',
  'eRecruit.candidateProfile.candidateStatus': 'Candidate status',
  'eRecruit.candidateProfile.applicationStatus': 'Application status',
  'eRecruit.candidateProfile.status': 'Status',
  'eRecruit.candidateProfile.updateDate': 'update date',
  'eRecruit.candidateProfile.visited': 'Visited',
  'eRecruit.candidateProfile.remoteApplicationDone': 'Remote application done',
  'eRecruit.candidateProfile.agentReviewSubmit': 'Agent review and submit',
  'eRecruit.candidateProfile.signedAndSubmitted': 'Signed and submitted',
  'eRecruit.candidateProfile.applicationSubmitted':
    'Application form completed',
  'eRecruit.candidateProfile.applicationInProgress': 'Application in progress',
  'eRecruit.candidateProfile.candidateSignatureSubmitted':
    'Candidate signature submitted',
  'eRecruit.candidateProfile.candidateSignature': 'Candidate signature',
  'eRecruit.candidateProfile.awaitingCandidateSignature':
    'Awaiting candidate signature',
  'eRecruit.candidateProfile.paid': 'Paid',
  'eRecruit.candidateProfile.pendingPayment': 'Pending payment',
  'eRecruit.candidateProfile.leaderRejected': 'Leader rejected',
  'eRecruit.candidateProfile.leaderApproved': 'Leader approved',
  'eRecruit.candidateProfile.pendingLeaderApproval': 'Pending leader approval',
  'eRecruit.candidateProfile.leaderApproval': 'Leader approval',
  'eRecruit.candidateProfile.resumeApplicationWarning':
    'This application is not completed yet. Please resume application.',
  'eRecruit.candidateProfile.remoteSignatureWarning':
    'This application is pending candidate to sign remotely. Send the link again.',

  'eRecruit.candidateProfile.removeCandidate': 'Remove Candidate',
  'eRecruit.candidateProfile.removecandidate': 'Remove candidate',
  'eRecruit.candidateProfile.candidateIsRemove': 'Candidate is removed.',
  'eRecruit.candidateProfile.candidateIsRemoveFail':
    'Error while removing candidate. Please try again.',
  'eRecruit.candidateProfile.areYouSure':
    'Are you sure to remove? Data cannot be recover once it is removed',
  'eRecruit.candidateProfile.cancel': 'Cancel',
  'eRecruit.candidateProfile.yes': 'Yes, I am sure',
  'eRecruit.candidateProfile.no': 'Go back',
  'eRecruit.candidateProfile.shareRemoteLink': 'Share Remote Link',
  'eRecruit.candidateProfile.resumeApplication': 'Resume application',
  'eRecruit.candidateProfile.checkApplication': 'Check application',
  'eRecruit.candidateProfile.startApplication': 'Start application',
  'eRecruit.candidateProfile.linkCopied': 'Link Copied',
  'eRecruit.candidateProfile.agentCodeIssued': 'Agent code issued',
  'eRecruit.candidateProfile.agentCode': 'Agent code',
  'eRecruit.candidateProfile.licenseActivated': 'License activated',
  'eRecruit.candidateProfile.approvedBy': 'Approved by',
  'eRecruit.candidateProfile.rejectedBy': 'Rejected by',
  'eRecruit.candidateProfile.progressBar.await':
    'Application awaits - please complete the form',
  'eRecruit.candidateProfile.progressBar.pendingSignature':
    'Application awaits - pending candidate signature',
  'eRecruit.candidateProfile.progressBar.pendingApproval':
    'Application nearly finished - pending leader approval',
  'eRecruit.candidateProfile.progressBar.licensingCompletion':
    'Application is approved, but requires agent licensing completion',
  'eRecruit.candidateProfile.progressBar.readyToGo':
    "Agent activated - you're ready to go!",
  'eRecruit.candidateProfile.progressBar.inactiveAccount':
    'Agent account inactive',
  'eRecruit.candidateProfile.progressBar.activeAccount':
    'Agent account active!',
  'eRecruit.candidateProfile.reviewerComment': "Reviewer's comment",
  'eRecruit.candidateProfile.autoDeclinedOn': 'Auto declined on {{date}}',
  'eRecruit.candidateProfile.createdOn': 'Created on {{date}}',

  // Application Form - Personal Details
  'eRecruit.application.personalDetails.identityDetails': 'Identity details',
  'eRecruit.application.personalDetails.name': 'Name',
  'eRecruit.application.personalDetails.fullName': 'Full name',

  'eRecruit.application.personalDetails.firstName': 'First name',
  'eRecruit.application.personalDetails.lastName': 'Last name',
  'eRecruit.application.personalDetails.gender': 'Gender',
  'eRecruit.application.personalDetails.title': 'Title',
  'eRecruit.application.personalDetails.dateOfBirth': 'Date of Birth',
  'eRecruit.application.personalDetails.age': 'Age',
  'eRecruit.application.personalDetails.icNumber': 'Identity number',
  'eRecruit.application.personalDetails.icNumber.optional':
    'NRIC number (optional)',
  'eRecruit.application.personalDetails.identity': 'Identity',
  'eRecruit.application.personalDetails.birthPlace': 'Birth place',

  'eRecruit.application.personalDetails.citizen': 'Citizen',
  'eRecruit.application.personalDetails.ethnicity': 'Ethnicity',
  'eRecruit.application.personalDetails.religion': 'Religion',
  'eRecruit.application.personalDetails.maritalStatus': 'Marital status',
  'eRecruit.application.personalDetails.numberOfDependents':
    'Number of dependents',
  'eRecruit.application.personalDetails.npwp': 'NPWP',
  'eRecruit.application.personalDetails.npwp.optional': 'NPWP (optional)',
  'eRecruit.application.personalDetails.npwp.hint': '16-digit numerals',
  'eRecruit.application.personalDetails.passport': 'Passport number (optional)',
  'eRecruit.application.personalDetails.passportNumber': 'Passport number',
  'eRecruit.application.personalDetails.oldIc': 'Old IC/Police/Army (optional)',
  'eRecruit.application.personalDetails.oldIc/Police/Army':
    'Old IC/Police/Army',
  'eRecruit.application.personalDetails.incomeTax': 'Income tax file number',
  'eRecruit.application.personalDetails.incomeTaxFileNumber':
    'Income tax file number',
  'eRecruit.application.personalDetails.yearsOld': ' ({{age}} years old)',
  'eRecruit.application.personalDetails.contactDetails': 'Contact details',
  'eRecruit.application.personalDetails.code': 'Code',
  'eRecruit.application.personalDetails.mobileNumber': 'Mobile number',
  'eRecruit.application.personalDetails.phoneNumber': 'Phone number',
  'eRecruit.application.personalDetails.address': 'Address',
  'eRecruit.application.personalDetails.email': 'Email',
  'eRecruit.application.personalDetails.officePhoneOptional':
    'Office phone (optional)',
  'eRecruit.application.personalDetails.officePhone': 'Office phone',
  'eRecruit.application.personalDetails.qualification': 'Qualification',
  'eRecruit.application.personalDetails.academicQualification':
    'Academic qualification',
  'eRecruit.application.personalDetails.education': 'Education',
  'eRecruit.application.personalDetails.industry': 'Industry',
  'eRecruit.application.personalDetails.presentOccupation':
    'Present Occupation',

  'eRecruit.application.personalDetails.takaful':
    'Takaful certificate (Choose 1 or more)',
  'eRecruit.application.personalDetails.tbeFamily': 'TBE Family',
  'eRecruit.application.personalDetails.tbeGeneral': 'TBE General',
  'eRecruit.application.personalDetails.insuranceCertificate':
    'Insurance certificate',
  'eRecruit.application.personalDetails.insuranceCertificate.oneOrMore':
    'Insurance certificate (Choose 1 or more)',

  'eRecruit.application.personalDetails.pce': 'PCE',
  'eRecruit.application.personalDetails.ceilli': 'CEILLI',
  'eRecruit.application.personalDetails.general': 'General',
  'eRecruit.application.personalDetails.otherInsuranceQualifications':
    'Other insurance qualifications',
  'eRecruit.application.personalDetails.otherInsuranceQualifications.oneOrMore':
    'Other insurance qualifications (Choose 1 or more)',
  'eRecruit.application.personalDetails.islamic': 'Islamic RFP/CFP/ChFC',
  'eRecruit.application.personalDetails.module2MFPC':
    'Module 2 (Risk Management and Takaful Planning) - Shariah Registered Financial Planner offered by MFPC',
  'eRecruit.application.personalDetails.yearOfPassing': 'Year of passing',
  'eRecruit.application.personalDetails.module2FPAM':
    'Module 2 (Risk Management and Takaful Planning) - Islamic Financial Planning offered by FPAM',
  'eRecruit.application.personalDetails.otherQualifications':
    'Other professional qualifications',
  'eRecruit.application.personalDetails.spouseInformation':
    'Spouse information',
  'eRecruit.application.personalDetails.numOfDependents':
    'Number of dependents',
  'eRecruit.application.personalDetails.occupation': 'Occupation',
  'eRecruit.application.personalDetails.occupationDetails':
    'Occupation details',
  'eRecruit.application.personalDetails.popup.title': 'TBE Family',
  'eRecruit.application.personalDetails.popup.content':
    'To process the application, candidate must have to Takaful TBE Family certificate. Is your candidate qualified with TBE Family certificate?',
  'eRecruit.application.personalDetails.popup.content.V2': `To complete the application, candidates must have PCE certification. Selecting "Yes" will automatically update the candidate's PCE status.`,
  'eRecruit.application.personalDetails.popup.title.PCE': 'PCE',
  'eRecruit.application.personalDetails.popup.title.PCE.V2':
    'Is your candidate PCE-certified?',
  'eRecruit.application.personalDetails.targetCert.PCE': 'PCE',
  'eRecruit.application.personalDetails.targetCert.TakafulTBEFamily':
    'Takaful TBE Family',
  'eRecruit.application.personalDetails.popup.contentWithTargetCert':
    'To process the application, candidate must have to {{target}} certificate. Is your candidate qualified with {{target}} certificate?',
  'eRecruit.application.personalDetails.popup.yes': 'Yes. TBE Family Qualified',
  'eRecruit.application.personalDetails.popup.yesWithTargetCert':
    'Yes. {{target}} Qualified',
  'eRecruit.application.personalDetails.popup.yesWithTargetCert.V2':
    'Yes, PCE Qualified',
  'eRecruit.application.personalDetails.popup.no': 'No. End the application',
  'eRecruit.application.personalDetails.popup.no.V2': 'No, exit application',
  'eRecruit.application.personalDetails.goToTheField': 'Go to the field',
  'eRecruit.application.personalDetails.incompleteFields':
    '{{total}} incomplete fields',
  'eRecruit.application.personalDetails.nricPlaceholder': 'YYMMDD - PB - 000G',
  'eRecruit.application.personalDetails.nricHint': 'YYMMDD-PB-###G',
  'eRecruit.application.personalDetails.insuranceExperience':
    'Insurance experience',
  'eRecruit.application.personalDetails.questionOne':
    'Have you worked in a life insurance company?',
  'eRecruit.application.personalDetails.questionTwo':
    'Have you worked in a non-life insurance company?',
  'eRecruit.application.personalDetails.question.yes': 'Yes',
  'eRecruit.application.personalDetails.question.no': 'No',
  'eRecruit.application.personalDetails.agentApproval.withEmailParam':
    'Agent candidates must complete a binding approval letter from the current employer by sending them to <emailLink>{{email}}</emailLink> or via FWD Ping!',
  'eRecruit.application.personalDetails.submissionError':
    'Sorry, we cannot proceed',
  'eRecruit.application.personalDetails.backToHome': 'Back to home',

  // Application Form - Occupation Details
  'eRecruit.application.dateHint': 'DD/MM/YYYY',
  'eRecruit.application.occupationDetails.currentOccupation':
    'Current occupation',
  'eRecruit.application.occupationDetails.currentOccupation(optional).phone':
    'Current occupation\n(optional)',
  'eRecruit.application.occupationDetails.position': 'Position held',
  'eRecruit.application.occupationDetails.nameOfCompany(inFull)':
    'Name of company (in full)',
  'eRecruit.application.occupationDetails.companyName': 'Name of company',
  'eRecruit.application.occupationDetails.basicSalary(RM)':
    'Last draw salary (RM)',
  'eRecruit.application.occupationDetails.basicSalary': 'Last draw salary',
  'eRecruit.application.occupationDetails.dateAppoint': 'Date appointed',
  'eRecruit.application.occupationDetails.dateTermination': 'Date termination',
  'eRecruit.application.occupationDetails.companyPhoneCountryCode': 'Code',
  'eRecruit.application.occupationDetails.contactNumber': 'Contact number',
  'eRecruit.application.occupationDetails.contactNo': 'Contact no',
  'eRecruit.application.occupationDetails.companyEmail': 'Email',
  'eRecruit.application.occupationDetails.companyAddress': 'Company address',
  'eRecruit.application.occupationDetails.previousOccupation':
    'Previous Occupation',
  'eRecruit.application.occupationDetails.previousOccupation(optional)':
    'Previous Occupation (optional)',
  'eRecruit.application.occupationDetails.previousOccupation(optional).phone':
    'Previous occupation\n(optional)',
  'eRecruit.application.occupationDetails.company': 'Company',
  'eRecruit.application.occupationDetails.add': 'Add',
  'eRecruit.application.occupationDetails.family(optional)':
    'Family / General Takaful Experience (optional)',
  'eRecruit.application.occupationDetails.family(optional).phone':
    'Family / General Takaful\nExperience (optional)',
  'eRecruit.application.occupationDetails.family':
    'Family / General Takaful Experience',
  'eRecruit.application.occupationDetails.typeOfIntermediary':
    'Type of intermediary',
  'eRecruit.application.occupationDetails.rank': 'Rank',
  'eRecruit.application.occupationDetails.lifeInsurance(optional)':
    'Life / General Insurance Experience (optional)',
  'eRecruit.application.occupationDetails.lifeInsurance(optional).phone':
    'Life / General Insurance\nExperience (optional)',
  'eRecruit.application.occupationDetails.lifeInsurance':
    'Life / General Insurance Experience',
  'eRecruit.application.occupationDetails.spouseInsurance':
    'Spouse Insurance Experience',
  'eRecruit.application.occupationDetails.spouseTakaful':
    'Is your spouse at present representing any Takaful or Insurance company',
  'eRecruit.application.occupationDetails.spouseTakafulState':
    'Your spouse at present is representing any Takaful or Insurance company',
  'eRecruit.application.occupationDetails.yes': 'Yes',
  'eRecruit.application.occupationDetails.no': 'No',
  'eRecruit.application.occupationDetails.otherDetails': 'Other details',

  // Application Form - Other Details
  'eRecruit.application.otherDetails.otherDetails': 'Other details',
  'eRecruit.application.otherDetails.saveForLater': 'Save for later',
  'eRecruit.application.otherDetails.next': 'Next',
  'eRecruit.application.otherDetails.reviewDocuments': 'Review documents',
  'eRecruit.application.otherDetails.yourDecision': 'Your decision',
  'eRecruit.application.otherDetails.review': 'Review',
  'eRecruit.application.otherDetails.addressInformation': 'Address information',
  'eRecruit.application.otherDetails.residentialAddress': 'Residential Address',
  'eRecruit.application.otherDetails.malaysia': 'Malaysia',
  'eRecruit.application.otherDetails.businessAddress': 'Business Address',
  'eRecruit.application.otherDetails.nationalIdAddress':
    'National Identity Address',
  'eRecruit.application.otherDetails.sameAsResidentialAddress':
    'Same as residential address',
  'eRecruit.application.otherDetails.addressLine': 'Address line',

  'eRecruit.application.otherDetails.addressLine1': 'Address line 1',
  'eRecruit.application.otherDetails.addressLine2': 'Address line 2',
  'eRecruit.application.otherDetails.addressLine2.optional':
    'Address line 2 (optional)',
  'eRecruit.application.otherDetails.addressLine1.hint':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'eRecruit.application.otherDetails.addressLine2.hint':
    'Street no./Street Name',
  'eRecruit.application.otherDetails.neighborhoodAssociation': 'RT',
  'eRecruit.application.otherDetails.communityAssociation': 'RW',
  'eRecruit.application.otherDetails.subdistrict': 'Subdistrict/ Village',
  'eRecruit.application.otherDetails.district': 'District',
  'eRecruit.application.otherDetails.province': 'Province',
  'eRecruit.application.otherDetails.residenceNumber': 'Residence Number',
  'eRecruit.application.otherDetails.residenceNumber.optional':
    'Residence Number (optional)',

  'eRecruit.application.otherDetails.residentialAddress.postCode': 'Postcode',
  'eRecruit.application.otherDetails.residentialAddress.city': 'City',
  'eRecruit.application.otherDetails.residentialAddress.province': 'Province',
  'eRecruit.application.otherDetails.residentialAddress.state': 'State',
  'eRecruit.application.otherDetails.agencyType': 'Agency Type',
  'eRecruit.application.otherDetails.bankAccountInformation':
    'Bank account information',
  'eRecruit.application.otherDetails.emergencyContact': 'Emergency contact',
  'eRecruit.application.otherDetails.emergencyContact.optional':
    'Emergency contact (Optional)',
  'eRecruit.application.otherDetails.financialCondition': 'Financial condition',
  'eRecruit.application.otherDetails.complianceAndReputationRecords':
    'Compliance And Reputation Records',
  'eRecruit.application.otherDetails.complianceReputationRecords':
    'Compliance & reputation records',
  'eRecruit.application.otherDetails.bankName': 'Bank name',
  'eRecruit.application.otherDetails.branch': 'Branch',
  'eRecruit.application.otherDetails.bankInfoQuestionOne':
    '1.  For the convenience and smoothness of the commission payment process, it is recommended to use the CIMBA NAIGA payroll account.',
  'eRecruit.application.otherDetails.bankInfoQuestionTwo':
    '2.  Receipt of commissions to accounts other than CIMBA NAIGA will be subjected to a valid transfer fee.',

  'eRecruit.application.otherDetails.accountNumber': 'Account number',
  'eRecruit.application.otherDetails.NRIC': 'NRIC number as per bank’s record',
  'eRecruit.application.otherDetails.supervisorCandidateInformation':
    'Supervisor & candidate information',
  'eRecruit.application.otherDetails.candidateInformation':
    'Candidate position',
  'eRecruit.application.otherDetails.salesOffice':
    'Sales office/GA/Service point',
  'eRecruit.application.otherDetails.domicile': 'Domicile',
  'eRecruit.application.otherDetails.areaManager': 'Area manager',
  'eRecruit.application.otherDetails.supervisor': 'Supervisor',
  'eRecruit.application.otherDetails.ref': 'Ref',
  'eRecruit.application.otherDetails.financingProgram': 'Financing program',
  'eRecruit.application.otherDetails.comment': 'Comments',
  'eRecruit.application.otherDetails.comment.optional': 'Comments (optional)',
  'eRecruit.application.otherDetails.comment.label':
    'Please leave your comment',
  'eRecruit.application.otherDetails.regulatory.comment.label':
    'Leave your comment',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.jobType':
    'Job type',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.agencyType':
    'Agency Type',
  'eRecruit.application.otherDetails.declarationOfCOI':
    'Declaration of Conflict of Interest',
  'eRecruit.application.otherDetails.declarationOfCOI.phone':
    'Declaration of Conflict of\nInterest',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch':
    'Reporting Branch',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition':
    'Candidate position',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName':
    'Introducer Name',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode':
    'Introducer code',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.branchCode':
    'Branch code',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.branchName':
    'Branch name',
  'eRecruit.application.otherDetails.leaderInformation': 'Leader information',
  'eRecruit.application.otherDetails.leaderInformation.optional':
    'Leader Information\n(optional)',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderName':
    'Superior name',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderCode':
    'Superior code',
  'eRecruit.application.otherDetails.leaderInformation.ALCFWDName':
    'ALC/ FA name',
  'eRecruit.application.otherDetails.leaderInformation.ALCFWDName.optional':
    'ALC/ FA name (optional)',
  'eRecruit.application.otherDetails.leaderInformation.check': 'Check',
  'eRecruit.application.otherDetails.uploadDocument': 'Upload Document',
  'eRecruit.application.otherDetails.remark': 'Remark',
  'application.otherDetails.agencyManagerCode': 'Agency manager code',
  'application.otherDetails.agencyManagerName': 'Agency manager name',
  'application.otherDetails.agentCodeVerifyMsg.success': 'Verify success',
  'application.otherDetails.agentCodeVerifyMsg.fail': 'Invalid superior code',
  'application.otherDetails.remark.optional': 'Remarks (optional)',
  'application.otherDetails.remark': 'Remarks',

  // Application Form - Declaration of COI
  'application.COI.record': 'Record {{number}}',
  'application.COI.addRecord': 'Add record',
  'application.COI.ownershipInterest': 'Ownership Interest',
  'application.COI.externalEmployment': 'External Directorship / Employment',
  'application.COI.businessAffiliationInterest':
    'Business Affiliations Interests',
  'application.COI.relationshipGovernmentOfficial':
    'Relationships with Government Officials',
  'application.COI.otherInterest':
    'Other Perceived, Potential or Actual Conflicts of Interest',
  'application.COI.nameOfBusinessEnterpriseOrEntity':
    'Name of business enterprise / entity',
  'application.COI.natureOfBusiness': 'Nature of business',
  'application.COI.nameOfOwner': 'Name of owner',
  'application.COI.nameOfOwnerAndRelationship':
    'Name of owner and relationship',
  'application.COI.relationship': 'Relationship',
  'application.COI.dateAcquired': 'Date acquired',
  'application.COI.percentageOfOwnership': 'Percentage of ownership',
  'application.COI.position': 'Position',
  'application.COI.details': 'Details',
  'application.COI.detailsDescription':
    'e.g. Job Description, Time Involved, Existing / Proposed',
  'application.COI.compensationReceived': 'Compensation received',
  'application.COI.yes': 'Yes',
  'application.COI.no': 'No',
  'application.COI.nameOfFamilyMember': 'Name of family member',
  'application.COI.nameOfFamilyMemberAndRelationship':
    'Name of family member and relationship',
  'application.COI.positionDepartment': 'Position and department',
  'application.COI.dateCommencementEmployment':
    'Date of commencement of employment',
  'application.COI.nameOfGovernment':
    'Name of the government entity/ organisation',
  'application.COI.relationshipWithGovOfficials':
    'Relationship with immediate family member',
  'application.COI.otherDetails': 'Other details',
  'application.COI.declarationTitle': 'Declaration',
  'application.COI.declarationMsg':
    'I affirm that the above information is true, complete and correct as of the date below. I understand that I am under an obligation during my employment with the Company to obtain the approval of my Department Head and Group Compliance prior to engaging in commercial activities outside FWD. I also agree to notify the Company promptly if there is a change in any of the above details.',

  // Application Form - Review Sheet
  'eRecruit.application.review.reviewInformation': 'Review information',
  'eRecruit.application.review.personalDetails': 'Personal details',
  'eRecruit.application.review.occupationDetails': 'Occupation details',
  'eRecruit.application.review.candidatePositionAndOtherDetails':
    'Candidate position & other details',
  'eRecruit.application.review.otherDetails': 'Other details',
  'eRecruit.application.review.salaryWithCurrency': 'RM {{salary}}',
  'eRecruit.application.review.back': 'Back',
  'eRecruit.application.review.confirm': 'Confirm',
  'eRecruit.application.review.nameOfGovernment':
    'Name of the Government Entity/ Organisation',
  'eRecruit.application.review.positionDepartment': 'Position and Department',
  'eRecruit.application.review.relationshipWithGovOfficials':
    'Relationship with Immediate Family Member',

  // Application Form - Documents
  'eRecruit.application.documents.doc': 'Documents',
  'eRecruit.application.documents.fileSize':
    'Accepted formats:jpg, jpeg, png, doc, docx, xls, xlsx or pdf (up to 2MB each)',
  'eRecruit.application.documents.consent': 'Consent',
  'eRecruit.application.documents.upload': 'Upload',
  'eRecruit.application.documents.uploadImageDescr': 'Upload image by',
  'eRecruit.application.documents.uploading': 'Document uploading...',
  'eRecruit.application.documents.image.uploadLimit':
    'Maximum 5 images allowed.',
  'eRecruit.application.documents.file.uploadLimit': 'Maximum 1 file allowed.',

  // Application Form - Consent
  'eRecruit.application.consent.privacy':
    'Privacy Notice and Declaration By Agent',
  'eRecruit.application.consent.nextCodeOfEthics': 'Next Code of ethics',
  'eRecruit.application.consent.nextConsentForm': 'Next Consent form',
  'eRecruit.application.consent.nextSignature': 'Next Signature',
  'eRecruit.application.consent.nextAgencyAgreement': 'Next Agency Agreement',
  'eRecruit.application.consent.voicePrint': 'Voice Print',
  'eRecruit.application.consent.faceToFaceSignature': 'Face to Face Signature',

  'eRecruit.application.consent.next': 'Next Signature',
  'eRecruit.application.consent.codeOfEthics': 'Code of Ethics',
  'eRecruit.application.consent.consentForm': 'Consent form',
  'eRecruit.application.consent.agreement': 'Agency Agreement',
  'eRecruit.application.consent.agentAgreement': 'Agent Agreement',
  'eRecruit.application.consent.leaderAgreement': 'Leader Agreement',
  'eRecruit.application.consent.aajiCodeOfEthics': 'AAJI Code of Ethics',
  'eRecruit.application.consent.fwdCodeOfEthicsOfSales':
    'FWD Insurance code of Ethics of Sales',
  'eRecruit.application.consent.fwdCodeOfEthics': 'FWD code of Ethics',
  'eRecruit.application.consent.nonTwistingLetter': 'Non Twisting Letter',
  'eRecruit.application.consent.personalDataProtection':
    'Personal Data Protection Confirmation',
  'eRecruit.application.consent.confirmationLetter': 'Confirmation Letter',
  'eRecruit.application.consent.remoteSignature': 'Remote signature',
  'eRecruit.application.consent.agree': 'Agree',
  'eRecruit.application.consent.scrollToBottom': 'Please scroll to the bottom',
  'eRecruit.application.consent.checkBoxStatement':
    'I understand, agree and declare that the contents stated are true and correct.',
  'eRecruit.application.consent.checkBoxStatementLeader':
    'I, as an agent leader, understand, agree and declare that the contents stated are true and correct.',
  'eRecruit.application.consent.checkBoxStatementAgent':
    'I, as an agent, understand, agree and declare that the contents stated in Section B are true and correct',
  'eRecruit.application.consent.clear': 'Clear',
  'eRecruit.application.consent.signatureWillBeAppeared':
    'Your signature will be appeared in the document you have reviewed',
  'eRecruit.application.consent.applicationSubmitted':
    'Application submitted, payment link emailed to candidate. Please refer to “Track my candidate status”.',
  'eRecruit.application.consent.remoteSignature.applicationSubmitted':
    'Pending for remote signature from candidate.  Please refer to “Track my candidate status”.',
  'eRecruit.application.consent.pendingRemoteSignature':
    'Application submitted, remote signature required. Please refer to “Track my candidate status”.',
  'eRecruit.application.consent.candidateSignature': "Candidate's signature",
  'eRecruit.application.consent.referrerSignature':
    'Referrer’s signature (recruiter)',
  'eRecruit.application.consent.agentSignature':
    'Agent’s signature (recruiter’s direct leader)',
  'eRecruit.application.consent.witnessSignature': "Witness's signature",
  'eRecruit.application.consent.signatureStatement':
    'I declare that all particulars and information given on this application are true and correct to the best of my acknowledge and I have not suppressed any material fact.',
  'eRecruit.application.consent.submit': 'Submit',
  'eRecruit.application.consent.cancel': 'Cancel',
  'eRecruit.application.consent.dateOfSigning': 'Date of signing:',
  'eRecruit.application.consent.payor': 'Payor',
  'eRecruit.application.consent.candidate': 'Candidate',
  'eRecruit.application.consent.agent': 'Agent',

  'eRecruit.application.consent.shareRemoteSignatureLink':
    'Share remote signature link',

  'eRecruit.application.consent.toCompleteTheApplication':
    'To complete the application, your candidate can continue the consent agreement and candidate signature from the link generated after this step. ',

  'eRecruit.application.signature.next': 'Next',
  'eRecruit.application.signature.submit': 'Submit',
  'eRecruit.application.signature.moreDetails': 'More details',
  'eRecruit.application.signature.declarationMadeByCandidate':
    'Declaration made by candidate',
  'eRecruit.application.signature.declarationMadeByWitness':
    'Declaration made by witness',
  'eRecruit.application.signature.submitted':
    'Application submitted. Pending leader’s approval for now.',

  'eRecruit.application.navigationBlock':
    'Unable to navigate back at this stage of the application. Please continue with the application.',
  'eRecruit.application.saved': 'Application saved.',
  'eRecruit.application.saveFailed': 'Please try again later.',
  'eRecruit.application.unableToProceed': 'Unable to proceed',
  'eRecruit.application.submitFailed':
    'Application submit fail. Please try again later.',
  'eRecruit.application.done': 'Done',

  'eRecruit.candidateProfile.pendingAgentReview':
    'This application is pending agent review.',

  'eRecruit.deleteUploadFile.areYouSureToDelete': 'Are you sure to delete?',
  'eRecruit.deleteUploadFile.areYouSureToUploadFile':
    'Are you sure to delete the uploaded file?',
  'eRecruit.deleteUploadFile.cancel': 'Cancel',
  'eRecruit.deleteUploadFile.delete': 'Delete',
  'eRecruit.deleteUploadFile.remove': 'Remove',
  'eRecruit.formButton.add': 'Add',
  'eRecruit.formButton.upload': 'Upload',
  'eRecruit.progressBar.personalDetails': 'Personal details',
  'eRecruit.progressBar.occupationDetails': 'Occupation details',
  'eRecruit.progressBar.otherDetails': 'Candidate position & other details',
  'eRecruit.progressBar.reviewInfo': 'Review information',
  'eRecruit.progressBar.documents': 'Documents',
  'eRecruit.progressBar.consent': 'Consent',

  'applicationStatus.approved': 'Approved',
  'applicationStatus.approvedCandidates': 'Approved candidates',
  'applicationStatus.inProgress': 'In progress',
  'applicationStatus.rejected': 'Rejected',
  'applicationStatus.rejectedCandidates': 'Rejected candidates',
  'applicationStatus.filterBy': 'Filter by',
  'applicationStatus.totalCaseShownFromLast90Days':
    'Total case ({{count}})   |   Displaying data from the last 90 days',
  'applicationStatus.table.candidateName': 'Candidate name',
  'applicationStatus.table.position': 'Position',
  'applicationStatus.table.status': 'Status',
  'applicationStatus.table.approvalDate': 'Approval date',
  'applicationStatus.table.lastDate': 'Last date',
  'applicationStatus.table.lastUpdate': 'Last update',
  'applicationStatus.table.rejectDate': 'Reject date',
  'applicationStatus.table.emptyRecord': 'Empty Record',

  // Filter Panel
  'filterPanel.title': 'In progress status',
  'filterPanel.reset': 'Reset',
  'filterPanel.apply': 'Apply',
  'candidate.status.created': 'Created',
  'candidate.status.resumeApplication': 'Resume application',
  'candidate.status.remoteCheckingRequired': 'Remote checking required',
  'candidate.status.pendingRemoteSignature': 'Pending remote signature',
  'candidate.status.pendingPayment': 'Pending payment',
  'candidate.status.pendingLeaderApproval': 'Pending leader approval',

  // Candidate Status tab
  'candidate.total.withCount': 'Total ({{count}})',

  //Banner
  'banner.title': 'Recruitment',
  'banner.slogan': 'Build a strong team',

  // Dashboard
  'title.overview': 'Overview',
  'title.application': 'Application',

  //Follow-up list
  'followUpList.emptyMsg':
    'No applications pending your action in last 30 days',

  // Review Application
  'review.application.totalCase': 'Total cases ({{count}})',
  'review.application.displayingDataFrom':
    'Displaying data from the last 90 days.',
  'review.application.agencyType': 'Agency type: {{value}}',
  'review.application.recruiterName': 'Recruiter name: {{value}}',
  'review.application.position': 'Position: {{value}}',
  'review.application.lastUpdate': 'Last update: {{value}}',
  'review.application.actionDate': '{{action}} date: {{date}}',
  'review.application.interviewDate': 'Interview date: {{date}}',
  'review.application.yourDecision': 'Your decision',
  'review.application.approve': 'Approve',
  'review.application.reject': 'Reject',
  'review.application.view': 'View',
  'review.application.confirmInterview':
    'Do you confirm you have interviewed this candidate?',
  'review.application.confirmDeclaration':
    'I hereby certify that I have personally interviewed the applicant and I am satisfied that the statement given in this application for Agency are true, and I agree that should this declaration be false in any respect, FWD is at liberty to terminate the Agent’s Agreement immediately without reference to me.',
  'review.application.enterInterviewDate':
    'Please enter the date of the interview',
  'review.application.interviewdate': 'Interview date',
  'review.application.dateFormat': 'DD/MM/YYYY',
  'review.application.decisionDetails': 'Details about your decision',
  'review.application.comments': 'Comments',
  'review.application.approvedMessage': 'Application is approved',
  'review.application.rejectedMessage': 'Application is rejected',
  'review.title': "Review agent's submission",
  'review.reviewApplication': 'Review application',
  'review.approved': 'Approved',
  'review.rejected': 'Rejected',
  'review.table.candidateName': 'Candidate name',
  'review.table.agencyType': 'Agency type',
  'review.table.position': 'Position',
  'review.table.recruiterName': 'Recruiter name',
  'review.table.lastUpdated': 'Last updated',
  'review.table.salesOffice': 'Sales office/GA/\nService point',
  'review.table.salesOfficeInLine': 'Sales office/GA/Service point',
  'review.table.decision': 'Decision',
  'review.application.approvalTracker': 'Approval tracker',
  'review.application.approved': 'approved',
  'review.application.rejected': 'rejected',
  'review.application.approvedDate': 'Approved date',
  'review.application.rejectedDate': 'Rejected date',

  // Validation
  'validation.address.notExceed':
    'Address length should not exceed {{length}} characters',
  'validation.invalidPercentRange': 'Invalid percentage range',
  'otpModal.requested': 'OTP requested',
  'otpModal.sentMessage':
    'We have sent an OTP (6-digit code) to candidate’s mobile number.',
  'otpModal.sentMessage.withPhone':
    'We have sent an OTP (6-digit code) to candidate’s mobile number {{phone}}.',
  'otpModal.placeholder': 'One-time password',
  'otpModal.failToReceiveOtp': 'Fail to receive OTP?',
  'otpModal.resendCode': 'Resend code',
  'otpModal.changeMobile': 'Change mobile',
  'otpModal.verifyAndContinue': 'Verify & continue',
  'otpModal.incorrect': 'OTP is incorrect, please try again',

  'reviewModal.title': 'Review Complete?',
  'reviewModal.content':
    'Please make sure everything is correct before you proceed.',
  'reviewModal.review.button': 'Review again',
  'reviewModal.confirm.button': 'Confirm',
  // E-recruit Materials
  'materials.shortCut.materials': 'Materials',
  'materials.category.recruitment': 'Recruitment',
  'materials.category.gyb': 'GYB Presenter',
  'materials.category.agent_to_agent': 'Agent to agent Presenter',
  'materials.fileType.video': 'Video',
  'materials.fileType.pdf': 'PDF',
  'materials.share': 'Share',
  'materials.search.placeholder': 'Search materials',
  'materials.search.total': 'Total',
  'materials.search.result': 'Result',
  'materials.search.searchResult': 'Search result',
  'materials.sort.newest': 'Newest',
  'materials.sort.oldest': 'Oldest',
  'materials.filter': 'Filter by',
  'materials.filter.type': 'Material type',
  'materials.filter.label.recruitment': 'Recruitment',
  'materials.filter.label.gyb': 'GYB Presenter',
  'materials.filter.label.agentToAgent': 'Agent to agent Presenter',
  'materials.filter.reset': 'Reset',
  'materials.filter.apply': 'Apply',
  'materials.networkAlert':
    'No WiFi Connection. You are not currently connected to a WiFi network. Please be aware that streaming video may consume significant mobile data.',
  'materials.search.noResult': 'No results found, try another filter',
};
