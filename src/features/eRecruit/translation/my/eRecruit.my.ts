export default {
  'eRecruit.home': 'Home',
  'eRecruit.recruitment': 'Recruitment',
  'eRecruit.application.shareLink': 'Share remote link',
  'eRecruit.application.startApplication': 'Start a new application',
  'eRecruit.application.applicationStatus': 'Application status',
  'eRecruit.application.viewAll': 'View all',
  'eRecruit.application.remote': 'Remote',
  'eRecruit.application.newApplication': 'New application',
  'eRecruit.application.shareApplicationLink': 'Share application link',
  'eRecruit.application.shareApplicationLink.modal.': 'Share application link',
  'eRecruit.remote.modal.title': 'Share application link',
  'eRecruit.remote.modal.description':
    'To ensure the recruitment is under you or your team, please share this unique application link to your candidate.',
  'eRecruit.remote.linkCopied': 'Link copied.',
  'eRecruit.application.sms': 'SMS',
  'eRecruit.application.copyLink': 'Copy Link',
  'eRecruit.application.others': 'Others',
  'eRecruit.recruitmentProgress': 'Recruitment progress',
  'eRecruit.week': 'This week',
  'eRecruit.month': 'This month',
  'eRecruit.candidate': 'Candidate',
  'eRecruit.candidates': 'Candidates',
  'eRecruit.submitted': 'Submitted',
  'eRecruit.approved': 'Approved',
  'eRecruit.recruitmentConversionThis': 'Recruitment Conversion This',
  'eRecruit.asOf': 'As of',
  'eRecruit.candidatesInProgress': 'Candidate in progress',
  'eRecruit.targetVsActualNumber':
    'This {{timePeriodLabel}} target VS actual number',

  'eRecruit.targetOfThis': 'Target of this',
  'eRecruit.this': 'This',
  'eRecruit.operationData': 'Oops! Empty operation data.',
  'eRecruit.noTarget': 'No target has been set up yet.',
  'eRecruit.edit': 'Edit',
  'eRecruit.thisCycle': ' This {{timeSection}} cycle',
  'eRecruit.pleaseTryAgainLater': 'Please try again later',
  'eRecruit.ok': 'Ok',
  'eRecruit.candidate.agencyType.fa': 'Financial Advisor',
  'eRecruit.candidate.filterBy': 'Filter by',
  'eRecruit.candidate.sortBy': 'Sort by',
  'eRecruit.candidate.latestUpdated': 'Latest updated',
  'eRecruit.candidate.latestCreated': 'Latest created',
  'eRecruit.candidate.candidates': 'Candidates',
  'eRecruit.candidate.pendingPayment': 'Pending payment',
  'eRecruit.candidate.pendingLeaderApproval': 'Pending leader approval',
  'eRecruit.candidate.approved': 'Approved',
  'eRecruit.candidate.rejected': 'Rejected',
  'eRecruit.candidate.declined': 'Declined',
  'eRecruit.candidate.remoteChecking': 'Remote checking',
  'eRecruit.candidate.remoteSignature': 'Remote signature',
  'eRecruit.candidate.addNewCandidate': 'Add new candidate',
  'eRecruit.candidate.firstName': 'First Name',
  'eRecruit.candidate.lastName': 'Last Name',
  'eRecruit.candidate.fullName': 'Full Name',
  'eRecruit.candidate.gender': 'Gender',
  'eRecruit.candidate.male': 'Male',
  'eRecruit.candidate.female': 'Female',
  'eRecruit.candidate.code': 'Code',
  'eRecruit.candidate.phoneNumber': 'Phone Number',
  'eRecruit.candidate.email': 'Email',
  'eRecruit.candidate.cancel': 'Cancel',
  'eRecruit.candidate.save': 'Save',

  'eRecruit.candidateProfile': 'Candidate Profile',
  'eRecruit.candidateProfile.profile': 'Profile',
  'eRecruit.candidateProfile.phoneNumberAndEmailAreNotProvided':
    'Phone number and email are not provided',
  'eRecruit.candidateProfile.personalInfo': 'Personal info',

  'eRecruit.candidateProfile.name': 'Name',
  'eRecruit.candidateProfile.gender': 'Gender',
  'eRecruit.candidateProfile.contactNo': 'Contact no.',
  'eRecruit.candidateProfile.email': 'Email',
  'eRecruit.candidateProfile.source': 'Source',
  'eRecruit.candidateProfile.candiateNo': 'Candidate no.',
  'eRecruit.candidateProfile.paymentLink': 'Payment Link',
  'eRecruit.candidateProfile.remoteSignatureLink': 'Remote signature link',
  'eRecruit.candidateProfile.shareLinkToComplete': 'Share link to complete',

  'eRecruit.shareRemoteLink.sms': 'SMS',
  'eRecruit.shareRemoteLink.email': 'Email',
  'eRecruit.shareRemoteLink.more': 'More',
  'eRecruit.shareRemoteLink.copyLink': 'Copy link',
  'eRecruit.shareRemoteLink.whatsapp': 'WhatsApp',
  'eRecruit.shareRemoteLink.shareLinkToComplete': 'Share link to complete',
  'eRecruit.shareRemoteLink.shareLinkToComplete.desc':
    'Please share this remote signature link to your candidate.',
  'eRecruit.shareRemoteLink.linkIsNotAvailable':
    'Link is not available. Please try again later.',
  'eRecruit.shareRemoteLink.linkCopied': 'Link Copied',

  'eRecruit.candidateProfile.createDate': 'Create date',
  'eRecruit.candidateProfile.candidateStatus': 'Candidate status',
  'eRecruit.candidateProfile.status': 'Status',
  'eRecruit.candidateProfile.updateDate': 'update date',
  'eRecruit.candidateProfile.visited': 'Visited',
  'eRecruit.candidateProfile.remoteApplicationDone': 'Remote application done',
  'eRecruit.candidateProfile.agentReviewSubmit': 'Agent review and submit',
  'eRecruit.candidateProfile.signedAndSubmitted': 'Signed and submitted',
  'eRecruit.candidateProfile.paid': 'Paid',
  'eRecruit.candidateProfile.pendingPayment': 'Pending payment',
  'eRecruit.candidateProfile.leaderRejected': 'Leader rejected',
  'eRecruit.candidateProfile.leaderApproved': 'Leader approved',
  'eRecruit.candidateProfile.pendingLeaderApproval': 'Pending leader approval',
  'eRecruit.candidateProfile.resumeApplicationWarning':
    'This application is not completed yet. Please resume application.',
  'eRecruit.candidateProfile.remoteSignatureWarning':
    'This application is pending candidate to sign remotely. Send the link again.',

  'eRecruit.candidateProfile.removeCandidate': 'Remove Candidate',
  'eRecruit.candidateProfile.removecandidate': 'Remove candidate',
  'eRecruit.candidateProfile.candidateIsRemove': 'Candidate is removed.',
  'eRecruit.candidateProfile.candidateIsRemoveFail':
    'Error while removing candidate. Please try again.',
  'eRecruit.candidateProfile.areYouSure':
    'Are you sure to remove? Data cannot be recover once it is removed',
  'eRecruit.candidateProfile.cancel': 'Cancel',
  'eRecruit.candidateProfile.yes': 'Yes, I am sure',
  'eRecruit.candidateProfile.shareRemoteLink': 'Share Remote Link',
  'eRecruit.candidateProfile.resumeApplication': 'Resume application',
  'eRecruit.candidateProfile.checkApplication': 'Check application',
  'eRecruit.candidateProfile.startApplication': 'Start application',
  'eRecruit.candidateProfile.shareLinkToComplete': 'Share link to complete',
  'eRecruit.candidateProfile.linkCopied': 'Link Copied',
  'eRecruit.candidateProfile.agentCodeIssued': 'Agent code issued',
  'eRecruit.candidateProfile.agentCode': 'Agent code',
  'eRecruit.candidateProfile.licenseActivated': 'License activated',
  'eRecruit.candidateProfile.approvedBy': 'Approved by',
  'eRecruit.candidateProfile.rejectedBy': 'Rejected by',
  'eRecruit.candidateProfile.progressBar.await':
    'Application awaits - please complete the form',
  'eRecruit.candidateProfile.progressBar.pendingSignature':
    'Application awaits - pending candidate signature',
  'eRecruit.candidateProfile.progressBar.pendingApproval':
    'Application nearly finished - pending leader approval',
  'eRecruit.candidateProfile.progressBar.licensingCompletion':
    'Application is approved, but requires agent licensing completion',
  'eRecruit.candidateProfile.progressBar.readyToGo':
    "Agent activated - you're ready to go!",
  'eRecruit.candidateProfile.progressBar.inactiveAccount':
    'Agent account inactive',
  'eRecruit.candidateProfile.progressBar.activeAccount':
    'Agent account active!',
  'eRecruit.candidateProfile.reviewerComment': "Reviewer's comment",
  'eRecruit.candidateProfile.autoDeclinedOn': 'Auto declined on {{date}}',
  'eRecruit.candidateProfile.createdOn': 'Created on {{date}}',

  'eRecruit.application.personalDetails.identityDetails': 'Identity details',
  'eRecruit.application.personalDetails.name': 'Name',
  'eRecruit.application.personalDetails.firstName': 'First name',
  'eRecruit.application.personalDetails.lastName': 'Last name',
  'eRecruit.application.personalDetails.gender': 'Gender',
  'eRecruit.application.personalDetails.title': 'Title',
  'eRecruit.application.personalDetails.dateOfBirth': 'Date of Birth',
  'eRecruit.application.personalDetails.age': 'Age',
  'eRecruit.application.personalDetails.icNumber': 'NRIC number',
  'eRecruit.application.personalDetails.citizen': 'Citizen',
  'eRecruit.application.personalDetails.ethnicity': 'Ethnicity',
  'eRecruit.application.personalDetails.religion': 'Religion',
  'eRecruit.application.personalDetails.maritalStatus': 'Marital status',
  'eRecruit.application.personalDetails.passport': 'Passport number (optional)',
  'eRecruit.application.personalDetails.passportNumber': 'Passport number',
  'eRecruit.application.personalDetails.oldIc': 'Old IC/Police/Army (optional)',
  'eRecruit.application.personalDetails.oldIc/Police/Army':
    'Old IC/Police/Army',
  'eRecruit.application.personalDetails.incomeTax': 'Income tax file number',
  'eRecruit.application.personalDetails.incomeTaxFileNumber':
    'Income tax file number',
  'eRecruit.application.personalDetails.yearsOld': ' ({{age}} years old)',

  'eRecruit.application.personalDetails.contactDetails': 'Contact details',
  'eRecruit.application.personalDetails.code': 'Code',
  'eRecruit.application.personalDetails.mobileNumber': 'Mobile number',
  'eRecruit.application.personalDetails.email': 'Email',
  'eRecruit.application.personalDetails.officePhoneOptional':
    'Office phone (optional)',

  'eRecruit.application.personalDetails.officePhone': 'Office phone',
  'eRecruit.application.personalDetails.qualification': 'Qualification',
  'eRecruit.application.personalDetails.academicQualification':
    'Academic qualification',
  'eRecruit.application.personalDetails.takaful':
    'Takaful certificate (Choose 1 or more)',

  'eRecruit.application.personalDetails.tbeFamily': 'TBE Family',
  'eRecruit.application.personalDetails.tbeGeneral': 'TBE General',
  'eRecruit.application.personalDetails.insuranceCertificate':
    'Insurance certificate',
  'eRecruit.application.personalDetails.insuranceCertificate.oneOrMore':
    'Insurance certificate (Optional)',

  'eRecruit.application.personalDetails.pce': 'PCE',
  'eRecruit.application.personalDetails.ceilli': 'CEILLI',
  'eRecruit.application.personalDetails.general': 'General',
  'eRecruit.application.personalDetails.otherInsuranceQualifications':
    'Other insurance qualifications',
  'eRecruit.application.personalDetails.otherInsuranceQualifications.oneOrMore':
    'Other insurance qualifications (Optional)',

  'eRecruit.application.personalDetails.islamic': 'Islamic RFP/CFP/ChFC',

  'eRecruit.application.personalDetails.module2MFPC':
    'Module 2 (Risk Management and Takaful Planning) - Shariah Registered Financial Planner offered by MFPC',

  'eRecruit.application.personalDetails.yearOfPassing': 'Year of passing',

  'eRecruit.application.personalDetails.module2FPAM':
    'Module 2 (Risk Management and Takaful Planning) - Islamic Financial Planning offered by FPAM',

  'eRecruit.application.personalDetails.otherQualifications':
    'Other professional qualifications',

  'eRecruit.application.personalDetails.spouseInformation':
    'Spouse information',
  'eRecruit.application.personalDetails.numOfDependents':
    'Number of dependents',
  'eRecruit.application.personalDetails.occupation': 'Occupation',
  'eRecruit.application.personalDetails.occupationDetails':
    'Occupation details',

  'eRecruit.application.personalDetails.popup.title': 'TBE Family',
  'eRecruit.application.personalDetails.targetCert.PCE': 'PCE',
  'eRecruit.application.personalDetails.targetCert.TakafulTBEFamily':
    'Takaful TBE Family',
  'eRecruit.application.personalDetails.popup.content':
    'To process the application, candidate must have to Takaful TBE Family certificate. Is your candidate qualified with TBE Family certificate?',
  'eRecruit.application.personalDetails.popup.yes': 'Yes. TBE Family Qualified',
  'eRecruit.application.personalDetails.popup.yesWithTargetCert':
    'Yes. {{target}} Qualified',
  'eRecruit.application.personalDetails.popup.no': 'No. End the application',
  'eRecruit.application.personalDetails.goToTheField': 'Go to the field',
  'eRecruit.application.personalDetails.incompleteFields':
    '{{total}} incomplete fields',
  'eRecruit.application.personalDetails.question.yes': 'Yes',
  'eRecruit.application.personalDetails.question.no': 'No',

  'eRecruit.application.occupationDetails.currentOccupation':
    'Current occupation',
  'eRecruit.application.occupationDetails.position': 'Position held',
  'eRecruit.application.occupationDetails.nameOfCompany(inFull)':
    'Name of company (in full)',
  'eRecruit.application.occupationDetails.companyName': 'Name of company',
  'eRecruit.application.occupationDetails.basicSalary(RM)':
    'Last draw salary (RM)',
  'eRecruit.application.occupationDetails.basicSalary': 'Last draw salary',
  'eRecruit.application.occupationDetails.dateAppoint': 'Date appointed',
  'eRecruit.application.occupationDetails.dateTermination': 'Date termination',
  'eRecruit.application.occupationDetails.companyPhoneCountryCode': 'Code',
  'eRecruit.application.occupationDetails.contactNumber': 'Contact number',
  'eRecruit.application.occupationDetails.contactNo': 'Contact no',
  'eRecruit.application.occupationDetails.companyEmail': 'Email',
  'eRecruit.application.occupationDetails.companyAddress': 'Company address',
  'eRecruit.application.occupationDetails.previousOccupation':
    'Previous Occupation',
  'eRecruit.application.occupationDetails.previousOccupation(optional)':
    'Previous Occupation',
  'eRecruit.application.occupationDetails.company': 'Company',
  'eRecruit.application.occupationDetails.add': 'Add',
  'eRecruit.application.occupationDetails.family(optional)':
    'Family / General Takaful Experience',
  'eRecruit.application.occupationDetails.family':
    'Family / General Takaful Experience',
  'eRecruit.application.occupationDetails.typeOfIntermediary':
    'Type of intermediary',

  'eRecruit.application.occupationDetails.rank': 'Rank',
  'eRecruit.application.occupationDetails.lifeInsurance(optional)':
    'Life / General Insurance Experience',
  'eRecruit.application.occupationDetails.lifeInsurance':
    'Life / General Insurance Experience',
  'eRecruit.application.occupationDetails.spouseInsurance':
    'Spouse Insurance Experience',
  'eRecruit.application.occupationDetails.spouseTakaful':
    'Is your spouse at present representing any Takaful or Insurance company',
  'eRecruit.application.occupationDetails.spouseTakafulState':
    'Your spouse at present is representing any Takaful or Insurance company',
  'eRecruit.application.occupationDetails.yes': 'Yes',
  'eRecruit.application.occupationDetails.no': 'No',
  'eRecruit.application.occupationDetails.otherDetails': 'Other details',
  'eRecruit.application.otherDetails.otherDetails': 'Other details',
  'eRecruit.application.otherDetails.saveForLater': 'Save for later',
  'eRecruit.application.otherDetails.next': 'Next',
  'eRecruit.application.otherDetails.review': 'Review',
  'eRecruit.application.otherDetails.addressInformation': 'Address information',
  'eRecruit.application.otherDetails.residentialAddress': 'Residential Address',
  'eRecruit.application.otherDetails.malaysia': 'Malaysia',
  'eRecruit.application.otherDetails.businessAddress': 'Business Address',
  'eRecruit.application.otherDetails.sameAsResidentialAddress':
    'Same as residential address',
  'eRecruit.application.otherDetails.addressLine1': 'Address line 1',
  'eRecruit.application.otherDetails.addressLine2': 'Address line 2',
  'eRecruit.application.otherDetails.addressLine1.hint':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'eRecruit.application.otherDetails.addressLine2.hint':
    'Street no./Street Name',
  'eRecruit.application.otherDetails.residentialAddress.postCode': 'Postcode',
  'eRecruit.application.otherDetails.residentialAddress.city': 'City',
  'eRecruit.application.otherDetails.residentialAddress.province': 'Province',
  'eRecruit.application.otherDetails.bankAccountInformation':
    'Bank account information',
  'eRecruit.application.otherDetails.bankName': 'Bank name',
  'eRecruit.application.otherDetails.accountNumber': 'Account number',
  'eRecruit.application.otherDetails.NRIC': 'NRIC number as per bank’s record',
  'eRecruit.application.otherDetails.supervisorCandidateInformation':
    'Supervisor & candidate information',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.jobType':
    'Job type',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.agencyType':
    'Agency Type',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.reportingBranch':
    'Reporting Branch',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.candidatePosition':
    'Candidate position',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerName':
    'Introducer Name',
  'eRecruit.application.otherDetails.supervisorCandidateInformation.introducerCode':
    'Introducer code',
  'eRecruit.application.otherDetails.leaderInformation': 'Leader information',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderName':
    'Immediate leader name',
  'eRecruit.application.otherDetails.leaderInformation.immediateLeaderCode':
    'Immediate leader code',
  'eRecruit.application.otherDetails.leaderInformation.ALCFWDName':
    'ALC/FWD name',
  'application.otherDetails.remark.optional': 'Remarks (optional)',
  'application.otherDetails.remark': 'Remarks',

  'eRecruit.application.review.reviewInformation': 'Review information',
  'eRecruit.application.review.personalDetails': 'Personal details',
  'eRecruit.application.review.occupationDetails': 'Occupation details',
  'eRecruit.application.review.otherDetails': 'Other details',
  'eRecruit.application.review.salaryWithCurrency': 'RM {{salary}}',
  'eRecruit.application.review.back': 'Back',
  'eRecruit.application.review.confirm': 'Confirm',

  'eRecruit.application.documents.doc': 'Documents',
  'eRecruit.application.documents.fileSize':
    'Accepted formats: jpg, jpeg, bmp, gif or pdf (up to 2MB each)',
  'eRecruit.application.documents.consent': 'Consent',
  'eRecruit.application.documents.upload': 'Upload',

  'eRecruit.application.consent.privacy':
    'Privacy Notice and Declaration By Agent',
  'eRecruit.application.consent.nextCodeOfEthics': 'Next Code of ethics',
  'eRecruit.application.consent.nextConsentForm': 'Next Consent form',
  'eRecruit.application.consent.nextSignature': 'Next Signature',
  'eRecruit.application.consent.nextAgencyAgreement': 'Next Agency Agreement',
  'eRecruit.application.consent.codeOfEthics':
    'Code of Ethics for Takaful Intermediaries',
  'eRecruit.application.consent.consentForm': 'Consent form',
  'eRecruit.application.consent.agreement': 'Agency Agreement',
  'eRecruit.application.consent.remoteSignature': 'Remote signature',
  'eRecruit.application.consent.agree': 'Agree',
  'eRecruit.application.consent.scrollToBottom': 'Please scroll to the bottom',
  'eRecruit.application.consent.clear': 'Clear',
  'eRecruit.application.consent.signatureWillBeAppeared':
    'Your signature will be appeared in the document you have reviewed',
  'eRecruit.application.consent.applicationSubmitted':
    "Application submitted. Pending candidate's payment.",
  'eRecruit.application.consent.remoteSignature.applicationSubmitted':
    'Pending remote signature.',

  'eRecruit.application.consent.pendingRemoteSignature':
    'Pending remote signature.',

  'eRecruit.application.consent.candidateSignature': "Candidate's signature",
  'eRecruit.application.consent.witnessSignature': "Witness's signature",
  'eRecruit.application.consent.signatureStatement':
    'I declare that all particulars and information given on this application are true and correct to the best of my acknowledge and I have not suppressed any material fact.',
  'eRecruit.application.consent.submit': 'Submit',
  'eRecruit.application.consent.cancel': 'Cancel',
  'eRecruit.application.consent.dateOfSigning': 'Date of signing:',
  'eRecruit.application.consent.payor': 'Payor',
  'eRecruit.application.consent.candidate': 'Candidate',

  'eRecruit.application.consent.shareRemoteSignatureLink':
    'Share remote signature link',

  'eRecruit.application.consent.toCompleteTheApplication':
    'To complete the application, your candidate can continue the consent agreement and candidate signature from the link generated after this step. ',

  'eRecruit.application.navigationBlock':
    'Unable to navigate back at this stage of the application. Please continue with the application.',
  'eRecruit.candidateProfile.pendingAgentReview':
    'This application is pending agent review.',

  'eRecruit.deleteUploadFile.areYouSureToDelete': 'Are you sure to delete?',
  'eRecruit.deleteUploadFile.areYouSureToUploadFile':
    'Are you sure to delete the uploaded file?',
  'eRecruit.deleteUploadFile.cancel': 'Cancel',
  'eRecruit.deleteUploadFile.delete': 'Delete',
  'eRecruit.formButton.add': 'Add',
  'eRecruit.formButton.upload': 'Upload',
  'eRecruit.progressBar.personalDetails': 'Personal details',
  'eRecruit.progressBar.occupationDetails': 'Occupation details',
  'eRecruit.progressBar.otherDetails': 'Other details',
  'eRecruit.progressBar.documents': 'Documents',
  'eRecruit.progressBar.consent': 'Consent',

  'applicationStatus.approved': 'Approved',
  'applicationStatus.approvedCandidates': 'Approved candidates',
  'applicationStatus.inProgress': 'In progress',
  'applicationStatus.rejected': 'Rejected',
  'applicationStatus.rejectedCandidates': 'Rejected candidates',
  'applicationStatus.filterBy': 'Filter by',
  'applicationStatus.totalCaseShownFromLast90Days':
    'Total case ({{count}})   |   Displaying data from the last 90 days',
  'applicationStatus.table.candidateName': 'Candidate name',
  'applicationStatus.table.position': 'Position',
  'applicationStatus.table.status': 'Status',
  'applicationStatus.table.approvalDate': 'Approval date',
  'applicationStatus.table.lastDate': 'Last date',
  'applicationStatus.table.lastUpdate': 'Last update',
  'applicationStatus.table.rejectDate': 'Reject date',
  'applicationStatus.table.emptyRecord': 'Empty Record',

  // Filter Panel
  'filterPanel.title': 'In progress status',
  'filterPanel.reset': 'Reset',
  'filterPanel.apply': 'Apply',
  'candidate.status.created': 'Created',
  'candidate.status.resumeApplication': 'Resume application',
  'candidate.status.remoteCheckingRequired': 'Remote checking required',
  'candidate.status.pendingRemoteSignature': 'Pending remote signature',
  'candidate.status.pendingPayment': 'Pending payment',
  'candidate.status.pendingLeaderApproval': 'Pending leader approval',

  // Candidate Status tab
  'candidate.total.withCount': 'Total ({{count}})',

  // Conflict of Interest COI

  'eRecruit.application.dateHint': 'DD/MM/YYYY',
  'eRecruit.application.otherDetails.declarationOfCOI':
    'Declaration of Conflict of Interest',
  'eRecruit.application.otherDetails.declarationOfCOI.phone':
    'Declaration of Conflict of\nInterest',
  'eRecruit.application.otherDetails.remark': 'Remark',
  'application.COI.record': 'Record {{number}}',
  'application.COI.addRecord': 'Add record',
  'application.COI.ownershipInterest': 'Ownership Interest',
  'application.COI.externalEmployment': 'External Directorship / Employment',
  'application.COI.businessAffiliationInterest':
    'Business Affiliations Interests',
  'application.COI.relationshipGovernmentOfficial':
    'Relationships with Government Officials',
  'application.COI.otherInterest':
    'Other Perceived, Potential or Actual Conflicts of Interest',
  'application.COI.nameOfBusinessEnterpriseOrEntity':
    'Name of business enterprise / entity',
  'application.COI.natureOfBusiness': 'Nature of business',
  'application.COI.nameOfOwner': 'Name of owner',
  'application.COI.relationship': 'Relationship',
  'application.COI.dateAcquired': 'Date acquired',
  'application.COI.percentageOfOwnership': 'Percentage of ownership',
  'application.COI.position': 'Position',
  'application.COI.details': 'Details',
  'application.COI.detailsDescription':
    'e.g. Job Description, Time Involved, Existing / Proposed',
  'application.COI.compensationReceived': 'Compensation received',
  'application.COI.yes': 'Yes',
  'application.COI.no': 'No',
  'application.COI.nameOfFamilyMember': 'Name of family member',
  'application.COI.positionDepartment': 'Position and department',
  'application.COI.dateCommencementEmployment':
    'Date of commencement of employment',
  'application.COI.nameOfGovernment':
    'Name of the government entity/ organisation',
  'application.COI.relationshipWithGovOfficials':
    'Relationship with immediate family member',
  'application.COI.otherDetails': 'Other details',
  'application.COI.declarationTitle': 'Declaration',
  'application.COI.declarationMsg':
    'I affirm that the above information is true, complete and correct as of the date below. I understand that I am under an obligation during my employment with the Company to obtain the approval of my Department Head and Group Compliance prior to engaging in commercial activities outside FWD. I also agree to notify the Company promptly if there is a change in any of the above details.',
};
