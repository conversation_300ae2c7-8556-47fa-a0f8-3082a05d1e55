import { useTheme } from '@emotion/react';
import { dateFormatUtil } from 'agent-guru';
import { getAgentInfoById } from 'api/agentInfoApi';
import { getAgentInfo } from 'api/eRecruitApi';
import {
  Typography,
  Row,
  CubePictogramIcon,
  Icon,
  Box,
  Column,
} from 'cube-ui-components';
import { useGetReviewAgentsApplicationDetails } from 'features/eRecruit/hooks/useGetReviewAgentsApplicationDetails';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { View, TouchableOpacity } from 'react-native';
import { AgentInfo } from 'types/agent';
import {
  ApprovalComments,
  ReviewAgentApplicationResponds,
  AgentInfoResponse,
  IDApprovalComments,
} from 'types/eRecruit';
import { country } from 'utils/context';

export function TrackerCard({ applicationId }: { applicationId: number }) {
  const { t } = useTranslation('eRecruit');
  const { colors, space, sizes } = useTheme();
  const [isExpanded, setIsExpanded] = useState(false);
  const [approvalTrackerData, setApprovalTrackerData] = useState<
    ApprovalComments[]
  >([]);
  const { data: applicationData, isLoading: isLoadingReviewApplication } =
    useGetReviewAgentsApplicationDetails(applicationId ? applicationId : 0);

  const approvalComments = (
    applicationData as ReviewAgentApplicationResponds | undefined
  )?.approvalComments;
  useEffect(() => {
    if (approvalComments) {
      const leaderComments =
        country === 'id'
          ? [...approvalComments].filter(
              comment => !!comment.action && comment.interviewDate,
            )
          : approvalComments;
      const sortedData = [...leaderComments].sort((a, b) => {
        return (
          new Date(b.actionDate).getTime() - new Date(a.actionDate).getTime()
        );
      });

      const agentCodes = Array.from(
        new Set(
          sortedData
            .filter(item => item.approverAgentCode)
            .map(item => item.approverAgentCode),
        ),
      );

      Promise.allSettled(
        agentCodes
          .filter(c => c != null)
          .map(agentCode => {
            return country === 'id'
              ? getAgentInfoById(agentCode)
              : getAgentInfo(agentCode);
          }),
      ).then(agentInfoResults => {
        const agentInfoMap: Record<string, AgentInfo | AgentInfoResponse> = {};
        agentInfoResults
          .filter(i => i.status === 'fulfilled')
          .forEach(({ value: agentInfo }) => {
            if (agentInfo) {
              const agentCode =
                country === 'id'
                  ? (agentInfo as AgentInfo).agentId
                  : (agentInfo as AgentInfoResponse).agentCode;
              agentInfoMap[agentCode] = agentInfo;
            }
          });

        const updatedData = sortedData
          .filter(
            item =>
              item.approverAgentCode && agentInfoMap[item.approverAgentCode],
          )
          .map(item => {
            if (item.approverAgentCode) {
              const agentInfo = agentInfoMap[item.approverAgentCode];
              if (agentInfo) {
                if (country === 'id') {
                  item.approverAgentName = (
                    agentInfo as AgentInfo
                  ).person?.fullName;
                  item.approverAgentRole =
                    (agentInfo as AgentInfo).designation ||
                    (item as unknown as IDApprovalComments).positionCode;
                } else {
                  item.approverAgentName = (
                    agentInfo as AgentInfoResponse
                  ).agentName;
                  item.approverAgentRole = (
                    agentInfo as AgentInfoResponse
                  ).designate;
                }
              }
            }
            return item;
          });

        setApprovalTrackerData(updatedData);
      });
    }
  }, [applicationData, approvalComments]);

  const { isTabletMode } = useLayoutAdoptionCheck();

  const Title = isTabletMode ? Typography.H6 : Typography.H7;
  return (
    <>
      {approvalTrackerData.length > 0 ? (
        <View
          style={{
            borderRadius: space[4],
            backgroundColor: colors.background,
            padding: space[6],
            gap: space[4],
            marginBottom: isTabletMode ? 0 : space[4],
          }}>
          <TouchableOpacity
            onPress={() => {
              setIsExpanded(!isExpanded);
            }}>
            <Row
              justifyContent="space-between"
              alignItems="center"
              paddingBottom={isTabletMode ? space[1] : 0}>
              <Row gap={space[2]} alignItems="center">
                <CubePictogramIcon.Binoculars size={sizes[10]} />
                <Title fontWeight="bold">
                  {t('review.application.approvalTracker')}
                </Title>
              </Row>
              {isExpanded ? (
                <Icon.ChevronUp fill={colors.primary} size={24} />
              ) : (
                <Icon.ChevronDown fill={colors.primary} size={24} />
              )}
            </Row>
          </TouchableOpacity>
          {approvalTrackerData.map((item, index) => {
            if (index === 0 || isExpanded) {
              return (
                <View
                  key={
                    index +
                    (item?.approverAgentCode ?? '') +
                    item?.interviewDate
                  }>
                  <Box gap={space[2]} paddingLeft={isTabletMode ? space[4] : 0}>
                    <Row gap={space[2]} alignItems="center">
                      <Icon.TickCircle
                        fill={colors.palette.alertGreen}
                        size={18}
                      />
                      <Typography.H7
                        fontWeight="bold"
                        color={colors.palette.alertGreen}>
                        {item?.approverAgentName}
                        {item?.approverAgentRole
                          ? ` (${item?.approverAgentRole})`
                          : ''}
                        {item?.action === 'APPROVE'
                          ? ` ${t('review.application.approved')}`
                          : item?.action === 'REJECT'
                          ? ` ${t('review.application.rejected')}}`
                          : ''}
                      </Typography.H7>
                    </Row>
                    {(isTabletMode || isExpanded) && (
                      <Column
                        borderLeft={1}
                        borderColor="#D9D9D9"
                        marginLeft={9}
                        gap={space[2]}>
                        <Row paddingLeft={8 + 9}>
                          <Typography.Body>{item?.comment}</Typography.Body>
                        </Row>
                        {(item?.action === 'APPROVE' ||
                          item?.action === 'REJECT') && (
                          <Row paddingLeft={8 + 9}>
                            <Typography.SmallLabel
                              color={colors.palette.fwdDarkGreen[50]}>
                              {item?.action === 'APPROVE'
                                ? t('review.application.approvedDate')
                                : t('review.application.rejectedDate')}
                              {': '}
                              {item?.actionDate
                                ? dateFormatUtil(item?.actionDate)
                                : 'N/A'}
                            </Typography.SmallLabel>
                          </Row>
                        )}
                        <Row paddingLeft={8 + 9}>
                          <Typography.SmallLabel
                            color={colors.palette.fwdDarkGreen[50]}>
                            {t('review.application.interviewDate', {
                              date: item?.interviewDate
                                ? dateFormatUtil(item?.interviewDate)
                                : 'N/A',
                            })}
                          </Typography.SmallLabel>
                        </Row>
                      </Column>
                    )}
                  </Box>
                </View>
              );
            }
            return null;
          })}
          {/* this is the end of the tracking section */}
        </View>
      ) : (
        <></>
      )}
    </>
  );
}
