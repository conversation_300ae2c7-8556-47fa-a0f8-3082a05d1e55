import { MutableRefObject, useEffect, useRef, useState } from 'react';
import { Box, Column } from 'cube-ui-components';
import { useGetERecruitStat } from 'features/eRecruit/hooks/useGetERecruitStat';
import { RecruitmentAction } from './components/ApplicationSection/RecruitmentAction';
import RecruitmentProgressSection from './components/OverviewSection/RecruitmentProgressSection';
import { useTheme } from '@emotion/react';
import SkeletonRecruitmentProgressSection from './components/Skeleton/SkeletonRecruitmentProgressSection';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  RefreshControl,
  ScrollView,
} from 'react-native';
import { RecruitmentHomeStat } from 'types/eRecruit';
import { useGetReviewApplicationList } from 'hooks/useGetApprovedAndRejectedLIst';
import { RouteProp, useRoute } from '@react-navigation/native';
import { MainTabParamList } from 'types';
import { ShareRemoteLinkModal } from './components/utils/ShareRemoteLinkModal';
import { useTranslation } from 'react-i18next';

// Component for the recruitment progress section
const RecruitmentProgress = ({
  isLoading,
  data,
  recruitmentProgressSectionRef,
}: {
  isLoading: boolean;
  data: RecruitmentHomeStat | undefined;
  recruitmentProgressSectionRef: MutableRefObject<null>;
}) => {
  return isLoading ? (
    <SkeletonRecruitmentProgressSection />
  ) : (
    <RecruitmentProgressSection
      homeData={data}
      scrollViewRef={recruitmentProgressSectionRef}
    />
  );
};

export const OverviewSection = ({
  handleScroll,
}: {
  handleScroll: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
}) => {
  const { t } = useTranslation('eRecruit');
  const { space, colors } = useTheme();
  const scrollViewRef = useRef<ScrollView>(null);
  const recruitmentProgressSectionRef = useRef(null);
  const { data, refetch, isLoading } = useGetERecruitStat();
  const { data: reviewApplicationData } = useGetReviewApplicationList({
    status: 'PENDING_LEADER_APPROVAL',
  });
  const reviewNo = reviewApplicationData?.length ?? 0;
  const EmptyBottomView = Box;

  const [refreshing, setRefreshing] = useState(false);
  const route = useRoute<RouteProp<MainTabParamList, 'ERecruit'>>();
  const paramValue = route?.params;
  const [isShowModal, setIsShowModal] = useState(
    paramValue?.shareMessage ? true : false,
  );
  const onRefresh = () => {
    setRefreshing(true);
    refetch();
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  useEffect(() => {
    if (paramValue?.shareMessage) {
      setIsShowModal(true);
    } else {
      setIsShowModal(false);
    }
  }, [paramValue?.shareMessage]);

  return (
    <ScrollView
      ref={scrollViewRef}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={() => onRefresh()} />
      }
      style={{ backgroundColor: colors.surface }}
      onScroll={handleScroll}>
      <Column gap={space[4]}>
        <RecruitmentAction homeData={data} reviewAgentSubmission={reviewNo} />
        <Box paddingX={space[4]}>
          <RecruitmentProgress
            isLoading={refreshing || isLoading}
            data={data}
            recruitmentProgressSectionRef={recruitmentProgressSectionRef}
          />
        </Box>
        {/* Empty bottom view for spacing */}
        <EmptyBottomView height={space[36]} />
        <ShareRemoteLinkModal
          isShowModal={isShowModal}
          setIsShowModal={setIsShowModal}
          shareMessage={paramValue?.shareMessage}
          title={t('eRecruit.shareRemoteLink.shareLinkToComplete')}
          subTitle={t('eRecruit.shareRemoteLink.shareLinkToComplete.desc')}
        />
      </Column>
    </ScrollView>
  );
};
