import React, { useEffect, useState } from 'react';
import {
  ActionPanel,
  ActionPanelProps,
  Row,
  Typography,
  Chip,
  Button,
  Column,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Platform } from 'react-native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { CubeStatusKeys, cubeStatusList, positionList } from 'types/eRecruit';

type CandidateChipsFilterPanelProps = ActionPanelProps & {
  visible: boolean;
  statusFilter: CubeStatusKeys[];
  setStatusFilter: (status: CubeStatusKeys[]) => void;
  positionFilter: string[];
  setPositionFilter: (position: string[]) => void;
};

export default function CandidateChipsFilterPanel({
  visible,
  handleClose,
  statusFilter,
  setStatusFilter,
  positionFilter,
  setPositionFilter,
}: CandidateChipsFilterPanelProps) {
  const { space, colors } = useTheme();
  const [tempStatusFilters, setTempStatusFilters] = useState<CubeStatusKeys[]>(
    [],
  );
  const [tempPositionFilters, setTempPositionFilters] = useState<string[]>([]);
  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation('eRecruit');

  useEffect(() => {
    setTempStatusFilters(statusFilter);
    setTempPositionFilters(positionFilter);
  }, [statusFilter, positionFilter]);

  const onPressApply = () => {
    setStatusFilter(tempStatusFilters);
    setPositionFilter(tempPositionFilters);
    handleClose();
  };

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => {
        handleClose();
      }}
      title={'Filter by'}
      contentContainerStyle={{
        padding: 0,
        paddingBottom: Platform.select({
          android: 0,
          ios: 0,
        }),
      }}>
      <FiltersView
        statusFilter={tempStatusFilters}
        setStatusFilter={setTempStatusFilters}
        positionFilter={tempPositionFilters}
        setPositionFilter={setTempPositionFilters}
      />

      <SafeAreaView
        edges={['bottom']}
        style={[
          {
            paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            paddingTop: space[4],
            paddingBottom: space[4],
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderColor: colors.palette.fwdGrey[100],
          },
        ]}>
        <Row gap={space[4]}>
          <Button
            variant="secondary"
            onPress={() => {
              setStatusFilter([]);
              setPositionFilter([]);
            }}
            text="Reset"
            style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          />
          <Button
            variant="primary"
            onPress={onPressApply}
            text="Apply"
            style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          />
        </Row>
      </SafeAreaView>
    </ActionPanel>
  );
}

export const FiltersView = ({
  statusFilter,
  setStatusFilter,
  positionFilter,
  setPositionFilter,
}: {
  statusFilter: CubeStatusKeys[];
  setStatusFilter: (status: CubeStatusKeys[]) => void;
  positionFilter: string[];
  setPositionFilter: (position: string[]) => void;
}) => {
  const { space } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { t } = useTranslation('eRecruit');

  const filterLabelMap: Record<CubeStatusKeys, string> = {
    PENDING_PAYMENT: t('candidate.status.pendingPayment'),
    PENDING_LEADER_APPROVAL: t('candidate.status.pendingLeaderApproval'),
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    REMOTE_CHECKING: t('candidate.status.remoteCheckingRequired'),
    REMOTE_SIGNATURE: t('candidate.status.pendingRemoteSignature'),
    RESUME_APPLICATION: t('candidate.status.resumeApplication'),
    POTENTIAL_CANDIDATE: t('candidate.status.created'),
  };

  return (
    <Container>
      <Typography.H7
        fontWeight="bold"
        children={t('filterPanel.title')}
        style={{ marginBottom: space[4] }}
      />
      <Row flexWrap="wrap" columnGap={space[1]} rowGap={space[3]}>
        {cubeStatusList
          .filter(item => item !== 'APPROVED' && item !== 'REJECTED')
          .map(status => {
            return (
              <Chip
                key={status}
                label={filterLabelMap?.[status]}
                focus={statusFilter.includes(status)}
                onPress={() => {
                  statusFilter?.includes(status)
                    ? setStatusFilter(
                        statusFilter.filter(item => item !== status),
                      )
                    : setStatusFilter([...statusFilter, status]);
                }}
              />
            );
          })}
      </Row>

      <SeparatorLine />

      <Column paddingBottom={space[4]}>
        <Typography.H7
          fontWeight="bold"
          children={'Position'}
          style={{ marginBottom: space[4] }}
        />
        <Row flexWrap="wrap" columnGap={space[1]} rowGap={space[3]}>
          {positionList.map(position => {
            return (
              <Chip
                key={position}
                focus={positionFilter?.includes(position)}
                label={position}
                onPress={() => {
                  positionFilter?.includes(position)
                    ? setPositionFilter(
                        positionFilter.filter(item => item !== position),
                      )
                    : setPositionFilter([...positionFilter, position]);
                }}
              />
            );
          })}
        </Row>
      </Column>
    </Container>
  );
};

const Container = styled.View(({ theme }) => ({
  paddingHorizontal: theme.sizes[4],
}));

const SeparatorLine = styled.View(({ theme }) => ({
  height: 1,
  backgroundColor: theme?.colors.palette.fwdGrey[50],
  marginVertical: theme.space[5],
}));
