import { ScrollView, TouchableOpacity } from 'react-native';
import React, { Dispatch, SetStateAction, useMemo, useState } from 'react';
import ERecruitFooter from '../utils/ERecruitFooter';
import ReviewApplicationDecisionForm from './ReviewApplicationDecisionForm';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { RootStackParamList } from 'types';
import { ReviewAgentApplicationResponds } from 'types/eRecruit';
import { FlashList } from '@shopify/flash-list';
import { useTheme } from '@emotion/react';
import { Column, H7, Icon, Row } from 'cube-ui-components';
import styled from '@emotion/native';
import RowSeparator from 'components/Table/RowSeparator';
import { useGetReviewAgentsApplicationDetails } from 'features/eRecruit/hooks/useGetReviewAgentsApplicationDetails';
import { useGetDocumentList } from 'features/eRecruit/hooks/useGetDocumentList';
import { getDocumentImageUriByRegistrationId } from 'features/eRecruit/ib/utils';
import PdfViewer from 'features/pdfViewer/components/PdfViewer';
import useBoundStore from 'hooks/useBoundStore';
import { getNewDocumentImageUriIDN } from 'features/eRecruit/my/utils';
import { country } from 'utils/context';
type Document = {
  name: string;
  uri: string;
  base64: string;
  fileName: string;
};

export default function DocumentSection() {
  const [showForm, setShowForm] = useState(false);
  const [showPdf, setShowPdf] = useState(false);

  const initialPdf = {
    name: '',
    uri: '',
    base64: '',
  };
  const [pdf, setPdf] = useState(initialPdf);
  const route = useRoute();
  const { space, colors, borderRadius } = useTheme();
  const { applicationId } = route.params as ReviewAgentApplicationResponds;
  const token = useBoundStore(state => state.auth.authInfo.accessToken);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const { data: reviewApplicationData, isLoading } =
    useGetReviewAgentsApplicationDetails(applicationId as number);

  const { data: documentConfigList, isLoading: documentListLoading } =
    useGetDocumentList({
      agentType: reviewApplicationData?.position?.agencyType ?? '',
    });

  const handleSetPdf = (item: Document) => setPdf(item);

  const files = reviewApplicationData?.documentFiles;
  const documentList = useMemo(() => {
    if (reviewApplicationData?.documentFiles && documentConfigList) {
      const docConfigList = documentConfigList
        ?.filter(doc => Object.keys(files).includes(doc?.fileKey))
        ?.map((item, index) => {
          if (files?.[item.fileKey])
            return {
              key: item.fileKey,
              name: item.enDesc || '',
              documents: files?.[item.fileKey].map(doc => ({
                uri:
                  country === 'id'
                    ? getNewDocumentImageUriIDN({
                        registrationStagingId: applicationId?.toString() || '',
                        fileId: doc?.fileId ?? doc?.fileName ?? '',
                        fileKey: item.fileKey,
                      })
                    : getDocumentImageUriByRegistrationId({
                        fileId: doc?.fileId,
                        registrationId: applicationId?.toString() || '',
                      }),
                name: item?.enDesc ?? '',
                fileName: doc?.fileName ?? '',
              })),
              fileType: files?.[item.fileKey]?.[0]?.fileType,
            };
        });
      return docConfigList;
    }
  }, [reviewApplicationData, documentConfigList]);

  const Document = ({
    item,
    setPdf,
    setShowPdf,
  }: {
    item: {
      name: string;
      fileType: string;
      documents: Document[];
    };
    setPdf: (item: Document) => void;
    setShowPdf: Dispatch<SetStateAction<boolean>>;
  }) => {
    const { space, colors, borderRadius } = useTheme();
    const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
    const onPress = (fileType: string, documents: Document[]) => {
      if (
        fileType === 'application/pdf' ||
        (country === 'id' && documents[0]?.fileName?.endsWith('.pdf'))
      ) {
        setPdf(documents[0]);
        setShowPdf(true);
      } else {
        navigate('SellerExpImageList', { data: documents, index: 0 });
      }
    };

    return (
      <TouchableOpacity
        onPress={() => onPress(item?.fileType, item?.documents)}>
        <Row
          padding={space[4]}
          backgroundColor={colors.background}
          alignItems="center">
          <Icon.Document fill={colors.primary} />
          <Column flex={1} marginLeft={space[1]}>
            <H7 fontWeight="bold">{item?.name}</H7>
          </Column>

          <Column paddingLeft={space[4]}>
            <Icon.ChevronRight
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
          </Column>
        </Row>
      </TouchableOpacity>
    );
  };

  return (
    <>
      <ScrollView
        contentContainerStyle={{
          paddingHorizontal: space[4],
          paddingTop: space[6],
        }}>
        <Container>
          <Header>
            <H7 fontWeight="bold" color={colors.background}>
              Documents
            </H7>
          </Header>
          <FlashList
            keyExtractor={Item => Item?.key}
            estimatedItemSize={100}
            data={documentList ?? []}
            renderItem={({ item }) => (
              <Document
                item={item}
                setPdf={handleSetPdf}
                setShowPdf={setShowPdf}
              />
            )}
            ItemSeparatorComponent={() => (
              <Row paddingX={space[4]} backgroundColor={colors.background}>
                <RowSeparator />
              </Row>
            )}
          />
        </Container>
      </ScrollView>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={false}
        onPrimaryPress={() => setShowForm(!showForm)}
        primarySubLabel="Your decision"
      />
      {showForm && (
        <ReviewApplicationDecisionForm
          setShowForm={setShowForm}
          applicationId={applicationId as number}
        />
      )}

      {showPdf && (
        <PdfViewer
          visible={showPdf}
          pdfGenerator={() => {
            return new Promise((resolve, reject) => {
              resolve({
                url: pdf?.uri,
                fileName: '',
                headers: {
                  Authorization: `Bearer ${token}`,
                  'x-agent-id': agentId ?? '',
                },
              });
            });
          }}
          onClose={() => setShowPdf(false)}
          title={pdf?.name ? `${pdf?.name}` : ''}
          headerContainerStyle={
            country === 'id'
              ? {
                  paddingTop: space[4],
                  paddingBottom: space[4],
                }
              : undefined
          }
        />
      )}
    </>
  );
}

const Container = styled.View(({ theme: { space, borderRadius } }) => {
  return {
    borderRadius: borderRadius.large,
    overflow: 'hidden',
  };
});

const Header = styled.View(({ theme: { space, borderRadius, colors } }) => {
  return {
    backgroundColor: colors.secondary,
    paddingVertical: space[3],
    paddingHorizontal: space[6],
  };
});
