import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useEffect, useImperativeHandle, useRef, useState } from 'react';
import {
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  CubeStatusKeys,
  ReviewAgentApplicationListResponds,
  SortDirectionKeys,
} from 'types/eRecruit';
import { ShowComponentHandle } from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import CandidatesList from '../CandidatesList/CandidatesList';
import { country } from 'utils/context';

export default function ReviewApplicationList({
  data,
  isLoading,
  isRefetching,
  refetch,
  status,
}: {
  data: ReviewAgentApplicationListResponds[];
  isLoading: boolean;
  isRefetching: boolean;
  refetch: () => void;
  status: CubeStatusKeys;
}) {
  const showComponentRef = useRef<ShowComponentHandle>(null);
  const prevOffset = useRef(0);
  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset === 0) {
      // refetch();
      scrollToShow();
    } else if (currentOffset > prevOffset.current && currentOffset !== 0) {
      scrollToHide();
    } else if (currentOffset < prevOffset.current && currentOffset !== 0) {
      scrollToShow();
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    prevOffset.current = currentOffset;
  };

  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const [sortedData, setSortedData] =
    useState<ReviewAgentApplicationListResponds[]>(data);

  useEffect(() => {
    if (!data) return;
    const sorted = [...data].sort((a, b) => {
      const dateA = new Date(a.lastUpdDate as string);
      const dateB = new Date(b.lastUpdDate as string);
      return order === 'oldest'
        ? dateA.getTime() - dateB.getTime()
        : dateB.getTime() - dateA.getTime();
    });
    setSortedData(sorted);
  }, [order, data]);

  useImperativeHandle(showComponentRef, () => ({
    hideComponent: () => hideComponent(),
    showComponent: () => showComponent(),
  }));

  const heightValue = sizes[20];
  const opacity = useSharedValue(1);
  const height = useSharedValue(heightValue);
  const ANIMATION_DURATION = 300;

  const hideComponent = () => {
    opacity.value = withTiming(0, { duration: ANIMATION_DURATION });
    height.value = withTiming(0, { duration: ANIMATION_DURATION });
  };

  const showComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(heightValue, {
      duration: ANIMATION_DURATION,
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
    };
  });

  return (
    <>
      <SearchResultContainer
        style={{
          paddingHorizontal: sizes[4],
        }}>
        <SearchResultHeader layout={LinearTransition} style={[animatedStyle]}>
          <Column gap={space[2]} paddingY={space[4]}>
            <Row gap={space[2]}>
              <Typography.Body color={colors.palette.fwdGreyDarkest}>
                {t('review.application.totalCase', {
                  count: sortedData?.length ?? 0,
                })}
              </Typography.Body>
              {(sortedData?.length > 0 || country === 'id') && (
                <Row style={{ opacity: sortedData?.length > 0 ? 1 : 0.5 }}>
                  <TouchableOpacity
                    disabled={sortedData?.length === 0}
                    onPress={() => {
                      if (order === 'newest') {
                        setOrder('oldest');
                      } else {
                        setOrder('newest');
                      }
                    }}>
                    <Row>
                      <Typography.Body
                        fontWeight="bold"
                        color={colors.palette.fwdAlternativeOrange[100]}>
                        {order === 'newest' ? 'Newest' : 'Oldest'}
                      </Typography.Body>
                      <Box justifyContent="center">
                        {order === 'newest' ? (
                          <Icon.ArrowDown
                            size={sizes[4]}
                            fill={colors.palette.fwdAlternativeOrange[100]}
                          />
                        ) : (
                          <Icon.ArrowUp
                            size={sizes[4]}
                            fill={colors.palette.fwdAlternativeOrange[100]}
                          />
                        )}
                      </Box>
                    </Row>
                  </TouchableOpacity>
                </Row>
              )}
            </Row>

            <Typography.Body color={colors.palette.fwdGreyDarkest}>
              {t('review.application.displayingDataFrom')}
            </Typography.Body>
          </Column>
        </SearchResultHeader>
        {!isRefetching ? (
          <CandidatesList
            navType={'ReviewApplication'}
            isLoading={isLoading}
            data={sortedData}
            isRefreshing={isRefetching}
            onRefresh={refetch}
            contentContainerStyle={{
              paddingBottom: 80,
              paddingHorizontal: 0,
              paddingVertical: 0,
            }}
            haveFilter={false}
            statusKey={`${status}`}
            handleScroll={handleScroll}
            handleScrollEnd={handleScrollEnd}
            isShowStatus={false}
          />
        ) : (
          <ActivityIndicator />
        )}
      </SearchResultContainer>
    </>
  );
}

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const SearchResultHeader = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
}));
