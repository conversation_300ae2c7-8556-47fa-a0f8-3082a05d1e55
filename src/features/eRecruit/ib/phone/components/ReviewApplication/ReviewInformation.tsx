import { Column } from 'cube-ui-components';

import React from 'react';
import { useTheme } from '@emotion/react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';

import { TabView } from 'react-native-tab-view';
import { useGetERecruitReviewApp } from 'features/eRecruit/hooks/useGetReviewApp';
import ProgressBar from '../ReviewApplicationProgressBar/ProgressBar';

const ReviewInformation = () => {
  const { space } = useTheme();

  const { setIndex, navigationState, renderScene } = useGetERecruitReviewApp();

  return (
    <>
      <Column flex={1}>
        <ScreenHeader
          route={'AgentPoliciesReview'}
          customTitle={'Review application'}
          leftChildren={<CustomHeaderBackButton />}
          showBottomSeparator={true}
        />
        <TabView
          swipeEnabled={false}
          navigationState={navigationState}
          onIndexChange={setIndex}
          renderScene={renderScene}
          lazy
          renderTabBar={() => null}
          sceneContainerStyle={{
            marginTop: 0,
          }}
          style={{
            marginTop: space[10],
          }}
        />
        <ProgressBar />
      </Column>
    </>
  );
};

export default ReviewInformation;
