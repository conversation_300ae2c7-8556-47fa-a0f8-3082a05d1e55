import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import { Icon } from 'cube-ui-components';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { RootStackParamList } from 'types/navigation';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { default as AddNewCandidateFormIB } from './AddNewCandidateInputForm';
import { default as AddNewCandidateFormID } from 'features/eRecruit/id/phone/components/AddNewCandidateInputForm';
import { country } from 'utils/context';

export default function AddCandidateScreen() {
  const AddNewCandidateForm =
    country === 'id' ? AddNewCandidateFormID : AddNewCandidateFormIB;

  return (
    <>
      <ScreenHeader route={'AddCandidate'} leftChildren={<CrossButton />} />

      <AddNewCandidateRootContainer>
        <AddNewCandidateForm />
      </AddNewCandidateRootContainer>
    </>
  );
}

const CrossButton = () => {
  const { colors, sizes } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  return (
    <TouchableOpacity
      style={{ marginRight: sizes[9] }}
      onPress={() => navigation.goBack()}>
      <Icon.Close size={sizes[5]} fill={colors.secondary} />
    </TouchableOpacity>
  );
};

const AddNewCandidateRootContainer = styled(View)(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.background,
}));
