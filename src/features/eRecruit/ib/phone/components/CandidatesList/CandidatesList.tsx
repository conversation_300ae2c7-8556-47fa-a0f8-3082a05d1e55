import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { ContentStyle } from '@shopify/flash-list';
import { Box, Column, LoadingIndicator, Typography } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TouchableOpacity,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamListMap } from 'types';
import {
  ApplicationListResponds,
  CubeStatusKeys,
  ReviewAgentApplicationListResponds,
} from 'types/eRecruit';
import CandidateListCard from './CandidateListCard';
import EmptyRecordSVG from 'features/eRecruit/assets/EmptyRecordSVG';
import EmptyFilterCaseSvg from 'features/eRecruit/assets/EmptyFilterCaseSVG';
import EmptyFolderSvG from 'features/eRecruit/assets/EmptyFolderSVG';
import { useERecruitStore } from 'features/eRecruit/util/store/ERecruitStore';
import { shallow } from 'zustand/shallow';
import { useERecruitCheckApplicationStore } from 'features/eRecruit/util/store/ERecruitCheckApplicationStore';
import ReviewApplicationListCard from './ReviewApplicationListCard';
import { country } from 'utils/context';

export default function CandidatesList({
  navType,
  data,
  isLoading,
  contentContainerStyle,
  onRefresh,
  isRefreshing,
  onPress,
  haveFilter,
  isSearch = false,
  label,
  statusKey,
  handleScroll,
  handleScrollEnd,
  isShowStatus = true,
}: {
  navType?: 'ReviewApplication' | string | undefined;
  data: Array<ApplicationListResponds | ReviewAgentApplicationListResponds>;
  isLoading: boolean;
  contentContainerStyle?: ContentStyle;
  onRefresh: () => void;
  isRefreshing: boolean;
  onPress?: () => void;
  haveFilter: boolean;
  isSearch?: boolean;
  label?: string;
  statusKey?: string;
  handleScroll?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  handleScrollEnd?: (event: NativeSyntheticEvent<NativeScrollEvent>) => void;
  isShowStatus?: boolean;
}) {
  const { colors, space, sizes } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();

  const {
    resetERecruitmentStoreState: resetERecruitmentCheckApplicationStoreState,
  } = useERecruitCheckApplicationStore(
    state => ({
      resetERecruitmentStoreState: state.resetERecruitmentStoreState,
    }),
    shallow,
  );

  const { resetERecruitmentStoreState } = useERecruitStore(
    state => ({
      resetERecruitmentStoreState: state.resetERecruitmentStoreState,
    }),
    shallow,
  );

  const { t } = useTranslation('eRecruit');

  const flatListRef = React.useRef<FlatList>(null);

  const onPressNav = (item: ApplicationListResponds) => {
    if (!navType) {
      navigate('ERecruitCandidateProfile', {
        id: item.registrationStagingId ?? item.registrationId ?? 0,
        registrationId: item.registrationId,
        registrationStagingId: item.registrationStagingId,
        stage: item.stage ?? '',
        cubeStatus: item.cubeStatus ?? '',
      });
    } else if (navType === 'ReviewApplication') {
      console.log('nav to Review Application');
      //TODO: fix the navigation route params
      navigate(
        'ERecruitReviewAgentsApplication',
        item as unknown as ReviewAgentApplicationListResponds,
      );
    }
  };

  const emptyTextColor =
    country === 'id'
      ? colors.palette.fwdGreyDarkest
      : colors.palette.fwdGreyDarker;

  return (
    <FlatList
      ref={flatListRef}
      onScroll={handleScroll}
      onScrollEndDrag={handleScrollEnd}
      keyExtractor={(item, index) => {
        return `${statusKey}_${item.registrationStagingId}_${index}`;
      }}
      data={data}
      refreshing={isRefreshing}
      onRefresh={onRefresh}
      contentContainerStyle={{
        paddingHorizontal: space[6],
        paddingBottom: bottom,
        ...contentContainerStyle,
      }}
      ListEmptyComponent={
        isLoading ? (
          <Box minHeight={60} justifyContent="center" alignItems="center">
            <Box h={space[5]} width={space[5]}>
              <LoadingIndicator size={space[5]} />
            </Box>
          </Box>
        ) : haveFilter ? (
          <Box
            width="100%"
            alignItems="center"
            justifyContent="center"
            minH={space[39]}
            padding={space[6]}
            paddingTop={sizes[15]}>
            <Box gap={space[4]} alignItems="center">
              <EmptyFilterCaseSvg height={sizes[33]} />
              <Typography.LargeBody color={emptyTextColor}>
                {label ?? 'No results found, try another filter'}
              </Typography.LargeBody>
            </Box>
          </Box>
        ) : isSearch ? (
          <Box
            width="100%"
            alignItems="center"
            justifyContent="center"
            minH={space[39]}
            padding={space[6]}
            paddingTop={sizes[15]}>
            <Box gap={space[4]} alignItems="center">
              <EmptyRecordSVG height={sizes[33]} />
              <Typography.LargeBody color={emptyTextColor}>
                {label ?? 'Empty record'}
              </Typography.LargeBody>
            </Box>
          </Box>
        ) : (
          <Box
            width="100%"
            alignItems="center"
            justifyContent="center"
            minH={space[39]}
            padding={space[6]}
            paddingTop={sizes[15]}>
            <Box alignItems="center">
              <EmptyFolderSvG height={sizes[33]} />
              <Typography.LargeBody
                color={emptyTextColor}
                style={{ textAlign: 'center' }}>
                {label ?? 'Empty record'}
              </Typography.LargeBody>
            </Box>
          </Box>
        )
      }
      renderItem={({ item }) => (
        <TouchableOpacity
          disabled={
            !!navType && (statusKey === 'APPROVED' || statusKey === 'REJECTED')
          }
          onPress={() => {
            onPress && onPress();
            resetERecruitmentStoreState();
            resetERecruitmentCheckApplicationStoreState();
            onPressNav(item);
          }}>
          <Column marginBottom={space[3]}>
            {'applicationId' in item ? (
              <ReviewApplicationListCard
                info={item}
                status={statusKey as CubeStatusKeys}
              />
            ) : (
              <CandidateListCard info={item} isShowStatus={isShowStatus} />
            )}
          </Column>
        </TouchableOpacity>
      )}
    />
  );
}
