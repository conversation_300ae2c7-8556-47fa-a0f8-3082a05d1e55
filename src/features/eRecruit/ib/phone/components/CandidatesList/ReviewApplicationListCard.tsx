import { useTheme } from '@emotion/react';
import FlagLabel from 'components/FlagLabel';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  CubeStatusKeys,
  ReviewAgentApplicationListResponds,
} from 'types/eRecruit';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';

export default function ReviewApplicationListCard({
  info,
  status,
}: {
  info: ReviewAgentApplicationListResponds;
  status: CubeStatusKeys;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const candidate = 'eRecruit.candidate';
  const filterLabelMap: Record<
    'PENDING_LEADER_APPROVAL' | 'APPROVED' | 'REJECTED',
    string
  > = {
    PENDING_LEADER_APPROVAL: 'Require your review',
    APPROVED: 'Approved by you',
    REJECTED: 'Rejected by you',
  };
  const updateDate = info.lastUpdDate;
  const candidateLabelHandler = (status: CubeStatusKeys) => {
    switch (status) {
      case 'APPROVED':
        return (
          <FlagLabel
            // medium
            type={'leadStatus_lightGreen20'}
            content={filterLabelMap.APPROVED}
          />
        );
      case 'REJECTED':
        return (
          <FlagLabel
            // medium
            type={'alertMild_lightRed'}
            content={filterLabelMap.REJECTED}
          />
        );
      default:
        return (
          <FlagLabel
            // medium
            type={'pending_allLightBlue'}
            content={filterLabelMap.PENDING_LEADER_APPROVAL}
          />
        );
    }
  };

  return (
    <Row
      style={{
        borderRadius: borderRadius.large,
        paddingVertical: space[3],
        paddingLeft: space[4],
        paddingRight: country === 'id' ? space[2] : space[4],
        backgroundColor: colors.background,
        alignItems: 'center',
      }}>
      <Column gap={space[2]} flex={9}>
        <Typography.LargeLabel fontWeight="bold">
          {info.name}
        </Typography.LargeLabel>
        {candidateLabelHandler(status)}
        {country === 'id' ? (
          <Typography.Label>
            {`${t('review.table.salesOfficeInLine')}: ${info.salesOfficeDesc}`}
          </Typography.Label>
        ) : (
          <Typography.Label>
            {t('review.application.agencyType', {
              value: info.agencyAgreementTypeDesc,
            })}
          </Typography.Label>
        )}
        <Typography.Label>
          {t('review.application.recruiterName', {
            value: info.introducerName,
          })}
        </Typography.Label>
        {info.candidatePosition ? (
          <Typography.Label>
            {t('review.application.position', {
              value: info.candidatePosition,
            })}
          </Typography.Label>
        ) : (
          <Typography.Label />
        )}
        <Typography.Label color={colors.palette.fwdGreyDarker}>
          {t('review.application.lastUpdate', {
            value: updateDate ? dateFormatUtil(updateDate) : '--',
          })}
        </Typography.Label>
      </Column>
      {status !== 'APPROVED' && status !== 'REJECTED' && (
        <Box flex={1} alignItems="flex-end">
          <Icon.ChevronRight
            size={space[6]}
            fill={colors.palette.fwdDarkGreen[50]}
          />
        </Box>
      )}
    </Row>
  );
}
