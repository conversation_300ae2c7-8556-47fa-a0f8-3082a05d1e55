import { useTheme } from '@emotion/react';
import FlagLabel from 'components/FlagLabel';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
} from 'types/eRecruit';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { t } from 'i18next';

export default function CandidateListCard({
  info,
  isShowStatus = true,
}: {
  info: ApplicationListResponds;
  isShowStatus: boolean;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const candidate = 'eRecruit.candidate';
  const processedDate = dateHandler({
    cubeStatus: info.cubeStatus,
    stage: info.stage,
    info,
  });
  const processedLabel = labelHandler({
    cubeStatus: info.cubeStatus,
    stage: info.stage,
    info,
  });
  const filterLabelMap: Record<CubeStatusKeys, string> = {
    PENDING_PAYMENT: t('candidate.status.pendingPayment'),
    PENDING_LEADER_APPROVAL: t('candidate.status.pendingLeaderApproval'),
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    REMOTE_CHECKING: t('candidate.status.remoteCheckingRequired'),
    REMOTE_SIGNATURE: t('candidate.status.pendingRemoteSignature'),
    RESUME_APPLICATION: t('candidate.status.resumeApplication'),
    POTENTIAL_CANDIDATE: t('candidate.status.created'),
  };
  const updateDate = processedDate ? new Date(processedDate) : undefined;
  const candidateLabelHandler = (status: CubeStatusKeys | null) => {
    switch (status) {
      case 'APPROVED':
        return (
          <FlagLabel
            medium
            type={'leadStatus_lightGreen20'}
            content={t(`${candidate}.approved`)}
            Icon={Icon.Tick}
          />
        );
      case 'REJECTED':
        return (
          <FlagLabel
            medium
            type={'alertMild_lightRed'}
            content={t(`${candidate}.rejected`)}
          />
        );
      case 'PENDING_PAYMENT':
      case 'PENDING_LEADER_APPROVAL':
        return (
          <FlagLabel
            medium
            type={'disable_grey'}
            content={filterLabelMap?.[status]}
          />
        );
      case 'REMOTE_CHECKING':
      case 'REMOTE_SIGNATURE':
        return (
          <FlagLabel
            medium
            type={'primary_orange'}
            content={filterLabelMap?.[status]}
          />
        );
      case 'POTENTIAL_CANDIDATE':
      case 'RESUME_APPLICATION':
        return (
          <FlagLabel
            medium
            type={'pending_allLightBlue'}
            content={filterLabelMap?.[status]}
          />
        );
      default:
        return <></>;
    }
  };

  return (
    <Row
      style={{
        borderRadius: borderRadius.large,
        paddingVertical: space[3],
        paddingLeft: space[4],
        paddingRight: country === 'id' ? space[2] : space[4],
        backgroundColor: colors.background,
        alignItems: 'center',
        gap: space[2],
      }}>
      <Column gap={country === 'id' ? space[1] : space[2]} flex={1}>
        <Typography.LargeLabel fontWeight="bold">
          {info.name}
        </Typography.LargeLabel>
        {isShowStatus && candidateLabelHandler(info.cubeStatus)}
        {info.candidatePosition ? (
          <Typography.Label>
            Position: {info.candidatePosition}
          </Typography.Label>
        ) : (
          <Typography.Label />
        )}
        <Typography.Label color={colors.palette.fwdGreyDarker}>
          {country === 'id' ? `${processedLabel}:` : 'Last update:'}{' '}
          {updateDate ? dateFormatUtil(updateDate) : '--'}
        </Typography.Label>
      </Column>
      <Icon.ChevronRight
        size={space[6]}
        fill={colors.palette.fwdDarkGreen[50]}
      />
    </Row>
  );
}

const dateHandler = ({
  cubeStatus,
  stage,
  info,
}: {
  cubeStatus: CubeStatusKeys;
  stage: ApplicationStageKeys;
  info: ApplicationListResponds;
}) => {
  //  * stage handling
  if (stage === 'NEW_APPLICATION') {
    return info.lstUpdDate;
  }

  //  *** cubeStatus handling
  switch (cubeStatus) {
    case 'PENDING_PAYMENT':
    case 'PENDING_LEADER_APPROVAL':
      return info.submissionDate;
    case 'APPROVED':
      return info.approvedDate;
    case 'REJECTED':
      return info.rejectedDate;
    case 'REMOTE_CHECKING':
    case 'REMOTE_SIGNATURE':
    default:
      return info.lstUpdDate;
  }
};

const labelHandler = ({
  cubeStatus,
  stage,
  info,
}: {
  cubeStatus: CubeStatusKeys;
  stage: ApplicationStageKeys;
  info: ApplicationListResponds;
}) => {
  //  * stage handling
  if (stage === 'NEW_APPLICATION') {
    return t('eRecruit:applicationStatus.table.lastUpdate');
  }

  //  *** cubeStatus handling
  switch (cubeStatus) {
    case 'APPROVED':
      return t('eRecruit:applicationStatus.table.approvalDate');
    case 'REJECTED':
      return t('eRecruit:applicationStatus.table.rejectDate');
    default:
      return t('eRecruit:applicationStatus.table.lastUpdate');
  }
};
