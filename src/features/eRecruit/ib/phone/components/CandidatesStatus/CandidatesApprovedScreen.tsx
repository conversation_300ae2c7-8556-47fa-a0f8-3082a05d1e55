import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Chip, Column, Icon, Row, Typography } from 'cube-ui-components';
import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
  positionList,
  SortDirectionKeys,
} from 'types/eRecruit';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import { lastUpdateHandler } from '../../../tablet/applicationStatus/ERInProgressTable';
import { ShowComponentHandle } from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import CandidatesList from '../CandidatesList/CandidatesList';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { country } from 'utils/context';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

export default function CandidatesApprovedScreen() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const showComponentRef = useRef<ShowComponentHandle>(null);
  const prevOffset = useRef(0);
  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset === 0) {
      refetch();
      scrollToShow();
    } else if (currentOffset > prevOffset.current && currentOffset !== 0) {
      scrollToHide();
    } else if (currentOffset < prevOffset.current && currentOffset !== 0) {
      scrollToShow();
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    prevOffset.current = currentOffset;
  };

  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [positionFilter, setPositionFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<CubeStatusKeys[]>([]);

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList: ['APPROVED'],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
  });

  const resetSearch = () => {
    navigation.navigate('Main', { screen: 'ERecruit' });
  };

  const candidatesList = useMemo(() => {
    if (positionFilter.length > 0) {
      return data?.data.filter(item =>
        positionFilter.includes(item.candidatePositionCode as string),
      );
    } else {
      return data?.data;
    }
  }, [data, positionFilter]);

  const [isSortDate, setIsSortDate] = useState(false);

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !data
        ? []
        : data?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePositionCode ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              approvalDate: item.approvedDate ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [data, isSortDate],
  );

  const dataOnPositionFilter = useMemo(
    () =>
      positionFilter.length > 0
        ? sortedAppStatusData.filter(item =>
            positionFilter.includes(item.position as string),
          )
        : sortedAppStatusData,
    [sortedAppStatusData, positionFilter],
  );

  const processedData = useMemo(
    () =>
      statusFilter.length > 0
        ? dataOnPositionFilter.filter(item =>
            statusFilter.includes(item.status),
          )
        : dataOnPositionFilter,
    [dataOnPositionFilter, statusFilter],
  );

  useEffect(() => {
    return () => {
      resetSearch();
    };
  }, []);

  useImperativeHandle(showComponentRef, () => ({
    hideComponent: () => hideComponent(),
    showComponent: () => showComponent(),
  }));

  const heightValue = sizes[30];
  const opacity = useSharedValue(1);
  const height = useSharedValue(heightValue);
  const ANIMATION_DURATION = 300;

  const hideComponent = () => {
    opacity.value = withTiming(0, { duration: ANIMATION_DURATION });
    height.value = withTiming(0, { duration: ANIMATION_DURATION });
  };

  const showComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(heightValue, {
      duration: ANIMATION_DURATION,
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
    };
  });

  const filterDisabled =
    country === 'id'
      ? !(positionFilter.length > 0 || statusFilter.length > 0) &&
        candidatesList?.length === 0
      : false;
  return (
    <SearchResultContainer
      style={{
        paddingHorizontal: sizes[4],
      }}>
      <SearchResultHeader layout={LinearTransition} style={[animatedStyle]}>
        <Column gap={space[2]} paddingY={space[4]}>
          {candidatesList &&
            (candidatesList?.length > 0 || country === 'id') && (
              <FilterSectionRow>
                <Typography.Body color={colors.palette.fwdGreyDarkest}>
                  Filter by
                </Typography.Body>
                <ScrollView
                  scrollEnabled={!filterDisabled}
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={{
                    gap: space[1],
                  }}>
                  {positionList.map(position => {
                    return (
                      <Chip
                        key={position}
                        focus={positionFilter?.includes(position)}
                        label={position}
                        onPress={() => {
                          positionFilter?.includes(position)
                            ? setPositionFilter(
                                positionFilter.filter(
                                  item => item !== position,
                                ),
                              )
                            : setPositionFilter([...positionFilter, position]);
                        }}
                        disabled={filterDisabled}
                      />
                    );
                  })}
                </ScrollView>
              </FilterSectionRow>
            )}

          <Row gap={space[2]}>
            <Typography.Body color={colors.palette.fwdGreyDarkest}>
              {positionFilter.length > 0 || statusFilter.length > 0
                ? 'Filtered results'
                : 'Total cases'}{' '}
              ({processedData.length})
            </Typography.Body>
            {(processedData.length > 0 || country === 'id') && (
              <Row>
                <TouchableOpacity
                  disabled={filterDisabled}
                  onPress={() => {
                    if (order === 'newest') {
                      setOrder('oldest');
                    } else {
                      setOrder('newest');
                    }
                  }}>
                  <Row style={{ opacity: filterDisabled ? 0.5 : 1 }}>
                    <Typography.Body
                      fontWeight="bold"
                      color={colors.palette.fwdAlternativeOrange[100]}>
                      {order === 'newest' ? 'Newest' : ' Oldest'}
                    </Typography.Body>
                    <Box justifyContent="center">
                      {order === 'newest' ? (
                        <Icon.ArrowDown
                          size={sizes[4]}
                          fill={colors.palette.fwdAlternativeOrange[100]}
                        />
                      ) : (
                        <Icon.ArrowUp
                          size={sizes[4]}
                          fill={colors.palette.fwdAlternativeOrange[100]}
                        />
                      )}
                    </Box>
                  </Row>
                </TouchableOpacity>
              </Row>
            )}
          </Row>

          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            Displaying data from last 90 days.
          </Typography.Body>
        </Column>
      </SearchResultHeader>

      {!isRefetching ? (
        <CandidatesList
          isLoading={isLoading}
          data={candidatesList as ApplicationListResponds[]}
          isRefreshing={isRefetching}
          onRefresh={refetch}
          contentContainerStyle={{
            paddingBottom: 80,
            paddingHorizontal: 0,
            paddingVertical: 0,
          }}
          haveFilter={
            country === 'id'
              ? false
              : statusFilter.length > 0 || positionFilter.length > 0
          }
          statusKey="CandidatesApprovedScreen"
          handleScroll={handleScroll}
          handleScrollEnd={handleScrollEnd}
          isShowStatus={false}
        />
      ) : (
        <ActivityIndicator />
      )}
    </SearchResultContainer>
  );
}

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const SearchResultHeader = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
}));
