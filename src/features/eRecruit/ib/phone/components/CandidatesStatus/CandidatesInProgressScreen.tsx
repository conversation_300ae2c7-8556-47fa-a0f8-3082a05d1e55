import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { Box, Chip, Column, Icon, Row, Typography } from 'cube-ui-components';
import {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  ActivityIndicator,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { ERecruitApplicationList, RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
  cubeStatusList,
  SortDirectionKeys,
} from 'types/eRecruit';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import { ShowComponentHandle } from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { lastUpdateHandler } from 'features/eRecruit/ib/tablet/applicationStatus/ERInProgressTable';
import CandidateChipsFilterPanel from '../utils/CandidateChipsFilterPanel';
import { FilterDot } from 'components/FilterDot';
import CandidatesList from '../CandidatesList/CandidatesList';
import { country } from 'utils/context';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

export default function CandidatesInProgressScreen() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const route =
    useRoute<RouteProp<ERecruitApplicationList, 'ERecruitApplicationStatus'>>();
  const { status } = route.params;
  const [selectedChipLayoutX, setSelectedChipLayoutX] = useState<number>(0);
  const [autoScroll, setAutoScroll] = useState(true);
  const chipScrollRef = useRef<ScrollView>(null);
  const showComponentRef = useRef<ShowComponentHandle>(null);
  const prevOffset = useRef(0);
  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleChipScroll = () => {
    if (autoScroll) {
      chipScrollRef.current?.scrollTo({
        x: selectedChipLayoutX,
        animated: true,
      });
    }
  };

  useEffect(() => {
    handleChipScroll();
    setTimeout(() => {
      setAutoScroll(false);
    }, 500);
  }, []);

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset === 0) {
      refetch();
      scrollToShow();
    } else if (currentOffset > prevOffset.current && currentOffset !== 0) {
      scrollToHide();
    } else if (currentOffset < prevOffset.current && currentOffset !== 0) {
      scrollToShow();
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    prevOffset.current = currentOffset;
  };

  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [positionFilter, setPositionFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<CubeStatusKeys[]>([]);

  const filterLabelMap: Record<CubeStatusKeys, string> = {
    PENDING_PAYMENT: t('candidate.status.pendingPayment'),
    PENDING_LEADER_APPROVAL: t('candidate.status.pendingLeaderApproval'),
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    REMOTE_CHECKING: t('candidate.status.remoteCheckingRequired'),
    REMOTE_SIGNATURE: t('candidate.status.pendingRemoteSignature'),
    RESUME_APPLICATION: t('candidate.status.resumeApplication'),
    POTENTIAL_CANDIDATE: t('candidate.status.created'),
  };

  useEffect(() => {
    if (status) {
      setStatusFilter([status]);
    }
  }, [status]);

  const { data: allData } = useGetERApplicationList({
    cubeStatusList: [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
  });

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList: statusFilter ? (statusFilter as CubeStatusKeys[]) : [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
  });

  const resetSearch = () => {
    navigation.navigate('Main', { screen: 'ERecruit' });
  };

  const candidatesList = useMemo(() => {
    if (positionFilter.length == 0) {
      return data?.data.filter(
        item =>
          item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
      );
    }
    if (positionFilter.length > 0) {
      return data?.data
        .filter(
          item =>
            item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
        )
        .filter(item =>
          positionFilter.includes(item.candidatePositionCode as string),
        );
    }
    if (positionFilter.length > 0) {
      return data?.data.filter(item =>
        positionFilter.includes(item.candidatePositionCode as string),
      );
    } else {
      return data?.data;
    }
  }, [data, positionFilter]);

  const [isSortDate, setIsSortDate] = useState(false);

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !data
        ? []
        : data?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePositionCode ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              approvalDate: item.approvedDate ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [data, isSortDate],
  );

  const dataOnPositionFilter = useMemo(
    () =>
      positionFilter.length > 0
        ? sortedAppStatusData.filter(item =>
            positionFilter.includes(item.position as string),
          )
        : sortedAppStatusData,
    [sortedAppStatusData, positionFilter],
  );

  const processedData = useMemo(
    () =>
      statusFilter.length > 0
        ? dataOnPositionFilter.filter(item =>
            statusFilter.includes(item.status),
          )
        : dataOnPositionFilter,
    [dataOnPositionFilter, statusFilter],
  );

  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    statusFilter.includes(tabFilter)
      ? setStatusFilter(statusFilter.filter(item => item !== tabFilter))
      : setStatusFilter([...statusFilter, tabFilter]);
  };

  useEffect(() => {
    return () => {
      resetSearch();
    };
  }, []);

  useImperativeHandle(showComponentRef, () => ({
    hideComponent: () => hideComponent(),
    showComponent: () => showComponent(),
  }));

  const heightValue = sizes[30];
  const opacity = useSharedValue(1);
  const height = useSharedValue(heightValue);
  const ANIMATION_DURATION = 300;

  const hideComponent = () => {
    opacity.value = withTiming(0, { duration: ANIMATION_DURATION });
    height.value = withTiming(0, { duration: ANIMATION_DURATION });
  };

  const showComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(heightValue, {
      duration: ANIMATION_DURATION,
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
    };
  });

  const filterDisabled =
    country === 'id'
      ? !(positionFilter.length > 0 || statusFilter.length > 0) &&
        candidatesList?.length === 0
      : false;

  return (
    <>
      <SearchResultContainer
        style={{
          paddingHorizontal: sizes[4],
        }}>
        <SearchResultHeader layout={LinearTransition} style={[animatedStyle]}>
          <Column gap={space[2]} paddingY={space[4]}>
            {allData &&
              (allData.data.filter(
                item =>
                  item.status !== 'APPROVED' && item.status !== 'REJECTED',
              ).length > 0 ||
                country === 'id') && (
                <FilterSectionRow>
                  <Typography.Body color={colors.palette.fwdGreyDarkest}>
                    Filter by
                  </Typography.Body>
                  <ScrollView
                    scrollEnabled={!filterDisabled}
                    ref={chipScrollRef}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{
                      gap: space[1],
                    }}>
                    {cubeStatusList
                      .filter(
                        status =>
                          status !== 'APPROVED' && status !== 'REJECTED',
                      )
                      .map(status => {
                        return (
                          <Chip
                            onLayout={event => {
                              if (status === statusFilter[0] && autoScroll) {
                                setSelectedChipLayoutX(
                                  event.nativeEvent.layout.x,
                                );
                                handleChipScroll();
                              }
                            }}
                            key={status}
                            label={filterLabelMap?.[status]}
                            focus={statusFilter.includes(status)}
                            onPress={() => tagOnPress(status)}
                            disabled={filterDisabled}
                          />
                        );
                      })}
                  </ScrollView>
                  <TouchableOpacity
                    disabled={filterDisabled}
                    onPress={() => {
                      setIsOpenFilter(true);
                    }}>
                    {(positionFilter.length > 0 || statusFilter.length > 0) &&
                    !filterDisabled ? (
                      <Icon.Filtered2 />
                    ) : (
                      <Icon.Filter
                        fill={
                          filterDisabled
                            ? colors.palette.fwdGreyDark
                            : colors.onBackground
                        }
                      />
                    )}
                  </TouchableOpacity>
                </FilterSectionRow>
              )}

            <Row gap={space[2]}>
              <Typography.Body color={colors.palette.fwdGreyDarkest}>
                {positionFilter.length > 0 || statusFilter.length > 0
                  ? 'Filtered results'
                  : 'Total cases'}{' '}
                (
                {
                  processedData.filter(
                    item =>
                      item.status !== 'APPROVED' && item.status !== 'REJECTED',
                  ).length
                }
                )
              </Typography.Body>
              {processedData.filter(
                item =>
                  item.status !== 'APPROVED' && item.status !== 'REJECTED',
              ).length > 0 && (
                <Row>
                  <TouchableOpacity
                    disabled={filterDisabled}
                    onPress={() => {
                      if (order === 'newest') {
                        setOrder('oldest');
                      } else {
                        setOrder('newest');
                      }
                    }}>
                    <Row style={{ opacity: filterDisabled ? 0.5 : 1 }}>
                      <Typography.Body
                        fontWeight="bold"
                        color={colors.palette.fwdAlternativeOrange[100]}>
                        {order === 'newest' ? 'Newest' : ' Oldest'}
                      </Typography.Body>
                      <Box justifyContent="center">
                        {order === 'newest' ? (
                          <Icon.ArrowDown
                            size={sizes[4]}
                            fill={colors.palette.fwdAlternativeOrange[100]}
                          />
                        ) : (
                          <Icon.ArrowUp
                            size={sizes[4]}
                            fill={colors.palette.fwdAlternativeOrange[100]}
                          />
                        )}
                      </Box>
                    </Row>
                  </TouchableOpacity>
                </Row>
              )}
            </Row>

            <Typography.Body color={colors.palette.fwdGreyDarkest}>
              Displaying data from last 90 days.
            </Typography.Body>
          </Column>
        </SearchResultHeader>
        {!isRefetching ? (
          <CandidatesList
            isLoading={isLoading}
            data={candidatesList as ApplicationListResponds[]}
            isRefreshing={isRefetching}
            onRefresh={refetch}
            contentContainerStyle={{
              paddingBottom: 80,
              paddingHorizontal: 0,
              paddingVertical: 0,
            }}
            haveFilter={
              country === 'id'
                ? false
                : statusFilter.length > 0 || positionFilter.length > 0
            }
            statusKey="CandidatesInProgressScreen"
            handleScroll={handleScroll}
            handleScrollEnd={handleScrollEnd}
          />
        ) : (
          <ActivityIndicator />
        )}
      </SearchResultContainer>

      <CandidateChipsFilterPanel
        visible={isOpenFilter}
        handleClose={() => setIsOpenFilter(false)}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        positionFilter={positionFilter}
        setPositionFilter={setPositionFilter}
      />
    </>
  );
}

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const SearchResultHeader = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
}));
