import React, { useImperativeHandle } from 'react';
import { View } from 'react-native';
import { Box, Typography } from 'cube-ui-components';
import styled from '@emotion/native';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import TreeBackgroundSVG from 'features/eRecruit/assets/TreeBackgroundSVG';
import AnnounceSVG from 'features/eRecruit/assets/AnnounceSVG';

const BANNER_HEIGHT = 100;
const ANIMATION_DURATION = 300;

export interface ShowComponentHandle {
  hideComponent: () => void;
  showComponent: () => void;
}

const BackgroundSVG = () => (
  <>
    <Box
      style={{
        position: 'absolute',
        top: 3,
        right: 0,
        zIndex: 99,
      }}>
      <TreeBackgroundSVG />
    </Box>
    <Box
      style={{
        position: 'absolute',
        top: 5,
        right: 40,
        zIndex: 100,
      }}>
      <AnnounceSVG />
    </Box>
  </>
);

const RecruitmentBanner = React.forwardRef<ShowComponentHandle>(
  (props, ref) => {
    const { t } = useTranslation('eRecruit');
    const { colors, space } = useTheme();

    useImperativeHandle(ref, () => ({
      hideComponent: () => hideComponent(),
      showComponent: () => showComponent(),
    }));

    const opacity = useSharedValue(1);
    const height = useSharedValue(BANNER_HEIGHT);

    const hideComponent = () => {
      opacity.value = withTiming(0, { duration: ANIMATION_DURATION });
      height.value = withTiming(0, { duration: ANIMATION_DURATION });
    };

    const showComponent = () => {
      opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
      height.value = withTiming(BANNER_HEIGHT, {
        duration: ANIMATION_DURATION,
      });
    };

    const animatedStyle = useAnimatedStyle(() => {
      return {
        opacity: opacity.value,
        height: height.value,
      };
    });

    return (
      <View style={{ backgroundColor: colors.primary }}>
        <ContentContainer layout={LinearTransition} style={[animatedStyle]}>
          <View style={{ paddingLeft: space[4] }}>
            <WelcomeText fontWeight="bold" children={t('banner.title')} />
            <WelcomeSubText children={t('banner.slogan')} />
          </View>
          <BackgroundSVG />
        </ContentContainer>
      </View>
    );
  },
);

export default RecruitmentBanner;

const ContentContainer = styled(Animated.View)(({ theme }) => ({
  flexDirection: 'row',
  backgroundColor: theme.colors.primary,
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const WelcomeText = styled(Typography.H6)(({ theme }) => ({
  color: theme.colors.onPrimary,
  marginBottom: theme.space[2],
}));

const WelcomeSubText = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.onPrimary,
}));
