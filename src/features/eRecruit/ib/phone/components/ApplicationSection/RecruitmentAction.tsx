import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import FormSVG from 'features/eRecruit/assets/icon/FormSVG';
import PhoneWithFingerSVG from 'features/eRecruit/assets/icon/PhoneWithFingerSVG';
import ShareRemoteLinkSVG from 'features/eRecruit/assets/icon/ShareRemoteLinkSVG';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { RootStackParamListMap } from 'types';
import { RecruitmentHomeStat } from 'types/eRecruit';
import { ShareRemoteLinkActionPanel } from '../utils/ShareRemoteLinkActionPanel';
import { useERecruitStore } from 'features/eRecruit/util/store/ERecruitStore';
import { shallow } from 'zustand/shallow';
import { useERecruitProgressBarStore } from 'features/eRecruit/util/store/ERecruitProgressBarStore';
import { useERecruitCheckApplicationStore } from 'features/eRecruit/util/store/ERecruitCheckApplicationStore';
import { DocumentWithTick } from 'cube-ui-components/dist/cjs/icons/pictograms';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { RecruitActionConfig } from 'features/eRecruit/types';
import { country } from 'utils/context';

export function RecruitmentAction({
  homeData,
  reviewAgentSubmission,
}: {
  homeData: RecruitmentHomeStat | undefined;
  reviewAgentSubmission: number;
}) {
  const { space, colors, sizes } = useTheme();
  const { t } = useTranslation('common');

  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();

  const { resetERecruitmentStoreState } = useERecruitStore(
    state => ({
      resetERecruitmentStoreState: state.resetERecruitmentStoreState,
    }),
    shallow,
  );

  const { resetProgressBarState } = useERecruitProgressBarStore(
    state => ({
      resetProgressBarState: state.resetProgressBarState,
    }),
    shallow,
  );

  const {
    resetERecruitmentStoreState: resetERecruitmentCheckApplicationStoreState,
  } = useERecruitCheckApplicationStore(
    state => ({
      resetERecruitmentStoreState: state.resetERecruitmentStoreState,
    }),
    shallow,
  );

  const [isOpen, setIsOpen] = useState(false);

  const { data: agentProfile, isLoading } = useGetAgentProfile();
  const isAGT = agentProfile?.designationCode == 'AGT';
  const isFWP = agentProfile?.designationCode == 'FWP';

  const countOnCandidateStatus = () => {
    const status = homeData?.status;
    const pendingRemoteSignatureCount = status?.pendingRemoteSignature ?? 0;
    const pendingLeaderApprovalCount = status?.pendingLeaderApproval ?? 0;
    const remoteCheckingRequiredCount = status?.remoteCheckingRequired ?? 0;
    const pendingPaymentCount = status?.pendingPayment ?? 0;

    return (
      pendingRemoteSignatureCount +
      pendingLeaderApprovalCount +
      remoteCheckingRequiredCount +
      pendingPaymentCount
    );
  };

  const isNewDesign = country === 'id';

  const buttonConfig = [
    {
      key: 'startApplication',
      icon: <FormSVG />,
      label: isNewDesign ? 'Start application' : 'Start \n application',
      count: 0,
      nav: () => {
        resetProgressBarState();
        resetERecruitmentStoreState();
        resetERecruitmentCheckApplicationStoreState();
        navigate('ERecruitApplication', { registrationStagingId: undefined });
      },
      isHidden: false,
    },
    {
      key: 'shareRemoteLink',
      icon: <ShareRemoteLinkSVG width={40} height={40} />,
      label: 'Share \n remote link',
      count: 0,
      nav: () => setIsOpen(true),
      isHidden: country === 'id',
    },
    {
      key: 'trackMyCandidateStatus',
      icon: <PhoneWithFingerSVG />,
      label: isNewDesign
        ? 'Track my \n candidate status'
        : 'Track my candidate status',
      count: countOnCandidateStatus() ?? 0,
      nav: () => navigate('ERecruitApplicationStatus', { status: undefined }),
      isHidden: false,
    },
    {
      key: 'reviewAgentSubmission',
      icon: <DocumentWithTick size={sizes[14]} />,
      label: `Review agent's submission`,
      count: reviewAgentSubmission,
      nav: () => navigate('ERecruitReviewAgentsSubmission'),
      isHidden: country === 'id' ? isFWP : isAGT,
    },
  ] satisfies RecruitActionConfig;

  const ConfigButton = TouchableOpacity;
  const ConfigLabelContainer = Box;
  const ConfigCountContainer = Box;
  const RecruitmentActionTitle = isNewDesign
    ? Typography.LargeBody
    : Typography.H7;

  const visibleButtonConfigCount = buttonConfig.filter(i => !i.isHidden).length;

  return (
    <>
      <Box
        backgroundColor={colors.background}
        paddingX={isNewDesign ? space[4] : space[6]}
        paddingY={space[4]}>
        <Box justifyContent="center" height={sizes[10]}>
          <RecruitmentActionTitle
            color={
              isNewDesign ? colors.palette.fwdGreyDarkest : colors.secondary
            }
            fontWeight={isNewDesign ? 'normal' : 'bold'}>
            {t('home.welcome.question')}
          </RecruitmentActionTitle>
        </Box>
        <Box boxSize={space[4]} />
        <Row justifyContent="space-between" flex={1}>
          {buttonConfig.map(config => {
            if (config.isHidden) {
              return null;
            }
            return (
              <Column key={config.key} alignItems="center" flex={1}>
                <ConfigButton
                  onPress={config.nav}
                  style={{
                    position: 'relative',
                    marginBottom: space[2],
                    width: sizes[18],
                    height: sizes[18],
                    backgroundColor: colors.palette.fwdOrange[20],
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: space[5],
                  }}>
                  {config.count > 0 && (
                    <ConfigCountContainer
                      position="absolute"
                      top={isNewDesign ? 0 : -space[2]}
                      right={-space[2]}
                      zIndex={100}
                      height={space[6]}
                      width={space[6]}
                      borderRadius={space[5]}
                      backgroundColor={colors.palette.alertRed}
                      alignItems="center"
                      justifyContent="center">
                      <Typography.SmallLabel color={colors.background}>
                        {config.count}
                      </Typography.SmallLabel>
                    </ConfigCountContainer>
                  )}
                  {config.icon}
                </ConfigButton>
                <ConfigLabelContainer
                  {...(isNewDesign
                    ? {
                        maxH: sizes[14],
                        maxW: visibleButtonConfigCount >= 4 ? 100 : undefined,
                        paddingBottom:
                          visibleButtonConfigCount >= 3 ? space[4] : 0,
                      }
                    : {
                        height: sizes[14],
                        width: sizes[20],
                      })}>
                  <Typography.H8
                    style={{ textAlign: 'center' }}
                    fontWeight="medium">
                    {config.label}
                  </Typography.H8>
                </ConfigLabelContainer>
              </Column>
            );
          })}
        </Row>
      </Box>

      <ShareRemoteLinkActionPanel isOpen={isOpen} setIsOpen={setIsOpen} />
    </>
  );
}
