import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import CandidateStatusIconSVG from 'features/eRecruit/assets/icon/CandidateStatusIconSVG';
import CreditCardInHandWithAlertSVG from 'features/eRecruit/assets/icon/CreditCardInHandWithAlertSVG';
import FormSignatureWithAlertSVG from 'features/eRecruit/assets/icon/FormSignatureWithAlertSVG';
import NotesWithAlertSVG from 'features/eRecruit/assets/icon/NotesWithAlertSVG';
import { Dispatch, Fragment, SetStateAction, useMemo } from 'react';
import { TouchableOpacity } from 'react-native-gesture-handler';
import {
  ApplicationListDataResponds,
  CubeStatusKeys,
  RecruitmentHomeStat,
  SortDirectionKeys,
} from 'types/eRecruit';
import SkeletonERecruitFollowUpApp from '../../../tablet/HomeScreen/SkeletonERecruitFollowUpApp';
import { RootStackParamListMap } from 'types';
import SkeletonERecruitFollowUpBtn from '../Skeleton/SkeletonERecruitFollowUpApp';
import { SvgProps } from 'react-native-svg';
import {
  QueryObserverResult,
  RefetchOptions,
  RefetchQueryFilters,
} from '@tanstack/react-query';
import CandidatesList from '../CandidatesList/CandidatesList';
import { country } from 'utils/context';

export function FollowUpApplication({
  data,
  isLoading,
  isRefetching,
  refetch,
  order,
  setOrder,
  homeData,
  homeDataRefetch,
}: {
  data: ApplicationListDataResponds | undefined;
  isLoading: boolean;
  isRefetching: boolean;
  refetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined,
  ) => Promise<QueryObserverResult<ApplicationListDataResponds, unknown>>;
  order: SortDirectionKeys;
  setOrder: Dispatch<SetStateAction<SortDirectionKeys>>;
  homeData: RecruitmentHomeStat | undefined;
  homeDataRefetch: <TPageData>(
    options?: (RefetchOptions & RefetchQueryFilters<TPageData>) | undefined,
  ) => Promise<QueryObserverResult<RecruitmentHomeStat, unknown>>;
}) {
  const { space, colors } = useTheme();
  const candidatesList = data?.data ?? [];
  const candidateCount = candidatesList?.length ?? 0;

  const paidVisible = country !== 'id';

  const followUpBtnList = useMemo(() => {
    return [
      paidVisible
        ? {
            label: 'Remote checking  required',
            count: homeData?.status?.remoteCheckingRequired ?? 0,
            status: 'REMOTE_CHECKING',
            icon: NotesWithAlertSVG,
          }
        : undefined,
      {
        label: 'Pending remote signature',
        count: homeData?.status?.pendingRemoteSignature ?? 0,
        status: 'REMOTE_SIGNATURE',
        icon: FormSignatureWithAlertSVG,
      },
      paidVisible
        ? {
            label: 'Pending payment',
            count: homeData?.status?.pendingPayment ?? 0,
            status: 'PENDING_PAYMENT',
            icon: CreditCardInHandWithAlertSVG,
          }
        : undefined,
      {
        label: 'Pending leader approval',
        count: homeData?.status?.pendingLeaderApproval ?? 0,
        status: 'PENDING_LEADER_APPROVAL',
        icon: CandidateStatusIconSVG,
      },
    ].filter(Boolean) as {
      label: string;
      count: number;
      status: CubeStatusKeys;
      icon: (props: SvgProps) => JSX.Element;
    }[];
  }, [homeData, paidVisible]);

  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();

  return (
    <>
      <Column gap={space[3]} paddingBottom={0}>
        <Typography.H6 fontWeight="bold">Follow up application</Typography.H6>
        <TouchableOpacity
          onPress={() =>
            navigate('ERecruitApplicationStatus', { status: undefined })
          }>
          <Typography.Label
            fontWeight="bold"
            color={colors.palette.fwdAlternativeOrange[100]}>
            View all status
          </Typography.Label>
        </TouchableOpacity>
      </Column>
      <Row gap={space[2]}>
        {followUpBtnList.map((btn, index) => (
          <Fragment key={`${btn}_${index}`}>
            {isLoading ? (
              <SkeletonERecruitFollowUpBtn
                key={index}
                label={btn.label}
                icon={btn.icon}
              />
            ) : (
              <FollowUpBtn
                key={index}
                label={btn.label}
                count={btn.count}
                status={btn.status}
                icon={btn.icon}
              />
            )}
          </Fragment>
        ))}
      </Row>
      <Column gap={space[2]}>
        <Row>
          <Typography.Body color={colors.palette.fwdGreyDarkest}>
            Total ({candidateCount})
          </Typography.Body>
          <TouchableOpacity
            style={{ paddingLeft: space[1] }}
            onPress={() => {
              setOrder(order === 'oldest' ? 'newest' : 'oldest');
            }}>
            <Row alignItems="center">
              <Typography.Body
                fontWeight="bold"
                color={colors.palette.fwdAlternativeOrange[100]}>
                {order === 'newest' ? 'Newest' : 'Oldest'}
              </Typography.Body>
              {order === 'newest' ? (
                <Icon.ArrowDown
                  size={space[4]}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
              ) : (
                <Icon.ArrowUp
                  size={space[4]}
                  fill={colors.palette.fwdAlternativeOrange[100]}
                />
              )}
            </Row>
          </TouchableOpacity>
        </Row>
        <Typography.Body color={colors.palette.fwdGreyDarkest}>
          Display data in the last 30 days
        </Typography.Body>
        <Box boxSize={space[3]} />
        {isLoading ? (
          <SkeletonERecruitFollowUpApp />
        ) : (
          <CandidatesList
            isLoading={isLoading}
            data={candidatesList}
            isRefreshing={isRefetching}
            onRefresh={refetch}
            contentContainerStyle={{ paddingBottom: 80, paddingHorizontal: 0 }}
            haveFilter={false}
            label={'No applications pending your action in last 30 days'}
            statusKey="FollowUpApplication"
          />
        )}
      </Column>
    </>
  );
}

function FollowUpBtn({
  label,
  count,
  icon,
  status,
}: {
  label: string;
  count: number;
  icon: (props: SvgProps) => JSX.Element;
  status: CubeStatusKeys;
}) {
  const { space, colors, sizes } = useTheme();
  const buttonIconSize = sizes[7];
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();
  const Icon = icon;

  const Label = country === 'id' ? Typography.SmallLabel : Typography.SmallBody;

  return (
    <Column
      flex={1}
      backgroundColor={colors.palette.fwdOrange[5]}
      borderRadius={space[4]}
      paddingX={space[3]}
      pt={space[2]}
      pb={country === 'id' ? space[4] : space[2]}>
      <TouchableOpacity
        onPress={() =>
          navigate('ERecruitApplicationStatus', { status: status })
        }
        style={{
          flex: 1,
        }}>
        <Column gap={country === 'id' ? space[1] : space[2]}>
          <Row
            justifyContent={
              country === 'id' && status === 'PENDING_LEADER_APPROVAL'
                ? 'flex-start'
                : 'space-between'
            }
            alignItems="center"
            gap={space[1]}>
            <Typography.H7
              style={{ minWidth: sizes[7] }}
              fontWeight="bold"
              color={
                count === 0
                  ? colors.palette.fwdGreyDark
                  : colors.palette.alertRed
              }>
              {count}
            </Typography.H7>
            <Icon width={buttonIconSize} height={buttonIconSize} />
          </Row>
          <Label fontWeight="medium">{label}</Label>
        </Column>
      </TouchableOpacity>
    </Column>
  );
}
