import { TouchableOpacity } from 'react-native';
import React, { useEffect, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  Button,
  Row,
  Typography,
  Icon,
  Box,
  addToast,
  ExtraSmallLabel,
  Column,
  H7,
  addErrorBottomToast,
} from 'cube-ui-components';
import RecruitmentProgressCard from 'features/eRecruit/my/tablet/components/RecruitmentProgress/RecruitmentProgressCard';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useTranslation } from 'react-i18next';
import {
  ERecruitTierKeys,
  RecruitmentHomeStat,
  TableConfigItem,
  TimeSectionKeys,
} from 'types/eRecruit';
import { useUpdateERecruitTarget } from 'features/eRecruit/hooks/useUpdateERecruitTarget';
import { useQueryClient } from '@tanstack/react-query';
import { RECRUIT_ENDPOINT } from 'api/eRecruitApi';
import { country } from 'utils/context';
import TargetDateRangeInfoButton from './TargetDateRangeInfoButton';
import EmptyTargetCaseSVG from 'features/eRecruit/assets/EmptyTargetCaseSVG';
import EmptyCaseSVG from 'features/teamManagement/assets/EmptyCaseSVG';
import TargetTableRow from './TargetTableRow';
import GATracking from 'utils/helper/gaTracking';
import usePrevious from 'hooks/usePrevious';
import { AxiosError } from 'axios';
import { TARGET_SETTING_INVALID_ERROR_CODE } from 'constants/errors';
import EmptyTaskSVG from 'features/home/<USER>/image/EmptyTaskSVG';
import EmptyAffiliateSVG from 'features/eRecruit/assets/EmptyAffiliateSVG';

export default function SetRecruitmentTarget({
  timeSection,
  scrollViewRef,
  homeData,
}: {
  timeSection: TimeSectionKeys;
  scrollViewRef: React.MutableRefObject<KeyboardAwareScrollView | null>;
  homeData: RecruitmentHomeStat | undefined;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { t: tLead } = useTranslation('lead');

  const timePeriod = ' ' + (timeSection ?? '-');

  const [isEditing, setIsEditing] = useState(false);
  const { mutateAsync, isLoading } = useUpdateERecruitTarget();
  const queryClient = useQueryClient();
  const targetDataTest = homeData?.performance?.[timeSection]?.target;
  const previousTargetDataTest = usePrevious(targetDataTest);
  const prevTimeSection = usePrevious(timeSection);

  const isNoTargetSet =
    targetDataTest?.approve === 0 &&
    targetDataTest?.candidate === 0 &&
    targetDataTest?.submitted === 0;

  const [localForm, setLocalForm] = useState<Record<ERecruitTierKeys, number>>({
    candidate: targetDataTest?.candidate ?? 0,
    submitted: targetDataTest?.submitted ?? 0,
    approve: targetDataTest?.approve ?? 0,
  });

  // ** fix wrong localForm when targetDataTest is not loaded
  useEffect(() => {
    if (
      (targetDataTest && previousTargetDataTest === undefined) ||
      timeSection !== prevTimeSection
    ) {
      setLocalForm({
        candidate: targetDataTest?.candidate ?? 0,
        submitted: targetDataTest?.submitted ?? 0,
        approve: targetDataTest?.approve ?? 0,
      });
    }
  }, [targetDataTest, timeSection]);

  const onSave = () => {
    mutateAsync(
      {
        candidate: localForm.candidate,
        submitted: localForm.submitted,
        approve: localForm.approve,
        typeCode: timeSection === 'month' ? 'M' : 'W',
      },
      {
        onSuccess: () => {
          GATracking.logCustomEvent('recruitment', {
            action_type:
              timeSection === 'month'
                ? 'monthly_targets_set'
                : 'weekly_targets_set',
          });
          queryClient.invalidateQueries({
            queryKey: [RECRUIT_ENDPOINT, 'home'],
          });
        },
        onError: e => {
          const error = e as AxiosError<
            {
              messageList?: Array<{ code: string; content: string }>;
            },
            unknown
          >;
          if (error.response) {
            const content = error.response.data.messageList?.[0]?.content;
            if (
              country === 'id' &&
              content === TARGET_SETTING_INVALID_ERROR_CODE
            ) {
              addErrorBottomToast([
                {
                  message: tLead('minimumRequirement'),
                },
              ]);
              return;
            }
          }
          addToast([
            {
              message: t('eRecruit.pleaseTryAgainLater'),
            },
          ]);
        },
        onSettled: () => setIsEditing(false),
      },
    );
  };

  const recruitmentTargetHeader: Array<TableConfigItem> = [
    {
      content: 'Candidates',
    },
    {
      content: 'Submitted',
    },
    {
      content: 'Approved',
    },
  ] as const;

  const eRecruitTargetStatConfig = useMemo(() => {
    return [
      {
        content: String(targetDataTest?.candidate ?? 0),
      },
      {
        content: String(targetDataTest?.submitted ?? 0),
      },
      {
        content: String(targetDataTest?.approve ?? 0),
      },
    ];
  }, [targetDataTest]);

  if (homeData === undefined) {
    return <EmptyDataCase timePeriod={timePeriod} />;
  }

  if (homeData !== undefined && isNoTargetSet && !isEditing) {
    return (
      <NoTargetSetCase timePeriod={timePeriod} setIsEditing={setIsEditing} />
    );
  }

  return (
    <Box padding={space[4]} gap={space[2]} borderRadius={borderRadius['large']}>
      <Row alignItems="center" justifyContent="space-between">
        <Row gap={space[1]} alignItems="center">
          <H7 fontWeight="bold" color={colors.palette.fwdDarkGreen[100]}>
            {t('eRecruit.targetOfThis')} {timePeriod.trim()}
          </H7>
          {!isEditing && (
            <TargetDateRangeInfoButton
              size={sizes[6]}
              timeSection={timeSection}
            />
          )}
        </Row>
        {!isEditing && (
          <TouchableOpacity
            onPress={() => {
              setIsEditing(!isEditing);
              if (!isEditing) {
                setTimeout(() => {
                  scrollViewRef?.current?.scrollToEnd();
                }, 300);
              }
            }}>
            <Row gap={space[1]} alignItems="center">
              <Icon.Edit
                size={space[6]}
                fill={
                  country == 'ib' || country == 'id'
                    ? colors.palette.fwdAlternativeOrange[100]
                    : colors.primary
                }
              />
            </Row>
          </TouchableOpacity>
        )}
      </Row>
      <Column gap={space[2]}>
        <Row gap={space[5]} paddingY={space[3]}>
          {recruitmentTargetHeader.map((item, index) => {
            return (
              <Box flex={1} alignItems="center" key={`${item}_${index}`}>
                <ExtraSmallLabel>{item.content}</ExtraSmallLabel>
              </Box>
            );
          })}
        </Row>

        <TargetTableRow
          isContentBold
          isEditing={isEditing}
          setLocalForm={setLocalForm}
          config={eRecruitTargetStatConfig}
        />
      </Column>
      {isEditing && (
        <Button
          variant="primary"
          text={t('eRecruit.candidate.save')}
          onPress={onSave}
          style={{
            width: sizes[50],
            alignSelf: 'center',
            paddingTop: space[2],
          }}
          disabled={isLoading}
          loading={isLoading}
        />
      )}
    </Box>
  );
}

function EmptyDataCase({ timePeriod }: { timePeriod: string }) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  return (
    <Box padding={space[5]} gap={space[5]} borderRadius={borderRadius['large']}>
      <RecruitmentProgressCard.Title>
        {t('eRecruit.targetOfThis')}
        {timePeriod}
      </RecruitmentProgressCard.Title>
      <Box gap={space[4]} alignItems="center">
        {country === 'id' ? <EmptyTaskSVG /> : <EmptyCaseSVG />}
        <Typography.LargeBody color={colors.palette.fwdGreyDarker}>
          {t('eRecruit.operationData')}
        </Typography.LargeBody>
      </Box>
    </Box>
  );
}

function NoTargetSetCase({
  timePeriod,
  setIsEditing,
}: {
  timePeriod: string;
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  return (
    <Box
      padding={country === 'id' ? space[4] : space[5]}
      gap={country === 'id' ? space[2] : space[5]}
      borderRadius={borderRadius['large']}>
      <RecruitmentProgressCard.Title>
        {t('eRecruit.targetOfThis')}
        {timePeriod}
      </RecruitmentProgressCard.Title>
      <Box gap={space[4]} alignItems="center">
        {country === 'id' ? (
          <EmptyAffiliateSVG height={100} width={120} />
        ) : (
          <EmptyTargetCaseSVG />
        )}
        <Typography.LargeBody color={colors.palette.fwdGreyDarker}>
          {t('eRecruit.noTarget')}
        </Typography.LargeBody>
      </Box>
      <Button
        onPress={() => setIsEditing(state => !state)}
        variant="secondary"
        text="Set target"
        style={{
          width: sizes[50],
          alignSelf: 'center',
          marginTop: country === 'id' ? space[3] : 0,
        }}
      />
    </Box>
  );
}
