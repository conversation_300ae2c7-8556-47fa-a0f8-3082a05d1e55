import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import LongArrowSVG from 'features/eRecruit/assets/LongArrowSVG';
import ShortArrowSVG from 'features/eRecruit/assets/ShortArrowSVG';
import CreditCardInHandSVG from 'features/eRecruit/assets/icon/CreditCardInHandSVG';
import FormSVG from 'features/eRecruit/assets/icon/FormSVG';
import HappyPeopleSVG from 'features/eRecruit/assets/icon/HappyPeopleSVG';
import ManSVG from 'features/eRecruit/assets/icon/ManSVG';
import { useTranslation } from 'react-i18next';
import { RootStackParamListMap } from 'types';
import { RecruitmentHomeStat, TimeSectionKeys } from 'types/eRecruit';
import React from 'react';
import { TouchableOpacity, useWindowDimensions } from 'react-native';
import { country } from 'utils/context';
import ShortArrowSVGV2 from 'features/eRecruit/assets/ShortArrowSVGV2';
import NonPaidLongArrowSVGV2 from 'features/eRecruit/assets/NonPaidLongArrowSVGV2';

export default function RecruitmentConversion({
  homeData,
  timeSection,
}: {
  homeData: RecruitmentHomeStat | undefined;
  timeSection: TimeSectionKeys;
}) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const timePeriodLabel = ' ' + (timeSection ?? '-');
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();
  const { width } = useWindowDimensions();

  const performanceData = homeData?.performance?.[timeSection];

  const totalSuccessRate = Math.min(
    ((performanceData?.conversion?.approve ?? 0) /
      ((performanceData?.conversion?.candidate ?? 1) > 0
        ? performanceData?.conversion?.candidate ?? 1
        : 1)) *
      100,
    100,
  ).toFixed(0);

  const submitToPaidRate = Math.min(
    ((performanceData?.conversion?.paid ?? 0) /
      ((performanceData?.conversion?.submitted ?? 1) > 0
        ? performanceData?.conversion?.submitted ?? 1
        : 1)) *
      100,
    100,
  ).toFixed(0);

  const approvedRate = Math.min(
    ((performanceData?.conversion?.approve ?? 0) /
      ((performanceData?.conversion?.submitted ?? 1) > 0
        ? performanceData?.conversion?.submitted ?? 1
        : 1)) *
      100,
    100,
  ).toFixed(0);

  const paidToApprovedRate = Math.min(
    ((performanceData?.conversion?.approve ?? 0) /
      ((performanceData?.conversion?.paid ?? 1) > 0
        ? performanceData?.conversion?.paid ?? 1
        : 1)) *
      100,
    100,
  ).toFixed(0);

  const paidVisible = country !== 'id';

  const funnelConfig = [
    {
      label: 'Candidate in progress',
      icon: <ManSVG width={space[6]} height={space[6]} />,
      value: performanceData?.conversion.candidate ?? 0,
      width: paidVisible ? 1 : 0.85, //100
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', { status: undefined }),
    },
    {
      label: 'Submitted',
      icon: <FormSVG width={space[6]} height={space[6]} />,
      value: performanceData?.conversion.submitted ?? 0,
      width: paidVisible ? 0.88 : 0.75, //88
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', {
          status: paidVisible ? 'PENDING_PAYMENT' : 'PENDING_LEADER_APPROVAL',
        }),
    },
    {
      label: 'Paid',
      icon: <CreditCardInHandSVG width={space[6]} height={space[6]} />,
      value: performanceData?.conversion.paid ?? 0,
      width: 0.75, //75
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', {
          status: 'PENDING_LEADER_APPROVAL',
        }),
      hidden: !paidVisible,
    },
    {
      label: 'Approved',
      icon: <HappyPeopleSVG width={space[6]} height={space[6]} />,
      value: performanceData?.conversion.approve ?? 0,
      width: paidVisible ? 0.61 : 0.53,
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', { status: 'APPROVED' }),
    },
  ];

  const isNewDesign = country === 'id';

  const viewWidth = paidVisible ? width - space[20] : width - space[8];

  return (
    <Box
      padding={isNewDesign ? space[4] : space[5]}
      paddingBottom={space[5]}
      borderRadius={space[4]}
      backgroundColor={colors.background}
      overflow="hidden">
      <Typography.H7 fontWeight="bold">
        This{timePeriodLabel} agent conversion
      </Typography.H7>
      <Box boxSize={space[5]} />
      <Column alignItems="center">
        <Box
          position="absolute"
          left={-space[2]}
          top={paidVisible ? space[7] : space[8]}>
          {paidVisible ? (
            <LongArrowSVG height={sizes[44]} />
          ) : (
            <NonPaidLongArrowSVGV2 />
          )}
          <Column position="absolute" bottom={space[6]} left={space[4]}>
            <Typography.SmallLabel fontWeight="medium">
              {totalSuccessRate}%
            </Typography.SmallLabel>
            <Typography.ExtraSmallLabel fontWeight="medium">
              success
            </Typography.ExtraSmallLabel>
          </Column>
        </Box>
        {/* first short arrow */}
        <Row
          alignItems="center"
          gap={space[1]}
          position="absolute"
          top={paidVisible ? space[22] : space[23]}
          right={-space[3]}>
          {paidVisible ? (
            <ShortArrowSVG height={sizes[12]} />
          ) : (
            <ShortArrowSVGV2 />
          )}
          <Column>
            <Typography.SmallLabel fontWeight="medium">
              {paidVisible ? submitToPaidRate : approvedRate}%
            </Typography.SmallLabel>
            <Typography.ExtraSmallLabel fontWeight="medium">
              {paidVisible ? 'paid' : 'approved'}
            </Typography.ExtraSmallLabel>
          </Column>
        </Row>
        {/* second short arrow */}
        {paidVisible && (
          <Row
            alignItems="center"
            gap={space[1]}
            position="absolute"
            top={space[38]}
            right={-space[3]}>
            <ShortArrowSVG height={sizes[12]} />
            <Column>
              <Typography.SmallLabel fontWeight="medium">
                {paidToApprovedRate}%
              </Typography.SmallLabel>
              <Typography.ExtraSmallLabel fontWeight="medium">
                success
              </Typography.ExtraSmallLabel>
            </Column>
          </Row>
        )}

        {funnelConfig.map((config, index) => {
          if (config.hidden) return null;
          return (
            <React.Fragment key={config.label}>
              <TouchableOpacity
                onPress={config.navigateTo}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: colors.palette.fwdOrange[20],
                  width: viewWidth * config.width,
                  borderRadius: 30,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingLeft: paidVisible ? space[4] : space[3],
                  paddingRight: paidVisible ? space[4] : space[2],
                  paddingVertical: paidVisible ? space[2] : space[2] - 1,
                  backgroundColor: colors.palette.fwdOrange[5],
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                  shadowOpacity: 0.16,
                  shadowRadius: 10,
                  elevation: 5, // This is for Android
                  gap: space[1],
                }}>
                {config.icon}
                <Typography.SmallLabel fontWeight="bold">
                  {config.label}
                </Typography.SmallLabel>
                <Row alignItems="center" justifyContent="center" gap={space[1]}>
                  <Typography.H8 fontWeight="bold">
                    {config.value}
                  </Typography.H8>
                  <Icon.ChevronRight size={space[4]} />
                </Row>
              </TouchableOpacity>
              {index !== funnelConfig.length - 1 && (
                <Box
                  width={viewWidth * 0.6 * config.width}
                  style={{
                    height: paidVisible ? sizes[4] : sizes[3],
                    backgroundColor: colors.primary,
                    transform: [{ skewX: '134deg' }],
                  }}
                />
              )}
            </React.Fragment>
          );
        })}
      </Column>
    </Box>
  );
}
