import { useTheme } from '@emotion/react';
import { Box, Chip, Column, Row, Typography } from 'cube-ui-components';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { RecruitmentHomeStat, TimeSectionKeys } from 'types/eRecruit';
import RecruitmentConversion from './RecruitmentConversion';
import RecruitmentTarget from './RecruitmentTarget';
import SetRecruitmentTarget from './SetRecruitmentTarget';
import styled from '@emotion/native';
import { country } from 'utils/context';

export default function RecruitmentProgressSection({
  homeData,
  scrollViewRef,
}: {
  homeData: RecruitmentHomeStat | undefined;
  scrollViewRef: React.MutableRefObject<KeyboardAwareScrollView | null>;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');

  const [timeSection, setTimeSection] = useState<TimeSectionKeys>('week');
  const isWeek = timeSection === 'week';
  const isMonth = timeSection === 'month';
  const isNewDesign = country === 'id';
  const Title = isNewDesign ? Typography.H7 : Typography.H6;

  return (
    <Column gap={space[4]} paddingBottom={space[5]}>
      <Title fontWeight="bold">{t('eRecruit.recruitmentProgress')}</Title>
      <Row gap={isNewDesign ? space[2] : space[1]}>
        <Chip
          size="medium"
          label={t('eRecruit.week')}
          focus={isWeek}
          onPress={() => setTimeSection('week')}
        />
        <Chip
          size="medium"
          label={t('eRecruit.month')}
          focus={isMonth}
          onPress={() => setTimeSection('month')}
        />
      </Row>

      <Box gap={space[3]}>
        <RecruitmentConversion homeData={homeData} timeSection={timeSection} />
        <KeyboardAwareScrollView>
          <Box backgroundColor={colors.background} borderRadius={space[4]}>
            <RecruitmentTarget timeSection={timeSection} />
            <Divider />
            <SetRecruitmentTarget
              homeData={homeData}
              timeSection={timeSection}
              scrollViewRef={scrollViewRef}
            />
          </Box>
        </KeyboardAwareScrollView>
      </Box>
    </Column>
  );
}

const Divider = styled.View(({ theme }) => ({
  marginHorizontal: theme.space[4],
  height: 1,
  backgroundColor: '#D9D9D9',
}));
