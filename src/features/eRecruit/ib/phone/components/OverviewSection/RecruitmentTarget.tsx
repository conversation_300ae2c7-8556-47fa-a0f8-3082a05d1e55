import { Dimensions } from 'react-native';
import React, { useMemo } from 'react';
import { useTheme } from '@emotion/react';
import { Box, Column, Typography } from 'cube-ui-components';
import RecruitmentProgressCard from 'features/eRecruit/my/tablet/components/RecruitmentProgress/RecruitmentProgressCard';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import Bar<PERSON>hart, {
  BarStyle,
  TargetLineStyle,
  calculateYAxisMax,
  yIntervalHandler,
} from 'components/Chart/BarChart';
import { useTranslation } from 'react-i18next';
import { useGetERecruitStat } from 'features/eRecruit/hooks/useGetERecruitStat';
import { TimeSectionKeys } from 'types/eRecruit';
import { country } from 'utils/context';

export default function RecruitmentTarget({
  timeSection,
}: {
  timeSection: TimeSectionKeys;
}) {
  const timePeriodLabel = timeSection ?? '-';
  const formattedDate = dateFormatUtil(new Date());
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');

  const { data } = useGetERecruitStat();
  const { width: windowWidth } = Dimensions.get('window');

  const conversionData = data?.performance?.[timeSection]?.conversion;
  const targetData = data?.performance?.[timeSection]?.target;

  const yAxisMax: number = useMemo(() => {
    if (conversionData && targetData) {
      const keysOfConversionData = Object.keys(conversionData) as Array<
        keyof typeof conversionData
      >;
      const keysOfTargetData = Object.keys(targetData) as Array<
        keyof typeof targetData
      >;
      return calculateYAxisMax(
        keysOfConversionData.map(key => conversionData[key]) as Array<number>,
        keysOfTargetData.map(key => targetData[key]) as Array<number>,
      );
    }
    return 0;
  }, [conversionData, targetData]);

  const chartDataOptions = {
    x: (d: any, i: number) => i,
    y: (d: any) => d.value,
    title: (d: any) => d.name,
    bottomPadding: space[2],
    leftPadding: space[2],
    width: windowWidth / 1.1,
    height: windowWidth / 2,
    yInterval: yIntervalHandler(yAxisMax),
    xPadding: 0,
    color: colors.palette.fwdOrange[100],
    internalPadding: space[4],
  };

  const barChartData = [
    {
      name: t('eRecruit.candidate'),
      value: conversionData?.candidate ?? 0,
      color: colors.palette.fwdOrange[20],
      target: targetData?.candidate ?? 0,
    },
    {
      name: t('eRecruit.submitted'),
      value: conversionData?.submitted ?? 0,
      color: colors.palette.fwdOrange[50],
      target: targetData?.submitted ?? 0,
    },
    {
      name: t('eRecruit.approved'),
      value: conversionData?.approve ?? 0,
      color: colors.palette.fwdOrange[100],
      target: targetData?.approve ?? 0,
    },
  ];

  const targetLabel = `- ${
    timeSection === 'week'
      ? 'Weekly'
      : timeSection === 'month'
      ? 'Monthly'
      : '--'
  } Target`;

  return (
    <Box
      padding={country === 'id' ? space[4] : space[5]}
      gap={space[5]}
      borderRadius={borderRadius['large']}>
      <Column gap={space[2]}>
        <RecruitmentProgressCardTitle>
          {t('eRecruit.targetVsActualNumber', {
            timePeriodLabel: timePeriodLabel,
          })}
        </RecruitmentProgressCardTitle>
        <RecruitmentProgressCard.Date>
          {t('eRecruit.asOf')} {formattedDate}
        </RecruitmentProgressCard.Date>
        <RecruitmentProgressCard.HeaderNote>
          {targetLabel}
        </RecruitmentProgressCard.HeaderNote>
      </Column>
      <Box left={space[2]}>
        <BarChart
          data={barChartData}
          dataOptions={chartDataOptions}
          xlabelWidth={space[17]}
          xlabelColor={colors.palette.fwdDarkGreen[100]}
          barWidthInput={space[17]}
          barTopBorderStyle={BarStyle.curved}
          targetLineStyle={TargetLineStyle.dashed}
          isWidthOffset={false}
        />
      </Box>
    </Box>
  );
}

function RecruitmentProgressCardTitle({
  children,
}: {
  children?: React.ReactNode;
}) {
  return <Typography.H7 fontWeight="bold">{children}</Typography.H7>;
}
