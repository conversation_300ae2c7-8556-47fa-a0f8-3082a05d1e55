import { TouchableOpacity, Modal } from 'react-native';
import React, { useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import { Button, Typography, Icon, Box, Column } from 'cube-ui-components';
import { isAfter, isBefore, isToday, parse } from 'date-fns';
import { useTranslation } from 'react-i18next';
import { TargetDetails } from 'types/leadTracking';
import { TimeSectionKeys } from 'types/eRecruit';
import { useGetLeadTracking } from 'features/lead/hooks/useGetSalesActivity';
import { country } from 'utils/context';
import { sizes } from 'cube-ui-components/dist/cjs/theme/base';

export default function TargetDateRangeInfoButton({
  timeSection,
  size,
}: {
  timeSection: TimeSectionKeys;
  size?: number;
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const [isOpen, setIsOpen] = useState(false);

  const { data: leadTrackingDetails, isLoading } = useGetLeadTracking();
  const dateInfo = useMemo(
    () =>
      leadTrackingDetails?.targets?.map(
        data =>
          ({
            week: data.week,
            startDate: data.startDate,
            endDate: data.endDate,
          } satisfies Partial<TargetDetails>),
      ),
    [leadTrackingDetails],
  );

  const currentWeekInfo = dateInfo?.find(wklyInfo => {
    if (
      isToday(parse(wklyInfo.startDate, 'yyyy-MM-dd', new Date())) ||
      isToday(parse(wklyInfo.endDate, 'yyyy-MM-dd', new Date()))
    ) {
      return true;
    }
    return (
      isAfter(
        new Date(),
        parse(wklyInfo.startDate, 'yyyy-MM-dd', new Date()),
      ) &&
      isBefore(new Date(), parse(wklyInfo.endDate, 'yyyy-MM-dd', new Date()))
    );
  });

  const formatter = new Intl.DateTimeFormat('en-GB', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  });

  const weekIdxNumber = leadTrackingDetails?.targets?.findIndex(
    week => week.week === currentWeekInfo?.week,
  );

  return (
    <>
      <TouchableOpacity onPress={() => setIsOpen(true)}>
        <Icon.InfoCircle
          size={size}
          fill={
            country == 'ib' || country == 'id'
              ? colors.palette.fwdAlternativeOrange[100]
              : colors.primary
          }
        />
      </TouchableOpacity>
      <Modal
        visible={isOpen}
        transparent={true}
        statusBarTranslucent
        animationType="fade">
        <Box
          flex={1}
          backgroundColor={'rgba(0, 0, 0, 0.5)'}
          alignItems="center"
          justifyContent="center">
          <Column
            p={space[6]}
            gap={space[6]}
            width={space[90]}
            backgroundColor={colors.background}
            borderRadius={borderRadius.large}>
            <Column gap={space[4]}>
              <Box>
                <Typography.H6 fontWeight="bold">
                  {t('eRecruit.thisCycle', { timeSection: timeSection })}
                </Typography.H6>
              </Box>
              <Box>
                {timeSection === 'month' ? (
                  leadTrackingDetails?.targets?.map((info, idx) => {
                    const startDate = new Date(info.startDate);
                    const endDate = new Date(info.endDate);

                    const dateRange = `${formatter.format(
                      startDate,
                    )} - ${formatter.format(endDate)}`;

                    return (
                      <Typography.LargeBody key={info.startDate}>
                        {'Week ' + `${idx + 1}: `}
                        {dateRange}
                      </Typography.LargeBody>
                    );
                  })
                ) : currentWeekInfo ? (
                  <Typography.LargeBody>
                    {`Week ${
                      weekIdxNumber != undefined ? weekIdxNumber + 1 : '--'
                    }: `}
                    {`${formatter.format(
                      new Date(currentWeekInfo.startDate),
                    )} - ${formatter.format(
                      new Date(currentWeekInfo.endDate),
                    )}`}
                  </Typography.LargeBody>
                ) : null}
              </Box>
            </Column>

            <Box alignItems="center">
              <Button
                variant="primary"
                text={t('eRecruit.ok')}
                onPress={() => setIsOpen(false)}
                style={{
                  height: sizes[10],
                  width: sizes[39],
                }}
              />
            </Box>
          </Column>
        </Box>
      </Modal>
    </>
  );
}
