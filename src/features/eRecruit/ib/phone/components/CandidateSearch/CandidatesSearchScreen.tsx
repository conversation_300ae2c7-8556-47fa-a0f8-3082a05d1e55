import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ResponsiveView from 'components/ResponsiveView';
import {
  Box,
  Chip,
  Column,
  H7,
  H8,
  Icon,
  Row,
  SearchBar,
  SearchBarRef,
  SmallBody,
  Typography,
} from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import {
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Platform,
  Pressable,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import { EdgeInsets, useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
  cubeStatusList,
  positionList,
  SortDirectionKeys,
} from 'types/eRecruit';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import { lastUpdateHandler } from '../../../tablet/applicationStatus/ERInProgressTable';
import CandidateChipsFilterPanel from '../utils/CandidateChipsFilterPanel';
import { ShowComponentHandle } from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { FilterDot } from 'components/FilterDot';
import CandidatesList from '../CandidatesList/CandidatesList';
import { country } from 'utils/context';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

export default function CandidatesSearchScreen() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const route = useRoute<RouteProp<RootStackParamList, 'CandidatesSearch'>>();
  const isShowFilter = route.params?.isShowFilter ?? true;
  const insets = useSafeAreaInsets();
  const showComponentRef = useRef<ShowComponentHandle>(null);
  const prevOffset = useRef(0);
  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset === 0) {
      refetch();
      scrollToShow();
    } else if (currentOffset > prevOffset.current && currentOffset !== 0) {
      scrollToHide();
    } else if (currentOffset < prevOffset.current && currentOffset !== 0) {
      scrollToShow();
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    prevOffset.current = currentOffset;
  };

  const { sizes, colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');

  const [query, setQuery] = useState<string>('');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [positionFilter, setPositionFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<CubeStatusKeys[]>([]);

  const eRecruitStat = [
    {
      type: 'inProgress',
      label: t('applicationStatus.inProgress'),
    },
    {
      type: 'approved',
      label: t('applicationStatus.approved'),
    },
    {
      type: 'rejected',
      label: t('applicationStatus.rejected'),
    },
  ];

  const [selectedERecruitStat, setSelectedERecruitStat] = useState({
    type: 'all',
    label: '',
  });

  const filterLabelMap: Record<CubeStatusKeys, string> = {
    PENDING_PAYMENT: t('candidate.status.pendingPayment'),
    PENDING_LEADER_APPROVAL: t('candidate.status.pendingLeaderApproval'),
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    REMOTE_CHECKING: t('candidate.status.remoteCheckingRequired'),
    REMOTE_SIGNATURE: t('candidate.status.pendingRemoteSignature'),
    RESUME_APPLICATION: t('candidate.status.resumeApplication'),
    POTENTIAL_CANDIDATE: t('candidate.status.created'),
  };

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList:
      selectedERecruitStat.type == 'inProgress' && statusFilter
        ? (statusFilter as CubeStatusKeys[])
        : selectedERecruitStat.type == 'approved'
        ? ['APPROVED']
        : selectedERecruitStat.type == 'rejected'
        ? ['REJECTED']
        : [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
    query: query,
  });

  const resetSearch = () => {
    setQuery('');
    navigation.navigate('Main', { screen: 'ERecruit' });
  };

  const candidatesList = useMemo(() => {
    if (
      selectedERecruitStat.type == 'inProgress' &&
      !(positionFilter.length > 0)
    ) {
      return data?.data.filter(
        item =>
          item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
      );
    }
    if (
      selectedERecruitStat.type == 'inProgress' &&
      positionFilter.length > 0
    ) {
      return data?.data
        .filter(
          item =>
            item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
        )
        .filter(item =>
          positionFilter.includes(item.candidatePositionCode as string),
        );
    }
    if (positionFilter.length > 0) {
      return data?.data.filter(item =>
        positionFilter.includes(item.candidatePositionCode as string),
      );
    } else {
      return data?.data;
    }
  }, [data, positionFilter]);

  const [isSortDate, setIsSortDate] = useState(false);

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !data
        ? []
        : data?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePositionCode ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              approvalDate: item.approvedDate ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [data, isSortDate],
  );

  const dataOnPositionFilter = useMemo(
    () =>
      positionFilter.length > 0
        ? sortedAppStatusData.filter(item =>
            positionFilter.includes(item.position as string),
          )
        : sortedAppStatusData,
    [sortedAppStatusData, positionFilter],
  );

  const processedData = useMemo(
    () =>
      statusFilter.length > 0
        ? dataOnPositionFilter.filter(item =>
            statusFilter.includes(item.status),
          )
        : dataOnPositionFilter,
    [dataOnPositionFilter, statusFilter],
  );

  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    statusFilter.includes(tabFilter)
      ? setStatusFilter(statusFilter.filter(item => item !== tabFilter))
      : setStatusFilter([...statusFilter, tabFilter]);
  };

  useEffect(() => {
    if (
      processedData.filter(
        item => item.status !== 'APPROVED' && item.status !== 'REJECTED',
      ).length > 0
    ) {
      setSelectedERecruitStat({
        type: 'inProgress',
        label: t('applicationStatus.inProgress'),
      });
      return;
    }
    if (processedData.filter(item => item.status === 'APPROVED').length > 0) {
      setSelectedERecruitStat({
        type: 'approved',
        label: t('applicationStatus.approved'),
      });
      return;
    }
    if (processedData.filter(item => item.status === 'REJECTED').length > 0) {
      setSelectedERecruitStat({
        type: 'rejected',
        label: t('applicationStatus.rejected'),
      });
      return;
    }
  }, [query]);

  useEffect(() => {
    return () => {
      resetSearch();
    };
  }, []);

  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();

  const searchRef = useRef<SearchBarRef>(null);

  const handleSearch = (text: string) => {
    setSelectedERecruitStat({
      type: 'inProgress',
      label: t('applicationStatus.inProgress'),
    });
    setQuery(text);
  };

  const handleClearBtn = () => {
    searchRef.current?.onClear();
    searchRef.current?.input.focus();
    setQuery('');
    setSelectedERecruitStat({ type: 'all', label: '' });
    setPositionFilter([]);
    setStatusFilter([]);
  };

  useImperativeHandle(showComponentRef, () => ({
    hideComponent: () => hideComponent(),
    showComponent: () => showComponent(),
  }));

  const heightValue = sizes[27];
  const opacity = useSharedValue(1);
  const height = useSharedValue(heightValue);
  const ANIMATION_DURATION = 300;

  const hideComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(0, { duration: ANIMATION_DURATION });
  };

  const showComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(heightValue, {
      duration: ANIMATION_DURATION,
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
    };
  });

  return (
    <>
      <SearchContainer insets={insets}>
        <HeaderContainer
          narrowStyle={{ height: sizes[11], paddingTop: sizes[6] }}>
          <ResponsiveView
            narrowStyle={{ alignItems: 'flex-end', paddingLeft: sizes[1] }}
            style={{ flex: 1, alignItems: 'flex-start' }}>
            <CustomHeaderBackButton
              fill={colors.background}
              size={sizes[6]}
              onPressBack={resetSearch}
            />
          </ResponsiveView>
          <H7
            fontWeight="bold"
            color={colors.background}
            style={{ flex: 4, textAlign: 'center' }}
            children={t('eRecruit.searchCandidate')}
          />
          <View style={{ flex: 1 }}></View>
        </HeaderContainer>

        <SearchBarContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchBar
            onChangeQuery={() => null}
            ref={searchRef}
            variant={'round'}
            placeholder=""
            collapseOnBlur={false}
            showClearBtnAfterQuery
            inputProps={{
              enablesReturnKeyAutomatically: true,
              returnKeyType: 'search',
              autoComplete: 'off',
              autoCorrect: false,
              spellCheck: false,
              onSubmitEditing(e) {
                handleSearch(e.nativeEvent.text);
              },
              autoFocus: true,
              style: { fontSize: sizes[4] },
            }}
            iconLeft={
              <ResponsiveView style={{ paddingRight: sizes[1] }}>
                <Icon.Search fill={colors.palette.fwdGreyDark} />
              </ResponsiveView>
            }
            isLoading={isLoading}
            activeBorderColor={colors.primary}
            inactiveBorderColor={colors.primary}
            style={{
              height: isWideScreen ? sizes[13] : sizes[12],
              paddingVertical: sizes[1],
            }}
            focusAfterClear
            debouncedDelay={0}
            onPressIconRight={handleClearBtn}
          />
        </SearchBarContainer>
        <SearchHintContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchHint children={'e.g. Candidate’s name, mobile'} />
        </SearchHintContainer>
        {query !== '' && isShowFilter && (
          <Box paddingTop={space[2]}>
            <ScrollView
              horizontal
              contentContainerStyle={{ gap: space[1], paddingLeft: space[4] }}>
              {eRecruitStat.map((item, index) => (
                <ChipButton
                  key={`${item}_${index}`}
                  label={`${item.label}`}
                  onSelect={() => {
                    setSelectedERecruitStat(item);
                  }}
                  isActive={selectedERecruitStat.label === item.label}
                />
              ))}
            </ScrollView>
          </Box>
        )}
      </SearchContainer>

      <SearchResultContainer
        style={{
          paddingHorizontal: sizes[4],
        }}>
        <SearchResultHeader
          layout={LinearTransition}
          style={[
            animatedStyle,
            {
              maxHeight:
                selectedERecruitStat.type != 'all' && query ? heightValue : 46,
            },
          ]}>
          <Column gap={space[5]} paddingY={space[4]}>
            {selectedERecruitStat.type != 'all' && query && (
              <FilterSectionRow>
                <Typography.Body color={colors.palette.fwdGreyDarkest}>
                  Filter by
                </Typography.Body>
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  contentContainerStyle={{
                    gap: space[1],
                  }}>
                  {selectedERecruitStat.type == 'inProgress'
                    ? cubeStatusList
                        .filter(
                          status =>
                            status !== 'APPROVED' && status !== 'REJECTED',
                        )
                        .map(status => {
                          return (
                            <Chip
                              key={status}
                              label={filterLabelMap?.[status]}
                              focus={statusFilter.includes(status)}
                              onPress={() => tagOnPress(status)}
                            />
                          );
                        })
                    : positionList.map(position => {
                        return (
                          <Chip
                            key={position}
                            focus={positionFilter?.includes(position)}
                            label={position}
                            onPress={() => {
                              positionFilter?.includes(position)
                                ? setPositionFilter(
                                    positionFilter.filter(
                                      item => item !== position,
                                    ),
                                  )
                                : setPositionFilter([
                                    ...positionFilter,
                                    position,
                                  ]);
                            }}
                          />
                        );
                      })}
                </ScrollView>
                {selectedERecruitStat.type == 'inProgress' && (
                  <TouchableOpacity
                    onPress={() => {
                      setIsOpenFilter(true);
                    }}>
                    {positionFilter.length > 0 || statusFilter.length > 0 ? (
                      <Icon.Filtered2 />
                    ) : (
                      <Icon.Filter fill={colors.onBackground} />
                    )}
                  </TouchableOpacity>
                )}
              </FilterSectionRow>
            )}

            {query && query != '' && (
              <Row gap={space[2]}>
                <Typography.Body color={colors.palette.fwdGreyDarkest}>
                  {positionFilter.length > 0 || statusFilter.length > 0
                    ? 'Filtered results'
                    : 'Search results'}{' '}
                  (
                  {selectedERecruitStat.type === 'inProgress'
                    ? processedData.filter(
                        item =>
                          item.status !== 'APPROVED' &&
                          item.status !== 'REJECTED',
                      ).length
                    : processedData.length}
                  )
                </Typography.Body>

                <TouchableOpacity
                  onPress={() => {
                    if (order === 'newest') {
                      setOrder('oldest');
                    } else {
                      setOrder('newest');
                    }
                  }}>
                  <Row>
                    <Typography.Body
                      fontWeight="bold"
                      color={colors.palette.fwdAlternativeOrange[100]}>
                      {order === 'newest' ? 'Newest' : ' Oldest'}
                    </Typography.Body>

                    <Box
                      width={sizes[4]}
                      height={sizes[5]}
                      justifyContent="center">
                      {order === 'newest' ? (
                        <Icon.ArrowDown
                          size={sizes[4]}
                          fill={colors.palette.fwdAlternativeOrange[100]}
                        />
                      ) : (
                        <Icon.ArrowUp
                          size={sizes[4]}
                          fill={colors.palette.fwdAlternativeOrange[100]}
                        />
                      )}
                    </Box>
                  </Row>
                </TouchableOpacity>
              </Row>
            )}
          </Column>
        </SearchResultHeader>

        {query && !isRefetching && (
          <CandidatesList
            isLoading={isLoading}
            data={candidatesList as ApplicationListResponds[]}
            isRefreshing={isRefetching}
            onRefresh={refetch}
            contentContainerStyle={{
              paddingBottom: 80,
              paddingHorizontal: 0,
              paddingVertical: 0,
            }}
            haveFilter={
              country === 'id'
                ? false
                : statusFilter.length > 0 || positionFilter.length > 0
            }
            isSearch={country !== 'id'}
            statusKey="CandidatesSearchScreen"
            handleScroll={handleScroll}
            handleScrollEnd={handleScrollEnd}
          />
        )}
      </SearchResultContainer>

      <CandidateChipsFilterPanel
        visible={isOpenFilter}
        handleClose={() => setIsOpenFilter(false)}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        positionFilter={positionFilter}
        setPositionFilter={setPositionFilter}
      />
    </>
  );
}

interface ChipButtonProps {
  type?: string;
  label: string;
  value?: string;
  icon?: JSX.Element;
  onSelect: () => void;
  isActive: boolean;
}

function ChipButton({ label, onSelect, isActive }: ChipButtonProps) {
  const { colors } = useTheme();
  return (
    <ButtonContainer isActive={isActive} onPress={onSelect}>
      <H8
        fontWeight={isActive ? 'bold' : 'normal'}
        color={colors.palette.white}>
        {label}
      </H8>
    </ButtonContainer>
  );
}

const ButtonContainer = styled(Pressable)<{ isActive: boolean; theme?: Theme }>(
  ({ isActive, theme }) => ({
    height: theme.sizes[10],
    flexDirection: 'row',
    backgroundColor: isActive
      ? theme.colors.palette.fwdAlternativeOrange[100]
      : theme.colors.primary,
    paddingHorizontal: theme.space[4],
    paddingVertical: theme.space[2],
    alignItems: 'center',
    borderWidth: isActive ? 2 : 1,
    borderColor: theme.colors.palette.white,
    borderRadius: theme.borderRadius['full'],
  }),
);

const SearchContainer = styled.View(
  ({ theme, insets }: { insets: EdgeInsets; theme?: Theme }) => ({
    paddingTop: insets.top,
    backgroundColor: theme?.colors.primary,
    paddingBottom: theme?.space[5],
  }),
);

const HeaderContainer = styled(ResponsiveView)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal: Platform.OS === 'ios' ? theme.space[3] : 0,
}));

const SearchBarContainer = styled(ResponsiveView)(({ theme }) => ({
  paddingTop: theme.space[3],
  paddingBottom: theme.space[2],
  paddingHorizontal: theme?.space[4],
}));

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const SearchHint = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.background,
  paddingHorizontal: theme?.space[4],
}));

const SearchHintContainer = styled(ResponsiveView)(({ theme }) => ({
  color: theme.colors.primary,
  paddingHorizontal: theme?.space[4],
}));

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const SearchResultHeader = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
}));
