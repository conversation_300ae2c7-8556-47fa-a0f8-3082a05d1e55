import { Platform, View } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import { Icon, Typography } from 'cube-ui-components';
import { SafeAreaView } from 'react-native-safe-area-context';
import ResponsiveText from 'components/ResponsiveTypography';
import useBoundStore from 'hooks/useBoundStore';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';

export default function CandidateSearchHeader() {
  const theme = useTheme();
  const { colors, space } = theme;
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const headerHeight = useBoundStore(store => store.lead.searchBarHeight);
  const { updateSearchBarHeight } = useBoundStore(store => store.leadActions);
  const { t } = useTranslation('eRecruit');

  return (
    <SafeAreaView edges={['top']} style={{ backgroundColor: colors.primary }}>
      <View
        style={[
          { paddingHorizontal: theme.space[4] },
          {
            ...(Platform.OS === 'android' && {
              paddingVertical: space[3],
            }),
          },
          {
            ...(Platform.OS === 'ios' && {
              paddingBottom: space[3],
              paddingTop: 0,
            }),
          },
        ]}
        onLayout={e => {
          !headerHeight && updateSearchBarHeight(e.nativeEvent.layout.height);
        }}>
        <SearchBarFrame
          onPress={() => {
            navigation.navigate('CandidatesSearch', {
              isShowFilter: country !== 'id',
            });
          }}>
          <Icon.Search fill={colors.background}></Icon.Search>
          <ResponsiveText
            TypographyDefault={Typography.LargeBody}
            TypographyWide={Typography.ExtraLargeBody}
            color={colors.onPrimary}
            style={{ paddingHorizontal: theme.space[3] }}>
            {t('eRecruit.searchCandidate')}
          </ResponsiveText>
        </SearchBarFrame>
      </View>
    </SafeAreaView>
  );
}

const SearchBarFrame = styled.Pressable(({ theme }) => ({
  width: '100%',
  backgroundColor: theme.colors.primaryVariant,
  borderRadius: theme.borderRadius.full,
  flexDirection: 'row',
  paddingHorizontal: theme.space[4],
  paddingVertical: theme.space[3],
  alignSelf: 'flex-end',
  justifyContent: 'flex-start',
}));
