import React, {
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import { SavedProposalsQueryParams } from 'api/caseApi';
import useDebounce from 'features/policy/hooks/useDebounce';
import {
  ApplicationListResponds,
  ApplicationStageKeys,
  CubeStatusKeys,
  cubeStatusList,
  positionList,
  SortDirectionKeys,
} from 'types/eRecruit';
import { useGetERApplicationList } from 'features/eRecruit/hooks/useGetERApplicationList';
import SearchModal from './SearchModal';
import styled from '@emotion/native';
import {
  Box,
  Chip,
  Column,
  H8,
  Icon,
  Row,
  Typography,
} from 'cube-ui-components';
import { Theme, useTheme } from '@emotion/react';
import Animated, {
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import {
  NativeScrollEvent,
  NativeSyntheticEvent,
  Pressable,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { lastUpdateHandler } from 'features/eRecruit/ib/tablet/applicationStatus/ERInProgressTable';
import { ShowComponentHandle } from 'features/eRecruit/ph/phone/components/RecruitmentBanner';
import CandidateChipsFilterPanel from '../utils/CandidateChipsFilterPanel';
import { FilterDot } from 'components/FilterDot';
import CandidatesList from '../CandidatesList/CandidatesList';
import { country } from 'utils/context';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

export const SearchCandidateModal = memo(function SearchProposalModal({
  visible,
  onClose,
}: {
  visible: boolean;
  onClose: () => void;
} & SavedProposalsQueryParams) {
  const { sizes, colors, space } = useTheme();
  const showComponentRef = useRef<ShowComponentHandle>(null);
  const prevOffset = useRef(0);
  const scrollToShow = () => showComponentRef.current?.showComponent();
  const scrollToHide = () => showComponentRef.current?.hideComponent();

  const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    if (currentOffset === 0) {
      refetch();
      scrollToShow();
    } else if (currentOffset > prevOffset.current && currentOffset !== 0) {
      scrollToHide();
    } else if (currentOffset < prevOffset.current && currentOffset !== 0) {
      scrollToShow();
    }
  };

  const handleScrollEnd = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
    const currentOffset = event.nativeEvent.contentOffset.y;
    prevOffset.current = currentOffset;
  };

  const { t } = useTranslation(['common', 'eRecruit']);
  const [query, setQuery] = useState('');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');

  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [positionFilter, setPositionFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<CubeStatusKeys[]>([]);

  const {
    data: allData,
    isLoading: isLoadingAll,
    refetch: refetchAll,
    isRefetching: isRefetchingAll,
  } = useGetERApplicationList(
    {
      cubeStatusList: [],
      limit: 99999,
      direction: order === 'oldest' ? 'ASC' : 'DESC',
      query: query,
    },
    country === 'id',
  );

  const eRecruitStat = useMemo(() => {
    return [
      {
        type: 'inProgress',
        label: t('eRecruit:applicationStatus.inProgress'),
        count:
          allData?.data.filter(
            item =>
              item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
          ).length ?? 0,
      },
      {
        type: 'approved',
        label: t('eRecruit:applicationStatus.approved'),
        count:
          allData?.data.filter(item => item.cubeStatus === 'APPROVED').length ??
          0,
      },
      {
        type: 'rejected',
        label: t('eRecruit:applicationStatus.rejected'),
        count:
          allData?.data.filter(item => item.cubeStatus === 'REJECTED').length ??
          0,
      },
    ];
  }, [t, allData]);

  const filterLabelMap: Record<CubeStatusKeys, string> = {
    PENDING_PAYMENT: t('eRecruit:candidate.status.pendingPayment'),
    PENDING_LEADER_APPROVAL: t(
      'eRecruit:candidate.status.pendingLeaderApproval',
    ),
    APPROVED: 'Approved',
    REJECTED: 'Rejected',
    REMOTE_CHECKING: t('eRecruit:candidate.status.remoteCheckingRequired'),
    REMOTE_SIGNATURE: t('eRecruit:candidate.status.pendingRemoteSignature'),
    RESUME_APPLICATION: t('eRecruit:candidate.status.resumeApplication'),
    POTENTIAL_CANDIDATE: t('eRecruit:candidate.status.created'),
  };

  const [selectedERecruitStat, setSelectedERecruitStat] = useState({
    type: 'all',
    label: '',
  });

  const debouncedQuery = useDebounce(query);

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList:
      selectedERecruitStat.type == 'inProgress' && statusFilter
        ? (statusFilter as CubeStatusKeys[])
        : selectedERecruitStat.type == 'approved'
        ? ['APPROVED']
        : selectedERecruitStat.type == 'rejected'
        ? ['REJECTED']
        : [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
    query: query,
  });

  const candidatesList = useMemo(() => {
    if (
      selectedERecruitStat.type == 'inProgress' &&
      !(positionFilter.length > 0)
    ) {
      return data?.data.filter(
        item =>
          item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
      );
    }
    if (
      selectedERecruitStat.type == 'inProgress' &&
      positionFilter.length > 0
    ) {
      return data?.data
        .filter(
          item =>
            item.cubeStatus !== 'APPROVED' && item.cubeStatus !== 'REJECTED',
        )
        .filter(item =>
          positionFilter.includes(item.candidatePositionCode as string),
        );
    }
    if (positionFilter.length > 0) {
      return data?.data.filter(item =>
        positionFilter.includes(item.candidatePositionCode as string),
      );
    } else {
      return data?.data;
    }
  }, [data, positionFilter]);

  const [isSortDate, setIsSortDate] = useState(false);

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !data
        ? []
        : data?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePositionCode ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              approvalDate: item.approvedDate ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [data, isSortDate],
  );

  const dataOnPositionFilter = useMemo(
    () =>
      positionFilter.length > 0
        ? sortedAppStatusData.filter(item =>
            positionFilter.includes(item.position as string),
          )
        : sortedAppStatusData,
    [sortedAppStatusData, positionFilter],
  );

  const processedData = useMemo(
    () =>
      statusFilter.length > 0
        ? dataOnPositionFilter.filter(item =>
            statusFilter.includes(item.status),
          )
        : dataOnPositionFilter,
    [dataOnPositionFilter, statusFilter],
  );

  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    statusFilter.includes(tabFilter)
      ? setStatusFilter(statusFilter.filter(item => item !== tabFilter))
      : setStatusFilter([...statusFilter, tabFilter]);
  };

  useEffect(() => {
    if (
      processedData.filter(
        item => item.status !== 'APPROVED' && item.status !== 'REJECTED',
      ).length > 0
    ) {
      setSelectedERecruitStat({
        type: 'inProgress',
        label: t('eRecruit:applicationStatus.inProgress'),
      });
      return;
    }
    if (processedData.filter(item => item.status === 'APPROVED').length > 0) {
      setSelectedERecruitStat({
        type: 'approved',
        label: t('eRecruit:applicationStatus.approved'),
      });
      return;
    }
    if (processedData.filter(item => item.status === 'REJECTED').length > 0) {
      setSelectedERecruitStat({
        type: 'rejected',
        label: t('eRecruit:applicationStatus.rejected'),
      });
      return;
    }
  }, [query]);

  useImperativeHandle(showComponentRef, () => ({
    hideComponent: () => hideComponent(),
    showComponent: () => showComponent(),
  }));

  const heightValue = country === 'id' ? sizes[34] - 2 : sizes[27];
  const opacity = useSharedValue(1);
  const height = useSharedValue(heightValue);
  const ANIMATION_DURATION = 300;

  const hideComponent = () => {
    opacity.value = withTiming(0, { duration: ANIMATION_DURATION });
    height.value = withTiming(0, { duration: ANIMATION_DURATION });
  };

  const showComponent = () => {
    opacity.value = withTiming(1, { duration: ANIMATION_DURATION });
    height.value = withTiming(heightValue, {
      duration: ANIMATION_DURATION,
    });
  };

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
      height: height.value,
    };
  });

  const handleSearch = (text: string) => {
    setSelectedERecruitStat({
      type: 'inProgress',
      label: t('eRecruit:applicationStatus.inProgress'),
    });
    setQuery(text);
  };

  const onDismiss = useCallback(() => {
    setQuery('');
    setSelectedERecruitStat({ type: 'all', label: '' });
    onClose();
    if (country === 'id') {
      setPositionFilter([]);
      setStatusFilter([]);
    }
  }, [onClose]);

  const onRefresh = useCallback(() => {
    refetch();
    if (country === 'id') {
      refetchAll();
    }
  }, [refetch, refetchAll]);

  return (
    <>
      <SearchModal
        label={'Search'}
        placeholder={'candidate'}
        hint={'e.g. Candidate’s name, mobile'}
        visible={visible}
        onClose={onDismiss}
        query={query}
        onChangeQuery={query => handleSearch(query)}
        loading={debouncedQuery !== '' && (isLoading || isLoadingAll)}
        resultCount={candidatesList?.length || 0}>
        {debouncedQuery !== '' && (
          <Box paddingBottom={space[5]}>
            <ScrollView
              horizontal
              contentContainerStyle={{ gap: space[1], paddingLeft: space[4] }}>
              {eRecruitStat.map((item, index) => (
                <ChipButton
                  key={`${item}_${index}`}
                  label={
                    country === 'id'
                      ? `${item.label} (${item.count})`
                      : `${item.label}`
                  }
                  onSelect={() => {
                    setSelectedERecruitStat(item);
                  }}
                  isActive={selectedERecruitStat.label === item.label}
                />
              ))}
            </ScrollView>
          </Box>
        )}

        {debouncedQuery !== '' && (
          <SearchResultContainer
            style={{
              paddingHorizontal: sizes[4],
            }}>
            <SearchResultHeader
              layout={LinearTransition}
              style={[
                animatedStyle,
                {
                  maxHeight:
                    selectedERecruitStat.type != 'all' && query
                      ? heightValue
                      : 46,
                },
              ]}>
              <Column gap={space[2]} paddingY={space[4]}>
                {selectedERecruitStat.type != 'all' && query && (
                  <FilterSectionRow>
                    <Typography.Body color={colors.palette.fwdGreyDarkest}>
                      Filter by
                    </Typography.Body>
                    <ScrollView
                      horizontal
                      showsHorizontalScrollIndicator={false}
                      contentContainerStyle={{
                        gap: space[1],
                      }}>
                      {selectedERecruitStat.type == 'inProgress'
                        ? cubeStatusList
                            .filter(
                              status =>
                                status !== 'APPROVED' && status !== 'REJECTED',
                            )
                            .map(status => {
                              return (
                                <Chip
                                  key={status}
                                  label={filterLabelMap?.[status]}
                                  focus={statusFilter.includes(status)}
                                  onPress={() => tagOnPress(status)}
                                />
                              );
                            })
                        : positionList.map(position => {
                            return (
                              <Chip
                                key={position}
                                focus={positionFilter?.includes(position)}
                                label={position}
                                onPress={() => {
                                  positionFilter?.includes(position)
                                    ? setPositionFilter(
                                        positionFilter.filter(
                                          item => item !== position,
                                        ),
                                      )
                                    : setPositionFilter([
                                        ...positionFilter,
                                        position,
                                      ]);
                                }}
                              />
                            );
                          })}
                    </ScrollView>
                    {selectedERecruitStat.type == 'inProgress' && (
                      <TouchableOpacity
                        onPress={() => {
                          setIsOpenFilter(true);
                        }}>
                        {positionFilter.length > 0 ||
                        statusFilter.length > 0 ? (
                          <Icon.Filtered2 />
                        ) : (
                          <Icon.Filter fill={colors.onBackground} />
                        )}
                      </TouchableOpacity>
                    )}
                  </FilterSectionRow>
                )}

                {query && query != '' && (
                  <Row marginTop={space[3]} gap={space[2]}>
                    <Typography.Body color={colors.palette.fwdGreyDarkest}>
                      {positionFilter.length > 0 || statusFilter.length > 0
                        ? 'Filtered results'
                        : 'Search results'}{' '}
                      (
                      {selectedERecruitStat.type === 'inProgress'
                        ? processedData.filter(
                            item =>
                              item.status !== 'APPROVED' &&
                              item.status !== 'REJECTED',
                          ).length
                        : processedData.length}
                      )
                    </Typography.Body>
                    <Row>
                      <TouchableOpacity
                        onPress={() => {
                          if (order === 'newest') {
                            setOrder('oldest');
                          } else {
                            setOrder('newest');
                          }
                        }}>
                        <Row>
                          <Typography.Body
                            fontWeight="bold"
                            color={colors.palette.fwdAlternativeOrange[100]}>
                            {order === 'newest' ? 'Newest' : ' Oldest'}
                          </Typography.Body>
                          <Box justifyContent="center">
                            {order === 'newest' ? (
                              <Icon.ArrowDown
                                size={sizes[4]}
                                fill={colors.palette.fwdAlternativeOrange[100]}
                              />
                            ) : (
                              <Icon.ArrowUp
                                size={sizes[4]}
                                fill={colors.palette.fwdAlternativeOrange[100]}
                              />
                            )}
                          </Box>
                        </Row>
                      </TouchableOpacity>
                    </Row>
                  </Row>
                )}
                {query && query != '' && country === 'id' && (
                  <Typography.Body color={colors.palette.fwdGreyDarkest}>
                    Displaying data from last 90 days.
                  </Typography.Body>
                )}
              </Column>
            </SearchResultHeader>

            {query && !isRefetching && !isRefetchingAll && (
              <CandidatesList
                isLoading={isLoading || isLoadingAll}
                data={candidatesList as ApplicationListResponds[]}
                isRefreshing={isRefetching || isRefetchingAll}
                onPress={onDismiss}
                onRefresh={onRefresh}
                contentContainerStyle={{
                  paddingBottom: 80,
                  paddingHorizontal: 0,
                  paddingVertical: 0,
                }}
                haveFilter={
                  country === 'id'
                    ? false
                    : statusFilter.length > 0 || positionFilter.length > 0
                }
                isSearch={country !== 'id'}
                statusKey="SearchCandidateModal"
                handleScroll={handleScroll}
                handleScrollEnd={handleScrollEnd}
                isShowStatus={
                  country !== 'id' || selectedERecruitStat.type === 'inProgress'
                }
              />
            )}
          </SearchResultContainer>
        )}

        <CandidateChipsFilterPanel
          visible={isOpenFilter}
          handleClose={() => setIsOpenFilter(false)}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          positionFilter={positionFilter}
          setPositionFilter={setPositionFilter}
        />
      </SearchModal>
    </>
  );
});

interface ChipButtonProps {
  type?: string;
  label: string;
  value?: string;
  icon?: JSX.Element;
  onSelect: () => void;
  isActive: boolean;
}

function ChipButton({ label, onSelect, isActive }: ChipButtonProps) {
  const { colors } = useTheme();
  return (
    <ButtonContainer isActive={isActive} onPress={onSelect}>
      <H8
        fontWeight={isActive ? 'bold' : 'normal'}
        color={colors.palette.white}>
        {label}
      </H8>
    </ButtonContainer>
  );
}

const ButtonContainer = styled(Pressable)<{ isActive: boolean; theme?: Theme }>(
  ({ isActive, theme }) => ({
    height: theme.sizes[10],
    flexDirection: 'row',
    backgroundColor: isActive
      ? theme.colors.palette.fwdAlternativeOrange[100]
      : theme.colors.primary,
    paddingHorizontal: theme.space[4],
    paddingVertical: theme.space[2],
    alignItems: 'center',
    borderWidth: isActive ? 2 : 1,
    borderColor: theme.colors.palette.white,
    borderRadius: theme.borderRadius['full'],
  }),
);

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  backgroundColor: theme?.colors.surface,
}));

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const SearchResultHeader = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme?.colors.surface,
}));
