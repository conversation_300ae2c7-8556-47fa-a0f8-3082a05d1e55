import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import LongArrowSVG from 'features/eRecruit/assets/LongArrowSVG';
import ShortArrowSVG from 'features/eRecruit/assets/ShortArrowSVG';
import CreditCardInHandSVG from 'features/eRecruit/assets/icon/CreditCardInHandSVG';
import FormSVG from 'features/eRecruit/assets/icon/FormSVG';
import HappyPeopleSVG from 'features/eRecruit/assets/icon/HappyPeopleSVG';
import ManSVG from 'features/eRecruit/assets/icon/ManSVG';
import { useTranslation } from 'react-i18next';
import { RootStackParamListMap } from 'types';
import { TimeSectionKeys } from 'types/eRecruit';
import React from 'react';
import Skeleton from 'components/Skeleton';
import { useWindowDimensions } from 'react-native';
import { country } from 'utils/context';
import NonPaidLongArrowSVGV2 from 'features/eRecruit/assets/NonPaidLongArrowSVGV2';
import ShortArrowSVGV2 from 'features/eRecruit/assets/ShortArrowSVGV2';

export default function SkeletonRecruitmentConversion({
  timeSection,
}: {
  timeSection: TimeSectionKeys;
}) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const timePeriodLabel = ' ' + (timeSection ?? '-');
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();
  const { width } = useWindowDimensions();

  const paidVisible = country !== 'id';

  const funnelConfig = [
    {
      label: 'Candidate in progress',
      icon: <ManSVG width={space[8]} height={space[8]} />,
      value: 0,
      width: paidVisible ? 1 : 0.85,
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', { status: undefined }),
    },
    {
      label: 'Submitted',
      icon: <FormSVG width={space[8]} height={space[8]} />,
      value: 0,
      width: paidVisible ? 0.88 : 0.75,
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', { status: 'PENDING_PAYMENT' }),
    },
    {
      label: 'Paid',
      icon: <CreditCardInHandSVG width={space[8]} height={space[8]} />,
      value: 0,
      width: 0.75,
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', {
          status: 'PENDING_LEADER_APPROVAL',
        }),
      hidden: !paidVisible,
    },
    {
      label: 'Approved',
      icon: <HappyPeopleSVG width={space[8]} height={space[8]} />,
      value: 0,
      width: paidVisible ? 0.61 : 0.53,
      navigateTo: () =>
        navigate('ERecruitApplicationStatus', { status: 'APPROVED' }),
    },
  ];

  const viewWidth = paidVisible ? width - space[20] : width - space[8];

  const isNewDesign = country === 'id';
  return (
    <Box
      padding={isNewDesign ? space[4] : space[5]}
      paddingBottom={space[5]}
      borderRadius={space[4]}
      backgroundColor={colors.background}
      overflow="hidden">
      <Typography.H6 fontWeight="bold">
        This{timePeriodLabel} agent conversion
      </Typography.H6>
      <Box boxSize={space[5]} />
      <Column alignItems="center">
        <Box
          position="absolute"
          left={-space[2]}
          top={paidVisible ? space[7] : space[8]}>
          {paidVisible ? (
            <LongArrowSVG height={sizes[44]} />
          ) : (
            <NonPaidLongArrowSVGV2 />
          )}
          <Column
            position="absolute"
            bottom={space[6]}
            left={space[4]}
            gap={space[1]}>
            <Skeleton width={sizes[5]} height={sizes[4]} radius={4} />

            <Skeleton width={sizes[7]} height={sizes[4]} radius={4} />
          </Column>
        </Box>
        {/* first short arrow */}
        <Row
          alignItems="center"
          gap={space[1]}
          position="absolute"
          top={paidVisible ? space[22] : space[23]}
          right={-space[3]}>
          {paidVisible ? <ShortArrowSVG /> : <ShortArrowSVGV2 />}
          <Column gap={space[1]}>
            <Skeleton width={sizes[5]} height={sizes[4]} radius={4} />

            <Skeleton width={sizes[7]} height={sizes[4]} radius={4} />
          </Column>
        </Row>
        {/* second short arrow */}
        {paidVisible && (
          <Row
            alignItems="center"
            gap={space[1]}
            position="absolute"
            top={space[38]}
            right={-space[3]}>
            <ShortArrowSVG />
            <Column gap={space[1]}>
              <Skeleton width={sizes[5]} height={sizes[4]} radius={4} />

              <Skeleton width={sizes[7]} height={sizes[4]} radius={4} />
            </Column>
          </Row>
        )}

        {funnelConfig.map((config, index) => {
          if (config.hidden) return null;
          return (
            <React.Fragment key={config.label}>
              <Box
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  borderWidth: 1,
                  borderColor: colors.palette.fwdOrange[20],
                  width: viewWidth * config.width,
                  borderRadius: 30,
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  paddingLeft: paidVisible ? space[4] : space[3],
                  paddingRight: paidVisible ? space[4] : space[2],
                  paddingVertical: paidVisible ? space[2] : space[2] - 1,
                  backgroundColor: colors.palette.fwdOrange[5],
                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 0,
                  },
                  shadowOpacity: 0.16,
                  shadowRadius: 10,
                  elevation: 5, // This is for Android
                }}>
                <Row
                  flex={1}
                  alignItems="center"
                  justifyContent="space-between">
                  <Skeleton width={sizes[6]} height={sizes[6]} radius={4} />

                  <Skeleton
                    width={sizes[28] + index * -sizes[6]}
                    height={sizes[4]}
                    radius={4}
                  />

                  <Skeleton width={sizes[5]} height={sizes[4]} radius={4} />
                </Row>
                <Icon.ChevronRight size={space[3]} />
              </Box>
              {index !== funnelConfig.length - 1 && (
                <Box
                  width={viewWidth * 0.6 * config.width}
                  style={{
                    height: paidVisible ? space[4] : space[3],
                    backgroundColor: colors.primary,
                    transform: [{ skewX: '134deg' }],
                  }}
                />
              )}
            </React.Fragment>
          );
        })}
      </Column>
    </Box>
  );
}
