import { useTheme } from '@emotion/react';
import {
  Box,
  Column,
  Icon,
  LoadingIndicator,
  Row,
  Typography,
  XView,
  addToast,
} from 'cube-ui-components';

import { useEffect, useMemo, useRef, useState } from 'react';
import {
  Linking,
  Modal,
  ScrollView,
  Share,
  TouchableOpacity,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RecruitmentProgressSection from './RecruitmentProgressSection';
import FormSVG from 'features/eRecruit/assets/icon/FormSVG';
import PhoneWithFingerSVG from 'features/eRecruit/assets/icon/PhoneWithFingerSVG';
import DocumentWithTick from 'features/eRecruit/assets/icon/DocumentWithTickSVG';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { MainTabParamList, RootStackParamListMap } from 'types';

import styled from '@emotion/native';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import ShareRemoteLinkSVG from 'features/eRecruit/assets/icon/ShareRemoteLinkSVG';
import AddNewCandidateModal from 'features/eRecruit/my/tablet/components/AddNewCandidateModal';
import AddNewCandidateFormMY from 'features/eRecruit/my/tablet/components/AddNewCandidateForm';
import AddNewCandidateFormID from 'features/eRecruit/id/tablet/components/AddNewCandidateForm';
import { useGetERecruitStat } from 'features/eRecruit/hooks/useGetERecruitStat';
import { RecruitmentHomeStat } from 'types/eRecruit';
import ShareRemoteLinkButton from 'features/eRecruit/my/tablet/ShareRemoteLinkButton';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import { useGetRemoteLinkQuery } from 'features/eRecruit/hooks/useGetRemoteLink';
import * as Clipboard from 'expo-clipboard';
import { useTranslation } from 'react-i18next';

import SearchingCandidatesListSection from './SearchingCandidatesList';
import { build, country } from 'utils/context';
import { useGetReviewApplicationList } from 'hooks/useGetApprovedAndRejectedLIst';
import { useGetAgentInfo } from 'hooks/useGetAgentInfo';
import useBoundStore from 'hooks/useBoundStore';
import ShareRecruitRemoteLinkModal from './components/ShareRecruitRemoteLinkModal';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { RecruitActionConfig } from 'features/eRecruit/types';
import MaterialsIconSVG from 'features/eRecruit/assets/MaterialsIconSVG';

export default function MainScreenLeftSection() {
  const { space, colors } = useTheme();
  const scrollViewRef = useRef<KeyboardAwareScrollView | null>(null);
  const [isSearching, setIsSearching] = useState<boolean>(false);

  const { data, isLoading } = useGetERecruitStat();
  const { data: reviewApplicationData, isLoading: isLoadingReviewApplication } =
    useGetReviewApplicationList({
      status: 'PENDING_LEADER_APPROVAL',
    });
  const reviewAgentSubmissionCount = reviewApplicationData?.length ?? 0;
  const route = useRoute<RouteProp<MainTabParamList, 'ERecruit'>>();
  const paramValue = route.params;
  const [isShowModal, setIsShowModal] = useState(
    paramValue?.shareMessage ? true : false,
  );

  useEffect(() => {
    if (paramValue?.shareMessage) {
      setIsShowModal(true);
    } else {
      setIsShowModal(false);
    }
  }, [paramValue?.shareMessage]);

  if (isSearching) {
    return (
      <CommonAnimatedViewWrapper>
        <SearchingCandidatesListSection setIsSearching={setIsSearching} />
      </CommonAnimatedViewWrapper>
    );
  }

  return (
    <>
      <AddCandidateButton />
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        style={{
          backgroundColor: colors.palette.fwdGrey[50],
          paddingTop: space[6],
          paddingRight: space[8],
          paddingLeft: space[8],
          flex: 1,
        }}>
        <RecruitmentHeader setIsSearching={setIsSearching} />
        <Box boxSize={space[6]} />
        <RecruitmentAction
          homeData={data}
          reviewAgentSubmissionCount={reviewAgentSubmissionCount}
        />
        <Box boxSize={space[6]} />
        <RecruitmentProgressSection
          homeData={data}
          scrollViewRef={scrollViewRef}
          isLoading={isLoading}
        />
        <Box boxSize={space[20]} />
      </KeyboardAwareScrollView>
      <ShareRecruitRemoteLinkModal
        isOpen={isShowModal}
        setIsOpen={setIsShowModal}
        message={paramValue?.shareMessage}
      />
    </>
  );
}

function AddCandidateButton() {
  const { colors, space, getElevation } = useTheme();
  const [newCandidateModalVisible, setNewCandidateModalVisible] =
    useState(false);

  const AddNewCandidateForm =
    country === 'id' ? AddNewCandidateFormID : AddNewCandidateFormMY;
  return (
    <>
      <AddNewCandidateModal visible={newCandidateModalVisible}>
        <AddNewCandidateForm
          onClose={() => setNewCandidateModalVisible(false)}
        />
      </AddNewCandidateModal>
      <TouchableOpacity
        onPress={() => {
          setNewCandidateModalVisible(true);
        }}
        style={{
          width: 166,
          borderRadius: space[10],
          backgroundColor: colors.primary,
          padding: space[3],
          zIndex: 100,
          position: 'absolute',
          ...getElevation(8),
          top: '90%',
          right: '50%',
        }}>
        <Row justifyContent="center" alignItems="center" gap={space[2]}>
          <Icon.AddAccount fill={colors.palette.white} />
          <Typography.H8 color={colors.background} fontWeight="bold">
            Add candidate
          </Typography.H8>
        </Row>
      </TouchableOpacity>
    </>
  );
}

function RecruitmentHeader({
  setIsSearching,
}: {
  setIsSearching: (value: boolean) => void;
}) {
  const { space, colors, getElevation } = useTheme();
  const { t } = useTranslation('eRecruit');

  return (
    <Row justifyContent="space-between" alignItems="center">
      <Typography.H6 fontWeight="bold">e-Recruit</Typography.H6>
      {/* need to put in the search later on  */}
      <TouchableOpacity onPress={() => setIsSearching(true)}>
        <Row
          justifyContent="center"
          gap={space[2]}
          alignItems="center"
          borderRadius={space[10]}
          paddingX={space[5]}
          paddingY={space[2]}
          borderColor={colors.palette.fwdOrange[50]}
          borderWidth={2}
          backgroundColor={colors.background}
          style={{
            ...getElevation(8),
          }}>
          <Icon.Search size={18} />
          <Typography.LargeLabel color={colors.primary} fontWeight="medium">
            {t('eRecruit.searchCandidate')}
          </Typography.LargeLabel>
        </Row>
      </TouchableOpacity>
    </Row>
  );
}

function RecruitmentAction({
  homeData,
  reviewAgentSubmissionCount,
}: {
  homeData: RecruitmentHomeStat | undefined;
  reviewAgentSubmissionCount: number;
}) {
  const { t } = useTranslation('eRecruit');
  const { data: agentProfile, isLoading } = useGetAgentProfile();
  const isAGT = agentProfile?.designationCode == 'AGT';
  const isFWP = agentProfile?.designationCode == 'FWP';

  const { space, colors, sizes } = useTheme();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['ib']>>();

  const [isOpen, setIsOpen] = useState(false);

  const paidVisible = country !== 'ib';

  const countOnCandidateStatus = () => {
    const status = homeData?.status;
    const pendingRemoteSignatureCount = status?.pendingRemoteSignature ?? 0;
    const pendingLeaderApprovalCount = status?.pendingLeaderApproval ?? 0;
    const remoteCheckingRequiredCount = status?.remoteCheckingRequired ?? 0;
    const pendingPaymentCount = status?.pendingPayment ?? 0;

    return paidVisible
      ? pendingRemoteSignatureCount +
          pendingLeaderApprovalCount +
          remoteCheckingRequiredCount +
          pendingPaymentCount
      : pendingLeaderApprovalCount + remoteCheckingRequiredCount;
  };

  const buttonConfig = [
    {
      key: 'startApplication',
      icon: <FormSVG />,
      label: 'Start application',
      count: 0,
      nav: () => navigate('ERecruitApplication'),
      isHidden: false,
    },
    {
      key: 'shareRemoteLink',
      icon: <ShareRemoteLinkSVG width={40} height={40} />,
      label: 'Share \n remote link',
      count: 0,
      nav: () => setIsOpen(true),
      isHidden: country === 'id',
    },
    {
      key: 'trackMyCandidateStatus',
      icon: <PhoneWithFingerSVG />,
      label: 'Track my candidate status',
      count: countOnCandidateStatus() ?? 0,
      nav: () => navigate('ERecruitApplicationStatus', { status: undefined }),
      isHidden: false,
    },
    {
      key: 'reviewAgentSubmission',
      icon: <DocumentWithTick />,
      label: t('review.title'),
      count: reviewAgentSubmissionCount,
      nav: () => navigate('ERecruitReviewAgentsSubmission'),
      isHidden: country === 'id' ? isFWP : isAGT,
    },
    {
      key: 'materials',
      icon: <MaterialsIconSVG />,
      label: t('materials.shortCut.materials'),
      count: 0,
      nav: () => {
        navigate('Materials');
      },
      isHidden: country !== 'id',
    },
  ] satisfies RecruitActionConfig;

  const visibleButtonConfigCount = buttonConfig.filter(i => !i.isHidden).length;

  return (
    <Box
      backgroundColor={colors.background}
      borderRadius={space[4]}
      paddingX={space[6]}
      paddingY={space[4]}>
      <Box justifyContent="center" height={42}>
        <Typography.H7 fontWeight="bold">
          What would you like to do?
        </Typography.H7>
      </Box>
      {country !== 'id' && (
        <ShareRemoteLinkModal isOpen={isOpen} setIsOpen={setIsOpen} />
      )}
      <Box boxSize={space[4]} />
      <Row justifyContent="space-between" flex={1}>
        {buttonConfig.map(config => {
          if (config.isHidden) {
            return null;
          }
          return (
            <Column key={config.key} alignItems="center" flex={1}>
              <TouchableOpacity
                onPress={config.nav}
                style={{
                  position: 'relative',
                  marginBottom: space[2],
                  width: sizes[18],
                  height: sizes[18],
                  backgroundColor: colors.palette.fwdOrange[20],
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: space[5],
                }}>
                {config.count > 0 && (
                  <Box
                    position="absolute"
                    top={-space[2]}
                    right={-space[2]}
                    zIndex={100}
                    height={space[6]}
                    width={space[6]}
                    borderRadius={space[5]}
                    backgroundColor={colors.palette.alertRed}
                    alignItems="center"
                    justifyContent="center">
                    <Typography.SmallLabel color={colors.background}>
                      {config.count}
                    </Typography.SmallLabel>
                  </Box>
                )}
                {config.icon}
              </TouchableOpacity>
              <Box
                maxH={54}
                maxW={visibleButtonConfigCount >= 4 ? 100 : undefined}>
                <Typography.H8 style={{ textAlign: 'center' }}>
                  {config.label}
                </Typography.H8>
              </Box>
            </Column>
          );
        })}
      </Row>
    </Box>
  );
}

const ShareRemoteLinkModal = ({
  isOpen,
  setIsOpen,
}: {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { space, colors } = useTheme();
  const { data, isLoading } = useGetRemoteLinkQuery('en');
  const { t } = useTranslation('eRecruit');

  const whatsappHandler = async () => {
    const whatsAppUrl = `http://api.whatsapp.com/send?text=${data?.message}`;

    const isAvailable = await Linking.canOpenURL(whatsAppUrl);

    if (isAvailable) {
      await Linking.openURL(whatsAppUrl);
    } else {
      addToast([
        {
          message: t('eRecruit.shareRemoteLink.linkIsNotAvailable'),
        },
      ]);
    }
    // setIsShowModal(false);
  };

  const emailHandler = async () => {
    const msgEmailUrl = `mailto:?&body=${data?.message ?? ''}`;
    const isAvailable = await Linking.canOpenURL(msgEmailUrl);
    if (isAvailable) {
      await Linking.openURL(msgEmailUrl);
    } else {
      addToast([
        {
          message: t('eRecruit.shareRemoteLink.linkIsNotAvailable'),
        },
      ]);
    }
    // setIsShowModal(false);
  };

  return (
    <Modal
      visible={isOpen}
      transparent={true}
      statusBarTranslucent
      animationType="fade">
      <ModalBackground>
        <InfoBox>
          <Row justifyContent="flex-end">
            <ModalCloseButton
              hitSlop={HIT_SLOP_SPACE(1)}
              onPress={() => setIsOpen(false)}>
              <Icon.Close size={space[6]} fill={colors.secondary} />
            </ModalCloseButton>
          </Row>
          <ModalContent>
            <Typography.H6 fontWeight="bold">
              {t('eRecruit.remote.modal.title')}
            </Typography.H6>
            <Box h={space[4]} />
            <Typography.LargeBody>
              {t('eRecruit.remote.modal.description')}
            </Typography.LargeBody>
          </ModalContent>
          <Box h={space[6]} />
          {/* <Row justifyContent="space-between" minH={space[14]}> */}
          <Row justifyContent="space-evenly" minH={space[14]}>
            {isLoading ? (
              <Row w={'100%'} justifyContent={'center'}>
                <Box alignSelf="center" h={space[8]} width={space[8]}>
                  <LoadingIndicator size={space[8]} />
                </Box>
              </Row>
            ) : (
              <>
                <Column gap={space[4]} alignItems="center">
                  {/* //* Whatsapp Button */}
                  <ShareLinkButton onPress={whatsappHandler}>
                    <Icon.Whatsapp fill={colors.background} size={space[5]} />
                  </ShareLinkButton>
                  <Typography.Body>
                    {t('eRecruit.shareRemoteLink.whatsapp')}
                  </Typography.Body>
                </Column>
                <Column gap={space[4]} alignItems="center">
                  {/* //* Email Button */}
                  <ShareLinkButton onPress={emailHandler}>
                    <Icon.Email fill={colors.background} size={space[5]} />
                  </ShareLinkButton>
                  <Typography.Body>
                    {t('eRecruit.shareRemoteLink.email')}
                  </Typography.Body>
                </Column>
              </>
            )}
          </Row>
        </InfoBox>
      </ModalBackground>
    </Modal>
  );
};

const CommonAnimatedViewWrapper = styled(AnimatedViewWrapper)(({ theme }) => ({
  flex: 1000,
  backgroundColor: theme.colors.surface,
  paddingTop: theme.space[5],
  paddingLeft: theme.space[8],
}));

const ModalBackground = styled.View(() => ({
  flex: 1,
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
  alignItems: 'center',
  justifyContent: 'center',
}));

const InfoBox = styled.View(({ theme }) => ({
  width: theme.space[95],
  paddingHorizontal: theme.space[6],
  paddingTop: theme.space[6],
  paddingBottom: theme.space[8],
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
}));

const ModalContent = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    paddingHorizontal: space[6],
  }),
);

const ModalCloseButton = styled.TouchableOpacity(({ theme }) => ({}));

const ShareLinkButton = styled.TouchableOpacity(({ theme }) => ({
  height: theme.space[14],
  width: theme.space[14],
  borderRadius: theme.borderRadius.full,
  backgroundColor: theme.colors.primary,
  justifyContent: 'center',
  alignItems: 'center',
}));
