import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  Column,
} from 'cube-ui-components';
import { useRootStackNavigation } from 'hooks/useRootStack';
import React, { Dispatch, SetStateAction } from 'react';
import { ScrollView } from 'react-native';
import { ProgressBarConfig } from 'screens/ERecruitScreen/ERecruitReviewAgentsApplicationScreen/ERecruitReviewAgentsApplicationScreen.tablet';
import { ReviewInformation as ReviewInformationFIB } from './ReviewInformation';
import { ReviewInformation as ReviewInformationIDN } from 'features/eRecruit/id/ReviewAgentsApplication/ReviewInformation';
import { BuildCountry } from 'types';
import { country } from 'utils/context';
import { useTranslation } from 'react-i18next';
import { TrackerCard } from '../../components/TrackerCard';

const ReviewInformationByCountry: Record<
  BuildCountry,
  typeof ReviewInformationFIB
> = {
  id: ReviewInformationIDN,
  ib: ReviewInformationFIB,
  ph: ReviewInformationFIB,
  my: ReviewInformationFIB,
};

const ReviewInformation = ReviewInformationByCountry[country];

export default function PersonalDetailsLayout({
  setBarStatus,
  navigation,
  applicationId,
}: {
  setBarStatus: Dispatch<SetStateAction<ProgressBarConfig>>;
  navigation: any;
  applicationId: number;
}) {
  const { colors, space } = useTheme();
  const { navigate } = useRootStackNavigation();
  const { t } = useTranslation('eRecruit');
  return (
    <>
      <ScrollView
        style={{
          paddingHorizontal: space[10],
          paddingVertical: space[5],
        }}>
        <Column gap={space[4]}>
          <TrackerCard applicationId={applicationId} />
          <ReviewInformation applicationId={applicationId} />
        </Column>
        <Box height={space[35]} />
      </ScrollView>
      <Footer>
        <Button
          variant="primary"
          onPress={() => {
            // now hide because DOCUMENT API is not ready
            setBarStatus([
              {
                label: t('eRecruit.progressBar.personalDetails'),
                name: 'personalDetails',
                completed: true,
                onfocus: false,
                hightlighted: true,
              },
              {
                label: t('eRecruit.progressBar.documents'),
                name: 'documents',
                completed: false,
                onfocus: true,
                hightlighted: true,
              },
            ]);
            navigation.navigate('documents');

            //DAY 1 solution, before the documentAPI is ready
            // setShowForm(true);
          }}
          text={t('eRecruit.application.otherDetails.next')}
          subtext={t('eRecruit.application.otherDetails.reviewDocuments')}
          style={{
            width: 200,
          }}
        />
      </Footer>
    </>
  );
}

const Footer = styled.View(({ theme }) => ({
  bottom: 0,
  borderTopWidth: 1,
  width: '100%',
  position: 'absolute',
  alignItems: 'flex-end',
  backgroundColor: theme.colors.background,
  paddingVertical: theme.space[4],
  paddingHorizontal: theme.space[6],
  borderTopColor: theme.colors.palette.fwdGrey[100],
}));
