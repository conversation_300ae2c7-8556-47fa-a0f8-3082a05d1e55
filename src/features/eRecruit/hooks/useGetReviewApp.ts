import { useFocusEffect } from '@react-navigation/native';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useCallback, useMemo, useState } from 'react';
import { SceneMap } from 'react-native-tab-view';
import { BuildCountry, UIMode } from 'types';
import { country } from 'utils/context';
import GATracking from 'utils/helper/gaTracking/gaTracking';
import { shallow } from 'zustand/shallow';
import { IBReviewApplicationCombinedRouteKey } from '../types/progressBarTypes';
import { IBReviewTabs } from '../ib/phone/components/ReviewApplicationProgressBar/tabs';
import { useERecruitReviewApplicationProgressBarStore } from '../util/store/ERecruitReviewApplicationProgressBarStore';
import { IDReviewTabs } from 'features/eRecruit/id/phone/components/ReviewApplicationProgressBar/tabs';

export type ERecruitrReviewTabs<
  RouteKey = IBReviewApplicationCombinedRouteKey,
> = {
  route: RouteKey;
  component: React.ComponentType;
  trackingId?: string;
};

export type ERecruitTabs<RouteKey = IBReviewApplicationCombinedRouteKey> =
  Record<UIMode, ERecruitrReviewTabs<RouteKey>[]>;

const TABS_BY_COUNTRY: Record<BuildCountry, ERecruitTabs | null> = {
  ph: null,
  my: null,
  ib: IBReviewTabs,
  id: IDReviewTabs,
};

export const useGetERecruitReviewApp = () => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { routes, renderScene } = useMemo(() => {
    const tabs: ERecruitrReviewTabs<IBReviewApplicationCombinedRouteKey>[] =
      (isTabletMode
        ? TABS_BY_COUNTRY[country]?.tablet
        : TABS_BY_COUNTRY[country]?.phone) || [];
    const renderScene = SceneMap(
      tabs.reduce<Record<string, React.ComponentType>>((map, tab) => {
        map[tab.route] = tab.component;
        return map;
      }, {}),
    );
    const routes = tabs.map(tab => ({ key: tab.route }));

    return { routes, renderScene };
  }, [isTabletMode]);

  const [index, setIndex] = useState(0);

  const navigationState = useMemo(() => {
    return {
      index,
      routes,
    };
  }, [index, routes]);

  const { groupKey, subGroupKey, itemKey } =
    useERecruitReviewApplicationProgressBarStore(
      state => ({
        groupKey: state.groupKey,
        subGroupKey: state.subgroupKey,
        itemKey: state.itemKey,
      }),
      shallow,
    );

  useFocusEffect(
    useCallback(() => {
      const tabs =
        (isTabletMode
          ? TABS_BY_COUNTRY[country]?.tablet
          : TABS_BY_COUNTRY[country]?.phone) || [];
      for (let i = 0; i < tabs.length; i++) {
        const trackingId = tabs[i].trackingId;
        const currentRouteKey = tabs[i].route;
        const [currentGroupKey, currentSubgroupKey, currentItemKey] =
          currentRouteKey.split('-');
        if (
          (currentGroupKey === '' || currentGroupKey === groupKey) &&
          (currentSubgroupKey === '' || currentSubgroupKey === subGroupKey) &&
          (currentItemKey === '' || currentItemKey === itemKey)
        ) {
          setIndex(i);
          if (trackingId) {
            GATracking.trackScreen(trackingId);
          }
        }
      }
    }, [groupKey, isTabletMode, itemKey, subGroupKey]),
  );

  return { index, setIndex, renderScene, navigationState };
};
