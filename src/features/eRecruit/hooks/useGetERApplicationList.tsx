import { useQuery } from '@tanstack/react-query';
import { RECRUIT_ENDPOINT, getApplicationList } from 'api/eRecruitApi';
import { ApplicationListQueryParams } from 'types/eRecruit';

const getERApplicationListKey = (params: ApplicationListQueryParams) => {
  return [RECRUIT_ENDPOINT, 'application', params];
};
export function useGetERApplicationList(
  params: ApplicationListQueryParams,
  isEnabled = true,
) {
  return useQuery({
    queryKey: getERApplicationListKey(params),
    queryFn: () => getApplicationList(params),
    enabled: isEnabled,
  });
}
