import { useMutation } from '@tanstack/react-query';
import { postERDocument } from 'api/eRecruitApi';
import { AxiosError } from 'axios';
import { CubeResponse } from 'types';

export function usePostDocumentUpload() {
  return useMutation<
    {
      fileId: number;
      fileKey: string;
      fileSize: number;
      fileType: string;
    },
    AxiosError<CubeResponse<unknown>>,
    FormData,
    unknown
  >({
    mutationFn: postERDocument,
    onError: error => {
      console.log('usePostDocumentUpload error', error);
    },
  });
}
