import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { fwdLoadingAnimation } from 'assets/images';
import {
  Body,
  Box,
  Center,
  H4,
  H7,
  Icon,
  LargeBody,
  LargeLabel,
  Row,
} from 'cube-ui-components';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Image, ScrollView, View } from 'react-native';
import { RootStackParamList } from 'types';
import EAppFooter from '../common/EAppFooter';
import SubmissionCoverageText from './SubmissionCoverageText';
import { format } from 'date-fns';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { formatCurrencyWithMask, i18n } from 'utils';
import { getPremByPaymentMode } from 'features/eApp/utils/planUtils';
import { Plan } from 'types/quotation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useRiders } from 'features/eApp/hooks/useRiders';
import { PartyRole } from 'types/party';
import FWDTakafulLogo from 'features/eApp/assets/FWDTakafulLogo';
import { SafeAreaView } from 'react-native-safe-area-context';
import { PlanCodeEnum } from 'types/proposal';
import { CaseStatus } from 'types/case';
import usePaymentInfo from 'features/eAppV2/ib/components/payment/info/usePaymentInfo';
import { PaymentMethod } from '../payment/PaymentMethods';
interface RowInfoFieldProps {
  isHighlight?: boolean;
  isMini?: boolean;
  isVertical?: boolean;
  fieldName?: string | null;
  fieldValue?: string | number | null;
}

const RowInfoField = ({
  fieldName,
  fieldValue,
  isHighlight,
  isMini,
  isVertical,
}: RowInfoFieldProps) => {
  const { colors, space } = useTheme();
  return (
    <Box flex={isVertical ? 0 : 1}>
      <Body color={colors.placeholder} fontWeight="bold">
        {fieldName}
      </Body>
      <Box height={space[isMini ? 1 : 2]} />
      {isMini ? (
        <LargeBody color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeBody>
      ) : (
        <LargeLabel color={isHighlight ? colors.primary : colors.secondary}>
          {fieldValue}
        </LargeLabel>
      )}
    </Box>
  );
};

const Submission = () => {
  const { colors, sizes, space, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp', 'common', 'policy']);
  const [loading, setLoading] = useState(true);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);

  // const {
  //   my_policyOwnerPersonalInfo,
  //   my_hasSpouseInsured,
  //   my_spousePersonalInfo,
  //   my_hasChildInsured,
  //   my_childrenPersonalInfo,
  // } = useEAppStore(
  //   state => ({
  //     my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
  //     my_hasSpouseInsured: state.my_hasSpouseInsured,
  //     my_spousePersonalInfo: state.my_spousePersonalInfo,
  //     my_hasChildInsured: state.my_hasChildInsured,
  //     my_childrenPersonalInfo: state.my_childrenPersonalInfo,
  //   }),
  //   shallow,
  // );

  const { data: optionList } = useGetOptionList();

  const owner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const personCoveredName = useMemo(() => {
    return (
      caseObj?.parties?.find(p => p.isMainInsured)?.person?.name.firstName ?? ''
    );
  }, [caseObj?.parties]);

  const dateSubmitted = useMemo(() => {
    return format(new Date(), 'd LLL yyyy');
  }, []);

  const quotation = useSelectedQuotation();
  const basicPlan = quotation?.plans?.[0];
  const productName = useMemo(
    () => getProductName(basicPlan?.productName, i18n.language),
    [basicPlan],
  );

  const contribution = useMemo(() => {
    return formatCurrencyWithMask(getPremByPaymentMode(quotation), 2);
  }, [quotation]);

  const { basicAnnualContribution, initialContribution } = useMemo(() => {
    if (/FAMILYCI/.test(quotation?.pid ?? '')) {
      return {
        basicAnnualContribution: basicPlan?.totalAnnualPrem,
        initialContribution: basicPlan?.totalInitialPrem,
      };
    }
    return {
      basicAnnualContribution:
        basicPlan?.annualPrem && basicPlan?.annualPrem !== 0
          ? basicPlan?.annualPrem
          : quotation?.summary?.annualPrem,
      initialContribution:
        basicPlan?.totalPrem && basicPlan?.totalPrem !== 0
          ? basicPlan?.totalPrem
          : quotation?.summary?.totalPrem,
    };
  }, [basicPlan, quotation]);

  const contributionMode = useMemo(() => {
    return (
      optionList?.PAYMENTMODE4.options.find(
        e => e.value === quotation?.basicInfo.paymentMode,
      )?.label ?? t('eApp:singlePremium')
    );
  }, [optionList?.PAYMENTMODE4.options, quotation?.basicInfo.paymentMode, t]);

  const riders = useRiders(quotation);

  const riderPairs = useMemo(() => {
    return riders.reduce<Array<Plan[]>>(function (result, value, index, array) {
      if (index % 2 === 0) result.push(array.slice(index, index + 2));
      return result;
    }, []);
  }, [riders]);

  const regularCashPayment = useMemo(() => {
    return optionList?.PAYOUT_OPTION.options.find(
      e => e.value === basicPlan?.payoutOption,
    )?.label;
  }, [basicPlan?.payoutOption, optionList?.PAYOUT_OPTION.options]);

  const shouldShowWaitingForApproval =
    caseObj?.latestStatus === CaseStatus.PENDING_FOR_LEADER;

  const { premium, formattedInitialPremium: initialPremium } = usePaymentInfo({
    advancePayment: {
      duration: caseObj?.application?.advancePayment?.duration ?? 0,
      amount:
        (caseObj?.application?.advancePayment?.duration ?? 0) *
        (getPremByPaymentMode(quotation) ?? 0),
    },
  });

  return (
    <Fragment>
      {loading ? (
        <Center flex={1} backgroundColor={colors.background}>
          <Image
            style={{
              width: sizes[50],
              height: sizes[50],
            }}
            resizeMode="contain"
            source={fwdLoadingAnimation}
          />
        </Center>
      ) : (
        <Box flex={1} bgColor={colors.surface}>
          <Wrapper edges={['top']}>
            <Row alignItems="center" gap={space[3]}>
              <Center>
                <Box
                  pos="absolute"
                  width={sizes[16]}
                  height={sizes[16]}
                  bgColor={colors.background}
                  borderRadius={borderRadius.full}
                />
                <Icon.TickCircleFill size={sizes[18] - 1} />
              </Center>
              <H4 fontWeight="bold">{t('eApp:payment.congratulation')}</H4>
            </Row>

            <Box height={space[3]} />
            <H7 fontWeight="normal">{t('eApp:application.submit.note')}</H7>
            <Box height={space[6]} />
            <Content height={space[6]}>
              <Row flexGrow={1}>
                <Box padding={space[6]} width={210}>
                  <ScrollView showsVerticalScrollIndicator={false}>
                    <Box gap={space[6]}>
                      <RowInfoField
                        fieldName={t(
                          'eApp:application.submit.certificateNumber',
                        )}
                        fieldValue={caseObj?.application?.policyNum}
                        isHighlight={true}
                        isVertical={true}
                      />
                      <RowInfoField
                        fieldName={t('eApp:certificate.menu.certificateOwner')}
                        fieldValue={
                          owner?.person?.name?.firstName ??
                          owner?.entity?.name ??
                          ''
                        }
                        isVertical={true}
                      />
                      <RowInfoField
                        fieldName={t('eApp:bar.insured')}
                        fieldValue={personCoveredName}
                        isVertical={true}
                      />
                      <RowInfoField
                        fieldName={t('eApp:application.submit.dateSubmitted')}
                        fieldValue={dateSubmitted}
                        isVertical={true}
                      />
                      <RowInfoField
                        fieldName={t('eApp:email')}
                        fieldValue={owner?.contacts?.email ?? ''}
                        isVertical={true}
                      />
                    </Box>
                  </ScrollView>
                </Box>
                <ApplicationContent>
                  <Row mb={space[6]} alignItems="center">
                    <H4 fontWeight="bold" style={{ flexShrink: 1 }}>
                      {productName}
                    </H4>
                    <Box flex={1} />
                    <FWDTakafulLogo width={86} height={44} />
                  </Row>
                  <ScrollView showsVerticalScrollIndicator={false}>
                    <Row gap={space[4]}>
                      <SubmissionCoverageText
                        label={t('eApp:review.sumCovered')}
                        currency={t('common:rm')}
                        value={formatCurrencyWithMask(basicPlan?.sumAssured, 2)}
                        isNormal={true}
                      />

                      <SubmissionCoverageText
                        label={t('eApp:review.contribution')}
                        currency={t('common:rm')}
                        value={contribution}
                        isNormal={true}
                      />
                    </Row>
                    <Divider />

                    <Row gap={space[4]}>
                      <RowInfoField
                        fieldName={t('eApp:review.certificateTerm')}
                        fieldValue={t('common:withYears', {
                          year:
                            basicPlan?.maxPolicyTerm ??
                            basicPlan?.policyTerm ??
                            0,
                        })}
                      />
                      <RowInfoField
                        fieldName={t('eApp:payment.contribution.term')}
                        fieldValue={t('common:withYears', {
                          year:
                            basicPlan?.maxPremiumTerm ??
                            basicPlan?.premiumTerm ??
                            0,
                        })}
                      />
                      <RowInfoField
                        fieldName={t('eApp:review.contributionMode')}
                        fieldValue={contributionMode}
                      />
                    </Row>
                    <Divider />
                    <Row gap={space[4]}>
                      <RowInfoField
                        fieldName={t('eApp:review.basicAnnualContribution')}
                        fieldValue={t('common:withCurrency', {
                          amount: formatCurrencyWithMask(
                            basicAnnualContribution,
                            2,
                          ),
                        })}
                      />
                      <RowInfoField
                        fieldName={t('eApp:review.initialContribution')}
                        fieldValue={t('common:withCurrency', {
                          amount:
                            caseObj?.application?.paymentMethod ===
                            PaymentMethod.BIRO
                              ? premium
                              : initialPremium,
                        })}
                      />
                      {regularCashPayment ? (
                        <RowInfoField
                          fieldName={t('eApp:payment.cash')}
                          fieldValue={regularCashPayment}
                        />
                      ) : (
                        <Box flex={1} />
                      )}
                    </Row>
                    {riderPairs.length > 0 && <Divider />}
                    {riderPairs.length > 0 && (
                      <Box>
                        <Row flex={1} justifyContent="space-between">
                          <Box flex={1}>
                            <Body color={colors.placeholder} fontWeight="bold">
                              {t('policy:coverage')}
                            </Body>
                          </Box>
                          <Row
                            gap={space[6]}
                            flex={1}
                            alignContent="flex-start"
                            justifyContent="space-between"
                            alignItems="flex-start">
                            <Box flex={1} alignContent="flex-start">
                              <Body
                                color={colors.placeholder}
                                fontWeight="bold">
                                {t('eApp:review.sumCovered')}
                              </Body>
                            </Box>
                            <Box flex={1} alignContent="flex-start">
                              <Body
                                color={colors.placeholder}
                                fontWeight="bold">
                                {t('eApp:review.contribution')}
                              </Body>
                            </Box>
                          </Row>
                        </Row>
                        <Box marginTop={space[2]} gap={space[2]}>
                          {riders.map(rider => {
                            let prem = '--';
                            if (
                              rider.pid === PlanCodeEnum.REGULAR_TOP_UP_MY &&
                              typeof rider.annualPrem === 'number'
                            ) {
                              prem = t('common:withCurrency', {
                                amount: formatCurrencyWithMask(
                                  rider.annualPrem,
                                  2,
                                ),
                              });
                            } else if (typeof rider.totalPrem === 'number') {
                              prem = t('common:withCurrency', {
                                amount: formatCurrencyWithMask(
                                  rider.totalPrem,
                                  2,
                                ),
                              });
                            }

                            return (
                              <Row
                                flex={1}
                                justifyContent="space-between"
                                key={`${rider.pid}`}>
                                <Box flex={1}>
                                  <Body fontWeight="normal">
                                    {getProductName(
                                      rider.productName,
                                      i18n.language,
                                    )}
                                  </Body>
                                </Box>
                                <Row
                                  flex={1}
                                  gap={space[6]}
                                  alignContent="flex-start"
                                  justifyContent="space-between"
                                  alignItems="flex-start">
                                  <Box flex={1} alignContent="flex-start">
                                    <Body fontWeight="normal">
                                      {typeof rider.sumAssured === 'number'
                                        ? t('common:withCurrency', {
                                            amount: formatCurrencyWithMask(
                                              rider.sumAssured,
                                              2,
                                            ),
                                          })
                                        : '--'}
                                    </Body>
                                  </Box>
                                  <Box flex={1} alignContent="flex-start">
                                    <Body fontWeight="normal">{prem}</Body>
                                  </Box>
                                </Row>
                              </Row>
                            );
                          })}
                        </Box>
                      </Box>
                    )}
                  </ScrollView>
                </ApplicationContent>
              </Row>
            </Content>

            {shouldShowWaitingForApproval && (
              <Row
                alignSelf="stretch"
                mt={space[3]}
                borderRadius={borderRadius.small}
                backgroundColor={colors.primaryVariant3}
                p={space[4]}
                gap={space[1]}
                alignItems="center">
                <Icon.InfoCircle size={18} fill={colors.primary} />
                <LargeBody>{t('eApp:submission.waitForApproval')}</LargeBody>
              </Row>
            )}

            <Box height={shouldShowWaitingForApproval ? space[6] : space[2]} />
            <LargeBody color={colors.secondaryVariant}>
              {t('eApp:application.submit.hint')}
            </LargeBody>
          </Wrapper>
          <EAppFooter
            progressLock="payment--"
            onPress={() =>
              navigation.navigate('Main', {
                screen: 'Home',
              })
            }
            disabled={false}
            label={t('eApp:application.submit.back')}
          />
        </Box>
      )}
    </Fragment>
  );
};

export default Submission;

const Wrapper = styled(SafeAreaView)(({ theme: { space } }) => ({
  flex: 1,
  alignContent: 'center',
  justifyContent: 'center',
  alignItems: 'center',
  marginTop: space[12],
  marginBottom: space[10],
  marginHorizontal: space[30],
}));

const Content = styled(Box)(({ theme: { colors, borderRadius } }) => ({
  backgroundColor: colors.palette.fwdGrey[20],
  borderRadius: borderRadius['large'],
  flexGrow: 1,
  flexDirection: 'row',
}));

const ApplicationContent = styled(View)(
  ({ theme: { colors, borderRadius, space } }) => ({
    backgroundColor: colors.background,
    borderTopRightRadius: borderRadius['large'],
    borderBottomRightRadius: borderRadius['large'],
    flex: 1,
    padding: space[6],
  }),
);

export const Divider = styled(View)(({ theme: { space, colors } }) => {
  return {
    height: 1,
    backgroundColor: colors.palette.fwdGrey[100],
    marginVertical: space[4],
  };
});
