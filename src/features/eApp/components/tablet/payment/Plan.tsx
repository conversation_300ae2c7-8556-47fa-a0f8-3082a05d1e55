import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, H6, H8, Icon, Row } from 'cube-ui-components';
import { useCheckEntity } from 'features/eApp/hooks/useCheckEntity';
import { useRiders } from 'features/eApp/hooks/useRiders';
import { getProductName } from 'features/eApp/utils/eAppFormat';
import { getPremByPaymentMode } from 'features/eApp/utils/planUtils';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import {
  useGetProductList,
  useProductImageQuery,
} from 'features/productSelection/hooks/useProducts';
import { useBasePlanFieldConfig } from 'features/proposal/hooks/useFields';
import { OptionConfig } from 'features/proposal/types';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import round from 'lodash/round';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CHANNELS } from 'types/channel';
import { formatCurrencyWithMask, i18n } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import PaymentItemDetail from './PaymentItemDetail';
import usePaymentInfo from 'features/eAppV2/ib/components/payment/info/usePaymentInfo';

const Plan = ({ isBiro }: { isBiro: boolean }) => {
  const { t } = useTranslation(['eApp', 'common']);
  const { sizes, space, colors } = useTheme();
  const quotation = useSelectedQuotation();
  const { data: optionList } = useGetOptionList();
  const isEntity = useCheckEntity();

  const riderData = useRiders(quotation);
  const basicPlan = quotation?.plans[0];

  const channel = useGetCubeChannel();
  const isAgency = channel === CHANNELS.AGENCY;
  const { data: products } = useGetProductList({ channel });
  const product = useMemo(() => {
    return products?.find(e => e.pid === basicPlan?.pid);
  }, [basicPlan?.pid, products]);
  const { data: imageUrl } = useProductImageQuery(product?.productThumbnailUrl);

  const advanceContributionDuration = useEAppStore(
    state => state.my_advanceContributionDuration,
  );

  const regularCashPayment = useMemo(() => {
    return optionList?.PAYOUT_OPTION.options.find(
      e => e.value === basicPlan?.payoutOption,
    )?.label;
  }, [basicPlan?.payoutOption, optionList?.PAYOUT_OPTION.options]);

  const { getConfigLabel, fieldConfigs } = useBasePlanFieldConfig();

  const payoutOptions = fieldConfigs.get('payoutOption')?.options as
    | OptionConfig<string>[]
    | undefined;

  const annualCashPayment = useMemo(() => {
    return renderLabelByLanguage(
      payoutOptions?.find(option => option.value === basicPlan?.payoutOption)
        ?.label,
    );
  }, [basicPlan?.payoutOption, payoutOptions]);

  const {
    paymentMode,
    advancePremium,
    formattedInitialPremium: initialPremium,
    premium,
    sst,
  } = usePaymentInfo({
    advancePayment: {
      duration: advanceContributionDuration,
      amount:
        advanceContributionDuration *
        (getPremByPaymentMode(quotation) ?? 0),
    },
  });

  return (
    <Container bounces={false} showsVerticalScrollIndicator={false}>
      <Box h={space[8]} />
      <Row alignContent="center" alignItems="center">
        <ProductImage source={{ uri: imageUrl }} resizeMode="cover" />
        <Box width={sizes[2]} />
        <H6 fontWeight="bold">
          {getProductName(basicPlan?.productName, i18n.language)}
        </H6>
      </Row>
      <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
        <Icon.Coin2 fill={colors.secondary} />
        <Box width={sizes[1]} />
        <H8 fontWeight="medium">{t('eApp:payment.initialPaymentDetails')}</H8>
      </Row>
      <PaymentItemDetail
        title={t('eApp:payment.initialPaymentTotal')}
        content={isBiro ? premium : initialPremium}
        isHighlight
        isCurrency
        currency={t('common:rm')}
      />
      <PaymentItemDetail
        title={t('eApp:payment.contribution', { mode: paymentMode })}
        content={premium}
        isCurrency
        currency={t('common:rm')}
      />
      {Boolean(advanceContributionDuration) && (
        <PaymentItemDetail
          title={t('eApp:payment.advanceContribution.title')}
          content={formatCurrencyWithMask(advancePremium, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}
      {isAgency && Boolean(sst) && (
        <PaymentItemDetail
          title={t('eApp:payment.sale')}
          content={formatCurrencyWithMask(sst, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}

      {Boolean(basicPlan?.stampDuty) && (
        <PaymentItemDetail
          title={t('eApp:payment.stamp')}
          content={formatCurrencyWithMask(basicPlan?.stampDuty, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}

      <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
        <Icon.Document fill={colors.secondary} />
        <Box width={sizes[1]} />
        <H8 fontWeight="medium">{t('eApp:payment.policy')}</H8>
      </Row>
      <PaymentItemDetail
        title={t('eApp:payment.covered')}
        content={t('common:withCurrency', {
          amount: formatCurrencyWithMask(basicPlan?.sumAssured, 2),
        })}
      />
      <PaymentItemDetail
        title={t('eApp:payment.type')}
        content={basicPlan?.paymentType ?? ''}
      />
      <PaymentItemDetail
        title={t('eApp:payment.cert')}
        content={t('common:withYears', {
          year: basicPlan?.maxPolicyTerm ?? basicPlan?.policyTerm ?? 0,
        })}
      />
      <PaymentItemDetail
        title={t('eApp:payment.contribution.mode')}
        content={paymentMode}
      />
      <PaymentItemDetail
        title={t('eApp:payment.contribution.term')}
        content={t('common:withYears', {
          year:
            basicPlan?.maxPremiumTerm ?? quotation?.plans[0].premiumTerm ?? 0,
        })}
      />
      {regularCashPayment !== undefined && (
        <PaymentItemDetail
          title={t('eApp:payment.cash')}
          content={regularCashPayment}
        />
      )}
      {regularCashPayment === undefined && annualCashPayment !== undefined && (
        <PaymentItemDetail
          title={getConfigLabel('payoutOption') || ''}
          content={annualCashPayment}
        />
      )}
      {riderData && riderData.length > 0 && (
        <>
          <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
            <Icon.Document fill={colors.secondary} />
            <Box width={sizes[1]} />
            <H8 fontWeight="medium">{t('eApp:payment.coverage')}</H8>
          </Row>
          {riderData?.map(item => (
            <PaymentItemDetail
              title={getProductName(item?.productName, i18n.language)}
              content={
                typeof item?.sumAssured === 'number'
                  ? t('common:withCurrency', {
                      amount: formatCurrencyWithMask(item?.sumAssured, 2),
                    })
                  : '--'
              }
            />
          ))}
        </>
      )}
      <Box h={space[4]} />
    </Container>
  );
};

const Container = styled.ScrollView(({ theme }) => ({
  backgroundColor: theme.colors.background,
}));

const ProductImage = styled.Image(({ theme: { sizes, borderRadius } }) => ({
  width: sizes[12],
  height: sizes[12],
  borderRadius: borderRadius.small,
}));

export default Plan;
