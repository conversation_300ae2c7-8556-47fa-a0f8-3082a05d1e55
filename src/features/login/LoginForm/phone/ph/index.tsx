import { TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { Button, Icon, TextField, Typography } from 'cube-ui-components';
import Input from 'components/Input/Input';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { FormValues, LoginFormProps } from 'types/login';
import { useLoginForm } from 'features/login/commonLogic';

export default function LoginForm({ navigation }: LoginFormProps) {
  const {
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      agentId: '50000979',
      password: 'Password1',
    },
  });
  const { colors, space } = useTheme();

  const [hidePassword, setHidePassword] = useState(true);
  const disableLoginButton = !(
    watch('agentId').length > 0 && watch('password').length > 0
  );
  const { t } = useTranslation();

  const { loginFail, loading, onPressLogin } = useLoginForm();

  return (
    <View>
      <Typography.LargeBody
        style={{
          textAlign: 'center',
          marginBottom: space[2],
        }}>
        {t('login.welcome')}
      </Typography.LargeBody>
      <Input
        control={control}
        as={TextField}
        name="agentId"
        style={{ width: '100%', marginTop: space[6] }}
        label={t('login.id')}
        error={errors.agentId?.message}
        onChangeText={username => setValue('agentId', username?.toUpperCase())}
        isError={loginFail}
      />
      <Input
        control={control}
        as={TextField}
        name="password"
        style={{ width: '100%', marginTop: space[6], marginBottom: space[4] }}
        label={t('login.password')}
        error={errors.password?.message}
        right={() => (
          <TouchableOpacity onPress={() => setHidePassword(prev => !prev)}>
            {!hidePassword ? (
              <Icon.SeePasswordFill fill={colors.secondary} />
            ) : (
              <Icon.UnSeePasswordFill fill={colors.secondary} />
            )}
          </TouchableOpacity>
        )}
        secureTextEntry={hidePassword}
        isError={loginFail}
      />
      {loginFail && (
        <Typography.Body
          style={{ width: '100%', marginTop: space[1] }}
          color={colors.error}>
          {t('login.error')}
        </Typography.Body>
      )}
      {/* <TouchableOpacity
        onPress={() => {
          navigate('ForgetPw');
        }}>
        <Typography.LargeLabel fontWeight="bold" color={colors.primary}>
          {'Forget password'}
        </Typography.LargeLabel>
      </TouchableOpacity> */}
      <ButtonsContainer>
        <Button
          text={t('login.login')}
          onPress={handleSubmit(onPressLogin)}
          loading={loading}
          disabled={disableLoginButton}
          style={{ maxWidth: 400, width: '100%' }}
        />
      </ButtonsContainer>
    </View>
  );
}

const ButtonsContainer = styled.View(({ theme }) => {
  return {
    width: '100%',
    marginTop: theme.space[6],
    alignItems: 'center',
  };
});
