import { TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import { Button, Icon, Row, TextField, Typography } from 'cube-ui-components';
import Input from 'components/Input/Input';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { FormValues, LoginFormProps } from 'types/login';
import { HIT_SLOP_SPACE } from 'constants/hitSlop';
import { useLoginForm } from 'features/login/commonLogic';
import UnSeePasswordSVG from 'features/login/assets/UnSeePasswordSVG';

export default function LoginForm({ navigation }: LoginFormProps) {
  const { t } = useTranslation();
  const { colors, space } = useTheme();

  const [hidePassword, setHidePassword] = useState(true);

  const { loginFail, loading, onPressLogin } = useLoginForm();

  const {
    control,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      agentId: '50000979',
      password: 'Password1',
    },
  });

  const disableLoginButton = !(
    watch('agentId').length > 0 && watch('password').length > 0
  );

  return (
    <SectionContainer>
      <Typography.H5
        fontWeight="bold"
        style={{ marginBottom: space[2] }}
        children={t('login.toCube')}
      />
      <FormContainer>
        <Input
          control={control}
          as={TextField}
          name="agentId"
          style={{ width: '100%' }}
          label={t('login.id')}
          error={errors.agentId?.message}
          isError={loginFail}
        />
        <Input
          control={control}
          as={TextField}
          name="password"
          style={{ width: '100%' }}
          label={t('login.password')}
          error={errors.password?.message}
          right={() => (
            <TouchableOpacity onPress={() => setHidePassword(prev => !prev)}>
              {!hidePassword ? (
                <Icon.SeePassword fill={colors.primary} />
              ) : (
                <UnSeePasswordSVG fill={colors.primary} />
              )}
            </TouchableOpacity>
          )}
          secureTextEntry={hidePassword}
          isError={loginFail}
        />

        {/* <Row>
          <Typography.Label>{t('login.forgetPassword')}</Typography.Label>
          <ForgetPasswordButton
            onPress={() => console.log('~~~forget password~~~')}
            hitSlop={HIT_SLOP_SPACE(1)}>
            <Typography.Label fontWeight="bold" color={colors.primary}>
              eIRIS
            </Typography.Label>
          </ForgetPasswordButton>
        </Row> */}

        {loginFail && (
          <Typography.Body
            style={{ width: '100%', marginTop: space[1] }}
            color={colors.error}
            children={t('login.error')}
          />
        )}
      </FormContainer>

      {/* <TouchableOpacity
        onPress={() => { navigate('ForgetPw') }}>
        <Typography.LargeLabel fontWeight="bold" color={colors.primary}>
          {'Forget password'}
        </Typography.LargeLabel>
      </TouchableOpacity> */}

      <Button
        text={t('login.login')}
        onPress={handleSubmit(onPressLogin)}
        loading={loading}
        disabled={disableLoginButton}
        style={{ width: '100%' }}
      />
    </SectionContainer>
  );
}

const SectionContainer = styled.View(({ theme }) => ({
  gap: theme.space[6],
}));

const FormContainer = styled.View(({ theme }) => ({
  gap: theme.space[4],
}));

// const ForgetPasswordButton = styled.TouchableOpacity(({ theme }) => ({
//   display: 'flex',
// }));
