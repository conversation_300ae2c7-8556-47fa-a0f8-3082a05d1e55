import { useState, useEffect } from 'react';
import {
  Alert,
  Platform,
  ActionSheetIOS,
  TouchableOpacity,
  Image as RNImage,
} from 'react-native';
import { useTheme } from '@emotion/react';
import { addToast, Icon } from 'cube-ui-components';
import { useQueryClient } from '@tanstack/react-query';
import { loginWithRefreshToken } from 'features/login/commonLogic';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useUploadAgentPicture } from 'hooks/useUploadAgentPicture';
import { useTranslation } from 'react-i18next';
import { getQueryKey as getAgentProfileQueryKey } from 'hooks/useGetAgentProfile';

import { build } from 'utils/context';
import DelProfilePicModal from 'features/agentProfile/components/tablet/DelProfilePicModal';
import ProfilePicModal from 'features/agentProfile/components/tablet/ProfilePicModal';
import * as ImagePicker from 'expo-image-picker';
import * as ImageCropper from 'features/agentProfile/components/ImageCropper';
import * as FileSystem from 'expo-file-system';

export default function EditPhotoButton() {
  const queryClient = useQueryClient();
  const { space, colors, borderRadius, elevation } = useTheme();
  const { t } = useTranslation('agentProfile');
  const [mediaStatus, requestMediaPermission] =
    ImagePicker.useMediaLibraryPermissions();

  const [cameraStatus, requestCameraPermission] =
    ImagePicker.useCameraPermissions();

  const { isLoading, data: agentProfile } = useGetAgentProfile();

  const agentProfilePicture = agentProfile?.agentPhotoUrl;

  const [isProPicModalShown, setIsProPicModalShown] = useState(false);
  const [isDelPicModalShown, setIsDelPicModalShown] = useState(false);
  const [selectedImage, setSelectedImage] =
    useState<ImagePicker.ImagePickerAsset>();
  const { mutateAsync: mutateAsyncToUpload } = useUploadAgentPicture();

  const agentId = useBoundStore(state => state.auth.agentCode);

  const resetSelectedImage = (agentPicture: string | undefined) => {
    if (agentPicture) {
      RNImage.getSize(agentPicture, (width, height) => {
        const profilePicInfo = {
          uri: agentPicture,
          width,
          height,
        } satisfies ImagePicker.ImagePickerAsset;

        if (!width || !height) {
          return;
        }
        setSelectedImage(profilePicInfo);
      });
    } else {
      setSelectedImage(undefined);
    }
  };

  useEffect(() => {
    resetSelectedImage(agentProfilePicture);
  }, [agentProfilePicture]);

  const dismissProPicModal = () => setIsProPicModalShown(false);
  const dismissDelProPicModal = () => setIsDelPicModalShown(false);

  const takePhoto = async () => {
    if (!cameraStatus?.granted) {
      const res = await requestCameraPermission();
      if (!res.granted) {
        Alert.alert('not granted');
        return;
      }
    }
    const result = await ImagePicker.launchCameraAsync({
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      cameraType: 'front',
    });

    if (!result.canceled) {
      const imgRes = result.assets[0];
      setSelectedImage(imgRes);
      dismissProPicModal();
      const croppedRes = await ImageCropper.cropImageAsync(imgRes, {
        onBack: takePhoto,
      });
      upload(croppedRes?.uri);
    }
  };
  const selectFromAlbum = async () => {
    console.log(
      '🚀 ~ file: AgentProfileScreen.tablet.tsx:316 ~ selectFromAlbum ~ selectFromAlbum:',
    );

    if (!mediaStatus?.granted) {
      const res = await requestMediaPermission();
      if (!res.granted) {
        Alert.alert('not granted');
        return;
      }
    }
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      quality: 1,
      allowsMultipleSelection: false,
      allowsEditing: false,
    });

    if (!result.canceled) {
      const imgRes = result.assets[0];
      if (imgRes.width < 620 || imgRes.height < 620) {
        Alert.alert('Must select image with width and height > 620px');
        selectFromAlbum();
        return;
      }
      setSelectedImage(imgRes);
      dismissProPicModal();
      const croppedRes = await ImageCropper.cropImageAsync(imgRes, {
        onBack: selectFromAlbum,
      });

      upload(croppedRes?.uri);
    }
  };

  const upload = (uri: string) => {
    console.log(
      '🚀 ~ file: AgentProfileScreen.tablet.tsx:341 ~ upload ~ uri:',
      uri,
    );
    let fetchUnauthorized = false;

    const imgToUpload = async () => {
      const res = await mutateAsyncToUpload(uri);
      console.log(
        '🚀 ~ file: AgentProfileScreen.tablet.tsx:333 ~ imgToUpload ~ res.status :',
        res.status,
      );

      switch (res.status) {
        case 200: {
          queryClient.invalidateQueries({
            queryKey: getAgentProfileQueryKey(agentId),
          });
          addToast([
            {
              message: t('saveChanges.success'),
              IconLeft: <Icon.Tick />,
            },
          ]);
          return;
        }
        case 401: {
          if (fetchUnauthorized) {
            console.log(
              '🚀🚀🚀 retried but still failed ~ file: AgentProfileScreen.tablet.tsx:356 ~ imgToUpload ~ res.status :',
              res.status,
            );
            return;
          }
          fetchUnauthorized = true;

          const refreshToken =
            useBoundStore.getState().auth.authInfo?.refreshToken ?? '';
          const setTokens = (
            accessToken: string,
            refreshToken: string,
            idToken?: string,
          ) => {
            useBoundStore
              .getState()
              .authActions.login(accessToken, refreshToken, idToken);
          };
          loginWithRefreshToken(refreshToken)
            .then(res => {
              setTokens(
                res.data?.access_token,
                res.data?.refresh_token,
                res.data?.id_token,
              );
              return imgToUpload()
                .then(() => {
                  console.log(
                    '🚀 ~ file: AgentProfileScreen.tablet.tsx:372 ~ ',
                  );
                })
                .catch(err => {
                  console.log(
                    '🚀 ~ file: AgentProfileScreen.tablet.tsx:377 ~ err:',
                    err,
                  );
                  setSelectedImage(undefined);
                  resetSelectedImage(agentProfilePicture);
                })
                .finally(() => console.log('final action 2'));
            })
            .catch(err => {
              setSelectedImage(undefined);
              resetSelectedImage(agentProfilePicture);
              console.log(
                '🚀 ~ file: AgentProfileScreen.tablet.tsx:389 ~ err:',
                err,
              );
            });
          return;
        }
        case 500: {
          const resErr =
            build === 'dev'
              ? ' ONLY-IN-DEV: ' +
                JSON.parse(res.body).status +
                ' ' +
                JSON.parse(res.body).error
              : '';

          console.log(
            '🚀 ~ file: AgentProfileScreen.tablet.tsx:397 ~ imgToUpload ~ resErr:',
            resErr,
          );
          const fileSize = await FileSystem.getInfoAsync(uri);
          const isFileTooLarge = fileSize.exists && fileSize.size > 1048576;

          addToast([
            {
              message: isFileTooLarge
                ? 'Image size is too big. Please select another image or crop it into a smaller image.'
                : t('saveChanges.fail') + resErr,
            },
          ]);

          resetSelectedImage(agentProfilePicture);
          return;
        }
        default: {
          setSelectedImage(undefined);
          resetSelectedImage(agentProfilePicture);
          return;
        }
      }
    };

    imgToUpload()
      .then(() => {
        console.log('🚀 ~ file: AgentProfileScreen.tablet.tsx:397 ~ ');
      })
      .catch(err => {
        console.log('🚀 ~ file: AgentProfileScreen.tablet.tsx:400 ~ err:', err);
      })
      .finally(() => console.log('final action 1111'));
  };

  const onPressCamera = async () => {
    if (!selectedImage) {
      console.log(
        '🚀 ~ file: AgentProfileScreen.tablet.tsx:407 ~ onPressCamera ~ selectedImage TRUE',
      );
      Platform.OS == 'ios'
        ? ActionSheetIOS.showActionSheetWithOptions(
            {
              options: [
                // t('IRIS.editProfile.takePhoto'),
                // t('IRIS.editProfile.selectFromAlbum'),
                // t('IRIS.editProfile.cancel'),
                t('takePhoto'),
                t('selectFromAlbum'),
                t('cancel'),
              ],
              cancelButtonIndex: 2,
              userInterfaceStyle: 'light',
            },
            buttonIndex => {
              if (buttonIndex === 0) {
                takePhoto();
              } else if (buttonIndex === 1) {
                selectFromAlbum();
              } else if (buttonIndex === 2) {
                return;
              }
            },
          )
        : setIsProPicModalShown(true);
      return;
    } else {
      console.log(
        '🚀 ~ file: AgentProfileScreen.tablet.tsx:438 ~ onPressCamera ~ selectedImage FFFFALSE',
      );
      const cropRes = await ImageCropper.cropImageAsync(selectedImage, {
        editMode: true,
        onPressEdit: (dismiss, setShowEditModal) => {
          if (Platform.OS == 'ios') {
            console.log(
              '🚀 ~ file: AgentProfileScreen.tablet.tsx:400 ~ onPressCamera ~ Platform.OS :',
              Platform.OS,
            );
            ActionSheetIOS.showActionSheetWithOptions(
              {
                options: [
                  // t('IRIS.editProfile.takePhoto'),
                  // t('IRIS.editProfile.selectFromAlbum'),
                  // t('IRIS.editProfile.deleteProfilePic.title'),
                  // t('IRIS.editProfile.cancel'),
                  t('takePhoto'),
                  t('selectFromAlbum'),
                  t('deletePhoto'),
                  t('cancel'),
                ],
                cancelButtonIndex: 3,
                userInterfaceStyle: 'light',
              },
              buttonIndex => {
                if (buttonIndex === 0) {
                  takePhoto();
                } else if (buttonIndex === 1) {
                  selectFromAlbum();
                } else if (buttonIndex === 2) {
                  setIsDelPicModalShown(true);
                } else {
                  return;
                }
                dismiss && dismiss();
              },
            );
          } else {
            setShowEditModal && setShowEditModal(true);
          }
        },
        editButtons: [
          {
            // label: t('IRIS.editProfile.takePhoto'),
            label: t('takePhoto'),
            onPress: takePhoto,
          },
          {
            // label: t('IRIS.editProfile.selectFromAlbum'),
            label: t('selectFromAlbum'),

            onPress: selectFromAlbum,
          },
          {
            // label: t('IRIS.editProfile.deleteProfilePic.title'),
            label: t('deletePhoto'),
            onPress: () => {
              setIsDelPicModalShown(true);
            },
          },
          {
            // label: t('IRIS.editProfile.cancel'),
            label: t('cancel'),

            isCancelBtn: true,
          },
        ],
      });
      upload(cropRes?.uri);
      return;
    }
  };

  return (
    <>
      <TouchableOpacity
        onPress={onPressCamera}
        style={{
          position: 'absolute',
          height: space[10],
          width: space[10],
          backgroundColor: colors.background,
          borderRadius: borderRadius.full,
          alignItems: 'center',
          justifyContent: 'center',
          bottom: 0,
          right: 0,
          ...elevation[4],
        }}>
        <Icon.Camera size={space[6]} />
      </TouchableOpacity>
      <ProfilePicModal
        dismiss={dismissProPicModal}
        takePhoto={takePhoto}
        selectFromAlbum={selectFromAlbum}
        isVisible={isProPicModalShown}
      />
      <DelProfilePicModal
        dismiss={dismissDelProPicModal}
        isVisible={isDelPicModalShown}
        setSelectedImage={setSelectedImage}
        setDelProPicModalVisible={setIsDelPicModalShown}
      />
    </>
  );
}
