import { parse } from 'date-fns';
import { ConcernId } from 'features/fna/types/concern';
import { GoalKey } from 'features/fna/types/goal';
import { LifeStage } from 'features/fna/types/lifeJourney';
import { Vulnerable } from 'features/fna/types/vulnerable';
import { Lead } from 'types';
import {
  Dependent,
  DependentRelationship,
  ExpenseMode,
  ExpenseType,
  Fna,
  ExistingPolicy as FnaExistingPolicy,
} from 'types/case';
import { Gender } from 'types/person';
import { ProductCurrencyEnum } from 'types/products';
import { StringBoolean } from 'types/quotation';
import { cloneDeep } from 'utils/helper/objectUtil';
import OmitKeyType from 'utils/typescript/OmitKeyType';
import { create, StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  checkEducationCompletion,
  checkHealthProtectionCompletion,
  checkIncomeProtectionCompletion,
  checkInvestmentCompletion,
  checkLegacyPlanningCompletion,
  checkLoanCoverageCompletion,
  checkRetirementCompletion,
  checkSavingsCompletion,
  getCompulsoryFna,
} from '../helper/checkGoalCompletion';
import { FnaSummary } from 'features/fna/types/summary';
import { RiderGroup } from 'features/fna/constants/riderListConstants';

const LIFE_STYLE_DEFAULT_SET: {
  [key in LifeStage]: {
    lifeStage: LifeStage;
    numberOfKids?: number;
    kidsAge?: number[];
    partnerAge?: number;
  };
} = {
  SINGLE: {
    lifeStage: 'SINGLE',
  },
  SINGLE_WITH_DEPENDENT: {
    lifeStage: 'SINGLE_WITH_DEPENDENT',
  },
  COUPLE: {
    lifeStage: 'COUPLE',
  },
  COUPLE_WITH_KIDS: {
    lifeStage: 'COUPLE_WITH_KIDS',
    numberOfKids: 1,
    kidsAge: [1],
  },
  EMPTY_NESTER: {
    lifeStage: 'EMPTY_NESTER',
    partnerAge: 58,
  },
  RETIRED: {
    lifeStage: 'RETIRED',
    partnerAge: 58,
  },
};

export enum InvestmentDurationKey {
  LESS_THAN_FIVE_YEARS = 'less5',
  FIVE_YEARS_AND_ABOVE = 'more5',
}

export type Expense = {
  type: ExpenseType;
  amount: number | null;
};

export enum FamilySharePlanType {
  PRIMARY = 'P',
  SUBSIDIARY = 'S',
}

export type ExistingPolicy = Omit<FnaExistingPolicy, 'maturityDate'> & {
  maturityDate: Date;
};

export const initialRetirementExpenses = [
  ExpenseType.Housing,
  ExpenseType.Travel,
  ExpenseType.Healthcare,
  ExpenseType.Transportation,
  ExpenseType.Food,
  ExpenseType.Other,
].map(type => ({ type, amount: null }));

export type KidGoal = {
  dependentId?: string;
  targetAmount: number | null;
  coverageAmount: number | null;
  gapAmount: number | null;
  firstName: string;
  middleName?: string;
  lastName?: string;
  gender: Gender | null;
  dateOfBirth: Date | null;
  childAge: number | null;
  collegeType: number | null;
  yearsInCollege: number | null;
  yearsToAchieve: number | null;
  annualLivingExpenses: number | null;
  annualTuitionCosts: number | null;
  inflationRate: number | null;
  expenseMode: ExpenseMode | null;
  universityLocation: string;
  hasSavings: boolean | null;
};

export enum AdviceType {
  FULL = 'FULL',
  PARTIAL = 'PARTIAL',
  UNKNOWN = 'UNKNOWN',
}

export interface FnaState {
  isFullScreen: boolean;
  shouldShowRequiredBeforeSavingError: boolean;
  selectedProductCode: string;
  isMultipleInsuredsProduct: boolean;
  adviceType: AdviceType;
  compulsoryFna: ConcernId[];
  goalCompletion: Record<ConcernId, boolean>;
  lifeJourney: {
    title?: string | null;
    firstName: string;
    middleName?: string;
    lastName?: string;
    lifeStage: LifeStage | null;
    dob: Date | null;
    ageToRetire: number;
    numberOfKids: number;
    kidsAge: number[];
    havePartner: boolean;
    partnerAge: number;
    dependents: Dependent[];
    planToTravel: boolean;
    expectedIncomeIncrement: number | null;
    totalAssets: number | null;
    totalLiabilities: number | null;
    haveKidsOrDependents: boolean;
    annuallyIncome: {
      from: number | null;
      to: number | null;
    };
    monthlyIncome: {
      from: number | null;
      to: number | null;
    };
    goalPercentage: {
      from: number | null;
      to: number | null;
    };
    insuranceProtectionPeriod: {
      from: number | null;
      to: number | null;
    };
    concerns: ConcernId[];
    gender: Gender | null;
    productWithPremiumDevelopmentPotential: boolean | null;
    productCurrency: ProductCurrencyEnum | null;
    existingPolicies: ExistingPolicy[] | null;
    idNumber?: string;
  };
  preSyncDependents: Dependent[]; // dependents data that originally comes from dependent form
  isCompleteFna: boolean;
  educationGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    numberOfKids: number | null;
    goals: KidGoal[];
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    yearsToCollege: number | null;
    yearsInCollege: number | null;
    yearsToAchieve: number | null;
  };
  retirementGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    yearsToAchieve: number | null;
    ageToRetire: number | null;
    yearsToRetire: number | null;
    monthlyAllowance: number | null;
    hasSavings: boolean | null;
    annualIncome: number | null;
    yearsToReplace: number | null;
    otherCoverageAmount: number | null;
    inflationRate: number | null;
    investmentRate: number | null;
    // calculator
    expenseMode: ExpenseMode | null;
    expenses: Expense[];
  };
  investmentGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    yearsToAchieve: number | null;
    purpose: string | null;
    hasSavings: boolean | null;
    initialInvestmentAmount: number | null;
    regularInvestmentAmount: number | null;
    investmentDuration: InvestmentDurationKey | '';
    monthlyPayout: number | null;
    payoutPeriod: number | null;
  };
  incomeProtectionGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    hasSavings: boolean | null;
    monthlyExpenses: number | null;
    expenses: Expense[];
    yearsToReplace: number | null;
    annualIncome: number | null;
  };
  healthProtectionGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    hasSavings: boolean | null;
    healthEmergenciesAmount: number | null;
    insuranceCoverageAmount: number | null;
    hospitalisation: {
      targetAmount: number | null;
      coverageAmount: number | null;
      gapAmount: number | null;
      expiryAge: number | null;
    } | null;
    criticalIllness: {
      targetAmount: number | null;
      coverageAmount: number | null;
      gapAmount: number | null;
      expiryAge: number | null;
    } | null;
  };
  legacyPlanningGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    netTaxableEstateAmount: number | null;
  };
  loanCoverageGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
    loanTerm: number | null;
    loanAmount: number | null;
    hasCIProtection: boolean | null;
    ciProtectionAmount: number | null;
  };
  savingsGoal: {
    enabled: boolean | null;
    toBeDiscussed: boolean | null;
    alreadyPlanned: boolean | null;
    purpose: string;
    yearsToAchieve: number | null;
    targetAmount: number | null;
    coverageAmount: number | null;
    gapAmount: number | null;
  };

  existingLead: Lead | null;
  vulnerable: Vulnerable;
  familySharePlanType: FamilySharePlanType | null;
  familySharePlanPolicyNumber: string;
  familySharePlanCustomerId: string;
  shouldHighlight: boolean;
}

export const GOAL_KEYS: Record<ConcernId, GoalKey> = {
  INCOME_PROTECTION: 'incomeProtectionGoal',
  HEALTH_PROTECTION: 'healthProtectionGoal',
  EDUCATION: 'educationGoal',
  RETIREMENT: 'retirementGoal',
  LOAN_PROTECTION: 'loanCoverageGoal',
  LEGACY_PLANNING: 'legacyPlanningGoal',
  INVESTMENT: 'investmentGoal',
  SAVINGS: 'savingsGoal',
  ACCIDENT_EXPENSES: 'healthProtectionGoal',
  CRITICAL_ILLNESS: 'healthProtectionGoal',
};

export const appropriateTypeofAdditionalBenefitConcernTarget: Record<
  ConcernId,
  RiderGroup[]
> = {
  HEALTH_PROTECTION: [RiderGroup.REGULAR_TOP_UP],
  CRITICAL_ILLNESS: [RiderGroup.REGULAR_TOP_UP],
  ACCIDENT_EXPENSES: [RiderGroup.REGULAR_TOP_UP],
  LEGACY_PLANNING: [RiderGroup.HOSPITAL_SURGICAL_RIDER],
  EDUCATION: [RiderGroup.CI_RIDER, RiderGroup.WAIVER_RIDER],
  RETIREMENT: [RiderGroup.ADDB_RIDER],
  SAVINGS: [RiderGroup.WAIVER_RIDER, RiderGroup.FAMILY_TERM_RIDER],
  INCOME_PROTECTION: [],
  INVESTMENT: [],
  LOAN_PROTECTION: [],
};

export const totalInsuranceCompensationIncomeTarget = [
  {
    from: null,
    to: 5000000,
    target: 'totalInsuranceCompensation.lessThan5M',
  },
  {
    from: 5000000,
    to: 25000000,
    target: 'totalInsuranceCompensation.between5MAnd25M',
  },
  {
    from: 25000000,
    to: 100000000,
    target: 'totalInsuranceCompensation.between25MAnd100M',
  },
  {
    from: 100000000,
    to: null,
    target: 'totalInsuranceCompensation.moreThan100M',
  },
];

export interface FnaStore extends FnaState {
  updateProfileName: (
    firstName: string,
    middleName?: string | null,
    lastName?: string | null,
  ) => void;
  updateSelectedProductCode: (
    productCode: string,
    isMultipleInsureds?: boolean,
  ) => void;
  updateLifeStage: (lifeStage: LifeStage | null) => void;
  updateDob: (dob: Date) => void;
  updateAnnuallyIncome: (annuallyIncome: {
    from: number | null;
    to: number | null;
  }) => void;
  updateMonthlyIncome: (monthlyIncome: {
    from: number | null;
    to: number | null;
  }) => void;
  updateGoalPercentage: (goalPercentage: {
    from: number | null;
    to: number | null;
  }) => void;
  updateInsuranceProtectionPeriod: (insuranceProtectionPeriod: {
    from: number | null;
    to: number | null;
  }) => void;
  updateConcerns: (concerns: ConcernId[]) => void;
  resetConcernsPlanning: () => void;
  updateFullScreen: (isFullScreen: boolean) => void;
  updateAdviceType: (adviceType: AdviceType) => void;
  updateGender: (gender: Gender | null) => void;
  updateProductPremiumDevelopmentPotential: (
    productPremiumDevelopmentPotential: StringBoolean | null,
  ) => void;
  updateProductCurrency: (productCurrency: ProductCurrencyEnum | null) => void;
  updateAgeRetire: (ageToRetire: number) => void;
  updateExpectedIncomeIncrement: (
    expectedIncomeIncrement: number | null,
  ) => void;
  updateTotalAssets: (totalAssets: number | null) => void;
  updateTotalLiabilities: (totalLiabilities: number | null) => void;
  updateHaveKidsOrDependents: (haveKidsOrDependents: boolean) => void;
  updateTitle: (title: string | null) => void;
  updateNumberOfKids: (numberOfKids: number) => void;
  updateHavePartner: (havePartner: boolean) => void;
  updatePlanToTravel: (planToTravel: boolean) => void;
  updatePartnerAge: (partnerAge: number) => void;
  updateKidsAge: (kidAge: number, kidIndex: number) => void;
  updateDependents: (dependents: Dependent[]) => void;
  updatePreSyncDependents: (dependents: Dependent[]) => void;
  updateIdNumber: (idNumber: string) => void;
  updateEducationGoal: (
    educationGoal: Partial<FnaState['educationGoal']>,
  ) => void;
  updateRetirementGoal: (
    retirementGoal: Partial<FnaState['retirementGoal']>,
  ) => void;
  updateInvestmentGoal: (
    investmentGoal: Partial<FnaState['investmentGoal']>,
  ) => void;
  updateIncomeProtectionGoal: (
    incomeGoal: Partial<FnaState['incomeProtectionGoal']>,
  ) => void;
  updateHealthProtectionGoal: (
    healthGoal: Partial<FnaState['healthProtectionGoal']>,
  ) => void;
  updateLegacyPlanningGoal: (
    legacyGoal: Partial<FnaState['legacyPlanningGoal']>,
  ) => void;
  updateLoanCoverageGoal: (
    loanGoal: Partial<FnaState['loanCoverageGoal']>,
  ) => void;
  updateSavingsGoal: (loanGoal: Partial<FnaState['savingsGoal']>) => void;
  updateFna: (fna: Partial<FnaState>) => void;
  resetFnaStoreState: () => void;
  updateCompleteQuestionFNA: (isCompleteFna: boolean) => void;
  setRequiredBeforeSavingError: (
    showRequiredBeforeSavingError: boolean,
  ) => void;
  setExistingLead: (existingLead: Lead | null) => void;
  updateVulnerable: (vulnerable: Partial<Vulnerable>) => void;
  setExistingPolicies: (policies: ExistingPolicy[] | null) => void;
  getCustomerName: () => string;
  getFnaCompletion: () => boolean;
  setFamilySharePlanType: (type: FamilySharePlanType) => void;
  setFamilySharePlanPolicyNumber: (policyNum: string) => void;
  setFamilySharePlanCustomerId: (customerId: string) => void;
  setHighlight: (shouldHighlight: boolean) => void;
  getFnaSummary: () => FnaSummary;
}

const createFnaStore: StateCreator<FnaStore> = (set, get) => ({
  ...initialFnaState,
  updateProfileName: (firstName, middleName, lastName) =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        firstName,
        middleName: middleName ?? undefined,
        lastName: lastName ?? undefined,
      },
    })),
  updateSelectedProductCode: (productCode, isMultipleInsureds) => {
    set(() => ({
      selectedProductCode: productCode,
      isMultipleInsuredsProduct: !!isMultipleInsureds,
    }));
  },
  updateGender: gender =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        gender,
      },
    })),
  updateProductPremiumDevelopmentPotential: type =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        productWithPremiumDevelopmentPotential: type === 'Y',
      },
    })),
  updateProductCurrency: productCurrency =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        productCurrency,
      },
    })),
  updateLifeStage: lifeStage =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        ...(lifeStage
          ? LIFE_STYLE_DEFAULT_SET[lifeStage]
          : {
              lifeStage: null,
              numberOfKids: 1,
              kidsAge: [],
              havePartner: false,
              partnerAge: 58,
              planToTravel: false,
            }),
        dependents:
          lifeStage !== get().lifeJourney.lifeStage
            ? []
            : get().lifeJourney.dependents,
      },
    })),
  updateDob: dob =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        dob,
      },
    })),
  updateAnnuallyIncome: annuallyIncome =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        annuallyIncome: annuallyIncome,
      },
    })),
  updateMonthlyIncome: monthlyIncome =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        monthlyIncome: monthlyIncome,
      },
    })),
  updateGoalPercentage: goalPercentage =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        goalPercentage,
      },
    })),
  updateInsuranceProtectionPeriod: insuranceProtectionPeriod =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        insuranceProtectionPeriod,
      },
    })),
  updateConcerns: concerns => {
    const oldState = get();
    const compulsoryFna = getCompulsoryFna(concerns, oldState.adviceType);
    const newState: Partial<OmitKeyType<FnaState, GoalKey>> = {
      lifeJourney: {
        ...oldState.lifeJourney,
        concerns: concerns,
      },
      compulsoryFna,
    };

    concerns.forEach(concern => {
      const goalKey = GOAL_KEYS[concern];
      newState[goalKey] = {
        ...oldState[goalKey],
        // Reprioritize concerns always reset toBeDiscussed
        toBeDiscussed: compulsoryFna.includes(concern),
      };
    });
    set(() => newState as Partial<FnaState>);
  },
  resetConcernsPlanning: () => {
    const concerns = get().lifeJourney.concerns;
    get().updateConcerns(concerns);
  },
  updateFullScreen: isFullScreen =>
    set(() => ({
      isFullScreen,
    })),
  updateAdviceType: adviceType =>
    set(() => ({
      adviceType: adviceType,
    })),
  updateAgeRetire: ageToRetire =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        ageToRetire: ageToRetire,
      },
    })),
  updateExpectedIncomeIncrement: expectedIncomeIncrement =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        expectedIncomeIncrement: expectedIncomeIncrement,
      },
    })),
  updateTotalAssets: totalAssets =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        totalAssets: totalAssets,
      },
    })),
  updateTotalLiabilities: totalLiabilities =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        totalLiabilities: totalLiabilities,
      },
    })),
  updateHaveKidsOrDependents: haveKidsOrDependents =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        haveKidsOrDependents,
      },
    })),
  updateTitle: title =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        title: title,
      },
    })),
  updateNumberOfKids: numberOfKids =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        numberOfKids,
        kidsAge: Array(numberOfKids).fill(1),
      },
    })),
  updateHavePartner: havePartner =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        havePartner,
      },
    })),
  updatePartnerAge: partnerAge =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        partnerAge,
      },
    })),
  updateDependents: dependents =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        dependents,
        numberOfKids: dependents.filter(
          ({ relationship }) => relationship === DependentRelationship.kid,
        ).length,
      },
    })),
  updatePreSyncDependents: preSyncDependents =>
    set(() => ({
      preSyncDependents,
    })),
  updateIdNumber: idNumber =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        idNumber,
      },
    })),
  updatePlanToTravel: planToTravel =>
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        planToTravel,
      },
    })),
  updateKidsAge: (kidAge, kidIndex) => {
    const newKidsAge = [...get().lifeJourney.kidsAge];
    newKidsAge[kidIndex] = kidAge;
    set(() => ({
      lifeJourney: {
        ...get().lifeJourney,
        kidsAge: newKidsAge,
      },
    }));
  },
  updateEducationGoal: updateGoal => {
    const { goalCompletion, educationGoal, adviceType, lifeJourney } = get();
    const newGoal = {
      ...educationGoal,
      ...updateGoal,
    };
    set(() => ({
      educationGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        EDUCATION: checkEducationCompletion(
          newGoal,
          adviceType,
          lifeJourney.concerns,
        ),
      },
    }));
  },
  updateRetirementGoal: updateGoal => {
    const { goalCompletion, retirementGoal } = get();
    const newGoal = {
      ...retirementGoal,
      ...updateGoal,
    };
    set(() => ({
      retirementGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        RETIREMENT: checkRetirementCompletion(newGoal),
      },
    }));
  },
  updateInvestmentGoal: updateGoal => {
    const { goalCompletion, investmentGoal } = get();
    const newGoal = {
      ...investmentGoal,
      ...updateGoal,
    };
    set(() => ({
      investmentGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        INVESTMENT: checkInvestmentCompletion(newGoal),
      },
    }));
  },
  updateIncomeProtectionGoal: updateGoal => {
    const { goalCompletion, incomeProtectionGoal } = get();
    const newGoal = {
      ...incomeProtectionGoal,
      ...updateGoal,
    };
    set(() => ({
      incomeProtectionGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        INCOME_PROTECTION: checkIncomeProtectionCompletion(newGoal),
      },
    }));
  },
  updateHealthProtectionGoal: updateGoal => {
    const { goalCompletion, healthProtectionGoal } = get();
    const newGoal = {
      ...healthProtectionGoal,
      ...updateGoal,
    };
    set(() => ({
      healthProtectionGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        HEALTH_PROTECTION: checkHealthProtectionCompletion(newGoal),
      },
    }));
  },
  updateLegacyPlanningGoal: updateGoal => {
    const { goalCompletion, legacyPlanningGoal } = get();
    const newGoal = {
      ...legacyPlanningGoal,
      ...updateGoal,
    };
    set(() => ({
      legacyPlanningGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        LEGACY_PLANNING: checkLegacyPlanningCompletion(newGoal),
      },
    }));
  },
  updateLoanCoverageGoal: updateGoal => {
    const { goalCompletion, loanCoverageGoal } = get();
    const newGoal = {
      ...loanCoverageGoal,
      ...updateGoal,
    };
    set(() => ({
      loanCoverageGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        LOAN_PROTECTION: checkLoanCoverageCompletion(newGoal),
      },
    }));
  },
  updateSavingsGoal: updateGoal => {
    const { goalCompletion, savingsGoal } = get();
    const newGoal = {
      ...savingsGoal,
      ...updateGoal,
    };
    set(() => ({
      savingsGoal: newGoal,
      goalCompletion: {
        ...goalCompletion,
        SAVINGS: checkSavingsCompletion(newGoal),
      },
    }));
  },
  updateFna: fna => {
    set(() => ({
      ...get(),
      ...fna,
    }));
  },
  resetFnaStoreState: () => {
    set(() => ({
      ...get(),
      ...initialFnaState,
    }));
  },
  updateCompleteQuestionFNA: (isCompleteFna: boolean) => {
    set(() => ({
      ...get(),
      isCompleteFna,
    }));
  },
  setRequiredBeforeSavingError: (
    shouldShowRequiredBeforeSavingError: boolean,
  ) => {
    set(() => ({
      shouldShowRequiredBeforeSavingError,
    }));
  },
  setExistingLead: (existingLead: Lead | null) => set({ existingLead }),
  updateVulnerable: (vulnerable: Partial<Vulnerable>) =>
    set({
      vulnerable: {
        ...get().vulnerable,
        ...vulnerable,
      },
    }),
  setExistingPolicies: (policies: ExistingPolicy[] | null) => {
    set({
      lifeJourney: {
        ...get().lifeJourney,
        existingPolicies: policies,
      },
    });
  },
  getCustomerName: () => {
    return [
      get().lifeJourney.firstName?.trim(),
      get().lifeJourney.middleName?.trim(),
      get().lifeJourney.lastName?.trim(),
    ]
      .filter(Boolean)
      .join(' ')
      .trim();
  },
  getFnaCompletion: () => {
    const { compulsoryFna, goalCompletion } = get();
    if (compulsoryFna.length === 0) return true;
    return compulsoryFna
      .map(concern => goalCompletion[concern])
      .every(complete => complete);
  },
  setFamilySharePlanType: (familySharePlanType: FamilySharePlanType) => {
    set({ familySharePlanType });
  },
  setFamilySharePlanPolicyNumber: (familySharePlanPolicyNumber: string) => {
    set({ familySharePlanPolicyNumber });
  },
  setFamilySharePlanCustomerId: (customerId: string) => {
    set({ familySharePlanCustomerId: customerId });
  },
  setHighlight: (shouldHighlight: boolean) => {
    set({ shouldHighlight });
  },
  getFnaSummary: () => {
    const { lifeJourney } = get();
    const { monthlyIncome, productWithPremiumDevelopmentPotential, concerns } =
      lifeJourney;
    return {
      totalInsuranceCompensation: totalInsuranceCompensationIncomeTarget.find(
        range =>
          range.from === monthlyIncome.from && range.to === monthlyIncome.to,
      )?.target,
      recommendedProductType: productWithPremiumDevelopmentPotential
        ? 'recommendedProductType.yes'
        : 'recommendedProductType.no',
      appropriateTypeofAdditionalBenefit: productWithPremiumDevelopmentPotential
        ? Array.from(
            new Set(
              concerns.flatMap(
                key => appropriateTypeofAdditionalBenefitConcernTarget[key],
              ),
            ),
          )
        : [],
    };
  },
});

export const initialEducationKidGoal = {
  targetAmount: null,
  coverageAmount: null,
  gapAmount: null,
  firstName: '',
  middleName: '',
  lastName: '',
  childAge: null,
  gender: null,
  dateOfBirth: null,
  collegeType: null,
  yearsInCollege: null,
  yearsToAchieve: null,
  annualTuitionCosts: null,
  annualLivingExpenses: null,
  inflationRate: null,
  universityLocation: '',
  expenseMode: null,
  hasSavings: null,
};

export const initialFnaState: FnaState = {
  shouldShowRequiredBeforeSavingError: false,
  selectedProductCode: '',
  isMultipleInsuredsProduct: false,
  adviceType: AdviceType.FULL,
  compulsoryFna: [],
  goalCompletion: {
    SAVINGS: false,
    INCOME_PROTECTION: false,
    HEALTH_PROTECTION: false,
    LOAN_PROTECTION: false,
    INVESTMENT: false,
    RETIREMENT: false,
    LEGACY_PLANNING: false,
    EDUCATION: false,
    CRITICAL_ILLNESS: false,
    ACCIDENT_EXPENSES: false,
  },
  lifeJourney: {
    firstName: '',
    middleName: '',
    lastName: '',
    lifeStage: null,
    title: null,
    ageToRetire: 65,
    expectedIncomeIncrement: 0,
    totalAssets: 0,
    totalLiabilities: 0,
    haveKidsOrDependents: false,
    dob: null,
    annuallyIncome: {
      from: null,
      to: null,
    },
    monthlyIncome: {
      from: null,
      to: null,
    },
    goalPercentage: {
      from: null,
      to: 10,
    },
    insuranceProtectionPeriod: {
      from: null,
      to: null,
    },
    concerns: [],
    numberOfKids: 1,
    kidsAge: [],
    havePartner: false,
    partnerAge: 58,
    dependents: [],
    planToTravel: false,
    gender: null,
    productWithPremiumDevelopmentPotential: null,
    productCurrency: null,
    existingPolicies: null,
  },
  preSyncDependents: [],
  isFullScreen: false,
  isCompleteFna: false,
  educationGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    numberOfKids: 1,
    goals: [cloneDeep(initialEducationKidGoal)],
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    yearsToCollege: null,
    yearsInCollege: null,
    yearsToAchieve: null,
  },
  retirementGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    yearsToAchieve: null,
    ageToRetire: 65,
    yearsToRetire: null,
    monthlyAllowance: null,
    hasSavings: null,
    annualIncome: null,
    yearsToReplace: null,
    otherCoverageAmount: null,
    inflationRate: null,
    investmentRate: null,
    expenseMode: null,
    expenses: initialRetirementExpenses,
  },
  investmentGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    yearsToAchieve: null,
    purpose: null,
    hasSavings: null,
    initialInvestmentAmount: null,
    regularInvestmentAmount: null,
    investmentDuration: '',
    monthlyPayout: null,
    payoutPeriod: null,
  },
  incomeProtectionGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    hasSavings: null,
    monthlyExpenses: null,
    expenses: [
      ExpenseType.Housing,
      ExpenseType.Travel,
      ExpenseType.Healthcare,
      ExpenseType.Transportation,
      ExpenseType.Food,
      ExpenseType.RegularCommitment,
      ExpenseType.Other,
    ].map(type => ({ type, amount: null })),
    yearsToReplace: null,
    annualIncome: null,
  },
  healthProtectionGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    hasSavings: null,
    insuranceCoverageAmount: null,
    healthEmergenciesAmount: null,
    hospitalisation: null,
    criticalIllness: null,
  },
  legacyPlanningGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    netTaxableEstateAmount: null,
  },
  loanCoverageGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    targetAmount: null,
    coverageAmount: null,
    gapAmount: null,
    loanTerm: null,
    loanAmount: null,
    hasCIProtection: null,
    ciProtectionAmount: null,
  },
  savingsGoal: {
    enabled: true,
    toBeDiscussed: null,
    alreadyPlanned: null,
    gapAmount: null,
    purpose: '',
    yearsToAchieve: null,
    targetAmount: null,
    coverageAmount: null,
  },

  existingLead: null,
  vulnerable: {
    vulnerableCustomerTag: '',
    vulnerableCustomerOption: '',
    isEmployed: undefined,
    haveExistingMhit: undefined,
  },
  familySharePlanType: null,
  familySharePlanPolicyNumber: '',
  familySharePlanCustomerId: '',
  shouldHighlight: false,
};

export const useFnaStore = create(immer(devtools(createFnaStore)));

type ParseFnaReturn = Pick<
  FnaState,
  | 'adviceType'
  | 'lifeJourney'
  | 'preSyncDependents'
  | 'educationGoal'
  | 'retirementGoal'
  | 'investmentGoal'
  | 'incomeProtectionGoal'
  | 'healthProtectionGoal'
  | 'legacyPlanningGoal'
  | 'loanCoverageGoal'
  | 'savingsGoal'
  | 'compulsoryFna'
  | 'goalCompletion'
  | 'vulnerable'
>;

export const parseFna = (fna: Fna): ParseFnaReturn => {
  const fnaState: ParseFnaReturn = {
    adviceType:
      fna.adviceType === AdviceType.UNKNOWN
        ? AdviceType.FULL
        : fna.adviceType || AdviceType.FULL,
    goalCompletion: {
      SAVINGS: false,
      INCOME_PROTECTION: false,
      HEALTH_PROTECTION: false,
      LOAN_PROTECTION: false,
      INVESTMENT: false,
      RETIREMENT: false,
      LEGACY_PLANNING: false,
      EDUCATION: false,
      CRITICAL_ILLNESS: false,
      ACCIDENT_EXPENSES: false,
    },
    lifeJourney: {
      ...fna,
      firstName: fna.firstName ?? fna.name,
      dob: fna.dateOfBirth.date
        ? parse(fna.dateOfBirth.date, 'yyyy-MM-dd', new Date())
        : null,
      lifeStage: fna.lifeStage as LifeStage,
      concerns: fna.concerns as ConcernId[],
      gender: fna.gender as Gender,
      productWithPremiumDevelopmentPotential:
        fna?.productWithPremiumDevelopmentPotential ?? null,
      productCurrency: fna.productCurrency as ProductCurrencyEnum,
      haveKidsOrDependents: false, // TODO: consider remove, can't find any usage
      existingPolicies: (fna.existingPolicies || []).map(p => ({
        ...p,
        maturityDate: p.maturityDate ? new Date(p.maturityDate) : new Date(),
      })),
      insuranceProtectionPeriod:
        fna?.insuranceProtectionPeriod ??
        initialFnaState.lifeJourney.insuranceProtectionPeriod,
    },
    preSyncDependents: fna.dependents,
    compulsoryFna: [],
    vulnerable: fna.vulnerable
      ? {
          vulnerableCustomerTag: fna.vulnerable.vulnerableCustomerTag ?? '',
          vulnerableCustomerOption:
            fna.vulnerable.vulnerableCustomerOption ?? '',
          isEmployed: fna.vulnerable.isEmployed,
          haveExistingMhit: fna.vulnerable.haveExistingMhit,
        }
      : initialFnaState.vulnerable,
    educationGoal: fna.educationGoal
      ? {
          ...fna.educationGoal,
          goals: fna.educationGoal.goals.map(goal => ({
            ...goal,
            dateOfBirth:
              goal.dateOfBirth && goal.dateOfBirth.date
                ? parse(goal.dateOfBirth.date, 'yyyy-MM-dd', new Date())
                : null,
          })),
          enabled:
            typeof fna.educationGoal.enabled === 'boolean'
              ? fna.educationGoal.enabled
              : true,
        }
      : initialFnaState.educationGoal,
    retirementGoal: fna.retirementGoal
      ? {
          ...fna.retirementGoal,
          enabled:
            typeof fna.retirementGoal.enabled === 'boolean'
              ? fna.retirementGoal.enabled
              : true,
        }
      : initialFnaState.retirementGoal,
    investmentGoal: fna.investmentGoal
      ? {
          ...fna.investmentGoal,
          investmentDuration: fna.investmentGoal
            .investmentDuration as InvestmentDurationKey,
          enabled:
            typeof fna.investmentGoal.enabled === 'boolean'
              ? fna.investmentGoal.enabled
              : true,
        }
      : initialFnaState.investmentGoal,
    incomeProtectionGoal: fna.incomeProtectionGoal
      ? {
          ...fna.incomeProtectionGoal,
          enabled:
            typeof fna.incomeProtectionGoal.enabled === 'boolean'
              ? fna.incomeProtectionGoal.enabled
              : true,
        }
      : initialFnaState.incomeProtectionGoal,
    healthProtectionGoal: fna.healthProtectionGoal
      ? {
          ...fna.healthProtectionGoal,
          enabled:
            typeof fna.healthProtectionGoal.enabled === 'boolean'
              ? fna.healthProtectionGoal.enabled
              : true,
        }
      : initialFnaState.healthProtectionGoal,
    legacyPlanningGoal: fna.legacyPlanningGoal
      ? {
          ...fna.legacyPlanningGoal,
          enabled:
            typeof fna.legacyPlanningGoal.enabled === 'boolean'
              ? fna.legacyPlanningGoal.enabled
              : true,
        }
      : initialFnaState.legacyPlanningGoal,
    loanCoverageGoal: fna.loanCoverageGoal
      ? {
          ...fna.loanCoverageGoal,
          enabled:
            typeof fna.loanCoverageGoal.enabled === 'boolean'
              ? fna.loanCoverageGoal.enabled
              : true,
        }
      : initialFnaState.loanCoverageGoal,
    savingsGoal: fna.savingsGoal
      ? {
          ...fna.savingsGoal,
          enabled:
            typeof fna.savingsGoal.enabled === 'boolean'
              ? fna.savingsGoal.enabled
              : true,
        }
      : initialFnaState.savingsGoal,
  };
  fnaState.compulsoryFna = fna.concerns
    ? getCompulsoryFna(fna.concerns as ConcernId[], fnaState.adviceType)
    : [];
  if (fna.concerns) {
    (fna.concerns as ConcernId[]).forEach(concernId => {
      switch (concernId) {
        case 'SAVINGS':
          fnaState.goalCompletion[concernId] = checkSavingsCompletion(
            fnaState.savingsGoal,
          );
          break;
        case 'INCOME_PROTECTION':
          fnaState.goalCompletion[concernId] = checkIncomeProtectionCompletion(
            fnaState.incomeProtectionGoal,
          );
          break;
        case 'HEALTH_PROTECTION':
          fnaState.goalCompletion[concernId] = checkHealthProtectionCompletion(
            fnaState.healthProtectionGoal,
          );
          break;
        case 'LOAN_PROTECTION':
          fnaState.goalCompletion[concernId] = checkLoanCoverageCompletion(
            fnaState.loanCoverageGoal,
          );
          break;
        case 'INVESTMENT':
          fnaState.goalCompletion[concernId] = checkInvestmentCompletion(
            fnaState.investmentGoal,
          );
          break;
        case 'RETIREMENT':
          fnaState.goalCompletion[concernId] = checkRetirementCompletion(
            fnaState.retirementGoal,
          );
          break;
        case 'LEGACY_PLANNING':
          fnaState.goalCompletion[concernId] = checkLegacyPlanningCompletion(
            fnaState.legacyPlanningGoal,
          );
          break;
        case 'EDUCATION':
          fnaState.goalCompletion[concernId] = checkEducationCompletion(
            fnaState.educationGoal,
            fnaState.adviceType,
            fnaState.lifeJourney.concerns,
          );
          break;
      }
    });
  }
  return fnaState;
};
