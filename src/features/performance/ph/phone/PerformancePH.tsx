import { useTheme } from '@emotion/react';
import PerformanceChartSection from 'features/performance/ph/phone/components/PerformanceChartSection';
import RankingLayout from 'features/performance/ph/phone/components/RankingInfoLayout';
import RecognitionAndCampaigns from 'features/performance/ph/phone/components/RecognitionAndCampaigns';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAllPerformance } from 'hooks/useGetPerformance';
import HeaderToolTip from 'navigation/components/HeaderToolTip';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { useEffect, useState } from 'react';
import { ScrollView, View } from 'react-native';
import TooltipContent from './components/TooltipContent';
import { build } from 'utils/context';

const SHOW_TOOLTIP = false; // Hide for phrase 1

export default function PerformancePH() {
  const { colors, space } = useTheme();

  const [isRanking, setIsRanking] = useState(false);

  const { isLoading } = useGetAllPerformance();

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  useEffect(() => {
    if (isLoading) {
      setAppLoading();
    } else {
      setAppIdle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoading]);

  return (
    <View style={{ backgroundColor: colors.background, flex: 1 }}>
      <ScreenHeader
        route={'Performance'}
        rightChildren={
          SHOW_TOOLTIP ? <HeaderToolTip children={<TooltipContent />} /> : <></>
        }
      />

      {isRanking ? (
        <>
          <ScrollView>
            <RankingLayout floatBtnOnPress={() => setIsRanking(!isRanking)} />
          </ScrollView>
        </>
      ) : (
        <>
          <ScrollView
            style={{ backgroundColor: colors.palette.fwdGrey[50] }}
            contentContainerStyle={{
              backgroundColor: colors.palette.fwdGrey[50],
              paddingBottom: space[25],
            }}>
            <PerformanceChartSection
              floatBtnOnPress={() => setIsRanking(!isRanking)}
            />
            <RecognitionAndCampaigns />
          </ScrollView>
        </>
      )}
    </View>
  );
}
