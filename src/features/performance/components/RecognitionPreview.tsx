import { useTheme } from '@emotion/react';
import LoadingIndicator from 'components/LoadingIndicator';
import { Box, Row, Typography } from 'cube-ui-components';
import { Image } from 'expo-image';
import EliteAgentCard from 'features/performance/components/EliteAgentCard';
import RecognitionCard from 'features/performance/components/RecognitionCard';
import { ElieteAgentMedalIcon } from 'features/recognition/assets/EliteAgentMedalIcons';
import { MDRTSmallIcon } from 'features/recognition/assets/MDRTIcons';
import {
  DemoEliteAgencyResponse,
  EliteAgencyResponseType,
  updateEliteAgentData,
} from 'features/recognition/utils/EliteAgencyUtils';
import { useRootStackNavigation } from 'hooks/useRootStack';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { MDRTTiers } from 'types/performance';
import FWDEliteImage from 'features/recognition/assets/FWDElite.png';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { country } from 'utils/context';

const IS_PH = country === 'ph';
const SHOW_ELITE_AGENT_CARD = false;

export default function RecognitionPreview({
  isLoading = false,
  data: { currentTier, nextTier, percent },
  agentCode,
  agentName,
}: {
  isLoading?: boolean;
  data: {
    currentTier?: MDRTTiers | undefined;
    nextTier: MDRTTiers | undefined;
    percent: number;
  };
  agentCode?: string | null;
  agentName?: string | null;
}) {
  const { colors, space } = useTheme();
  const navigation = useRootStackNavigation();
  const { t } = useTranslation(['performance', 'recognition']);
  const { isTabletMode } = useLayoutAdoptionCheck();

  const MDRTData = {
    icon:
      nextTier == 'FWDElite' ? (
        <Image
          source={FWDEliteImage}
          contentFit={'contain'}
          style={{
            height: 42 + 4,
            width: 42 + 4,
          }}
        />
      ) : (
        <MDRTSmallIcon />
      ),
    nextTier: nextTier ?? '--',
    percent: percent && percent > 100 ? 100 : percent ?? 0,
  };

  const EliteAgentData: EliteAgencyResponseType = updateEliteAgentData({
    EliteAgencyResponse: DemoEliteAgencyResponse,
  });

  return (
    <Box
      p={space[5]}
      borderRadius={space[4]}
      backgroundColor={colors.background}>
      <Typography.H6 fontWeight={'bold'} style={{ paddingBottom: space[4] }}>
        {t('performance:performance.recognition')}
      </Typography.H6>

      {isLoading ? (
        <Box px={space[4]}>
          <LoadingIndicator />
        </Box>
      ) : (
        <Row style={{ flex: 1, gap: space[3] }}>
          <RecognitionCard
            mode={isTabletMode ? 'tablet' : 'phone'}
            icon={MDRTData.icon}
            // ! Change only the title
            title={
              IS_PH
                ? MDRTData.nextTier == 'FWDElite'
                  ? t('performance:performance.recognition.FWDElite')
                  : MDRTData.nextTier
                : t('performance:recognition.title')
            }
            subtitle={
              MDRTData.nextTier == 'FWDElite'
                ? t('performance:performance.recognition.FWDElite')
                : MDRTData.nextTier
            }
            style={{
              width: isTabletMode ? 300 : '100%',
              borderWidth: 1,
              borderColor: colors.palette.fwdGrey[100],
            }}
            percentage={MDRTData.percent}
            onPressCard={() =>
              navigation.navigate('RecognitionDetails', {
                title: MDRTData.nextTier,
                percent: MDRTData.percent,
                agentCode,
                agentName,
              })
            }
          />

          {SHOW_ELITE_AGENT_CARD ? (
            <EliteAgentCard
              icon={
                <ElieteAgentMedalIcon
                  medalColor={EliteAgentData.currentTier}
                  containerStyle={{ width: 42, height: 42 }}
                />
              }
              style={{
                flex: 1,
                borderWidth: 1,
                paddingVertical: space[3],
                paddingLeft: space[4],
                paddingRight: space[2],
                borderColor: colors.palette.fwdGrey[100],
                minHeight: space[22],
              }}
              title={'Elite Agent'}
              subtitle={t(
                `recognition:recognition.eliteAgent.${
                  EliteAgentData.currentTier || 'bronze'
                }`,
              )}
              percentage={70}
              dayCount={
                EliteAgentData.dayCounCal || EliteAgentData.dayCount || 0
              }
              onPressCard={() => navigation.navigate('EliteAgencyDetails')}
            />
          ) : (
            <Box flex={1}></Box>
          )}
        </Row>
      )}
    </Box>
  );
}
