export default {
  'performance.yourPerformance': 'Your performance',
  'performance.tooltip.cheer': ' Woohoo! ',
  'performance.tooltip.ifClause':
    'If you can close the {{count}} saved proposals, you can get:',
  'performance.potentialCustomType': 'Potential {{type}}',
  'performance.myCustomType': 'My {{type}}',
  'performance.bestCustomType': 'No.1  {{type}}',
  'performance.toBeTopCustomType': 'To be the Top ({{type}})',
  'performance.metric.APE': 'APE',
  'performance.metric.ACE': 'ACE',
  'performance.metric.FYP': 'FYP',
  'performance.metric.AFYP': 'AFYP',
  'performance.metric.FYC': 'FYC',
  'performance.metric.Case': 'Case',
  'performance.metric.issued': '{{metric}} Issued',
  'performance.metric.completed': '{{metric}} Completed',
  'performance.metric.submitted': ' {{metric}} Submitted',
  'performance.metric.totalIssued': 'Total {{metric}} Issued',
  'performance.metric.totalSubmitted': 'Total {{metric}} Submitted',
  'performance.metric.totalCompleted': 'Total {{metric}} Completed',
  'performance.potentialAPE': 'Potential APE',
  'performance.mySalesAPE': 'My sales APE',
  'performance.bestSalesAPE': 'No.1 sales APE',
  'performance.toBeTop': 'To be the Top (FYP)',
  'performance.tab.MTD': 'MTD',
  'performance.tab.YTD': 'YTD',
  'performance.ranking.directTeam': 'Direct team',
  'performance.ranking.MTD': 'PH (MTD)',
  'performance.ranking.YTD': 'PH (YTD)',
  'performance.caseCount': 'Case Count',
  'performance.salesAPE': 'Sales APE',
  'performance.label.seeDetails': 'See details',
  'performance.recognition': 'Recognition',
  'performance.recognition.emptyCase': `You haven't got any recognition yet.`,
  'performance.daysLeft.single': '{{dayCount}} day left',
  'performance.daysLeft.plural': '{{dayCount}} days left',
  'performance.recognition.details.nextTier': 'Next tier',
  'performance.details.month.others': 'This month other performance',
  'performance.details.totalMetric.submitted': 'Total {{metric}} submitted',
  'performance.details.totalMetric.completed': 'Total {{metric}} completed',
  'performance.details.year.others.metric': 'This year {{metric}} count',
  'performance.details.metric.submitted': '{{metric}} submitted',
  'performance.details.metric.completed': '{{metric}} completed',
  'performance.details.total.submitted': 'Total Submitted',
  'performance.details.total.completed': 'Total Completed',
  'performance.details.tabLabel.salesApe': 'Sales APE',
  'performance.details.tabLabel.caseOthers': 'Case & others',
  'performance.ranking.chart.directTeam.label': 'Ranking among Direct Team',
  'performance.ranking.chart.wholeRegion.label':
    'Ranking among Philippines FWD',
  // Recognition
  'performance.recognition.overAllAchievement': 'Overall Achievement',
  'performance.recognition.requirementsWithMetric': 'MDRT 2024 Requirement',
  'performance.recognition.currentTier': 'Current Tier',
  'performance.recognition.achievedTiers': 'Achieved tiers',
  'performance.recognition.complete': 'Complete',
  'performance.recognition.inprogress': 'In progress',
  'performance.recognition.mdrtComplete': 'completed',
  'performance.recognition.target': 'Target',
  'performance.recognition.completion': 'Completion',
  'performance.recognition.shortfall': 'Shortfall',
  'performance.recognition.Incomplete': 'Incomplete',
  'performance.recognition.congratulation': ' Congratulation',
  'top.congratulation': 'Congratulations!',
  'performance.recognition.yourNextTierWillBe': ', your next tier will be:',
  firstYearPremium: 'First year premium',
  firstYearCommissions: 'First year commissions',
  'performance.recognition.achieved': 'Achieved',
  'performance.recognition.NoAchievement': 'None',
  'performance.recognition.FWDElite': 'FWD Elite',
  'performance.recognition.MDRT': 'MDRT',
  'performance.recognition.COT': 'COT',
  'performance.recognition.TOT': 'TOT',
  'current.rank.by': 'Current rank by FYP',
  'over.active.agents': ' /{{totalActiveAgents}} active agents',
  'recognition.title': 'MDRT tracking',
  'performance.recognition.overAllAchievementWithYear':
    'Overall Achievements in {{year}}',
  'performance.recognition.achievedTiersWithYeah':
    'Achieved tiers for {{year}}',

  //Target Setting
  'target.title.monthly': 'Monthly target',
  'target.title.yearly': 'Yearly target',
  'performance.editTarget': 'Edit Target',
  'performance.editTarget.reset': 'Reset',
  'performance.editTarget.save': 'Save',
  'performance.editTarget.screenTitle': 'Edit targets',
  'performance.editTarget.success.message':
    'Targets have been saved successfully',
  'performance.editTarget.exitModal.title': 'Exit edit target',
  'performance.editTarget.exitModal.content':
    'Do you want to save before exiting the edit targets?',
  'performance.editTarget.exitModal.save': 'Save',
  'performance.editTarget.exitModal.notSave': "Don't save",
  editTargetOne: 'Target APE',
  editTargetTwo: 'Target cases',
  // Others
  'performance.done': 'Done',
  overallPerformance: 'Overall performance',
  monthly: '(Monthly)',
  yearly: '(Yearly)',
  viewPerformanceDetails: 'View performance details',
  currentMonthCase: 'Current month case count',
  totalCaseSubmitted: 'Total Case Submitted',
  totalCaseIssued: 'Total Case Issued',
  currentMonthACE: 'Current month Annual Contribution Equivalent ',
  currentMonthAPE: 'This month Annual Premium Equivalent (APE)',
  currentMonthFYP: 'Current month FYP ',
  currentMonthAFYP: 'Current month AFYP ',
  currentMonthFYC: 'Current month FYC ',
  totalACESub: 'Total ACE Submitted',
  target: 'Target',
  shortfall: 'Shortfall',
  totalACEIssued: 'Total ACE Issued',
  thisYearACE: 'This year Annual Contribution Equivalent',
  thisYearAPE: 'This year Annual Premium Equivalent (APE)',
  thisYearFYP: 'This year Annual Premium Equivalent (APE)',
  thisYearFYC: 'This year Annual Premium Equivalent (FYC)',
  thisYearCase: 'This year Annual Premium Equivalent (APE)',
  numberOfCaseYear: 'Number of Case in This Year',
  caseSubmitted: 'Case Submitted',
  caseIssued: 'Case Issued',
  caseCompleted: 'Case Completed',
  aceSubmitted: 'ACE Submitted',
  aceIssued: 'ACE Issued',
  persistency: 'Persistency',
  //tip
  'performance.button.viewProposal': 'View proposals',
  RM: 'RM',
  yourRanking: 'Your ranking',
  campaignsAndIncentives: 'Campaigns and incentives',
};
