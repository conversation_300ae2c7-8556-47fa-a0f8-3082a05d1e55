import { RouteProp, useRoute } from '@react-navigation/native';
import { ageLimit } from 'constants/dateOfBirth';
import { MY_COUNTRY } from 'constants/optionList';
import { EntityFormValues } from 'features/coverageDetails/validation/common/entitySchema';
import { InsuredFormValues } from 'features/coverageDetails/validation/common/insuredSchema';
import {
  IbOwnerFormValues,
  OwnerFormValues,
} from 'features/coverageDetails/validation/common/ownerSchema';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import {
  titleFemales,
  titleMales,
} from 'features/lead/validation/insuredSchema';
import { RelationshipValue } from 'features/proposal/types';
import useBoundStore from 'hooks/useBoundStore';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCase } from 'hooks/useGetCase';
import {
  useGetOptionList,
  useGetOptionListManually,
} from 'hooks/useGetOptionList';
import { useGetUnionPolicyList } from 'hooks/useGetUnionPolicyList';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Control,
  DefaultValues,
  UseFormSetValue,
  UseFormTrigger,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { BuildCountry, RootStackParamList } from 'types';
import { MYOptionList, Nationality, OptionList } from 'types/optionList';
import { Party, PartyRole } from 'types/party';
import { Gender, SmokingHabit } from 'types/person';
import { country } from 'utils/context';
import { countryModuleSiConfig } from 'utils/config/module';
import { calculateAge } from 'utils/helper/calculateAge';
import { capitalFirst } from 'utils/helper/formatUtil';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';

type SchemaType = OwnerFormValues | InsuredFormValues | EntityFormValues;

export const useIdsFromRoute = () =>
  useRoute<RouteProp<RootStackParamList, 'CoverageDetailsScreen'>>().params ??
  {};

export const useDefaultValuesFromOptionList = () => {
  const coverageConfig = countryModuleSiConfig.coverageDetails;

  const { mutateAsync: fetchOptionList } = useGetOptionListManually();

  return useCallback(
    async (
      fallbackValues: DefaultValues<SchemaType>,
    ): Promise<DefaultValues<SchemaType>> => {
      if (!coverageConfig.nationality.visible) {
        return fallbackValues;
      }

      try {
        let { nationality, residence, residencyType, code } =
          fallbackValues as DefaultValues<IbOwnerFormValues>;

        const optionList = await fetchOptionList();
        const nationalityOptions =
          optionList?.[
            (coverageConfig.nationality?.optionListField as keyof OptionList) ||
              'NATIONALITY'
          ]?.options ?? [];
        const countryOptions = optionList?.COUNTRY?.options ?? [];
        const countryCodeOptions = optionList?.COUNTRY_CODE?.options ?? [];

        if (
          nationalityOptions &&
          nationalityOptions.length > 0 &&
          coverageConfig.nationality.visible
        ) {
          nationality = nationalityOptions[0].value.toString();
        }

        if (
          countryOptions &&
          countryOptions.length > 0 &&
          coverageConfig.residence.visible
        ) {
          residence = countryOptions[0].value;
        }

        if (
          nationality === MY_COUNTRY &&
          coverageConfig.residencyType.visible
        ) {
          residencyType = 'N';
        }

        if (countryCodeOptions && countryCodeOptions.length > 0) {
          code = countryCodeOptions[0].value;
        }

        return {
          ...fallbackValues,
          nationality,
          residence,
          residencyType,
          code,
        };
      } catch (e) {
        console.error('cannot get option list', e);
        return fallbackValues;
      }
    },
    [
      coverageConfig.nationality?.optionListField,
      coverageConfig.nationality.visible,
      coverageConfig.residence.visible,
      coverageConfig.residencyType.visible,
      fetchOptionList,
    ],
  );
};

const useCoverageForm = <Country extends BuildCountry = BuildCountry>({
  ownerGender,
  setValue,
  control,
  trigger,
  isExtraInsured,
  isEntity,
}: {
  ownerGender?: Gender;
  setValue:
    | UseFormSetValue<OwnerFormValues<Country>>
    | UseFormSetValue<InsuredFormValues<Country>>
    | UseFormSetValue<EntityFormValues<Country>>;
  control:
    | Control<OwnerFormValues<Country>>
    | Control<InsuredFormValues<Country>>
    | Control<EntityFormValues<Country>>;
  trigger?:
    | UseFormTrigger<OwnerFormValues<Country>>
    | UseFormTrigger<InsuredFormValues<Country>>
    | UseFormTrigger<EntityFormValues<Country>>;
  isExtraInsured?: boolean;
  isEntity?: boolean;
}) => {
  const [
    dob,
    nationality,
    residence,
    relationship,
    occupationClass,
    title,
    occupation,
    smokingHabit,
  ] = useWatch({
    name: [
      'dob',
      'nationality',
      'residence',
      'relationship',
      'occupationClass',
      'title',
      'occupation',
      'smokingHabit',
    ],
    control: control as unknown as Control<SchemaType>,
  });

  const idNumber = useFnaStore(state => state.lifeJourney.idNumber);

  const primaryPolicyNumber = useFnaStore(
    state => state.familySharePlanPolicyNumber,
  );

  const caseStore = useBoundStore(state => state.case);
  const { data: activeCase } = useGetCase(caseStore?.caseId ?? '');

  const ownerParty: Party | undefined = Array.isArray(activeCase?.parties)
    ? activeCase?.parties?.find(p => p?.roles.includes(PartyRole.PROPOSER))
    : undefined;

  const policyListParams = useMemo(() => {
    if (ownerParty) {
      return {
        firstName: ownerParty?.person?.name?.firstName ?? '',
        lastName: ownerParty?.person?.name?.lastName,
        gender: ownerParty?.person?.gender,
        dob: ownerParty?.person?.dateOfBirth?.date,
        idNumber: idNumber ?? '',
      };
    }
  }, [ownerParty, idNumber]);

  const { data: policies } = useGetUnionPolicyList(policyListParams);

  const primaryPolicy = policies?.find(
    p => p.policyNumber === primaryPolicyNumber,
  );

  const { t } = useTranslation(['common', 'proposal', 'coverageDetails']);

  const coverageConfig = countryModuleSiConfig.coverageDetails;

  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();

  const { data: agentInfo } = useGetAgentProfile();

  const agentChannel = agentInfo?.channel;

  const age = useMemo(() => {
    if (dob) {
      return calculateAge(dob);
    }
  }, [dob]);

  const genderStatusList = useMemo(() => {
    return (
      optionList?.GENDER?.options?.map(option => ({
        value: option.value,
        text: option.label,
      })) || []
    );
  }, [optionList?.GENDER?.options]);

  const residencyTypeOptions = useMemo(() => {
    return (
      optionList?.MY_PR?.options.map(option => ({
        value: option.value,
        text: option.label,
      })) || []
    );
  }, [optionList?.MY_PR?.options]);

  const isResidencyTypeShow = useMemo(() => {
    return (
      nationality &&
      nationality !== 'MYS' &&
      coverageConfig.residencyType.visible
    );
  }, [coverageConfig.residencyType.visible, nationality]);

  useEffect(() => {
    if (nationality === 'MYS' && coverageConfig.residencyType.visible) {
      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'residencyType',
        'N',
      );
    }
  }, [coverageConfig.residencyType, nationality, setValue]);

  const smokingStatusList = useMemo(() => {
    return (
      optionList?.SMK_STATUS?.options?.map(option => ({
        value: option.value,
        text: option.label,
      })) || []
    );
  }, [optionList?.SMK_STATUS]);

  const relationshipOptions = useMemo(
    () =>
      (optionList as OptionList<string, 'ib'>)?.RELATIONSHIP?.options
        .filter(option => {
          const partyCondition = isEntity
            ? option?.party?.entity === 'True'
            : option?.party?.individual === 'True';
          const roleCondition = option?.role?.insured === 'True';

          const groupCondition = isEntity
            ? option?.group === RelationshipValue.EMPLOYEE
            : option?.group !== RelationshipValue.EMPLOYEE;

          const valueCondition = isEntity
            ? option.value === RelationshipValue.EMPLOYEE
            : option.value !== RelationshipValue.OWNER;

          const baseCondition =
            partyCondition && roleCondition && groupCondition && valueCondition;

          if (option?.group === RelationshipValue.SPOUSE) {
            const genderString = ownerGender === 'F' ? 'male' : 'female';
            return baseCondition && option?.gender[genderString] === 'True';
          }

          if (option.channel) {
            return (
              baseCondition && option.channel[agentChannel ?? ''] === 'True'
            );
          }

          return baseCondition;
        })
        .filter(option => {
          // TODO: this is a temporary solution to disable grandchild relationship for IB
          // https://fwdnextgen.atlassian.net/browse/CUBEFIB-7243
          if (country === 'ib') {
            return option.value !== 'GRANDCHILD';
          }
          return true;
        })
        .filter(option => {
          /* if family sharing plan, filter the relationship based the number of quota*/
          if (primaryPolicy) {
            const maxSpouseQuota = primaryPolicy?.quota?.spouseMax;
            const maxChildQuota = primaryPolicy?.quota?.childMax;
            const spouseQuota = primaryPolicy?.quota?.spouse;
            const childQuota = primaryPolicy?.quota?.child;

            if (
              maxSpouseQuota === spouseQuota &&
              option?.group === RelationshipValue.SPOUSE
            ) {
              return false;
            }

            if (
              maxChildQuota === childQuota &&
              option?.group === RelationshipValue.CHILD
            ) {
              return false;
            }

            return true;
          }

          return true;
        })
        .map(option => ({ ...option, label: capitalFirst(option.label) })),
    [isEntity, optionList, ownerGender, primaryPolicy, agentChannel],
  );

  const religionOptions = useMemo(
    () => optionList?.RELIGION?.options ?? [],
    [optionList?.RELIGION?.options],
  );
  const nationalityOptions = useMemo(
    () =>
      optionList?.[
        (coverageConfig.nationality?.optionListField as keyof OptionList) ||
          'NATIONALITY'
      ]?.options ?? [],
    [coverageConfig.nationality?.optionListField, optionList],
  ) as Nationality[];
  const countryOptions = useMemo(
    () => optionList?.COUNTRY?.options ?? [],
    [optionList?.COUNTRY?.options],
  );
  const occupationOptions = useMemo(
    () => (optionList as OptionList<string, 'ib'>)?.OCCUPATION?.options ?? [],
    [optionList],
  );
  const countryCodeOptions = useMemo(
    () => optionList?.COUNTRY_CODE?.options ?? [],
    [optionList?.COUNTRY_CODE?.options],
  );

  const isSpouse =
    (optionList as OptionList<string, 'ib'>)?.RELATIONSHIP?.options.find(
      item => item.value === relationship,
    )?.group === RelationshipValue.SPOUSE;

  /* max age of child and student defined by each country */
  const maxChildOccupationAge = agentChannel
    ? ageLimit[agentChannel]?.CHILD_OCCUPATION?.max?.value
    : undefined;
  const maxStudentOccupationAge = agentChannel
    ? ageLimit[agentChannel]?.STUDENT?.max?.value
    : undefined;

  const isDisableSmokingHabit = useMemo(
    () =>
      !!(
        age &&
        maxChildOccupationAge &&
        age < maxChildOccupationAge &&
        !!smokingHabit &&
        country !== 'ib'
      ),
    [age, maxChildOccupationAge, smokingHabit],
  );

  const isDisableOccupation = useMemo(() => {
    // dont disable the occupation if insured is spouse and it's additional insured
    if (isExtraInsured && isSpouse) {
      return false;
    }

    // only disable the occupation if the age of insured as child is valid
    return !!(
      age &&
      maxChildOccupationAge &&
      age < maxChildOccupationAge &&
      coverageConfig.occupation.disabled
    );
  }, [
    age,
    maxChildOccupationAge,
    coverageConfig.occupation.disabled,
    isExtraInsured,
    isSpouse,
  ]);

  const selectedRelationship = useMemo(() => {
    return relationshipOptions?.find(p => p.value === relationship);
  }, [relationship, relationshipOptions]);

  const [isGenderDisable, setGenderDisable] = useState<boolean>(false);

  useEffect(() => {
    if (!title) return;

    let gender: Gender | undefined = undefined;

    if (titleMales.includes(title)) gender = Gender.MALE;
    if (titleFemales.includes(title)) gender = Gender.FEMALE;

    if (gender) {
      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'gender',
        gender,
      );
      setGenderDisable(true);
      (trigger as unknown as UseFormTrigger<OwnerFormValues>)?.([
        'gender',
        'title',
      ]);
    } else {
      setGenderDisable(false);
      (trigger as unknown as UseFormTrigger<OwnerFormValues>)?.('title');
    }
  }, [setValue, title, trigger]);

  const isGenderDisabled =
    selectedRelationship?.gender?.female !==
      selectedRelationship?.gender?.male || isGenderDisable;

  const handleOccupationChange = useCallback(
    (value?: string | null) => {
      const selectedOccupation = occupationOptions.find(o => o.value === value);

      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'occupationClass',
        selectedOccupation?.occupationClass?.value ?? '',
      );
      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'occupationGroupCode',
        selectedOccupation?.occupationGroupCode ?? '',
      );
    },
    [occupationOptions, setValue],
  );

  useEffect(() => {
    if (relationship && relationshipOptions) {
      const selectedRelationship = relationshipOptions.find(
        p => p.value === relationship,
      );
      if (selectedRelationship?.otherValue?.nano === 'CHILD') {
        const nonSmoker = optionList?.SMK_STATUS?.options?.find(
          st => st.value === SmokingHabit.NONSMOKER,
        );
        const childOccupation = occupationOptions.find(
          o => o.label === 'Child',
        );
        const studentOccupation = occupationOptions.find(
          o => o.value === 'Student',
        );

        // set the occupation as Child only when the age of the insured is valid
        if (
          childOccupation &&
          (!age ||
            (age && maxChildOccupationAge && age < maxChildOccupationAge))
        ) {
          (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
            'occupation',
            childOccupation.value,
            {
              shouldValidate: true,
            },
          );
          handleOccupationChange(childOccupation.value);
        } else if (
          studentOccupation &&
          (!age ||
            (age && maxStudentOccupationAge && age <= maxStudentOccupationAge))
        ) {
          // else set the occupation as Student only when the age of the insured is valid
          (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
            'occupation',
            studentOccupation.value,
          );
          handleOccupationChange(studentOccupation.value);
        }

        // if insured has smoking habit is set on modal or child is older than 17, skip auto toggle it
        const skipAutoToggleSmokingHabit =
          (isExtraInsured && !!smokingHabit) ||
          (maxChildOccupationAge && age && age >= maxChildOccupationAge) ||
          country === 'ib'; // skip default Non-Smoking with LA is below 16 in IB

        if (nonSmoker && !skipAutoToggleSmokingHabit) {
          (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
            'smokingHabit',
            nonSmoker.value,
            { shouldValidate: true },
          );
        }
      }

      if (selectedRelationship?.gender) {
        (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
          'nanoRelationship',
          selectedRelationship?.otherValue?.nano,
        );

        if (
          selectedRelationship?.gender?.female === 'True' &&
          selectedRelationship?.gender?.male === 'False'
        ) {
          (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
            'gender',
            Gender.FEMALE,
          );
        }

        if (
          selectedRelationship?.gender?.male === 'True' &&
          selectedRelationship?.gender?.female === 'False'
        ) {
          (setValue as unknown as UseFormSetValue<InsuredFormValues>)(
            'gender',
            Gender.MALE,
          );
        }
      }
    }
  }, [
    relationship,
    relationshipOptions,
    occupationOptions,
    setValue,
    optionList?.SMK_STATUS?.options,
    handleOccupationChange,
    isExtraInsured,
    smokingHabit,
    age,
    maxChildOccupationAge,
  ]);

  useEffect(() => {
    if (!coverageConfig.nationality.visible) {
      return;
    }

    const nationalityValue = nationality;
    if (
      !nationalityValue &&
      nationalityOptions &&
      nationalityOptions.length > 0
    ) {
      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'nationality',
        nationalityOptions[0].value.toString(),
      );
    }

    const residenceValue = residence;
    if (
      !residenceValue &&
      countryOptions &&
      countryOptions.length > 0 &&
      coverageConfig.residence.visible
    ) {
      (setValue as unknown as UseFormSetValue<OwnerFormValues>)(
        'residence',
        countryOptions[0].value,
      );
    }
  }, [
    nationality,
    residence,
    setValue,
    nationalityOptions,
    countryOptions,
    coverageConfig.nationality.visible,
    coverageConfig.residence.visible,
  ]);

  useEffect(() => {
    if (!occupation) {
      return;
    }

    // manually trigger the occupation validation when the relationship or age of insured is updated
    if (relationship || age) {
      (trigger as unknown as UseFormTrigger<InsuredFormValues>)?.('occupation');
    }
  }, [age, occupation, relationship, trigger]);

  const titleOptions = useMemo(
    () => optionList?.TITLE?.options ?? [],
    [optionList?.TITLE?.options],
  );

  const extensionsOptions = useMemo(
    () => optionList?.EXTENSION?.options ?? [],
    [optionList?.EXTENSION?.options],
  );

  return {
    isFetchingOptionList,
    optionList,
    age,
    gender: {
      disabled: isGenderDisabled,
      options: genderStatusList,
    },
    smokingHabit: {
      disabled: isDisableSmokingHabit,
      options: smokingStatusList,
    },
    occupation: {
      disabled: isDisableOccupation,
      options: occupationOptions,
      occupationClass: occupationClass
        ? `${t('coverageDetails:class')} ${occupationClass}`
        : '',
      onChange: handleOccupationChange,
    },
    religion: {
      options: religionOptions,
    },
    nationality: {
      options: nationalityOptions,
    },
    country: {
      options: countryOptions,
    },
    countryCode: {
      options: countryCodeOptions,
      disabled: coverageConfig.phoneCode.disabled,
    },
    relationship: {
      options: relationshipOptions,
      disabled: isEntity,
    },
    title: {
      options: titleOptions,
    },
    extension: {
      options: extensionsOptions,
    },
    residencyType: {
      options: residencyTypeOptions,
      visible: isResidencyTypeShow,
    },
    isSubFamilySharingPlan: !!primaryPolicyNumber,
  };
};

export default useCoverageForm;
