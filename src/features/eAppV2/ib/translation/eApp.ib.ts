import eAppCommonIb from '../../common/translation/eApp.ib';

export default {
  ...eAppCommonIb,
  more: ' ...more',
  done: 'Done',
  'bar.appDetail': 'Your details',
  'bar.policyOwner': 'Policy owner',
  'bar.policyOwnerDetails': 'Policy owner details',
  'bar.company': 'Company info',
  'bar.companyDetails': 'Company details',
  'bar.representative': 'Authorized signatory info',
  'bar.representativeDetails': 'Authorized signatory details',
  'bar.insured': 'Life assured',
  'bar.insuredDetails': 'Life assured details',
  'bar.otherStakeholder': 'Other(s)',
  'bar.payor': 'Payor',
  'bar.beneficiary': 'Nominee',
  'bar.trustee': 'Trustee',
  'bar.witness': 'Witness',
  'bar.parent': 'Parent/Legal Guardian',
  'bar.fatca': 'FATCA / CRS',
  'bar.rop': 'Replacement of Policy (ROP)',
  'bar.takeOver': 'Take over',
  'bar.healthQuestion': 'Health questions',
  'bar.consents': 'Consents',
  'bar.declaration': 'Declaration',
  'bar.pdp': 'PDP',
  'bar.pre-contractualDutyOfDisclosure': 'Pre-Contractual Duty of Disclosure',
  'bar.theProvisionalAccidentalDeathInsuranceCoverage':
    'The Provisional Accidental Death Insurance Coverage',
  'bar.authorizationToChargePremium': 'Authorization to charge premium',
  'bar.ePolicyAndENotices': 'ePolicy & eNotices',
  'bar.payoutAccount': 'Payout account',
  'bar.documentUpload': 'Upload documents',
  'bar.reviewSignature': 'Review & signature',
  'bar.review': 'Review',
  'bar.payment': 'Payment',
  'declaration.fatca': 'FATCA / CRS',
  'declaration.fatca.title':
    'Foreign Account Tax Compliance Act (FATCA) & Common Reporting Standard (CRS)',
  'declaration.fatca.message':
    "In relation to Foreign Account Tax Compliance Act (FATCA) and/or Common Reporting Standard (CRS)'s requirement, FWD Insurance require you to answer question as follow:",
  'declaration.fatca.hint':
    '*According to the record, your nationality is not Malaysian.',
  'declaration.fatca.question.1':
    'Do you have and/or act for yourself and/or others who have tax residency from other countries outside Malaysia?',
  'declaration.fatca.question.2':
    'Are you United States citizen/United States resident for US federal tax purposes/ United States Green Card holder?',
  'declaration.fatca.taxResidency': 'Tax residency ({{index}})',
  'declaration.fatca.taxResidency.country':
    'Country/Jurisdiction of tax residence',
  'declaration.fatca.taxResidency.tin.question':
    'Do you have tax identification number (TIN)?',
  'declaration.fatca.taxResidency.tin.number':
    'Tax identification number (TIN)',
  'declaration.fatca.taxResidency.tin.add': 'Add tax residency',
  'declaration.fatca.taxResidency.noTin.title':
    'If no TIN available, please provide reason',
  'declaration.fatca.taxResidency.noTin.additionalInfomation':
    'Additional information for reason',
  'declaration.fatca.taxResidency.noTin.1':
    'The country/jurisdiction where the Account Holder is resident does not issue TiNs to its residents',
  'declaration.fatca.taxResidency.noTin.2':
    'The Account Holder is unable to obtain a TIN or equivalent number (Please explain why you are unable to obtain a TIN in the above table if you have selected this reason)',
  'declaration.fatca.taxResidency.noTin.3':
    'No TIN is required (Note: only select this reason if the domestic law of the relevant jurisdiction does not require the collection of the TIN issued by such jurisdiction)',
  'declaration.fatca.consent.title': 'Declaration',
  'declaration.fatca.consent.1':
    'I, as the prospective Policy Owner, hereby declare:',
  'declaration.fatca.consent.2':
    'The Compliance Statement relates to tax residents outside Malaysia',
  'declaration.fatca.consent.2.a':
    'All the information that I give to FWD Insurance Berhad as stated in this declaration form is correct and complete, and shall supersede any previous information given in relation to the Foreign Account Tax Compliance Act (FATCA) and/or Common Reporting Standard (CRS). I shall be fully responsible for all the consequences that may occur if there is any wrong information. I agree to disclose, inform or provide FWD Insurance Berhad with my personal information or data within 30 calendar days since the proposal / changes that relate to my personal information and status, or I am being taxed in more than one country at a certain time as disclosure to any authorities. The data update will be my responsibility and it will not be FWD Insurance Berhad ’s responsibility.',
  'declaration.fatca.consent.2.b':
    'I understand that it is mandatory for FWD Insurance Berhad  and/or affiliation to comply with any regulation, guideline, instruction and requirement that have been stipulated in the local regulations applicable in Malaysia or foreign law including the Foreign Account Tax Compliance Act of the United States of America (FATCA), or any public agreement, judicative, taxation, government and/or other authority such as the Inland Revenue Board of Malaysia and Internal Revenue Service (IRS) that are pertinent in several jurisdictions including Malaysia that have been announced together with changes from time to time (Law Obligation).',
  'declaration.fatca.consent.2.c':
    'I agree to provide necessary assistance to FWD Insurance Berhad to comply with all regulations as stipulated in the mandated law on my Policies. If the provision of data is more than 30 days since the transaction, I shall bear all the risks including financial loss that is incurred due to transaction rejection and/or freezing of the transaction.',
  'declaration.fatca.consent.2.d':
    'I shall give authority to FWD Insurance Berhad to report tax information and/or data that I own to related authorities.',
  'declaration.rop': 'Replacement of Policy (ROP)',
  'declaration.rop.title': 'Replacement of Policy (ROP)',
  'declaration.rop.question.number.1': '1. ',
  'declaration.rop.question.number.2': '2. ',
  'declaration.rop.question.number.2a': '2a. ',
  'declaration.rop.question.number.3': '3. ',
  'declaration.rop.question.1':
    'Do you intend to surrender or terminate any of your existing life insurance policies with the application of this new policy?',
  'declaration.rop.question.2':
    'Is there any party who has influenced you to surrender or terminate any of your existing policies?',
  'declaration.rop.question.2a':
    'Are you fully satisfied with the explanation given to you?',
  'declaration.rop.replacementReason': 'Replacement reason',
  'declaration.rop.insuranceOperator': 'Insurance operator',
  'declaration.rop.planName': 'Plan name',
  'declaration.rop.sumAssured': 'Sum assured (RM)',
  'declaration.rop.comment': 'Comment (optional)',
  'declaration.note': 'NOTE: ',
  'declaration.rop.note':
    'It may not be advantageous to replace an existing life insurance policy with a new one. If you intend to do so, we recommend that you consult your present insurer before making a final decision.',
  'declaration.takeOver': 'Take Over',
  'declaration.takeOver.note':
    'Take Over decision shall subject to the final underwriting decision.',
  'declaration.takeOver.1': '1. ',
  'declaration.takeOver.question.1':
    'Do you intend to replace (take over) any of your existing medical/hospital riders?',
  'declaration.takeOver.policyNumber': 'Policy number',
  'declaration.takeOver.planName': 'Plan name',
  'applicationDetails.declaration': 'DECLARATION: ',
  'applicationDetails.payorInformation': "Payor's information",
  'applicationDetails.personalDetails': 'Personal details',
  'applicationDetails.nationalityDetails': 'Nationality details',
  'applicationDetails.occupationDetails': 'Occupation details',
  'applicationDetails.contactDetails': 'Contact details',
  'applicationDetails.addressInformation': 'Address information',
  'applicationDetails.companyDetails': 'Company details',
  'applicationDetails.companyDetails.companyName': 'Company name',
  'applicationDetails.companyDetails.registrationNumber':
    'Registration number (Latest)',
  'applicationDetails.companyDetails.registrationDate': 'Registration date',
  'applicationDetails.companyDetails.oldRegistrationNumber':
    'Old registration number',
  'applicationDetails.companyDetails.natureOfBusiness': 'Nature of business',
  'applicationDetails.companyDetails.email': 'Email',
  'applicationDetails.companyDetails.businessPhoneNumber':
    'Business phone number',
  'applicationDetails.companyDetails.preferredLanguage':
    'Preferred document language',
  'applicationDetails.businessRegistration': 'Business registration',
  'applicationDetails.taxDetails': 'Tax details',
  'applicationDetails.sst': 'Sales and service tax act 2018 (SST)',
  'applicationDetails.sst.registered': 'Registered',
  'applicationDetails.sst.nonRegistered': 'Non-Registered',
  'applicationDetails.sst.number': 'Sales and service tax registration number',
  'applicationDetails.sst.taxId': 'Malaysia Tax Identification Number (TIN)',
  fullName: 'Full name',
  gender: 'Gender',
  dateOfBirth: 'Date of birth',
  smokingHabit: 'Smoking Habit',
  smoker: 'Smoker',
  nonSmoker: 'Non-Smoker',
  sourceLead: 'Lead referred by',
  identity: 'Identity',
  title: 'Title',
  idType: 'ID Type',
  idNumber: 'Identification number',
  religion: 'Religion',
  purposeOfTransaction: 'Purpose of transaction',
  race: 'Race',
  maritalStatus: 'Marital status',
  campaignCode: 'Campaign code (optional)',
  additionalIdType: 'Additional ID type (optional)',
  additionalIdNumber: 'Additional identification number (optional)',
  'additionalIdNumber.required': 'Additional identification number',
  nationality: 'Nationality',
  residencyType: 'Residency type',
  countryOfBirth: 'Place of birth - Country',
  stateOfBirth: 'Place of birth - State',
  cityOfBirth: 'Place of birth - City',
  cityName: 'City name',
  occupation: 'Occupation',
  occupationClass: 'Occupation class',
  occupationDescription: 'Occupation description',
  nameOfBusiness: 'Name of business/ employer',
  natureOfWork: 'Nature of work/ business',
  exactDuties: 'Exact duties',
  'exactDuties.hint': 'E.g. description your main job responsibility',
  annualIncome: 'Annual income',
  annualIncomeAmount: 'Annual income amount (RM)',
  countryCode: 'Code',
  mobileNumber: 'Mobile number',
  homeNumber: 'Home phone number (optional)',
  officeNumber: 'Office phone number (optional)',
  email: 'Email',
  preferredCopy: 'Preferred copy of your policy',
  preferredContact: 'Preferred contact mode',
  preferredLanguage: 'Preferred document language',
  correspondenceAddress: 'Correspondence address',
  addressLine1: 'Address line 1',
  'addressLine1.hint':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  addressLine2: 'Address line 2 (optional)',
  'addressLine2.hint': 'Street no./Street Name',
  addressLine3: 'Address line 3 (optional)',
  country: 'Country',
  postcode: 'Postcode',
  city: 'City',
  state: 'State',
  residentialAddress: 'Residential address',
  businessAddress: 'Business address',
  'residentialAddress.correspondence': 'Same as correspondence address',
  relationshipWithOwner: 'Relationship with Policy Owner',
  next: 'Next',
  policyOwnerInfo: "Policy owner's information",
  'policyOwnerInfo.declaration':
    '**DECLARATION**: By providing the above information, I consent to FWD Insurance Berhad contacting me via any of the above email addresses, phone numbers and/or alternate contacts for matters related to this proposal for customer care programmes and other matters to serve my interests.',
  insuredInfo: "Life assured's information",
  'insuredInfo.declaration':
    '**DECLARATION**: By providing the above information, I consent to FWD Insurance Berhad contacting me via any of the above email addresses, phone numbers and/or alternate contacts for matters related to this proposal for customer care programmes and other matters to serve my interests.',

  'representativeInfo.authorizedSignatory': 'Authorized signatory',
  'representativeInfo.lifeAssured': 'Life assured info',
  'representativeInfo.authorizedSignatoryInfo': 'Authorized signatory info',
  'representativeInfo.declaration':
    '**DECLARATION**: By providing the above information, I consent to FWD Insurance Berhad contacting me via any of the above email addresses, phone numbers and/or alternate contacts for matters related to this proposal for customer care programmes and other matters to serve my interests.',
  'representativeDetails.personalDetails': 'Personal details',
  'representativeDetails.designation': 'Designation',
  'representativeDetails.age': 'Age',
  'representativeDetails.authorizedSignatoryDetails':
    'Authorized signatory’s details',

  electronicCopy: 'Electronic Copy',

  'certificate.title': 'Policy owner info',
  'certificate.subtitle1': 'Importance of truthful disclosure: ',
  'certificate.subtitle2':
    'It’s important that you provide truthful answers in the Application Form. Concealment of any facts may cause FWD to deny a claim on this policy.',
  'certificate.participantTitle': 'Personal details',
  'certificate.menu.certificateOwner': 'Policy owner',
  'certificate.menu.personCovered': 'Person covered',
  'certificate.menu.andreaGarcia': 'Andrea Garcia',
  'certificate.title.correspondenceAddress': 'Correspondence address',
  'certificate.title.residentialAddress': 'Residential address',
  'certificate.title.businessAddress': 'Business address',
  'certificate.button.addChild': 'Add child / dependents',
  'certificate.form.title': 'Title',
  'certificate.form.maritalStatus': 'Marital status',
  'certificate.form.dateOfBirth': 'Date of birth',
  dateFormat: 'DD/MM/YYYY',
  'certificate.form.age': 'Age',
  'certificate.form.primaryIdType': 'ID Type',
  'certificate.form.additionalIDType': 'Additional ID Type (optional)',
  'certificate.form.additionalIdType': 'Additional ID Type',
  'certificate.form.additionalIDType.required': 'Additional ID Type',
  'certificate.form.source': 'Lead Source',
  'certificate.form.fullName': 'Full name',
  'certificate.form.ethnicity': 'Ethnicity',
  'certificate.form.race': 'Race',
  'certificate.form.religion': 'Religion',
  'certificate.form.identificationNumber': 'Identification number',
  nricHint: 'YYMMDD-PB-###G',
  nricPlaceholder: 'YYMMDD - PB - 000G',
  'certificate.error.cannotBeSame.lifeAssured':
    'Nominee cannot be the same as Life Assured',
  'certificate.error.cannotBeSame.witness':
    'Nominee cannot be the same as Witness',
  'certificate.form.additionalIdentification':
    'Additional identification number (optional)',
  'certificate.form.additionalIdentification.required':
    'Additional identification number',
  'certificate.form.additional.identification':
    'Additional identification number',
  'certificate.nationalityTitle': 'Nationality details',
  'certificate.form.nationality': 'Nationality',
  'certificate.form.countryOfBirth': 'Place of birth - Country',
  'certificate.form.stateOfBirth': 'Place of birth - State',
  'certificate.form.cityOfBirth': 'Place of birth - City',
  'certificate.form.cityName': 'City name',
  'certificate.occupationTitle': 'Occupation details',
  'certificate.form.occupation': 'Occupation',
  'certificate.form.nameOfBusiness': 'Name of business/Employer',
  'certificate.form.exactDuties': 'Exact duties',
  'certificate.form.annualIncomeAmount': 'Annual income amount (RM)',
  'certificate.form.natureOfWork': 'Nature of work/Business',
  'certificate.form.annualIncome': 'Annual income',
  'certificate.form.annualIncomeOptional': 'Annual income (optional)',
  'certificate.contactTitle': 'Contact details',
  'certificate.form.email': 'Email',
  'certificate.form.countryCode': 'Code',
  'certificate.form.mobileNumber': 'Mobile number',
  'certificate.form.homePhone': 'Home phone number (optional)',
  'certificate.form.mobilePhone': 'Mobile phone no.',
  'certificate.form.homeNumber': 'Home phone no.',
  'certificate.form.officeNumber': 'Office phone no.',
  'certificate.form.businessNumber': 'Business phone no.',
  'certificate.form.faxNumber': 'Fax number (optional)',
  'certificate.form.officePhone': 'Office phone number (optional)',
  'certificate.addressInfoTitle': 'Address Information',
  'certificate.form.addressLine1': 'Address line 1',
  'certificate.form.SubAddressLine1':
    'House no./Unit no./Lot no., Block no., Residence/Building',
  'certificate.form.addressLine2': 'Address line 2 (optional)',
  'certificate.form.SubAddressLine2': 'Street no./Street Name',
  'certificate.form.addressLine3': 'Address line 3 (optional)',
  'certificate.form.postCode': 'Postcode',
  'certificate.form.city': 'City',
  'certificate.form.state': 'State',
  'certificate.form.country': 'Country of Residence',
  'certificate.form.correspondenceAddress': 'Correspondence Address',
  'certificate.form.residentialAddress': 'Residential address',
  'certificate.form.businessAddress': 'Business address',
  'certificate.form.nameOfChild': 'Name of child / dependent',
  'certificate.form.relationship': 'Relationship',
  'certificate.form.relationshipWithCertificate':
    'Relationship with policy owner',
  'certificate.form.yearToSupport': 'Year to support',
  'certificate.form.occupationChild': 'Occupation',
  'certificate.form.preferredCertificate': 'Preferred copy of your certificate',
  'certificate.form.preferredContact': 'Preferred contact mode',
  'certificate.form.preferredDocument': 'Preferred document language',
  'certificate.form.paymentMethod': 'Payment method',
  'certificate.form.transactionAmount': 'Transaction amount (RM)',
  'certificate.occupationClass': 'Occupation class',
  'certificate.className': 'Class 1',
  'certificate.occupationDescription': 'Occupation description',
  'certificate.occupationDescContent':
    'Profession in specific business or services (e.g. accountant, company secretary, lawyer, property agent or developer.)',

  'certificate.correspondenceAddress.certificate':
    'Same as policy owner address',
  'certificate.correspondenceAddress.new': 'New address',
  'certificate.correspondenceAddress.mainInsured':
    'Same as main person covered address',

  'certificate.residentialAddress.correspondence':
    'Same as correspondence address',
  'certificate.residentialAddress.new': 'New address',
  'certificate.residentialAddress.owner':
    'Same as policy owner registered business address',

  'certificate.businessAddress.new': 'New address',

  'other.nominationDetails.title': 'Do you want to make a nomination?',
  'other.nominationDetails.chooseHibahOrWasi':
    'Please choose either Hibah or Wasi.',
  'other.nominationDetails.hibahDetails': 'Hibah’s details',
  'other.nominationDetails.nominationNotApplicable':
    'Nomination is not applicable.',
  'other.nominationDetails.nominationDuplicated':
    'The nomination is duplicated.',
  'other.nominationDetails.minimumAllocation': 'Minimum allocation is 10%',
  'other.nominationDetails.wasiDetails': 'Wasi’s details',
  'other.nominationDetails.maximumHibah': '*Maximum to add 5 nominees',
  'other.nominationDetails.maximumWasi': '*Maximum to add 2 wasi',
  'other.nominationDetails.addHibah': 'Add nominee',
  'other.nominationDetails.addWasi': 'Add wasi',
  'other.nominationDetails.nominee': 'Nominee',
  'other.nominationDetails.guardianee': 'Guardianee',
  'other.nominationDetails.wasi': 'Wasi',
  'other.nominationDetails.confitionalHibah': 'Confitional hibah',
  'other.nominationDetails.allocation': 'Allocation (%)',
  'other.nominationDetails.payablePercentageArrangement':
    'Payable percentage arrangement',
  'other.nominationDetails.remove.title': 'Are you sure to remove?',
  'other.nominationDetails.remove.description':
    'Filled information under the existing policy will be removed.',
  'other.totalPercentage': '{{totalPercentage}}/100%',
  'other.nominationDetails.total': 'Total:',
  'other.nominationDetails.payorDetails.title': 'Payor’s details',
  'other.nominationDetails.payorDetails.question':
    'Is the policy owner the same as payor?',
  'other.nominationDetails.details': "Nominee's details",
  'other.nominationDetails.information': "Nominee's information",
  'other.nominationDetails.switchConfirmationDialog.title':
    'Are you sure to choose {{type}}?',
  'other.nominationDetails.switchConfirmationDialog.des':
    'You can only select either Hibah or Wasi for the nomination details. If you choose Hibah, the information you’ve filled in Wasi will be removed.',
  'other.nominationDetails.circularProgressChart.totalProportion':
    'Total proportion',
  'other.trusteeDetails.title': 'Do you want to appoint a trustee?',
  'other.trusteeDetails.subtitle': 'Please choose the type of Trustee.',
  'other.trusteeDetails.details': "Trustee's details",
  'other.trusteeDetails.companyName': 'Company name',
  'other.trusteeDetails.dateOfRegistration': 'Date of registration',
  'other.trusteeDetails.registrationNumberNew': 'Registration number (New)',
  'other.trusteeDetails.registrationNumberOld':
    'Registration number (Old) (optional)',
  'other.trusteeDetails.natureOfBusiness': 'Nature of business',
  'other.trusteeDetails.businessPhoneNumber': 'Business phone number',
  'other.trusteeDetails.countryOfIncorporation': 'Country of Incorporation',
  'other.trusteeDetails.authorizedSignatory': 'Authorized Signatory',
  'other.trusteeDetails.trusteeNotAllowed':
    'Appointment of Trustee is not allowed',
  'other.witnessDetails.witness': "Witness's",
  'other.witnessDetails.title': 'Is the Witness same as agent?',
  'other.witnessDetails.note':
    'Witness must be at least 18 years old and a person of sound mind and not named as Nominee.',
  'other.witnessDetails.details': "Witness's details",
  'other.parentDetails.details': "Parent/Legal guardian's details",
  'other.parentDetails': 'Parent/Legal Guardian',
  'other.parentDetails.declaration':
    'I being the father/mother/legal guardian of the Life to be Assured who is also Policy Owner hereby give consent to him/her to effect a life insurance policy on his/her own life',
  yes: 'Yes',
  no: 'No',
  cancel: 'Cancel',
  remove: 'Remove',
  pdp: 'Personal Data Protection (PDP)',
  'pdp.1':
    'Personal Data/Sensitive Personal Data may include Proposer, Life to be Assured, Contingent Owner and/or Trusteeʼs name, gender, religion, race, identification number, address, phone number, email address as well as non-public information including details of condition or history of medical, health and hospitalization, financial, familial and non-familial information and any updated information of the same for the applicable product or service and such information as defined in “personal data” and “sensitive personal data” of the PDPA.',
  'pdp.2':
    'Save as stated above, the Company shall not disclose the Personal Data/Sensitive Personal Data without prior consent of the Proposer, Life to be Assured, Contingent Owner and/or Trustee.',
  'pdp.3':
    'The Proposer, Life to be Assured, Contingent Owner and/or Trustee has the right to access his/their Personal Data/Sensitive Personal Data and the Company has the right to impose fee for this purpose. The Proposer Life to be Assured, Contingent Owner and/or Trustee shall be allowed to make any update or correction to his/their Personal Data/Sensitive Personal Data through a written request to the Company.',
  'pdp.4':
    'The Company shall take reasonable steps to protect the Personal Data/Sensitive Personal Data from any unauthorized access or misuse and in ensuring accuracy of the Personal Data/Sensitive Personal Data at all times and shall not keep the Personal Data/Sensitive Personal Data longer than necessary for the purpose of this application and/or proposal and maintenance of the Policy Contract.',
  'pdp.5':
    'For more information, please visit the Companyʼs website to view the Companyʼs Privacy Statement',
  'pdp.agreement.1':
    'By submitting this application, I/we confirm that I/we have read and agreed to the ',
  'pdp.agreement.2': ' set out in the hyperlink',
  'pdp.notice': 'Personal Data Protection Act 2010 Notice',
  'pdp.marketing': 'Marketing',
  'pdp.marketing.description':
    'If you would like to receive information regarding any exclusive promotions and offers from FWD Insurance Berhad or third parties, please provide your consent by ticking the box below (Optional):',
  'pdp.marketing.checkbox':
    "I/We hereby /give consent to Company to use, collect, hold, process or disclose my/our Personal Data/Sensitive Personal Data for Company 's own use or the use by any third party for direct marketing, marketing and promotional purposes including for statistics and research, study of consumer behaviour or performing policy review and customer's needs analysis for purposes of processing this Policy Contract as according to the Personal Data Protection Act 2020 (PDPA).",
  disclosure: 'Pre-Contractual Duty of Disclosure',
  'disclosure.i': 'i.',
  'disclosure.ii': 'ii.',
  'disclosure.a': 'a)',
  'disclosure.b': 'b)',
  'disclosure.consumerContract': 'Consumer Insurance Contract',
  'disclosure.consumerContract.definition':
    'A "consumer insurance contract" means a contract of insurance entered into, varied or renewed by an individual wholly for purposes unrelated to the individual\'s trade, business or profession.',
  'disclosure.consumerContract.statement':
    'Paragraph 5 of Schedule 9 of the Financial Services Act 2013 requires You:',
  'disclosure.consumerContract.statement.a':
    'to take reasonable care not to make any misrepresentation when answering any questions in this proposal form; and',
  'disclosure.consumerContract.statement.b':
    'to take reasonable care to disclose to Us any other matter (other than the questions in this proposal form) that You know may be relevant to Our decision on whether to accept the risk which We are being asked to accept and the rates and terms to be applied',
  'disclosure.nonConsumerContract': 'Non-Consumer Insurance Contract',
  'disclosure.nonConsumerContract.definition':
    'A "non-consumer insurance contract" means a contract of insurance entered into, varied or renewed by an individual wholly for purposes related to the individual\'s trade, business or profession.',
  'disclosure.nonConsumerContract.statement':
    'Sub Paragraph 4(1) of Schedule 9 of the Financial Services Act 2013 require You:',
  'disclosure.nonConsumerContract.statement.a':
    'to disclose to Us a matter that You know to be relevant to Our decision on whether to accept the risk or not and the rates and terms to be applied; and',
  'disclosure.nonConsumerContract.statement.b':
    'to disclose to Us a matter that a reasonable person in the circumstances could be expected to know to be relevant.',
  'disclosure.1':
    'You are also required to continue to disclose to Us fully and correctly / accurately any changes or additional material facts in respect of Your health, occupation, financial status etc. which may arise between the time of submission of the proposal form and the time the contract of insurance is entered into, varied or renewed. If You are in doubt whether or not a matter is relevant to Our decision, you should disclose that matter. Your duty of disclosure above continues until the time the contract is entered into, varied or renewed. You also understand and agree that this proposal may be re-underwritten based on such changes or additional material facts.',
  'disclosure.2':
    'You are advised that You should not sign this proposal form unless it has been completed to Your full satisfaction as You are solely responsible for the answers given in this proposal form once it has been signed by You.',
  'disclosure.3':
    'In accordance with the laws and guidelines on anti-money laundering and anti-terrorism financing governing Malaysia, We are required to verify the identity of Our customers. If We do not receive sufficient proof of identification, We may not be able to consider Your proposal.',
  'disclosure.4':
    'If you do not understand Your obligation / duty as stated above or if You need further explanation, kindly contact Your Intermediary or Our Customer Careline.',
  insuranceCoverage: 'The Provisional Accidental Death Insurance Coverage',
  'insuranceCoverage.a': 'a)',
  'insuranceCoverage.b': 'b)',
  'insuranceCoverage.c': 'c)',
  'insuranceCoverage.d': 'd)',
  'insuranceCoverage.1':
    'Terms And Conditions For The Provisional Accidental Death Insurance Coverage',
  'insuranceCoverage.2':
    'IN CONSIDERATION of your submission for the life insurance application and proposal deposit to an authorised Intermediary of the Company, a provisional accidental death insurance coverage is provided by the Company to the Life to be Assured commencing on the date of the application submission, subject to the terms and conditions stated below.',
  'insuranceCoverage.2.a':
    'The provisional accidental death coverage amount shall be the sum assured applied for the basic plan less the application deposit, and the maximum coverage amount, shall not exceed RM500,000. / Amaun perlindungan sementara kematian akibat kemalangan ini adalah jumlah diinsuranskan yang dipohon untuk pelan asas kurang deposit permohonan ini, dan amaun maksima perlindungan ini, tidak boleh melebihi RM500,000.',
  'insuranceCoverage.2.b':
    'The death of the Life to be Assured must be resulted solely, directly and independently of all other causes, (i) from bodily injury by external,v violent and accidental means with visible contusion or wound on the exterior of the body or internal injury revealed by autopsy; or (ii) from accidental drowning revealed by autopsy. Exclusions in para (c) shall apply.',
  'insuranceCoverage.2.c':
    'The provisional accidental death coverage amount is not payable if the death of Life to be Assured was caused directly or indirectly, wholly or partly (i) By assault or murder; or (ii) By war declared or undeclared; or (iii) By riot, civil commotion, strikes or terrorist activities; or (iv ) By military, police, air or naval service in time of declared or undeclared war or while under orders for warlike operation or restoration of public order; or (v) By the Life to be Assured participating in any criminal act or brawl; or (vi) By the Life to be Assured participating in any hazardous pursuits like boxing, skiing, wrestling, diving, mountaineering, rappelling, river rafting, rapid shooting; or (vii) By the Life to be Assured participating in any airborne activities other than as a fare paying passenger or a crew member of an aircraft operated by an international airline and licensed or passenger service over a regular scheduled commercial route; or (viii) By the Life to be Assured participating in any form of racing other than on foot, any speed or endurance contest; or. (ix) By self destruction or any attempt thereat while sane or insane; or (x) By suicide whether sane or insane; or (xi) While or because the Life to be Assured is under the influence of alcohol, narcotic, drug or poison or as a result of inhaling gas or fumes; or (xii) By pregnancy or childbirth; or (xiii) By pre-existing physical or mental defect or infirmity; or (xiv) By sickness, disease or infections of any kind; or (xv) By infections of any kind or bodily injury due to a gradual operating cause; or (xvi) By the Life Assured committing or attempting to commit an unlawful act under any prevalent law in force.',
  'insuranceCoverage.2.d':
    'This provisional coverage shall terminate on the date the Company (i) issues a certificate of insurance; or (ii) declines the life insurance application; or (iii) makes a counter offer; or (iv) requests for further deposit; or (v) requests for further evidence of insurability, whichever is earliest PROVIDED THAT this provisional coverage shall not exceed 90 days from the date of the application submission (inclusive).',
  'insuranceCoverage.3':
    'The proposal deposit shall be refunded if the life insurance application is declined by the Company or if within the time stipulated the counter offer is not accepted, or further deposit is not received or further evidence of insurability is not received, provided that no accidental death claim has arisen, or lodged or made under this provisional coverage, while this provisional coverage as been in force.',
  'insuranceCoverage.4':
    "If an accidental claim has arisen, or been lodged or made under this provisional coverage, while this provisional coverage is in force, upon the company's approval of the claim, the costs of insurance for this provisional coverage shall be deducted from the proposal deposit.",
  changePremiumAuthorization: 'Authorization to charge premium',
  'changePremiumAuthorization.financialPlanningAndKnowledge.question':
    'How would you rate your level of understanding and knowledge in financial planning and management?',
  'changePremiumAuthorization.financialPlanningAndKnowledge.low': 'Low',
  'changePremiumAuthorization.financialPlanningAndKnowledge.medium': 'Medium',
  'changePremiumAuthorization.financialPlanningAndKnowledge.high': 'High',
  'changePremiumAuthorization.declaration': 'Declaration',
  'changePremiumAuthorization.declaration.content':
    'I/We hereby instruct the Company, upon approval of the above mentioned Application for Life Assurance and issuance of the policy contract, and subject to the terms and conditions of the policy which is to be issued upon approval of the Application for Life Assurance, to charge  the Account Value automatically for the premium of any supplementary benefit(s) attaching thereto on the Commencement Date and on each monthly anniversary of the Commencement Date thereafter and whilst the policy is maintained in force in accordance with the Premium Holiday provision of the policy, if any.',
  ePolicyAndENotices: 'ePolicy & eNotices',
  'ePolicyAndENotices.consent':
    "I/We give consent to the Company’s delivery of my/our ePolicy and eNotices to the WhatsApp account registered to the Life to be Assured's (or Proposer, if applicable) mobile number as stated in the Personal Particulars section above.",
  pdpaNotice: 'Personal Data Protection Notice',
  invalidAccountNumberLengthMin: 'Account number length must be {{minLength}}',
  invalidAccountNumberLengthRange:
    'Account number length must be between {{minLength}} and {{maxLength}}',
  invalidAccountNumberLengthStops:
    'Account number length must be either {{stops}}',
  payoutAccountDetails: 'Payout account details',
  'payoutAccount.method': 'Payment method:',
  'payoutAccount.bankDirect': 'Bank direct',
  'payoutAccount.bankName': 'Bank name',
  'payoutAccount.searchForBankName': 'Search for bank name',
  'payoutAccount.bankAccountNumber': 'Bank account number',
  'payoutAccount.accountHolderName': 'Account holder name',
  'payoutAccount.terms': 'Terms and Conditions of Payout account Instruction',
  'payoutAccount.terms.title': 'Terms & Conditions for Payout account Service',
  'payoutAccount.terms.subtitle':
    'TERMS AND CONDITIONS FOR Payout account SERVICE / TERMA DAN SYARAT PERKHIDMATAN Payout account ',
  'payoutAccount.terms.opening':
    'In consideration of FWD Insurance Berhad’s agreement to accept my authorisation to credit my bank account to receive monies payable to me under the given Policy number(s), I expressly agree to the following terms and conditions:',
  'payoutAccount.terms.1':
    'FWD Insurance Berhad to use this bank account for the types of payments due to me, unless FWD Insurance Berhad receives a written instruction to revoke this instruction and provide the new payout account details which must be approved by FWD Insurance Berhad prior to the payment effective date.',
  'payoutAccount.terms.2':
    'I hereby agree to keep FWD Insurance Berhad indemnified against any claims, loss, damage and expenses which FWD Insurance Berhad may suffer arising from my authorisation to credit my bank account as aforesaid.',
  'payoutAccount.terms.3':
    'I understand that the payout account facility is only allowed for a valid bank account with a licensed financial institution in Malaysia that participates in the Interbank Giro (IBG) payment platform and I will provide my payout account details to fulfil this condition.',
  'payoutAccount.terms.4':
    'I understand that all or a portion of the Contribution(s), charges and payments for this Application may be subjected to the Sales & Service Tax (SST) at the applicable prevailing rate, which must be paid.',
  'payoutAccount.terms.5':
    'Any fee(s) imposed by the bank for this service shall be fully borne by me.',
  'payoutAccount.terms.6':
    'I understand that the payout account instruction shall remain binding until FWD Insurance Berhad’s receipt of a written notification from me to cancel the instruction.',
  'payoutAccount.terms.7':
    'I hereby agree that FWD Insurance Berhad shall not be held responsible for any loss due to the wrong banking account provided by me for the purpose of payment of the Policy.',
  'payoutAccount.terms.8':
    'Within a reasonable period, FWD Insurance Berhad shall provide a written notice:',
  'payoutAccount.terms.8.a':
    'To discontinue, interrupt, withdraw or suspend this service as it deems fit and FWD Insurance Berhad is not to be held liable for any loss or damage which may be suffered by me/us as a result of such action by FWD Insurance Berhad.',
  'payoutAccount.terms.8.b':
    'To add, delete or amend any of the above terms and conditions from time to time. Such amendments shall become effective on such date as FWD Insurance Berhad may elect to adopt and the continued use of this service by me/us shall constitute my/our acceptance of the said amendments.',
  'payoutAccount.terms.8.c':
    'To hold monies pursuant to the Policy related payment due to me in the event the information provided above is incorrect, incomplete and/or otherwise not possible due to no fault of FWD Insurance Berhad to successfully process this request until the new payout account detail is received from me.',
  close: 'Close',
  'documents.policyOwner': 'Documents - Policy owner',
  'documentUpload.frontID': 'Front of ID (NRIC/Passport/Birth Certificate)',
  'documentUpload.backID': 'Back of ID (NRIC/Passport/Birth Certificate)',
  'documentUpload.foreignerQuestionnaire': 'Foreigner questionnaire',
  'documentUpload.visa': 'Visa',
  'documentUpload.fatcaCrsDeclarationForm': 'FATCA/CRS declaration form',
  'documentUpload.permanentResident': 'Permanent resident',
  'documentUpload.largeAmountQuestionnaire': 'Financial Questionnaire',
  'documentUpload.agentDeclarationForm': 'Agent Declaration Form',
  'documentUpload.consentLetter': 'Consent letter',
  'documentUpload.selfieNRIC': 'Selfie photo with ID copy',
  'documentUpload.familyPortrait': 'Family Portrait Photo',
  'documentUpload.consentFormFromParentLegalGuardian':
    'Consent Form from Parent/Legal Guardian',
  'documentUpload.authorisationLetter':
    'Supplementary Questionnaire for Business Application',
  'documentUpload.certifiedTrueForm24':
    'Certified true copy of Form 24/ Section 78 (Return of Allotment of Shares) OR Section 14 (Super Form)',
  'documentUpload.certifiedTrueForm49':
    'Certified true copy of Form 49/ Section 58 (Register of Directors, Manager and Secretaries) OR Section 14 (Super Form)',
  'documentUpload.certifiedTrueForm9':
    'Certified true copy of Form 9/ Section 17 (Certificate of Incorporation)/ Section 15 (Notice of Registration)',
  'documentUpload.frontMyKad': 'Front of MyKad/ Passport (if foreigner) for the Authorised Signatory',
  'documentUpload.backMyKad': 'Back of MyKad/ Passport (if foreigner) for the Authorised Signatory',
  'documentUpload.passport': 'Passport for Authorised Personnel',
  'documentUpload.foreignerQuestionaire': 'Foreigner questionaire',
  'documentUpload.businessEntityForm': 'Self-Certification Business Entity Form',
  'documentUpload.validPass':
    'Valid VISA/Employment Pass/Resident Pass/MM2H VISA',
  'documentUpload.optional': '{{document}} (optional)',
  'documentUpload.footer.focusOnIncompleteField': 'Go to item',
  'documentUpload.footer.totalIncompleteRequiredFields': 'incomplete uploads',
  'documentUpload.footer.totalIncompleteRequiredField': 'incomplete upload',
  'review.applicationSummary': 'Application summary',
  'review.personalInfo': 'Personal information',
  'review.company': 'Company',
  'review.authorizedSignatoryDetails': 'Authorized signatory details',
  'review.beneficiary': 'Nomination',
  'review.healthQuestions': 'Health questions',
  'review.declaration': 'Consents and declarations',
  'review.documentsUploaded': 'Documents uploaded',
  'review.reviewSummary': 'Review summary',
  'review.homeNumber': 'Home number',
  'review.officeNumber': 'Office number',
  'review.policyDocuments': 'Policy documents',
  'review.salutationTitle': 'Salutation/Title',
  'review.trusteeType': 'Trustee type',
  'review.fullName': 'Full name',
  'review.gender': 'Gender',
  'review.dateOfBirth': 'Date of birth (DD/MM/YYYY)',
  'review.customerType': 'Customer type',
  'review.age': 'Age',
  'review.relationshipWithPO': 'Relationship with Policy owner',
  'review.race': 'Race',
  'review.religion': 'Religion',
  'review.maritalStatus': 'Marital status',
  'review.smokingHabit': 'Smoking habit',
  'review.idType': 'ID type',
  'review.idNumber': 'Identification number',
  'review.additionalIdType': 'Additional ID Type',
  'review.additionalIdNumber': 'Additional identification number',
  'review.source': 'Source',
  'review.campaignCode': 'Campaign code',
  'review.nationality': 'Nationality',
  'review.residencyType': 'Residency type',
  'review.countryOfBirth': 'Place of birth - Country',
  'review.stateOfBirth': 'Place of birth - State',
  'review.cityOfBirth': 'Place of birth - City',
  'review.cityName': 'City name',
  'review.occupation': 'Occupation',
  'review.occupationClass': 'Occupation class',
  'review.occupationDescription': 'Occupation description',
  'review.nameOfBusiness': 'Name of business/Employer',
  'review.natureOfWork': 'Nature of work/Business',
  'review.exactDuties': 'Exact duties',
  'review.annualIncome': 'Annual income',
  'review.annualIncomeAmount': 'Annual income amount (RM)',
  'review.annualIncomeAmount.phone': 'Annual income amount',
  'review.email': 'Email',
  'review.mobilePhone': 'Mobile phone no.',
  'review.homePhone': 'Home phone no.',
  'review.officePhone': 'Office phone no.',
  'review.businessPhone': 'Business phone no.',
  'review.preferredCopy': 'Preferred copy of your policy',
  'review.preferredLanguage': 'Preferred document language',
  'review.correspondenceAddress': 'Correspondence address',
  'review.residentialAddress': 'Residential address',
  'review.businessAddress': 'Business address',
  'review.answer': 'Answer',
  'review.declaration.rpq': 'RPQ of policy owner',
  'review.declaration.fatca': 'FATCA/CRS',
  'review.declaration.rop': 'Replacement of Policy (ROP)',
  'review.declaration.takeOver': 'Take Over',
  'review.declaration.pdp': 'Personal Data Protection (PDP)',
  'review.declaration.disclosure': 'Pre-Contractual Duty of Disclosure',
  'review.declaration.insuranceCoverage':
    'The Provisional Accidental Death Insurance',
  'review.declaration.chargePremiumAuthorization':
    'Authorization to charge premium',
  'review.declaration.ePolicyAndENotices': 'ePolicy & eNotices',
  'review.view': 'View',
  'review.declaration.title':
    'Consents and declarations you have read and agreed to:',

  'review.personalDetails': 'Personal details',
  'review.companyName': 'Company name',
  'review.registrationNumber': 'Registration number (Latest)',
  'review.registrationDate': 'Date of registration',
  'review.oldRegistrationNumber': 'Registration number (Old) (optional)',
  'review.natureOfBusiness': 'Nature of business',
  'review.businessPhoneNumber': 'Business phone number',
  'review.sst': 'Sales and service tax act 2018 (SST)',
  'review.sst.number': 'Sales and service tax registration number',
  'review.address': 'Address',
  'review.designation': 'Designation',
  'review.taxDetails': 'Tax details',
  'review.businessRegistration': 'Business registration',
  'review.contactDetails': 'Contact details',

  'review.salesIllustration': 'Sales illustration',
  'review.customerFactFinding': 'Customer Fact Finding',
  'review.cert.customerFactFinding': 'Customer Fact Finding',
  'review.applicationForm': 'Application form',
  'review.comment': 'Comment',
  'review.si.pdfTitle': 'Sales illustration - {{time}}',
  'review.si': 'Sales illustration',
  'review.pleaseReview': 'Please review your {{title}}',
  'review.basePlanSummary': 'Base plan',
  'review.topUp': 'Top-up',
  'review.additionalProtection': 'Additional protection',
  'review.fundAllocation': 'Fund allocation',
  'payment.advancePremium.months': 'months',
  'payment.advancePremium.quarters': 'quarters',
  'payment.advancePremium.semiAnnual': 'semi-annual',
  'payment.advancePremium.title': 'Advance premium',
  'payment.advancePremium.selectAdvancePremium':
    'Please select your advance premium period',
  'payment.advancePremium.secondYearDiscount':
    'Enjoy a discount in the second year when you pay the first and second year in full upfront',
  'payment.advancePremium.firstYearPremium': 'First year premium',
  'payment.advancePremium.secondYearPremium': 'Second year premium',
  'payment.checking': 'Checking payment status',
  'payment.submitted': 'Payment request submitted',
  'payment.submitted.desc': 'No payment yet, please check again later.',
  'payment.check': 'Check payment status',
  'payment.openPaymentGateway': 'Open payment gateway',
  'payment.back': 'Back to payment',
  'payment.method.online': 'Online Banking (FPX)',
  'payment.method.credit': 'Credit/Debit card',
  'payment.method.cheque': `Cheque`,
  'payment.method.biro': 'Biro Angkasa',
  'payment.method.direct': 'Direct transfer',
  'payment.method.CC': 'Credit/Debit card',
  'payment.method.CC.desc': 'You can pay with VISA or MasterCard',
  'payment.method.FPX': 'Online Banking (FPX)',
  'payment.method.FPX.desc':
    'To ensure you don’t miss your renewal premium, please register eMandate after paying through Online Banking (FPX).',
  'payment.method.Slip': 'Manual Upload Proof of Payment',
  'payment.method.Slip.desc': 'Only for FPX payment more than RM 30,000.',
  'payment.method.eWallet': 'eWallet',
  'payment.method.eWallet.desc':
    'To ensure you don’t miss your renewal premium, please register eMandate after paying through eWallet.',
  'payment.method.EPP': 'Easy Payment Plan (EPP)',
  'payment.method.EPP.desc':
    'Pay with your Maybank or Hong Leong Credit Card to sign up for a 12-month instalment payment with 0% interest. Refer to EPP Terms and Conditions.',
  'payment.method.select': 'Select your payment method',
  'payment.method.disclaimer':
    'Kindly ensure transaction limit is updated with the respective payment provider before payment.',
  'payment.method.no': 'Application no.',
  'payment.method.policy.no': 'Policy no.',
  'payment.submethod.inapp': 'Pay in App',
  'payment.submethod.vialink': 'Send payment link',
  'payment.submethod.vialink.remoteSelling': 'Remote selling',
  'payment.tc': 'Terms and condition of credit/debit card instruction',
  'payment.tc.statement':
    'I, hereby declare / agree / undertake the following:',
  'payment.tc.statement.a':
    'To charge all premiums for this Proposal No./Policy No., as and when due to my “credit card / debit card / bank account” (“Account”) subject to the terms of the policy contract.',
  'payment.tc.statement.b':
    'To ensure that my Account has sufficient funds for the execution of the transaction(s)',
  'payment.tc.statement.c':
    'That the Account details provided are my own and that the details of the Account provided are accurate.',
  'payment.tc.statement.d':
    'I understand that the transaction(s) for payment to FWD Insurance Berhad (“Company”) shall be subject to the acceptance by the Company whereupon I and /or the policy owner shall be informed in writing by the Company of the governing procedures and the verification/authorisation from the issuing bank of the Account',
  'payment.tc.statement.e':
    'To take full responsibility for any transaction(s) arising from the use of the said Account in payment to the Company,',
  'payment.tc.statement.f':
    'That I shall immediately notify the Company in writing of changes to the Account number and expiry date as well as any changes to the Account which may affect the payment transaction,',
  'payment.tc.statement.g':
    'That either I or the Company may terminate this instruction by giving the other thirty (30) days written notice, and thereafter I shall forward all payments due (if any) directly to the Company,',
  'payment.tc.statement.h':
    'That I shall indemnify the Company against all losses, damages, expenses, claims and demands which the Company may incur or sustain by reason or as a result of processing the transaction(s),',
  'payment.tc.statement.i':
    'That the Company has the sole discretion to change the date of the recurring billing and/or the premium chargeable as per the terms and conditions of the policy contract.',
  'payment.tc.statement.j':
    'That I understand and agree that the Company shall not bill me unnecessarily (for example, in the event my policy lapses, terminates, matures, surrenders, etc).',
  'payment.tc.statement.conclusion':
    'That I understand that the Company shall not be held responsible or liable for any claim, loss, damages, cost, interest and expenses arising from the unsuccessful processing of the transactions/debits due to insufficient funds, malfunctions of system, electricity failure and any other factors beyond the control of the Company, including but not limited to the wrongful transactions/debits of my account due to inaccurate information provided to the Company where upon I shall forward premiums due directly to the Company.',
  'payment.success': 'Payment successful',
  'payment.failed': 'Payment fail',
  'payment.failed.title': 'Your transaction details are given below:',
  'payment.failed.orderId': 'Order ID',
  'payment.failed.paymentId': 'Payment ID',
  'payment.failed.status': 'Transaction status',
  'payment.failed.amount': 'Transaction amount',
  'payment.status.success': 'Success',
  'payment.status.fail': 'Fail',
  'payment.status.eMandateRegistration': 'eMandate registration',
  'payment.submit': 'Submit application',
  'payment.sendRegistrationLink': 'Send registration link',
  'payment.eMandate': 'eMandate',
  'payment.failedToGenerate': 'Failed to generate payment link',
  'payment.failedToSend': 'Failed to send payment link',
  'payment.expried.title': 'Payment link generation fail!',
  'payment.expried.des':
    'Sales illustration is no longer valid. Please create a new application.',
  'payment.link.sent': 'Payment link is sent',
  'payment.proceed': 'Proceed',
  'paymentMode.L': 'Single Premium',
  'payment.initialPaymentDetails': 'Initial payment details',
  'payment.initialPaymentTotal': 'Initial payment total',
  'payment.premium': 'Premium/{{mode}}',
  'payment.sale': 'Sales & Service Tax (SST)',
  'payment.stamp': 'Stamp Duty',
  'payment.policy': 'Policy details',
  'payment.sumAssured': 'Sum assured',
  'payment.type': 'Payment type',
  'payment.policy.term': 'Policy term',
  'payment.premium.mode': 'Premium mode',
  'payment.premium.term': 'Premium term',
  'payment.cash': 'Regular cash payment',
  'payment.coverage': 'Coverage details',
  'payment.eMandateConfirmation.title': 'Important:',
  'payment.eMandateConfirmation.content':
    'Successful eMandate registration is required to process your application after the initial premium payment is made through {{paymentMethod}}.\n\nDo you wish to continue?',
  'payment.eMandateConfirmation.continue': 'Yes, continue',
  gotIt: 'OK, got it',
  'review.party.health': "{{name}}'s health questions",
  'signature.policyOwner': 'Policy owner',
  'signature.insured': 'Life Assured',
  'signature.witness': 'Witness',
  'signature.trustee': 'Trustee',
  'signature.parent': 'Parent/Legal Guardian',
  'signature.agent': 'Agent/Intermediary',
  'signature.placeOfSigning': 'Place of signature',
  'signature.placeOfSigning.format': 'Place of signature: {{place}}',
  'signature.okGotIt': 'OK, got it',
  'decision.failed': 'Failed to get decision',
  'review.decision.goodNews': 'Good news!',
  'review.confirm.proceed': 'Proceed',
  'review.confirm.title': 'Confirm to proceed',
  'review.confirm.description': 'Once you click proceed, you will not be able to edit the information in previous section.',
  'review.decision.continueToSignature': 'Continue to signature',
  'review.decision.next': 'Next',
  'review.decision.back': 'Back',
  'review.decision.youAreAlmostThere': 'You’re almost there!',
  'review.decision.revisedOffer': 'Revised offer!',
  'review.decision.inTheMeanTime':
    'In the meantime, take a closer look at our new offer, designed especially for you. Based on the information you’ve provided, we adjusted your coverage/premium.',
  'review.decision.basedOnInformation':
    'Based on the information you’ve provided, we made a few changes on the coverage/premium we can offer.',
  'review.decision.preview': 'Preview:',
  'review.decision.totalPremium': 'Total Premium',
  'review.decision.tooltip.title': 'Information of monthly premiums',
  'review.decision.tooltip.increased':
    'We’ve increased your premium because of the following:',
  'review.decision.tooltip.following': 'The following is/are not covered:',
  'review.decision.tooltip.gotIt': 'Ok, got it',
  'review.decision.yourAppIsApproved.yourInsuranceIsApproved':
    'Your insurance application is approved. You just need to submit additional requirements for us to issue your policy.\n\nPlease send us:',
  'review.decision.yourAppIsApproved.weJustNeedAFewDocuments':
    'We just need a few documents from you to help us better assess your application and offer you the best possible plan.\n\nPlease send us:',
  'review.decision.yourAppIsApproved.note':
    'Your financial advisor will help you complete and submit these requirements.',
  'review.decision.yourAppIsApproved.weJustNeedAFewDocs':
    'We just need a few documents from you!',
  'review.decision.yourAppIsApproved.yourAppIsApproved':
    'Your application is approved!',
  'review.decision.yourAppIsApproved.yourInsuranceAppIsApproved': `Your insurance application is approved. Thanks for taking this first step to protect yourself and your family’s future.\n\nNow, let's continue the journey to get you covered!`,
  'review.decision.revisedOffer.newOfferWithExclusion':
    ' Take a closer look at our new offer with exclusion.',
  'review.decision.revisedOffer.newOffer':
    ' Take a closer look at our new offer, designed especially for you.',
  'review.decision.youNeedToKnow.somethingYouNeedToKnow':
    'Something you need to know',
  'review.decision.youNeedToKnow.willBeAttached':
    ' will be attached to and will form part of the policy',
  'review.decision.youNeedToKnow.noBenefit': 'No benefit will be paid for ',
  'review.decision.youNeedToKnow.policy':
    'I may request FWD Insurance, at any time, to terminate the effectivity of this Exclusion document upon presentation of satisfactory proof and justifiable reason, subject to FWD Insurance’s approval',
  'review.decision.others.title': 'Thanks for your application!',
  'review.decision.others.stayTuned': 'Stay tuned!\nWe’ll come back to you.',
  'review.decision.others.content': `Thanks for going through these important details.\n\nWe need a little more time to consider your application. We aim to get back to your financial advisor within 2 working days.\nThank you for your patience.`,
  'review.decision.policy.ifTheInsured':
    ' if the insured’s injury, sickness, loss, or claim is directly or indirectly, wholly or partly, caused by or arising from conditions and/or complications related to the',
  'renewal.payment.renewalPayment': 'Renewal payment',
  'renewal.payment.newApplication': 'New application',
  'eMandate.offline': 'Offline',
  'directCredit.form.bankName': 'Bank name',
  'directCredit.form.accountNumber': 'Bank account number',
  'directCredit.form.accountHolderName': 'Account holder name',
  'directCredit.form.mobileNumber': 'Mobile number',
  'directCredit.form.identityType': 'Identity type',
  'directCredit.form.identityNumber': 'Identification number',
  'directCredit.form.email': 'Email address',
  'directCredit.form.debitAmount': 'Direct debit amount (RM)',
  'directCredit.form.maxNumberDebiting': 'Max. number of debiting',
  'directCredit.form.paymentPurpose': 'Purpose of payment',
  'directCredit.form.paymentFrequencyNo': 'Payment reference no.',
  'directCredit.form.paymentFrequency': 'Frequency of debiting',
  'directCredit.form.emailWarning':
    'An email will be sent to your customer if eMandate registration process.',
  'directCredit.form.termCondition1':
    'By clicking this box, I fully understand to the ',
  'directCredit.form.termCondition2': 'Terms & Conditions',
  'directCredit.form.termCondition3':
    ' of Direct Debit services and I acknowledge that upon successful completion of this online application, RM {{amount}} shall be debited from my selected account for the purpose of this service and the amount shall be refunded to me by FWD Insurance Berhad.',
  'eMandate.success.desc': 'Thank you for your eMandate registration',
  'eMandate.success.title':
    'Your bank account details has been registered for the direct debit instruction',
  'eMandate.failed.desc': 'Your eMandate registration is not successful ',
  'eMandate.failed.title':
    'Your bank account details has not been registered for the direct debit instruction',
  'eMandate.failed.detail': 'Transaction details:',
  'eMandate.failed.date': 'Transaction date:',
  'eMandate.failed.type': 'Application type',
  'eMandate.failed.number': 'Transaction amount',
  'eMandate.failed.refNumber': 'Payment ref number',
  'eMandate.failed.debitAmount': 'Direct Debit amount',
  'eMandate.failed.fpxID': 'FPX transaction ID',
  'eMandate.failed.bankName': 'Bank name',
  'eMandate.failed.status': 'Transaction status',
  'eMandate.inProgress': 'Your eMandate registration is in progress...',
  'eMandate.checkStatus': 'Check registration status',
  'eMandate.registration': 'eMandate re-registration',
  'witnessWarning.title': 'Witness must be an agent',
  'witnessWarning.desc':
    'We would reset the witness to be you (agent), since you are not meeting your customers face-to-face.',
  'witnessWarning.backToWitness': 'Back to witness setup',
  'witnessWarning.accept': 'Yes, witness is agent',
  'witnessWarning.note':
    'It has to be a face-to-face application if witness is not same as agent.',
  'paymentSlip.upload': 'Upload payment slip',
  'paymentSlip.chequeBankInSlip': 'Cheque bank in slip',
  'paymentSlip.fullNamePayor': 'Full name payor',
  'paymentSlip.title': 'Manual Upload Proof of Payment',
  'paymentSlip.note':
    'Premium payment more than 30K need to be credited to the account below:',
  'paymentSlip.note.bank': 'Bank: {{bankName}}',
  'paymentSlip.note.accountNumber': 'Account number: {{accountNumber}}',
  'paymentSlip.note.multipleApplication':
    'Payment slip for multiple application',
  'paymentSlip.identificationNumber': 'Identification number',
  'paymentSlip.quotationNumber': 'Quotation number',
  'paymentSlip.initialContributionAmount': 'Initial premium amount (RM)',
  'paymentSlip.chequeAmount': 'Payment amount (RM)',
  'paymentSlip.chequeNumber': 'Payment slip transaction number',
  'paymentSlip.chequeDate': 'Payment banked in date',
  'paymentSlip.error.short':
    'Payment amount are not sufficient for initial premium amount. Please ensure insufficient balance are paid for this application.',
  'paymentSlip.error.over':
    'Payment slip amount are more than Initial premium amount.',
  'paymentSlip.submitted': 'Payment request submitted.',
  'paymentSlip.done': 'Payment done! ',
  'paymentSlip.applicationNumber': 'Application number',
  back: 'Back',
  singlePremium: 'Single premium',
  'payment.congratulation': 'Congratulations!',
  'application.submit.note':
    "Your application is submitted. You may refer the underwriting decision at customer's Submitted applications page.",
  'application.submit.policyNumber': 'Policy number',
  'application.submit.dateSubmitted': 'Date submitted',
  'review.sumAssured': 'Sum assured',
  'review.premium': 'Premium',
  'review.policyTerm': 'Policy term',
  'review.premiumTerm': 'Premium term',
  'review.paymentMode': 'Payment mode',
  'review.basicAnnualPremium': 'Basic annual premium',
  'review.initialPremium': 'Initial premium',
  'review.coverage': 'Coverage',
  'submission.waitForApproval':
    'Application will be sent to your agency leader for approval.',
  'application.submit.hint':
    'You can find the summary of your submitted application in your mailbox.',
  'application.submit.back': 'Back To Home',
  'application.owner.secondaryAgentId': 'Joint Field Agent ID',
  'application.owner.sourceOfWealth': 'Source of Wealth',
  'application.owner.sourceOfFund': 'Source of Fund',
  'application.owner.wealth.inheritances': 'Inheritances',
  'application.owner.wealth.investments': 'Investments',
  'application.owner.wealth.business.ownership.interest':
    'Business Ownership interest',
  'application.owner.wealth.employment.income': 'Employment income',
  'application.owner.wealth.proceeds.insurance': 'Proceeds from Insurance',
  'application.owner.wealth.accumulated.savings.investments':
    'Accumulated savings and investments',
  'application.owner.fund.personal.savings': 'Personal savings',
  'application.owner.fund.pension.releases': 'Pension releases',
  'application.owner.fund.share.sales.dividends': 'Share sales and dividends',
  'application.owner.fund.inheritances.gifts': 'Inheritances and gifts',
  'application.owner.fund.parents.income': "Parents' income",
  'application.owner.fund.allowance': 'Allowance',
  'application.owner.fund.proceeds.insurance': 'Proceeds from Insurance',
  'application.owner.fund.income.investments.business':
    'Income from other Investments /Business',
  'application.owner.fund.spouse.salary': 'Spouse salary',
  'application.owner.fund.salary': 'Salary',
  'application.owner.wealth.tooltip.sow':
    'Describes how a customer or beneficial owner acquired their total wealth.',
  'application.owner.wealth.tooltip.sof':
    'Origin of the funds involved in the business relationship or occasional transaction.',
  'review.customerFactFinding.pdfTitle': 'Customer Fact Finding - {{time}}',
  'other.parentDetails.declaration.short':
    '**DECLARATION**: I being the father/mother/legal guardian of the Life to be Assured [...More]()',
  'other.parentDetails.declaration.full':
    '**DECLARATION**: I being the father/mother/legal guardian of the Life to be Assured who is also Policy Owner hereby give consent to him/her to effect a life insurance policy on his/her own life. [Close]()',
  'payment.paymentMethodSelectionWarning':
    'The payment has been completed and no other payment method can be selected',
  'other.witnessDetails.witness.information': 'Witness’s information',
  markdownMore: ' [...more]()',
  markdownClose: ' [Close]()',
  'other.trusteeDetails.information': 'Trustee’s information',
  'other.trusteeDetails.type.trustee': 'Type of Trustee',
  'other.witnessDetails.witness.details': 'Witness details',
  'other.nominationDetails.nomination': 'Nomination',
  details: '{{screen}} details',
  add: 'Add',
  'payment.yourPlan': 'Your plan',
  'payment.product': 'Product',
  'payment.method.title': 'Payment method',
  'payment.yourPolicySummary': 'Your policy summary',
  'payment.application.details': 'Application details',
  or: 'OR',
  'payment.resendPaymentLink.title': 'Want to resend payment link?',
  'payment.resendPaymentLinkEmandate.title':
    'Want to resend eMandate registration link?',
  'payment.resendPaymentLink.scanToPay': 'Scan to pay',
  'payment.resendPaymentLinkEmandate.scanToRegister.tablet':
    'Scan to register eMandate',
  'payment.resendPaymentLinkEmandate.scanToRegister.phone': 'Scan to register',
  'payment.resendPaymentLink.whatsapp': 'Whatsapp',
  'payment.resendPaymentLink.whatsapp.error': 'Failed to share via whatsapp',
  'payment.resendPaymentLink.email': 'Email',
  'payment.resendPaymentLink.email.error':
    'Failed to share payment link via email',
  'payment.resendPaymentLink.copyLink': 'Copy link',
  'payment.resendPaymentLink.copyLink.success': 'Link is copied',
  'payment.resendPaymentLink.copyLink.error': 'Failed to copy payment link',
  'payment.resendEMandate.title': 'Want to resend eMandate registration link?',
  'payment.resendEMandate.scanToPay': 'Scan to register eMandate',
  'payment.unableToProceed': 'Unable to proceed',
  'payment.unableToProceed.desc':
    "We couldn't proceed with payment because your family plan quota is full already.",
  'signature.title.authorizedSignatory':
    "{{name}}'s (Authorized signatory) signature",
  'signature.authorizedSignatory':
    'Authorized signatory: {{name}} | Signature date: {{date}}',
  'correspondenceAddress.same.po': 'Same as policy owner',
  'payment.tc.emandate': 'Terms & Conditions',
  'application.owner.purposeOfTransaction': 'Purpose of Transaction',
  'application.appointmentOfNomineeAndTrustee.note': 'NOTE',
  'application.appointmentOfNomineeAndTrustee.description':
    'Appointment of nominee and trustee are not applicable for NFTF application',
};
