import {
  MainPartyAddressInformationForm,
  OthersAddressInformationForm,
  mainPartyAddressInformationDefaultValue,
  mainPartyAddressInformationSchema,
  othersAddressInformationDefaultValue,
  othersAddressInformationSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/addressInformation';
import { FormState, UseFormReturn } from 'react-hook-form';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import React from 'react';
import {
  Box,
  Checkbox,
  Column,
  H6,
  Icon,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useController,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Country, Postcode } from 'types/optionList';
import {
  MY_OPTION_LIST,
  MY_COUNTRY,
  NEW_ADDRESS_OPTION,
  NON_INCOME_OCC_GROUP,
} from 'constants/optionList';
import { TFuncKey } from 'i18next';
import { useAddressInformationOptions } from 'features/customerFactFind/hooks/useAddressInformationOptions';
import styled from '@emotion/native';
import { Fragment, useCallback, useMemo, useEffect } from 'react';
import AutocompletePopup from 'components/AutocompletePopup';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import {
  MYAddressOption,
  MYAddressType,
} from 'features/eAppV2/my/validations/commonSchema';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { Address, PartyRole } from 'types/party';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';

interface Props
  extends UseFormReturn<
    MainPartyAddressInformationForm | OthersAddressInformationForm
  > {
  role: 'main-party' | 'others';
  occGroup?: string;
  enableCorrespondenceSelector?: boolean;
}

export default function AddressInformationTablet({
  role,
  control,
  setValue,
  getValues,
  trigger,
  occGroup,
  enableCorrespondenceSelector,
}: Props) {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  const isDone = useSchemaValid(
    control,
    role === 'main-party'
      ? mainPartyAddressInformationDefaultValue
      : othersAddressInformationDefaultValue,
    role === 'main-party'
      ? mainPartyAddressInformationSchema
      : othersAddressInformationSchema,
  );
  const addressTypes = useMemo<MYAddressType[]>(
    () =>
      role === 'main-party'
        ? ['correspondence', 'residential', 'business']
        : ['correspondence', 'residential'],
    [role],
  );

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.addressInformation')}
      icon={<PictogramIcon.Home2 size={40} />}
      isDone={isDone}>
      <Box mt={space[5]}>
        <AddressInformationForm
          addressTypes={addressTypes}
          control={control as Control<AddressInfo>}
          setValue={setValue as UseFormSetValue<AddressInfo>}
          getValues={getValues as UseFormGetValues<AddressInfo>}
          trigger={trigger as UseFormTrigger<AddressInfo>}
          shouldHighlight
          occGroup={occGroup}
          enableCorrespondenceSelector={enableCorrespondenceSelector}
        />
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}

interface AddressInformationFormProps {
  addressTypes: MYAddressType[];
  control: Control<AddressInfo>;
  setValue: UseFormSetValue<AddressInfo>;
  getValues: UseFormGetValues<AddressInfo>;
  trigger: UseFormTrigger<AddressInfo>;
  shouldHighlight?: boolean;
  initialHighlight?: boolean;
  occGroup?: string;
  hideLegend?: boolean;
  enableCorrespondenceSelector?: boolean;
}

export const AddressInformationForm = ({
  addressTypes,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight,
  initialHighlight = true,
  occGroup,
  hideLegend,
  enableCorrespondenceSelector,
}: AddressInformationFormProps) => {
  return (
    <>
      {addressTypes.map((type, idx, arr) => (
        <Fragment key={type}>
          <AddressSection
            type={type}
            control={control}
            setValue={setValue}
            getValues={getValues}
            trigger={trigger}
            shouldHighlight={shouldHighlight}
            initialHighlight={initialHighlight}
            noAddressOption={
              type === 'correspondence' && !enableCorrespondenceSelector
            }
            occGroup={occGroup}
            hideLegend={hideLegend}
            enableCorrespondenceSelector={enableCorrespondenceSelector}
          />
          {idx !== arr.length - 1 && <Line />}
        </Fragment>
      ))}
    </>
  );
};

export const AddressSection = ({
  type,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight: shouldHighlightProp,
  initialHighlight,
  noAddressOption,
  occGroup,
  hideLegend,
  enableCorrespondenceSelector,
}: Omit<AddressInformationFormProps, 'addressTypes' | 'isOwner'> & {
  type: MYAddressType;
  noAddressOption?: boolean;
  enableCorrespondenceSelector?: boolean;
}) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { disabledForm } = useDisabledEAppForm();
  const { data: optionList } = useGetOptionList<'ib'>();

  const {
    postCodeList,
    stateList,
    cityList,
    isMY,
    onCountryChange,
    onPostCodeChange,
  } = useAddressInformationOptions(
    type,
    optionList,
    control,
    setValue,
    getValues,
    trigger,
  );

  const isEntity = useCheckEntity();

  const addressOptions = useMemo(() => {
    let addressOptions = MY_OPTION_LIST.IB_CORRESPONDENCE_ADDRESSES;
    if (type === 'residential') {
      addressOptions = MY_OPTION_LIST.RESIDENTIAL_ADDRESSES;
    } else if (type === 'business' && !isEntity) {
      addressOptions = MY_OPTION_LIST.IB_BUSINESS_ADDRESSES;
    } else if (type === 'business' && isEntity) {
      addressOptions = MY_OPTION_LIST.IB_ENTITY_BUSINESS_ADDRESSES;
    }
    return addressOptions;
  }, [type, isEntity]);

  const {
    field: { value: selectedAddressOption },
  } = useController({
    name: `${type}Address`,
    control: control,
  });

  const isNewAddress = selectedAddressOption === NEW_ADDRESS_OPTION;

  const { caseObj } = useGetActiveCase();
  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const saveAddress = useCallback(
    (res: Address) => {
      const {
        street,
        subDistrict,
        district,
        countryCode,
        zipCode,
        city,
        province,
      } = res;

      setValue(`${type}AddressLine1`, street, {
        shouldValidate: true,
      });
      setValue(`${type}AddressLine2`, subDistrict, {
        shouldValidate: true,
      });
      setValue(`${type}AddressLine3`, district, {
        shouldValidate: true,
      });
      setValue(`${type}PostCode`, zipCode, {
        shouldValidate: true,
      });
      setValue(`${type}City`, city, {
        shouldValidate: true,
      });
      setValue(`${type}State`, province, {
        shouldValidate: true,
      });
      setValue(`${type}Country`, countryCode, {
        shouldValidate: true,
      });
    },
    [setValue, type],
  );

  useEffect(() => {
    const isCheckedBusinessAddressForEntity =
      (type === 'business' || type === 'correspondence') && !isNewAddress && policyOwner && isEntity;
    if (isCheckedBusinessAddressForEntity) {
      const res = (policyOwner?.addresses || []).find(
        el => el.addressType === 'WORK',
      );
      if (res) {
        saveAddress(res);
      }
    }
  }, [isNewAddress, type, policyOwner, setValue, isEntity, saveAddress]);

  useEffect(() => {
    const isCheckedCorrespondenceAddressForIndividual =
      type === 'correspondence' &&
      !isNewAddress &&
      policyOwner &&
      enableCorrespondenceSelector;

    if (isCheckedCorrespondenceAddressForIndividual) {
      const res = (policyOwner?.addresses || []).find(
        el => el.addressType === 'MAIN',
      );
      if (res) {
        saveAddress(res);
      }
    }
  }, [
    isNewAddress,
    type,
    policyOwner,
    setValue,
    isEntity,
    enableCorrespondenceSelector,
    saveAddress,
  ]);

  const onChangeAddressOption = useCallback(
    (checked: boolean) => {
      if (checked) {
        setValue(`${type}Address`, addressOptions[0].value as MYAddressOption, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
      } else {
        setValue(`${type}Address`, addressOptions[1].value as MYAddressOption, {
          shouldValidate: true,
          shouldDirty: true,
          shouldTouch: true,
        });
        if (addressOptions[1].value === NEW_ADDRESS_OPTION) {
          setValue(`${type}AddressLine1`, '');
          setValue(`${type}AddressLine2`, '');
          setValue(`${type}AddressLine3`, '');
          setValue(`${type}PostCode`, '');
          setValue(`${type}City`, '');
          setValue(`${type}State`, '');
          setValue(`${type}Country`, MY_COUNTRY);
        }
      }
    },
    [addressOptions, setValue, type],
  );

  const shouldHighlight =
    type === 'business'
      ? occGroup !== NON_INCOME_OCC_GROUP
      : shouldHighlightProp;

  return (
    <Column backgroundColor={colors.background} px={space[6]}>
      {!hideLegend && (
        <Row gap={space[1]}>
          <Icon.Location fill={colors.palette.black} />
          <H6 fontWeight="bold">{t(`eApp:${type}Address`)}</H6>
        </Row>
      )}
      {!noAddressOption && addressOptions.length > 1 ? (
        <AddressOptionCheckBox
          label={t(`eApp:${addressOptions[0].label}` as TFuncKey<['eApp']>)}
          checked={selectedAddressOption === addressOptions[0].value}
          onChange={onChangeAddressOption}
          disabled={disabledForm}
        />
      ) : (
        <Box h={7} />
      )}
      <Box
        display={isNewAddress ? 'flex' : 'none'}
        gap={space[5]}
        mt={space[hideLegend ? 0 : 5]}
        key={type === 'business' ? occGroup : type}>
        <Input
          control={control}
          as={TextField}
          name={`${type}AddressLine1`}
          label={t('eApp:addressLine1')}
          hint={t('eApp:addressLine1.hint')}
          shouldHighlightOnUntouched={value =>
            Boolean(shouldHighlight && !value)
          }
          initialHighlight={initialHighlight}
        />
        <Input
          control={control}
          as={TextField}
          name={`${type}AddressLine2`}
          label={t('eApp:addressLine2')}
          hint={t('eApp:addressLine2.hint')}
          style={eAppCommonStyles.tabletTextField}
        />
        <Input
          control={control}
          as={TextField}
          name={`${type}AddressLine3`}
          label={t('eApp:addressLine3')}
          style={eAppCommonStyles.tabletTextField}
        />
        {/* 1 */}
        <Row gap={space[6]}>
          <Input
            control={control}
            as={AutocompletePopup<Country, string>}
            name={`${type}Country`}
            label={t('eApp:country')}
            data={optionList?.COUNTRY.options ?? []}
            getItemLabel={item => item.label}
            getItemValue={item => String(item.value)}
            style={eAppCommonStyles.tabletTextField}
            onChange={onCountryChange}
            searchable
            shouldHighlightOnUntouched={value =>
              Boolean(shouldHighlight && !value)
            }
            initialHighlight={initialHighlight}
          />
          {isMY ? (
            <Input
              control={control}
              as={AutocompletePopup<Postcode<string, 'ib'>, string>}
              name={`${type}PostCode`}
              label={t('eApp:postcode')}
              data={postCodeList}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.tabletTextField}
              onChange={onPostCodeChange}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
              initialHighlight={initialHighlight}
            />
          ) : (
            <Input
              control={control}
              as={TextField}
              name={`${type}PostCode`}
              label={t('eApp:postcode')}
              style={eAppCommonStyles.tabletTextField}
              maxLength={10}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
              initialHighlight={initialHighlight}
            />
          )}
        </Row>
        {/* 2 */}
        <Row gap={space[6]}>
          {isMY ? (
            <Input
              control={control}
              as={AutocompletePopup<{ value: string; label: string }, string>}
              name={`${type}City`}
              label={t('eApp:city')}
              data={cityList}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.tabletTextField}
              searchable
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
              initialHighlight={initialHighlight}
            />
          ) : (
            <Input
              control={control}
              as={TextField}
              name={`${type}City`}
              label={t('eApp:city')}
              style={eAppCommonStyles.tabletTextField}
              maxLength={60}
              shouldHighlightOnUntouched={value =>
                Boolean(shouldHighlight && !value)
              }
              initialHighlight={initialHighlight}
            />
          )}
          <Input
            control={control}
            as={AutocompletePopup<{ value: string; label: string }, string>}
            name={`${type}State`}
            label={t('eApp:state')}
            data={stateList}
            getItemLabel={item => item.label}
            getItemValue={item => String(item.value)}
            style={eAppCommonStyles.tabletTextField}
            searchable
            shouldHighlightOnUntouched={value =>
              Boolean(shouldHighlight && !value)
            }
            initialHighlight={initialHighlight}
          />
        </Row>
      </Box>
    </Column>
  );
};

const AddressOptionCheckBox = styled(Checkbox)(({ theme }) => ({
  marginTop: theme.space[3],
}));

const Line = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  height: 1,
  marginVertical: space[5],
  marginHorizontal: space[6],
}));
