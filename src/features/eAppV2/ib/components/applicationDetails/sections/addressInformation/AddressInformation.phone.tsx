import {
  MainPartyAddressInformationForm,
  mainPartyAddressInformationSchema,
} from 'features/eAppV2/ib/validations/applicationDetails/sections/addressInformation';
import { useForm } from 'react-hook-form';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Box,
  Checkbox,
  Column,
  Label,
  LargeLabel,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useController,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { Country, Postcode } from 'types/optionList';
import {
  MY_OPTION_LIST,
  MY_COUNTRY,
  NEW_ADDRESS_OPTION,
  NON_INCOME_OCC_GROUP,
} from 'constants/optionList';
import { TFunc<PERSON>ey } from 'i18next';
import { useAddressInformationOptions } from 'features/customerFactFind/hooks/useAddressInformationOptions';
import styled from '@emotion/native';
import {
  Component,
  RefObject,
  useCallback,
  useMemo,
  useRef,
  useEffect,
} from 'react';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { MYAddressType } from 'features/eAppV2/my/validations/commonSchema';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { ApplicationDetailsPhoneForm } from 'features/eAppV2/common/components/ApplicationDetailsPhoneSection';
import { useYupResolver } from 'utils/validation/useYupResolver';
import ApplicationDetailsBottomSheet from 'features/eAppV2/common/components/ApplicationDetailsBottomSheet';
import {
  BottomSheetScrollView,
  BottomSheetScrollViewMethods,
} from '@gorhom/bottom-sheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import SearchableDropdown from 'components/SearchableDropdown';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { Platform } from 'react-native';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { Address, PartyRole } from 'types/party';
import { useEAppForm } from 'features/eAppV2/common/hooks/useEAppForm';
import { useDisabledEAppForm } from 'features/eAppV2/common/hooks/useDisabledEAppForm';
export default function AddressInformationPhone({
  value,
  onDismiss,
  onDone,
  occGroup,
  enableCorrespondenceSelector,
}: ApplicationDetailsPhoneForm<MainPartyAddressInformationForm> & {
  occGroup?: string;
  enableCorrespondenceSelector?: boolean;
}) {
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();
  const { data: optionList } = useGetOptionList();
  const resolver = useYupResolver(mainPartyAddressInformationSchema);
  const addressInformationForm = useEAppForm({
    mode: 'onBlur',
    defaultValues: value,
    resolver,
    context: {
      optionList,
    },
  });
  const addressTypes = useMemo<MYAddressType[]>(
    () => ['correspondence', 'residential', 'business'],
    [],
  );

  const scrollRef = useRef<BottomSheetScrollViewMethods | null>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control: addressInformationForm.control,
      schema: mainPartyAddressInformationSchema,
      watch: addressInformationForm.watch,
      scrollRef: scrollRef as RefObject<Component<unknown>>,
      scrollTo: offset => scrollRef.current?.scrollTo(offset),
    });

  return (
    <ApplicationDetailsBottomSheet
      onDismiss={onDismiss}
      disabled={
        !addressInformationForm.formState.isValid ||
        addressInformationForm.formState.disabled
      }
      onPress={addressInformationForm.handleSubmit(data => {
        onDone(data);
      })}
      focusOnIncompleteField={focusOnNextIncompleteField}
      totalIncompleteRequiredFields={totalIncompleteRequiredFields}>
      <Row alignItems="center" px={space[4]} gap={space[1]} mb={space[4]}>
        <PictogramIcon.House2 size={40} />
        <LargeLabel fontWeight="bold" color={colors.primary}>
          {t('eApp:applicationDetails.addressInformation')}
        </LargeLabel>
      </Row>
      <BottomSheetScrollView
        ref={scrollRef}
        keyboardDismissMode="on-drag"
        contentContainerStyle={{ paddingBottom: space[4] }}>
        <AddressInformationForm
          addressTypes={addressTypes}
          control={addressInformationForm.control as Control<AddressInfo>}
          setValue={
            addressInformationForm.setValue as UseFormSetValue<AddressInfo>
          }
          getValues={
            addressInformationForm.getValues as UseFormGetValues<AddressInfo>
          }
          trigger={
            addressInformationForm.trigger as UseFormTrigger<AddressInfo>
          }
          shouldHighlight
          occGroup={occGroup}
          enableCorrespondenceSelector={enableCorrespondenceSelector}
        />
        <BottomSheetFooterSpace />
      </BottomSheetScrollView>
    </ApplicationDetailsBottomSheet>
  );
}

interface AddressInformationFormProps {
  addressTypes: MYAddressType[];
  control: Control<AddressInfo>;
  setValue: UseFormSetValue<AddressInfo>;
  getValues: UseFormGetValues<AddressInfo>;
  trigger: UseFormTrigger<AddressInfo>;
  shouldHighlight?: boolean;
  initialHighlight?: boolean;
  occGroup?: string;
  hideLegend?: boolean;
  enableCorrespondenceSelector?: boolean;
}

export const AddressInformationForm = ({
  addressTypes,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight,
  initialHighlight = true,
  occGroup,
  hideLegend,
  enableCorrespondenceSelector,
}: AddressInformationFormProps) => {
  const { space } = useTheme();

  return (
    <Box gap={space[6]}>
      {addressTypes.map(type => (
        <AddressSection
          key={type}
          type={type}
          control={control}
          setValue={setValue}
          getValues={getValues}
          trigger={trigger}
          shouldHighlight={shouldHighlight}
          initialHighlight={initialHighlight}
          noAddressOption={
            type === 'correspondence' && !enableCorrespondenceSelector
          }
          occGroup={occGroup}
          hideLegend={hideLegend}
          enableCorrespondenceSelector={enableCorrespondenceSelector}
        />
      ))}
    </Box>
  );
};

export const AddressSection = ({
  type,
  control,
  setValue,
  getValues,
  trigger,
  shouldHighlight: shouldHighlightProp,
  initialHighlight,
  noAddressOption,
  occGroup,
  hideLegend,
  enableCorrespondenceSelector,
}: Omit<AddressInformationFormProps, 'addressTypes' | 'isOwner'> & {
  type: MYAddressType;
  noAddressOption?: boolean;
  enableCorrespondenceSelector?: boolean;
}) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { disabledForm } = useDisabledEAppForm();
  const { data: optionList } = useGetOptionList<'ib'>();

  const {
    postCodeList,
    stateList,
    cityList,
    isMY,
    onCountryChange,
    onPostCodeChange,
  } = useAddressInformationOptions(
    type,
    optionList,
    control,
    setValue,
    getValues,
    trigger,
  );

  const isEntity = useCheckEntity();

  const addressOptions = useMemo(() => {
    let addressOptions = MY_OPTION_LIST.IB_CORRESPONDENCE_ADDRESSES;
    if (type === 'residential') {
      addressOptions = MY_OPTION_LIST.RESIDENTIAL_ADDRESSES;
    } else if (type === 'business' && !isEntity) {
      addressOptions = MY_OPTION_LIST.IB_BUSINESS_ADDRESSES;
    } else if (type === 'business' && isEntity) {
      addressOptions = MY_OPTION_LIST.IB_ENTITY_BUSINESS_ADDRESSES;
    }
    return addressOptions;
  }, [type, isEntity]);

  const {
    field: { value: selectedAddressOption, onChange: setAddressOption },
  } = useController({
    name: `${type}Address`,
    control: control,
  });

  const isNewAddress = selectedAddressOption === NEW_ADDRESS_OPTION;

  const { caseObj } = useGetActiveCase();
  const companyParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const saveAddress = useCallback(
    (res: Address) => {
      const {
        street,
        subDistrict,
        district,
        countryCode,
        zipCode,
        city,
        province,
      } = res;

      setValue(`${type}AddressLine1`, street);
      setValue(`${type}AddressLine2`, subDistrict);
      setValue(`${type}AddressLine3`, district);
      setValue(`${type}PostCode`, zipCode);
      setValue(`${type}City`, city);
      setValue(`${type}State`, province);
      setValue(`${type}Country`, countryCode);
    },
    [setValue, type],
  );

  useEffect(() => {
    const isCheckedBusinessAddressForEntity =
      (type === 'business' || type === 'correspondence') && !isNewAddress && companyParty && isEntity;
    if (isCheckedBusinessAddressForEntity) {
      const res = (companyParty?.addresses || []).find(
        el => el.addressType === 'WORK',
      );
      if (res) {
        saveAddress(res);
        trigger();
      }
    }
  }, [
    isNewAddress,
    type,
    companyParty,
    setValue,
    isEntity,
    saveAddress,
    trigger,
  ]);

  useEffect(() => {
    const isCheckedCorrespondenceAddressForIndividual =
      type === 'correspondence' &&
      !isNewAddress &&
      companyParty &&
      enableCorrespondenceSelector;

    if (isCheckedCorrespondenceAddressForIndividual) {
      const res = (companyParty?.addresses || []).find(
        el => el.addressType === 'MAIN',
      );
      if (res) {
        saveAddress(res);
        trigger();
      }
    }
  }, [
    isNewAddress,
    type,
    companyParty,
    setValue,
    isEntity,
    enableCorrespondenceSelector,
    saveAddress,
    trigger,
  ]);

  const onChangeAddressOption = useCallback(
    (checked: boolean) => {
      if (checked) {
        setAddressOption(addressOptions[0].value);
      } else {
        setAddressOption(addressOptions[1].value);
        if (addressOptions[1].value === NEW_ADDRESS_OPTION) {
          setValue(`${type}AddressLine1`, '');
          setValue(`${type}AddressLine2`, '');
          setValue(`${type}AddressLine3`, '');
          setValue(`${type}PostCode`, '');
          setValue(`${type}City`, '');
          setValue(`${type}State`, '');
          setValue(`${type}Country`, MY_COUNTRY);
        }
      }
    },
    [addressOptions, setAddressOption, setValue, type],
  );

  const shouldHighlight =
    type === 'business'
      ? occGroup !== NON_INCOME_OCC_GROUP
      : shouldHighlightProp;

  return (
    <Column backgroundColor={colors.background} px={space[4]}>
      {!hideLegend && (
        <Label color={colors.primary} fontWeight="bold">
          {t(`eApp:${type}Address`)}
        </Label>
      )}
      {!noAddressOption && addressOptions.length > 1 ? (
        <AddressOptionCheckBox
          label={t(`eApp:${addressOptions[0].label}` as TFuncKey<['eApp']>)}
          checked={selectedAddressOption === addressOptions[0].value}
          onChange={onChangeAddressOption}
          disabled={disabledForm}
        />
      ) : (
        <Box h={7} />
      )}
      {isNewAddress && (
        <Box
          gap={space[3]}
          mt={space[hideLegend ? 0 : 3]}
          key={type === 'business' ? occGroup : type}>
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine1`}
            label={t('eApp:addressLine1')}
            hint={t('eApp:addressLine1.hint')}
            shouldHighlightOnUntouched={
              value =>
                Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
            }
            initialHighlight={initialHighlight}
            shouldUnregister={true}
          />
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine2`}
            label={t('eApp:addressLine2')}
            hint={t('eApp:addressLine2.hint')}
            style={eAppCommonStyles.phoneTextField}
          />
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine3`}
            label={t('eApp:addressLine3')}
            style={eAppCommonStyles.phoneTextField}
          />
          {/* 1 */}
          <Row gap={space[3]}>
            <Input
              control={control}
              as={SearchableDropdown<Country, string>}
              name={`${type}Country`}
              label={t('eApp:country')}
              data={optionList?.COUNTRY.options ?? []}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={[eAppCommonStyles.phoneTextField, { flex: 1 }]}
              onChange={onCountryChange}
              searchable
              shouldHighlightOnUntouched={
                value =>
                  Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={initialHighlight}
            />
            {isMY ? (
              <Input
                control={control}
                as={SearchableDropdown<Postcode<string, 'ib'>, string>}
                name={`${type}PostCode`}
                label={t('eApp:postcode')}
                data={postCodeList}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={[eAppCommonStyles.phoneTextField, { flex: 1 }]}
                onChange={onPostCodeChange}
                searchable
                shouldHighlightOnUntouched={
                  value =>
                    Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
                }
                initialHighlight={initialHighlight}
              />
            ) : (
              <Input
                control={control}
                as={TextField}
                name={`${type}PostCode`}
                label={t('eApp:postcode')}
                style={[eAppCommonStyles.phoneTextField, { flex: 1 }]}
                maxLength={10}
                shouldHighlightOnUntouched={
                  value =>
                    Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
                }
                initialHighlight={initialHighlight}
              />
            )}
          </Row>
          {/* 2 */}
          <Box gap={space[3]}>
            {isMY ? (
              <Input
                control={control}
                as={
                  SearchableDropdown<{ value: string; label: string }, string>
                }
                name={`${type}City`}
                label={t('eApp:city')}
                data={cityList}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={eAppCommonStyles.phoneTextField}
                searchable
                shouldHighlightOnUntouched={
                  value =>
                    Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
                }
                initialHighlight={initialHighlight}
              />
            ) : (
              <Input
                control={control}
                as={TextField}
                name={`${type}City`}
                label={t('eApp:city')}
                style={eAppCommonStyles.phoneTextField}
                maxLength={60}
                shouldHighlightOnUntouched={
                  value =>
                    Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
                }
                initialHighlight={initialHighlight}
              />
            )}
            <Input
              control={control}
              as={SearchableDropdown<{ value: string; label: string }, string>}
              name={`${type}State`}
              label={t('eApp:state')}
              data={stateList}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.phoneTextField}
              searchable
              shouldHighlightOnUntouched={
                value =>
                  Boolean(shouldHighlight && !value) && Platform.OS === 'ios' // TODO: remove later
              }
              initialHighlight={initialHighlight}
            />
          </Box>
        </Box>
      )}
    </Column>
  );
};

const AddressOptionCheckBox = styled(Checkbox)(({ theme }) => ({
  marginTop: theme.space[3],
  alignSelf: 'stretch',
}));
