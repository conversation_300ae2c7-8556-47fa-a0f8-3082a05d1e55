import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useGetProductList,
  useProductImageQuery,
} from 'features/productSelection/hooks/useProducts';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { formatCurrencyWithMask } from 'utils';
import { useRiders } from 'features/eAppV2/common/hooks/useRiders';
import round from 'lodash/round';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { getPremByPaymentMode } from '../../../../common/utils/quotationUtils';
import { ENTITY_ADVANCE_PREMIUM_RATE } from '../form/advancePremium/useAdvancePremium';
import { PaymentMode } from 'types/proposal';
import { AdvancePayment } from '../form/usePaymentForm';

export const ENTITY_PREMIUM_RATE = 1.08;

const usePaymentInfo = ({
  advancePayment,
}: {
  advancePayment: AdvancePayment;
}) => {
  const { t } = useTranslation(['eApp', 'common']);
  const quotation = useSelectedQuotation();
  const { data: optionList } = useGetOptionList<'ib'>();
  const isEntity = useCheckEntity();

  const riderData = useRiders(quotation);
  const basicPlan = quotation?.plans[0];

  const channel = useGetCubeChannel();
  const { data: products } = useGetProductList({ channel });
  const product = useMemo(() => {
    return products?.find(e => e.pid === basicPlan?.pid);
  }, [basicPlan?.pid, products]);
  const { data: imageUrl } = useProductImageQuery(product?.productThumbnailUrl);

  const advancePremium = advancePayment.amount;
  const advancePremiumDuration = advancePayment.duration;

  const paymentMode = useMemo(() => {
    return (
      optionList?.PAYMENTMODE4.options.find(
        e => e.value === quotation?.basicInfo.paymentMode,
      )?.label ?? t('eApp:paymentMode.L')
    );
  }, [optionList?.PAYMENTMODE4.options, quotation?.basicInfo.paymentMode, t]);

  const premium = useMemo(() => {
    return formatCurrencyWithMask(getPremByPaymentMode(quotation), 2);
  }, [quotation]);

  const regularCashPayment = useMemo(() => {
    return optionList?.PAYOUT_OPTION.options.find(
      e => e.value === basicPlan?.payoutOption,
    )?.label;
  }, [basicPlan?.payoutOption, optionList?.PAYOUT_OPTION.options]);

  const sst = useMemo(() => {
    const initialSST = quotation?.summary?.extraPrem || 0;
    const advanceSST = advancePremium * (ENTITY_PREMIUM_RATE - 1);
    return isEntity ? round(initialSST + advanceSST, 2) : undefined;
  }, [advancePremium, isEntity, quotation]);

  const initialPremium = useMemo(() => {
    const advanceAmount =
      advancePremium * (isEntity ? ENTITY_ADVANCE_PREMIUM_RATE : 1);
    return advanceAmount
      ? round(advanceAmount + (quotation?.summary.initialPrem ?? 0), 2)
      : quotation?.summary.initialPrem ?? 0;
  }, [advancePremium, isEntity, quotation?.summary.initialPrem]);

  const formattedInitialPremium = useMemo(() => {
    return formatCurrencyWithMask(initialPremium, 2);
  }, [initialPremium]);

  const originalInitialPremium = useMemo(() => {
    const advanceAmount = round(
      (getPremByPaymentMode(quotation) ?? 0) *
        advancePremiumDuration *
        (isEntity ? ENTITY_ADVANCE_PREMIUM_RATE : 1),
      2,
    );
    return advanceAmount
      ? round(advanceAmount + (quotation?.summary.initialPrem ?? 0), 2)
      : quotation?.summary.initialPrem ?? 0;
  }, [advancePremiumDuration, isEntity, quotation]);

  const formattedOriginalInitialPremium = useMemo(() => {
    return formatCurrencyWithMask(originalInitialPremium, 2);
  }, [originalInitialPremium]);

  const isAnnualAdvancePremium = useMemo(() => {
    return (
      quotation?.basicInfo.paymentMode === PaymentMode.ANNUAL &&
      quotation?.cubeMetadata?.supportedAdvancePaymentModes?.includes(
        PaymentMode.ANNUAL,
      )
    );
  }, [
    quotation?.basicInfo.paymentMode,
    quotation?.cubeMetadata?.supportedAdvancePaymentModes,
  ]);

  return {
    imageUrl,
    basicPlan,
    formattedInitialPremium,
    formattedOriginalInitialPremium,
    initialPremium,
    originalInitialPremium,
    paymentMode,
    premium,
    advancePremiumDuration,
    advancePremium,
    sst,
    quotation,
    regularCashPayment,
    riderData,
    isAnnualAdvancePremium,
  };
};

export default usePaymentInfo;
