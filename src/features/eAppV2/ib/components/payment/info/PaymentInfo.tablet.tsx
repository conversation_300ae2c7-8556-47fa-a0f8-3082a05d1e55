import React from 'react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, H6, H8, Icon, Row } from 'cube-ui-components';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import { formatCurrencyWithMask, i18n } from 'utils';
import PaymentItemDetail from './PaymentItemDetail';
import usePaymentInfo from './usePaymentInfo';
import { AdvancePayment } from '../form/usePaymentForm';

const PaymentInfoTablet = ({
  advancePayment,
}: {
  advancePayment: AdvancePayment;
}) => {
  const { t } = useTranslation(['eApp', 'common']);
  const { sizes, space, colors } = useTheme();

  const {
    imageUrl,
    basicPlan,
    formattedInitialPremium: initialPremium,
    formattedOriginalInitialPremium: originalInitialPremium,
    paymentMode,
    premium,
    advancePremiumDuration,
    advancePremium,
    sst,
    quotation,
    regularCashPayment,
    riderData,
    isAnnualAdvancePremium,
  } = usePaymentInfo({ advancePayment });

  return (
    <Container bounces={false} showsVerticalScrollIndicator={false}>
      <Box h={space[8]} />
      <Row alignContent="center" alignItems="center">
        <ProductImage source={{ uri: imageUrl }} resizeMode="cover" />
        <Box width={sizes[2]} />
        <H6 fontWeight="bold">
          {getProductName(basicPlan?.productName, i18n.language)}
        </H6>
      </Row>
      <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
        <Icon.Coin2 fill={colors.secondary} />
        <Box width={sizes[1]} />
        <H8 fontWeight="medium">{t('eApp:payment.initialPaymentDetails')}</H8>
      </Row>
      <PaymentItemDetail
        title={t('eApp:payment.initialPaymentTotal')}
        content={initialPremium}
        oldContent={
          originalInitialPremium === initialPremium
            ? undefined
            : originalInitialPremium
        }
        isHighlight
        isCurrency
        currency={t('common:rm')}
      />
      {isAnnualAdvancePremium ? (
        <>
          <PaymentItemDetail
            title={t('eApp:payment.advancePremium.firstYearPremium')}
            content={formatCurrencyWithMask(
              quotation?.summary.initialPrem ?? 0,
              2,
            )}
            isCurrency
            currency={t('common:rm')}
          />
          {advancePremium > 0 && (
            <PaymentItemDetail
              title={t('eApp:payment.advancePremium.secondYearPremium')}
              content={formatCurrencyWithMask(advancePremium, 2)}
              isCurrency
              currency={t('common:rm')}
            />
          )}
        </>
      ) : (
        <PaymentItemDetail
          title={t('eApp:payment.premium', { mode: paymentMode })}
          content={premium}
          isCurrency
          currency={t('common:rm')}
        />
      )}
      {Boolean(advancePremiumDuration) && !isAnnualAdvancePremium && (
        <PaymentItemDetail
          title={t('eApp:payment.advancePremium.title')}
          content={formatCurrencyWithMask(advancePremium, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}
      {Boolean(sst) && (
        <PaymentItemDetail
          title={t('eApp:payment.sale')}
          content={formatCurrencyWithMask(sst, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}

      {Boolean(basicPlan?.stampDuty) && (
        <PaymentItemDetail
          title={t('eApp:payment.stamp')}
          content={formatCurrencyWithMask(basicPlan?.stampDuty, 2)}
          isCurrency
          currency={t('common:rm')}
        />
      )}

      <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
        <Icon.Document fill={colors.secondary} />
        <Box width={sizes[1]} />
        <H8 fontWeight="medium">{t('eApp:payment.policy')}</H8>
      </Row>
      <PaymentItemDetail
        title={t('eApp:payment.sumAssured')}
        content={t('common:withCurrency', {
          amount: formatCurrencyWithMask(basicPlan?.sumAssured, 2),
        })}
      />
      <PaymentItemDetail
        title={t('eApp:payment.type')}
        content={basicPlan?.paymentType ?? ''}
      />
      <PaymentItemDetail
        title={t('eApp:payment.policy.term')}
        content={t('common:withYears', {
          year: basicPlan?.policyTerm ?? 0,
        })}
      />
      <PaymentItemDetail
        title={t('eApp:payment.premium.mode')}
        content={paymentMode}
      />
      <PaymentItemDetail
        title={t('eApp:payment.premium.term')}
        content={t('common:withYears', {
          year: quotation?.plans[0].premiumTerm ?? 0,
        })}
      />
      {regularCashPayment !== undefined && (
        <PaymentItemDetail
          title={t('eApp:payment.cash')}
          content={regularCashPayment}
        />
      )}
      {riderData && riderData.length > 0 && (
        <>
          <Row alignItems="center" marginTop={sizes[5]} marginBottom={sizes[3]}>
            <Icon.Document fill={colors.secondary} />
            <Box width={sizes[1]} />
            <H8 fontWeight="medium">{t('eApp:payment.coverage')}</H8>
          </Row>
          {riderData?.map(item => (
            <PaymentItemDetail
              key={item.pid}
              title={getProductName(item?.productName, i18n.language)}
              content={
                typeof item?.sumAssured === 'number'
                  ? t('common:withCurrency', {
                      amount: formatCurrencyWithMask(item?.sumAssured, 2),
                    })
                  : '--'
              }
            />
          ))}
        </>
      )}
      <Box h={space[4]} />
    </Container>
  );
};

const Container = styled.ScrollView(({ theme }) => ({
  backgroundColor: theme.colors.background,
  flex: 1,
}));

const ProductImage = styled.Image(({ theme: { sizes, borderRadius } }) => ({
  width: sizes[12],
  height: sizes[12],
  borderRadius: borderRadius.small,
}));

export default PaymentInfoTablet;
