import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';

import {
  Box,
  H6,
  H8,
  Icon,
  Row,
  H7,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import { formatCurrencyWithMask, i18n } from 'utils';
import usePaymentInfo from './usePaymentInfo';
import { AdvancePayment } from '../form/usePaymentForm';

const PaymentInfoPhone = ({
  advancePayment,
}: {
  advancePayment: AdvancePayment;
}) => {
  const { t } = useTranslation(['eApp', 'common']);
  const { space, colors, borderRadius } = useTheme();
  const [showPlan, setShowPlan] = useState(false);

  const {
    basicPlan,
    formattedInitialPremium: initialPremium,
    formattedOriginalInitialPremium: originalInitialPremium,
    paymentMode,
    premium,
    advancePremiumDuration,
    advancePremium,
    sst,
    quotation,
    regularCashPayment,
    riderData,
    isAnnualAdvancePremium,
  } = usePaymentInfo({ advancePayment });

  return (
    <Box
      bgColor={colors.background}
      borderRadius={borderRadius['large']}
      paddingX={space[4]}
      paddingY={space[3]}>
      <TouchableOpacity onPress={() => setShowPlan(!showPlan)}>
        <Row alignItems={'center'} justifyContent="space-between">
          <H7 fontWeight="bold">{t('eApp:payment.yourPlan')}</H7>
          {showPlan ? <Icon.ChevronUp /> : <Icon.ChevronDown />}
        </Row>
        <Row
          alignItems="center"
          justifyContent="space-between"
          mt={space[2]}
          columnGap={space[4]}>
          <H8 color={colors.palette.fwdGreyDarkest}>
            {t('eApp:payment.product')}
          </H8>
          <H8 fontWeight="bold">
            {getProductName(basicPlan?.productName, i18n.language)}
          </H8>
        </Row>
        <Box
          backgroundColor={colors.palette.fwdOrange[5]}
          padding={space[2]}
          mt={space[2]}>
          <Row alignItems={'center'} justifyContent="space-between">
            <H8 fontWeight="bold">{t('eApp:payment.initialPaymentTotal')}</H8>
            <Box alignItems="flex-end">
              <Row>
                <SmallBody
                  style={{
                    marginRight: space[1],
                  }}
                  color={colors.palette.fwdOrange[100]}>
                  {t('common:rm')}
                </SmallBody>
                <H6 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                  {initialPremium}
                </H6>
              </Row>
              {originalInitialPremium !== initialPremium && (
                <SmallLabel
                  style={{ textDecorationLine: 'line-through' }}
                  color={colors.palette.fwdGreyDarkest}>
                  {t('common:rm')} {originalInitialPremium}
                </SmallLabel>
              )}
            </Box>
          </Row>
        </Box>
      </TouchableOpacity>
      {showPlan && (
        <Box>
          <Box h={space[8]} />
          <Row alignItems="center" marginBottom={space[2]}>
            <H7 fontWeight="bold">{t('eApp:payment.initialPaymentDetails')}</H7>
          </Row>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.initialPaymentTotal')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>
                {t('common:rm')} {initialPremium}
              </H8>
            </Box>
          </Row>
          {isAnnualAdvancePremium ? (
            <>
              <Row gap={space[3]} marginBottom={space[2]}>
                <Box flex={1}>
                  <H8 color={colors.palette.fwdGreyDarkest}>
                    {t('eApp:payment.advancePremium.firstYearPremium')}
                  </H8>
                </Box>
                <Box flex={1}>
                  <H8>
                    {t('common:rm')}{' '}
                    {formatCurrencyWithMask(
                      quotation?.summary.initialPrem ?? 0,
                      2,
                    )}
                  </H8>
                </Box>
              </Row>
              {advancePremium > 0 && (
                <Row gap={space[3]} marginBottom={space[2]}>
                  <Box flex={1}>
                    <H8 color={colors.palette.fwdGreyDarkest}>
                      {t('eApp:payment.advancePremium.secondYearPremium')}
                    </H8>
                  </Box>
                  <Box flex={1}>
                    <H8>
                      {t('common:rm')}{' '}
                      {formatCurrencyWithMask(advancePremium, 2)}
                    </H8>
                  </Box>
                </Row>
              )}
            </>
          ) : (
            <Row gap={space[3]} marginBottom={space[2]}>
              <Box flex={1}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.premium', { mode: paymentMode })}
                </H8>
              </Box>
              <Box flex={1}>
                <H8>
                  {t('common:rm')} {premium}
                </H8>
              </Box>
            </Row>
          )}
          {Boolean(advancePremiumDuration) && !isAnnualAdvancePremium && (
            <Row gap={space[3]} marginBottom={space[2]}>
              <Box flex={1}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.advancePremium.title')}
                </H8>
              </Box>
              <Box flex={1}>
                <H8>
                  {t('common:rm')} {formatCurrencyWithMask(advancePremium, 2)}
                </H8>
              </Box>
            </Row>
          )}
          {Boolean(sst) && (
            <Row gap={space[3]} marginBottom={space[2]}>
              <Box flex={1}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.sale')}
                </H8>
              </Box>
              <Box flex={1}>
                <H8>
                  {t('common:rm')} {formatCurrencyWithMask(sst, 2)}
                </H8>
              </Box>
            </Row>
          )}

          {Boolean(basicPlan?.stampDuty) && (
            <Row gap={space[3]} marginBottom={space[2]}>
              <Box flex={1}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.stamp')}
                </H8>
              </Box>
              <Box flex={1}>
                <H8>
                  {t('common:rm')}{' '}
                  {formatCurrencyWithMask(basicPlan?.stampDuty, 2)}
                </H8>
              </Box>
            </Row>
          )}

          <Divider />

          <Box marginBottom={space[3]}>
            <H8 fontWeight="bold">{t('eApp:payment.policy')}</H8>
          </Box>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.sumAssured')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>
                {t('common:withCurrency', {
                  amount: formatCurrencyWithMask(basicPlan?.sumAssured, 2),
                })}
              </H8>
            </Box>
          </Row>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.type')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>{basicPlan?.paymentType ?? ''}</H8>
            </Box>
          </Row>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.policy.term')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>
                {t('common:withYears', {
                  year: basicPlan?.policyTerm ?? 0,
                })}
              </H8>
            </Box>
          </Row>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.premium.mode')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>{paymentMode}</H8>
            </Box>
          </Row>
          <Row gap={space[3]} marginBottom={space[2]}>
            <Box flex={1}>
              <H8 color={colors.palette.fwdGreyDarkest}>
                {t('eApp:payment.premium.term')}
              </H8>
            </Box>
            <Box flex={1}>
              <H8>
                {t('common:withYears', {
                  year: quotation?.plans[0].premiumTerm ?? 0,
                })}
              </H8>
            </Box>
          </Row>
          {regularCashPayment !== undefined && (
            <Row gap={space[3]} marginBottom={space[2]}>
              <Box flex={1}>
                <H8 color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.cash')}
                </H8>
              </Box>
              <Box flex={1}>
                <H8>{regularCashPayment}</H8>
              </Box>
            </Row>
          )}
          {riderData && riderData.length > 0 && (
            <>
              <Divider />
              <Box marginBottom={space[2]}>
                <H8 fontWeight="bold">{t('eApp:payment.coverage')}</H8>
              </Box>
              <Box gap={space[2]}>
                {riderData?.map(item => (
                  <Row gap={space[3]}>
                    <Box flex={1}>
                      <H8 color={colors.palette.fwdGreyDarkest}>
                        {getProductName(item?.productName, i18n.language)}
                      </H8>
                    </Box>
                    <Box flex={1}>
                      <H8>
                        {typeof item?.sumAssured === 'number'
                          ? t('common:withCurrency', {
                              amount: formatCurrencyWithMask(
                                item?.sumAssured,
                                2,
                              ),
                            })
                          : '--'}
                      </H8>
                    </Box>
                  </Row>
                ))}
              </Box>
            </>
          )}
        </Box>
      )}
    </Box>
  );
};

const Divider = styled(View)(({ theme }) => ({
  height: 1,
  width: '100%',
  backgroundColor: theme.colors.palette.fwdGrey[100],
  marginVertical: theme.space[3],
}));

export default PaymentInfoPhone;
