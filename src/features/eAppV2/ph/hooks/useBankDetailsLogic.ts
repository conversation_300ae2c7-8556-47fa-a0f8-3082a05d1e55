import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCase } from 'hooks/useGetCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useEffect } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormHandleSubmit,
  UseFormSetValue,
  UseFormTrigger,
  UseFormWatch,
  useWatch,
} from 'react-hook-form';
import { toApplicationBankDetails } from '../utils/caseUtils';
import {
  BANK_PESO_CODE,
  BANK_USD_CODE,
  BankDetailsForm,
  MAX_LENGTH_BY_BANK_CODE,
} from '../validations/renewalPaymentSetupValidation';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { DirectCredit } from 'types/case';

const useBankDetailLogic = ({
  control,
  watch,
  trigger,
  setValue,
  handleSubmit,
  getValues,
}: {
  control: Control<BankDetailsForm>;
  watch: UseFormWatch<BankDetailsForm>;
  trigger: UseFormTrigger<BankDetailsForm>;
  setValue: UseFormSetValue<BankDetailsForm>;
  handleSubmit: UseFormHandleSubmit<BankDetailsForm>;
  getValues: UseFormGetValues<BankDetailsForm>;
}) => {
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();
  const quotation = useSelectedQuotation();
  const next = useEAppProgressBarStore(state => state.next);
  const setData = useEAppStore(state => state.updateBankDetails);
  const agentId = useBoundStore(state => state.auth.agentCode);
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj, isFetching: isGettingCase } = useGetCase(caseId ?? '');
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const bankList =
    quotation?.basicInfo.currency === 'USD'
      ? optionList?.BANK_NAME_USD_LIST?.options
      : optionList?.BANK_NAME_PESO_LIST?.options;

  const formatAccountNumber = (accountNumber: string, maxDigits: number) => {
    return accountNumber.padStart(maxDigits, '0');
  };
  const { bankName, accountNumber } = useWatch({ control });

  const onBlurCardNumberInput = () => {
    const bankName = watch('bankName');
    const accountNumber = watch('accountNumber') || '';
    if (!bankName && accountNumber) {
      return;
    }
    let valueAccountNumber = accountNumber;
    switch (bankName) {
      case BANK_PESO_CODE.BANCO_DE_ORO:
      case BANK_USD_CODE.BANCO_DE_ORO:
        valueAccountNumber = formatAccountNumber(
          accountNumber,
          MAX_LENGTH_BY_BANK_CODE.BANCO_DE_ORO,
        );
        break;
      case BANK_PESO_CODE.BANK_OF_THE_PHILIPPINE_ISLANDS:
      case BANK_USD_CODE.BANK_OF_THE_PHILIPPINE_ISLANDS:
        valueAccountNumber = formatAccountNumber(
          accountNumber,
          MAX_LENGTH_BY_BANK_CODE.BANK_OF_THE_PHILIPPINE_ISLANDS,
        );
        break;
      case BANK_PESO_CODE.METRO_BANK:
      case BANK_USD_CODE.METRO_BANK:
      case BANK_PESO_CODE.SECURITY_BANK:
      case BANK_USD_CODE.SECURITY_BANK:
        valueAccountNumber = formatAccountNumber(
          accountNumber,
          MAX_LENGTH_BY_BANK_CODE.METRO_BANK_OR_SECURITY_BANK,
        );
        break;
    }

    setValue('accountNumber', valueAccountNumber, {
      shouldTouch: true,
      shouldValidate: true,
    });
  };

  useEffect(() => {
    if (accountNumber) {
      trigger('accountNumber');
    }
  }, [bankName, accountNumber, trigger]);

  const onSubmit = (autoNext = true, saved = false) => {
    handleSubmit(async data => {
      setData(data);
      if (agentId && caseId && caseObj) {
        await saveApplication({
          caseId,
          data: {
            ...caseObj.application,
            // @ts-expect-error Application type is not specific to 'ph'
            directCredit: {
              ...(caseObj.application?.directCredit as DirectCredit<'ph'>),
              ...(caseObj.application?.directCredit?.paymentMethod
                ? undefined
                : { paymentMethod: 'NA' }),
              ...toApplicationBankDetails(data, agentId),
            },
          },
        });
      }
      if (!saved) {
        next(false, autoNext);
      }
    })();
  };

  const onSave = async () => {
    const data = getValues();
    setData(data);
    if (agentId && caseId && caseObj) {
      await saveApplication({
        caseId,
        data: {
          ...caseObj.application,
          // @ts-expect-error Application type is not specific to 'ph'
          directCredit: {
            ...(caseObj.application?.directCredit as DirectCredit<'ph'>),
            ...(caseObj.application?.directCredit?.paymentMethod
              ? undefined
              : { paymentMethod: 'NA' }),
            ...toApplicationBankDetails(data, agentId),
          },
        },
      });
    }
  };

  return {
    optionList,
    isFetchingOptionList,
    onBlurCardNumberInput,
    isNarrowScreen,
    bankList,
    isGettingCase,
    isSavingApplication,
    onSubmit,
    onSave,
  };
};

export default useBankDetailLogic;

export type DataPrivacyModal = ReturnType<typeof useBankDetailLogic>;
