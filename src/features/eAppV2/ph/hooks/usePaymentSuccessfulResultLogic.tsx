import {
  NavigationProp,
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import {
  RemoteSellingStatus,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import { useReplaceQuotation } from 'features/proposal/hooks/useReplaceQuotation';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetQuotation } from 'hooks/useGetQuotation';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { RootStackParamList } from 'types';
import { Application } from 'types/case';
import { Plan } from 'types/quotation';
import { shallow } from 'zustand/shallow';
import { toApplicationInitialPayment } from '../utils/caseUtils';
import { useUpdateSbcProducts } from './useUpdateSbcProducts';
import IconPaymentWallet from '../components/payment/icons/IconPaymentWallet';
import LottieView from 'lottie-react-native';
import { enhancedRemoteSellingEnabled } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import IconApplicationComplete from '../components/payment/icons/IconApplicationComplete';
import {
  addErrorBottomToast,
  addToast,
  Icon,
  LoadingIndicator,
  Toast,
} from 'cube-ui-components';
import { PartyRole } from 'types/party';
import { AxiosError } from 'axios';
import RootSiblings from 'react-native-root-siblings';
import { useSendRSEmailConfirm } from 'features/eAppV2/ph/hooks/useSendRSEmailConfirm';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { useQueryClient } from '@tanstack/react-query';

export const usePaymentSuccessfulResultLogic = () => {
  const { colors } = useTheme();
  const { t } = useTranslation(['eApp']);
  const [sbcProducts, setSbcProducts] = useState<Array<string>>([]);
  const cubeChannel = useGetCubeChannel();
  const toastRef = useRef<RootSiblings>();
  const queryClient = useQueryClient();

  const { updateSbcProduct, isLoading: isSavingSbcProduct } =
    useUpdateSbcProducts();

  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const {
    applicationNum,
    policyOwnerPersonalInfo,
    insuredPersonalInfo,
    initialPayment,
    initialPaymentOffline,
    activeQuotationId,
    isRemoteSelling,
    isRemoteSellingInsured,
    remoteSellingStatus,
    updateRemoteSellingStatus,
    isPIEqualPO,
  } = useEAppStore(
    state => ({
      applicationNum: state.applicationNum,
      policyOwnerPersonalInfo: state.policyOwnerPersonalInfo,
      insuredPersonalInfo: state.insuredPersonalInfo,
      initialPayment: state.initialPayment,
      initialPaymentOffline: state.initialPaymentOffline,
      activeQuotationId: state.activeQuotationId,
      isRemoteSelling: state.isRemoteSelling,
      isRemoteSellingInsured: state.isRemoteSellingInsured,
      remoteSellingStatus: state.remoteSellingStatus,
      updateRemoteSellingStatus: state.updateRemoteSellingStatus,
      isPIEqualPO: state.isPIEqualPO,
    }),
    shallow,
  );
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: quotation } = useGetQuotation(
    caseId || '',
    activeQuotationId || '',
    Boolean(caseId && activeQuotationId),
  );
  const { mutateAsync: getCase, isLoading: isGettingCase } =
    useGetCaseManually();
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { mutateAsync: replaceQuotation, isLoading: isSavingQuotation } =
    useReplaceQuotation({ onSuccess: () => null });
  const { mutate: sendConfirmationEmail } = useSendRSEmailConfirm();

  const isLoading =
    isGettingCase ||
    isSavingApplication ||
    isSavingQuotation ||
    isSavingSbcProduct;
  const [isBackToHome, setBackToHome] = useState(false);
  const saveInitialPayment = useCallback(async () => {
    if (!caseId) {
      throw new Error('Case id not found when saving initial payment');
    }
    const applicationInitialPayment = toApplicationInitialPayment(
      initialPayment,
      initialPaymentOffline,
    );
    if (
      !applicationInitialPayment.payType ||
      !applicationInitialPayment.paymentOption
    ) {
      throw new Error('Initial payment info is empty');
    }
    const caseObj = await getCase(caseId);
    const application: Application = caseObj.application ?? {};
    application.initialPayment = applicationInitialPayment;
    await saveApplication({
      caseId,
      data: application,
    });
  }, [getCase, saveApplication, initialPayment, initialPaymentOffline, caseId]);

  const riders = useMemo(
    () =>
      (quotation?.plans.filter(Boolean) as Plan[])?.slice?.(
        1,
        quotation?.plans.length,
      ) ?? [],
    [quotation?.plans],
  );

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const dataPayment =
    useRoute<RouteProp<RootStackParamList, 'PaymentResult'>>().params;
  const isOffline =
    dataPayment?.fromScreen === 'OfflinePayment' &&
    !isRemoteSelling &&
    !isRemoteSellingInsured;
  const isPending =
    dataPayment?.fromScreen === 'PendingPayment' &&
    !isRemoteSelling &&
    !isRemoteSellingInsured;
  const isOnlineButPendingRS =
    dataPayment?.fromScreen === 'OnlinePayment' &&
    enhancedRemoteSellingEnabled &&
    (isRemoteSelling || isRemoteSellingInsured);
  const isOfflineButPendingRS =
    dataPayment?.fromScreen === 'OfflinePayment' &&
    enhancedRemoteSellingEnabled &&
    (isRemoteSelling || isRemoteSellingInsured);

  const pendingRemoteSellingTitle = useMemo(() => {
    switch (remoteSellingStatus) {
      case RemoteSellingStatus.PO_PI_PENDING:
        return t('eApp:payment.resultRemoteSellingPending.pendingPOPI');
      case RemoteSellingStatus.PO_PENDING:
        return t('eApp:payment.resultRemoteSellingPending.pendingPO');
      case RemoteSellingStatus.PI_PENDING:
        return t('eApp:payment.resultRemoteSellingPending.pendingPI');
      default:
        return '';
    }
  }, [remoteSellingStatus, t]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const goToACR = async () => {
    try {
      if (caseId && quotation) {
        if (sbcProducts.length > 0) {
          await updateSbcProduct(sbcProducts);
        }
        if (!isOnlineButPendingRS && !isOfflineButPendingRS) {
          await saveInitialPayment();
        }
        await replaceQuotation({
          caseId,
          quotation: {
            ...quotation,
            isSelectedQuotation: true,
          },
          quotationName: quotation?.quotationName,
        });
        navigation.navigate('ACR');
      }
    } catch (e) {
      console.log('error', e);
    }
  };

  const goToHome = async () => {
    setBackToHome(true);

    try {
      if (caseId && quotation) {
        await updateSbcProduct(sbcProducts);
        if (!isOnlineButPendingRS && !isOfflineButPendingRS) {
          await saveInitialPayment();
        }
        await replaceQuotation({
          caseId,
          quotation: {
            ...quotation,
            isSelectedQuotation: true,
          },
          quotationName: quotation?.quotationName,
        });
        navigation.navigate('Main', {
          screen: 'Home',
        });
      }
    } finally {
      setBackToHome(false);
    }
  };

  const { sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const statusElements = useMemo(() => {
    let icon: React.ReactNode = <Icon.TickCircleFill size={sizes[15]} />;

    if (enhancedRemoteSellingEnabled) {
      if (remoteSellingStatus) {
        icon = (
          <LottieView
            autoPlay
            loop={false}
            style={{
              width: sizes[isTabletMode ? 18 : 15],
              height: sizes[isTabletMode ? 18 : 15],
            }}
            source={require('assets/lottie/application-complete.json')}
          />
        );
      } else if (isOfflineButPendingRS) {
        icon = <IconApplicationComplete size={isTabletMode ? 72 : 60} />;
      } else if (isOnlineButPendingRS) {
        icon = <IconPaymentWallet />;
      }
    }

    let title = t('eApp:payment.resultTitle');

    if (enhancedRemoteSellingEnabled) {
      if (remoteSellingStatus) {
        title = t('eApp:payment.resultRemoteSellingComplete.title');
      } else if (isOnlineButPendingRS) {
        title = t('eApp:payment.paymentSuccessful');
      }
    }

    return { icon, title };
  }, [
    isOfflineButPendingRS,
    isOnlineButPendingRS,
    remoteSellingStatus,
    isTabletMode,
    sizes,
    t,
  ]);

  const isInRemoteSellingFlow =
    enhancedRemoteSellingEnabled &&
    (isOnlineButPendingRS || isOfflineButPendingRS);
  const isPendingByRemoteSelling =
    isInRemoteSellingFlow &&
    remoteSellingStatus !== RemoteSellingStatus.COMPLETED;
  const isPrimaryLoading = !isBackToHome && isLoading;
  const isSecondaryLoading = isBackToHome && isLoading;
  const handleResendEmail = useCallback(async () => {
    if (caseId) {
      toastRef.current = addToast(
        [
          {
            message: 'Resending email',
            IconLeft: LoadingIndicator,
          },
        ],
        true,
      );
      const caseObj = await getCase(caseId);
      const policyOwner = caseObj.parties?.find(p =>
        p.roles.includes(PartyRole.PROPOSER),
      );
      const uwmeResult = policyOwner?.uw?.result;
      if (uwmeResult) {
        const isHCBProduct = uwmeResult?.exclusions.some(exclusion =>
          exclusion.furtherChecks.benefit.includes('MEDEX'),
        );
        const isCIProduct = uwmeResult?.exclusions.some(exclusion =>
          exclusion.furtherChecks.benefit.includes('CI'),
        );
        sendConfirmationEmail(
          {
            caseId,
            exclusiveLetter: isHCBProduct
              ? 'HCB'
              : isCIProduct
              ? 'CI'
              : undefined,
          },
          {
            onSuccess: () => {
              Toast.hide(toastRef.current);
              addToast([
                {
                  message: t('eApp:payment.resendEmail.successfully'),
                  IconLeft: Icon.Tick,
                },
              ]);
            },
            onError: error => {
              Toast.hide(toastRef.current);
              if ((error as AxiosError).status === 425) {
                addErrorBottomToast(
                  [
                    {
                      message: t('eApp:payment.resendEmail.limited'),
                      IconLeft: <Icon.Warning fill={colors.error} />,
                    },
                  ],
                  true,
                );
              } else {
                addErrorBottomToast(
                  [
                    {
                      message: t('eApp:payment.resendEmail.failed'),
                      IconLeft: <Icon.Warning fill={colors.error} />,
                    },
                  ],
                  true,
                );
              }
            },
          },
        );
      }
    }
  }, [caseId]);

  const handleRefreshPage = useCallback(async () => {
    if (caseId) {
      try {
        setAppLoading();
        const caseObj = await getCase(caseId);
        if (caseObj.remoteSelling) {
          if (caseObj.remoteSelling.finishedAt) {
            updateRemoteSellingStatus(RemoteSellingStatus.COMPLETED);
          } else if (
            caseObj.remoteSelling.isPoConfirmed === false &&
            caseObj.remoteSelling.isPiConfirmed === false
          ) {
            updateRemoteSellingStatus(RemoteSellingStatus.PO_PI_PENDING);
          } else if (caseObj.remoteSelling.isPoConfirmed === false) {
            updateRemoteSellingStatus(RemoteSellingStatus.PO_PENDING);
          } else if (caseObj.remoteSelling.isPiConfirmed === false) {
            updateRemoteSellingStatus(RemoteSellingStatus.PI_PENDING);
          }
        }
      } catch (error) {
        console.error('Error refreshing page:', error);
      } finally {
        setAppIdle();
      }
    }
  }, [caseId, getCase, setAppIdle, setAppLoading, updateRemoteSellingStatus]);

  useFocusEffect(
    useCallback(() => {
      saveInitialPayment();
      handleRefreshPage();
    }, [saveInitialPayment, handleRefreshPage]),
  );

  return {
    sbcProducts,
    setSbcProducts,
    cubeChannel,
    isLoading,
    isBackToHome,
    isOffline,
    isPending,
    isOnlineButPendingRS,
    remoteSellingStatus,
    pendingRemoteSellingTitle,
    isOfflineButPendingRS,
    policyOwnerPersonalInfo,
    insuredPersonalInfo,
    basePlan: quotation?.plans[0],
    quotation,
    riders,
    applicationNum,
    goToACR,
    goToHome,
    isNarrowScreen,
    isPIEqualPO,
    statusElements,
    isInRemoteSellingFlow,
    isPendingByRemoteSelling,
    isPrimaryLoading,
    isSecondaryLoading,
    handleResendEmail,
    handleRefreshPage,
  };
};
