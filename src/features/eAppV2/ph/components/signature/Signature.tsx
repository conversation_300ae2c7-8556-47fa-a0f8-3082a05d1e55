import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetSelectedActiveQuotation } from 'hooks/useSelectedQuotation';
import { Fragment, useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole, PartyType } from 'types/party';
import {
  agentSignatureTermsAndCondition,
  termsAndCondition,
  vulTermsAndCondition,
} from '../../constants/signatureTermsAndCondition';
import {
  enhancedRemoteSellingEnabled,
  fullRemoteSellingEnabled,
} from 'utils/context';
import SignatureBase, {
  SignatureTab,
  SignatureTerms,
} from 'features/eAppV2/common/components/signature/SignatureBase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { isDot, isLine } from 'features/eAppV2/common/utils/signatureUtils';
import { formatCurrency } from 'utils';
import SummaryModal from './modals/SummaryModal';
import useToggle from 'hooks/useToggle';
import { checkJuvenile } from '../../constants/partyUtils';
import { AGENT_BRANCHES, CHANNELS } from 'types/channel';
import { SolicitingSignaturePlaceholder } from 'features/eAppV2/ph/components/signature/components/SolicitingSignaturePlaceholder';
import { cloneDeep } from 'utils/helper/objectUtil';
import { LanguagesKeys } from 'utils/translation';
import VerifyInfoModal from 'features/eAppV2/ph/components/signature/modals/VerifyInfoModal';
import { useSendRSEmailConfirm } from 'features/eAppV2/ph/hooks/useSendRSEmailConfirm';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { addErrorBottomToast, Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useGetInitialPremium } from 'features/eAppV2/common/hooks/useGetInitialPremium';
import { DecisionState } from 'features/eAppV2/common/types/uwmeTypes';

export default function Signature({
  visible,
  onDismiss,
  onFinish,
  startingIndex,
  decisionState,
}: {
  visible: boolean;
  onDismiss: () => void;
  onFinish: () => void;
  startingIndex: number;
  decisionState: DecisionState;
}) {
  const { t } = useTranslation(['eApp']);
  const { colors } = useTheme();
  const { caseObj } = useGetActiveCase();
  const quotation = useGetSelectedActiveQuotation();
  const { data: agentInfo } = useGetAgentProfile();
  const { mutate: sendRSEmailConfirm, isLoading: sendingEmailConfirm } =
    useSendRSEmailConfirm();
  const { data: initialPremiumData } = useGetInitialPremium();
  const updateRemoteSellingStatus = useEAppStore(
    state => state.updateRemoteSellingStatus,
  );

  const exclusiveLetterType = useMemo(() => {
    const isHCBProduct = decisionState.exclusions.some(exclusion =>
      exclusion.furtherChecks.benefit.includes('MEDEX'),
    );
    const isCIProduct = decisionState.exclusions.some(exclusion =>
      exclusion.furtherChecks.benefit.includes('CI'),
    );
    if (isHCBProduct) {
      return 'HCB';
    }
    if (isCIProduct) {
      return 'CI';
    }
    return undefined;
  }, [decisionState]);

  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => isRole(p, PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const insured = useMemo(
    () => caseObj?.parties?.find(p => isRole(p, PartyRole.INSURED)),
    [caseObj?.parties],
  );

  const overridingRemoteSellingInsured = useMemo(() => {
    // If policy owner is remote selling, then insured should be remote selling
    if (policyOwner?.isRemoteSelling) {
      return true;
    }
    return undefined;
  }, [policyOwner]);

  const tabs = useMemo<SignatureTab[]>(() => {
    return [
      ...((caseObj?.parties
        ?.map(p => {
          if (isRole(p, PartyRole.PROPOSER)) {
            const isEntity = p.clientType === PartyType.ENTITY;
            const name = isEntity
              ? p.entity?.representative?.name?.fullName?.trim()
              : p.person?.name.fullName?.trim();
            return {
              role: isEntity
                ? t('eApp:signature.authorizedRepresentative')
                : t('eApp:signature.policyOwner'),
              signatureRole: 'proposers',
              declarationRole: isEntity
                ? t('eApp:signature.authorizedRepresentative')
                : t('eApp:signature.policyOwner'),
              name,
              partyId: p.id,
              terms: putAgreementIntoSignatureTerms(
                quotation?.plans?.[0]?.isVUL
                  ? vulTermsAndCondition
                  : termsAndCondition,
                t('eApp:signature.agreement', {
                  name,
                }),
              ),
              customerSeq: '1',
              shouldAllowRemoteSellingToggle:
                !fullRemoteSellingEnabled && !enhancedRemoteSellingEnabled,
            };
          }
          if (isRole(p, PartyRole.INSURED) && !checkJuvenile(p)) {
            return {
              role: t('eApp:signature.insured'),
              signatureRole: 'insureds',
              declarationRole: t('eApp:signature.insured'),
              name: p.person?.name.fullName?.trim(),
              partyId: p.id,
              terms: putAgreementIntoSignatureTerms(
                quotation?.plans?.[0]?.isVUL
                  ? vulTermsAndCondition
                  : termsAndCondition,
                t('eApp:signature.agreement', {
                  name: p.person?.name.fullName?.trim(),
                }),
              ),
              customerSeq: '1',
              shouldAllowRemoteSellingToggle:
                !fullRemoteSellingEnabled && !enhancedRemoteSellingEnabled,
              overridingRemoteSelling: overridingRemoteSellingInsured,
            };
          }
          return null;
        })
        .filter(Boolean) as SignatureTab[]) || []),
      ...(agentInfo?.branch.name === AGENT_BRANCHES.CODE_XL
        ? ([
            {
              role: t('eApp:signature.solicitingOfficer'),
              signatureRole: 'agent',
              declarationRole: t('eApp:signature.solicitingOfficer'),
              name: 'Roxanne R. Ramiscal',
              partyId: '',
              terms: putAgreementIntoSignatureTerms(
                agentSignatureTermsAndCondition,
                t('eApp:signature.agreement', {
                  name: 'Roxanne R. Ramiscal',
                }),
              ),
              customerSeq: '1',
              shouldAllowRemoteSellingToggle: false,
              customDrawPad: SolicitingSignaturePlaceholder,
              shouldByPassVerifySignature: true,
            },
          ] as SignatureTab[])
        : []),
      {
        role:
          agentInfo?.branch.name === AGENT_BRANCHES.CODE_XL
            ? t('eApp:signature.brokerRepresentativeOfficer')
            : t('eApp:signature.agent'),
        signatureRole: 'agent',
        declarationRole:
          agentInfo?.branch.name === AGENT_BRANCHES.CODE_XL
            ? t('eApp:signature.brokerRepresentativeOfficer')
            : agentInfo?.channel === CHANNELS.BANCA ||
              agentInfo?.channel === CHANNELS.AFFINITY
            ? t('eApp:signature.financialSolutionsConsultant')
            : t('eApp:signature.financialWealthPlanner'),
        name: agentInfo?.person.fullName?.trim() || 'N/A',
        partyId: '',
        terms: putAgreementIntoSignatureTerms(
          agentSignatureTermsAndCondition,
          t('eApp:signature.agreement', {
            name: agentInfo?.person.fullName?.trim(),
          }),
        ),
        customerSeq: '1',
        shouldAllowRemoteSellingToggle: false,
      },
    ];
  }, [
    agentInfo?.branch.name,
    agentInfo?.channel,
    agentInfo?.person.fullName,
    caseObj?.parties,
    quotation?.plans,
    t,
    overridingRemoteSellingInsured,
  ]);

  const [summaryVisible, showSummary, hideSummary] = useToggle();
  const summaryContent = useMemo(() => {
    if (quotation?.plans[0].premiumType === 'SP') {
      return t('eApp:signature.singlePayPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
      });
    } else if (quotation?.plans[0].paymentMode === 'M') {
      return t('eApp:signature.monthlyADAPlanSum', {
        initialAmount: `${formatCurrency(initialPremiumData?.initialPremium)} ${
          initialPremiumData?.currency || 'PHP'
        }`,
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        term: quotation?.plans[0].premiumTerm,
      });
    } else {
      return t('eApp:signature.regularPlanSum', {
        amount: `${formatCurrency(quotation?.summary.totalPrem)} ${
          quotation?.basicInfo.currency || 'PHP'
        }`,
        mode: t(
          `eApp:paymentMode.${
            quotation?.plans[0].paymentMode as 'A' | 'S' | 'M' | 'Q'
          }`,
        ).toLowerCase(),
        term: quotation?.plans[0].premiumTerm,
      });
    }
  }, [
    quotation?.plans,
    quotation?.summary.totalPrem,
    quotation?.basicInfo.currency,
    t,
    initialPremiumData,
  ]);

  const onFinishSignature = useCallback(() => {
    showSummary();
  }, [showSummary]);

  const onConfirmSummary = useCallback(() => {
    if (
      enhancedRemoteSellingEnabled &&
      caseObj?.id &&
      caseObj?.isRemoteSelling
    ) {
      sendRSEmailConfirm(
        {
          caseId: caseObj?.id,
          exclusiveLetter: exclusiveLetterType,
        },
        {
          onSuccess: () => {
            onDismiss?.();
            hideSummary();
            onFinish?.();
          },
          onError: () => {
            addErrorBottomToast([
              {
                message:
                  'An error occurred. Please try again later or contact support.',
                IconLeft: <Icon.Warning fill={colors.error} />,
              },
            ]);
          },
        },
      );
    } else {
      onDismiss?.();
      hideSummary();
      onFinish?.();
    }
    // onFinish?.();
  }, [
    caseObj?.id,
    caseObj?.isRemoteSelling,
    exclusiveLetterType,
    hideSummary,
    onDismiss,
    onFinish,
    sendRSEmailConfirm,
  ]);

  const { updateRemoteSelling, updateRemoteSellingInsured } = useEAppStore(
    state => ({
      updateRemoteSelling: state.updateRemoteSelling,
      updateRemoteSellingInsured: state.updateRemoteSellingInsured,
    }),
    shallow,
  );

  useEffect(() => {
    if (typeof policyOwner?.isRemoteSelling === 'boolean') {
      updateRemoteSelling(policyOwner?.isRemoteSelling);
    }
  }, [policyOwner?.isRemoteSelling, updateRemoteSelling]);

  useEffect(() => {
    if (policyOwner?.isRemoteSelling === true) {
      updateRemoteSellingInsured(policyOwner?.isRemoteSelling);
    } else if (
      typeof insured?.isRemoteSelling === 'boolean' &&
      !checkJuvenile(insured)
    ) {
      updateRemoteSellingInsured(insured?.isRemoteSelling);
    }
  }, [
    insured,
    insured?.isRemoteSelling,
    policyOwner?.isRemoteSelling,
    updateRemoteSellingInsured,
  ]);

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  const preventDismiss = useCallback(() => {}, []);

  const isRemoteSellingLayout =
    enhancedRemoteSellingEnabled && caseObj?.isRemoteSelling;

  return (
    <Fragment>
      <SignatureBase
        visible={visible}
        startingIndex={startingIndex}
        tabs={tabs}
        validateSignature={validateSignature}
        reviewTermsRequired
        defaultPlaceOfSigning="Philippines"
        f2fLevel="party"
        onDismiss={onDismiss}
        onDismissByNext={isRemoteSellingLayout ? preventDismiss : undefined}
        onFinish={onFinishSignature}>
        {isRemoteSellingLayout && (
          <VerifyInfoModal
            visible={summaryVisible}
            onClose={hideSummary}
            onConfirm={onConfirmSummary}
            isLoading={sendingEmailConfirm}
          />
        )}
      </SignatureBase>
      {!isRemoteSellingLayout && (
        <SummaryModal
          visible={summaryVisible}
          onClose={hideSummary}
          onConfirm={onConfirmSummary}
          content={summaryContent}
        />
      )}
    </Fragment>
  );
}

const validateSignature = (strokes: string[]) => {
  //Disabled if one stroke only
  if (strokes.length <= 1) {
    return false;
  }
  //Disabled if dots only
  const dots = strokes.filter(stroke => isDot(stroke));
  if (dots.length === strokes.length) {
    return false;
  }

  //Disabled if lines only
  const lines = strokes.filter(stroke => isLine(stroke));
  if (lines.length === strokes.length) {
    return false;
  }
  return true;
};

const putAgreementIntoSignatureTerms = (
  terms: SignatureTerms,
  agreement: string,
) => {
  const newTerms: SignatureTerms = cloneDeep(terms);
  (Object.keys(newTerms) as LanguagesKeys[]).map(language => {
    if (newTerms[language]) {
      newTerms[language].short = `${agreement}\n${newTerms[language].short}`;
      newTerms[language].full = `__${agreement}__\n${newTerms[language].full}`;
    }
  });
  return newTerms;
};
