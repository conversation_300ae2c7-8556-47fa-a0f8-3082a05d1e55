import { Box, H6, H7, LargeBody, Row, Switch } from 'cube-ui-components';
import React, { ForwardedRef, useCallback } from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import NationalityDetails from 'features/eAppV2/ph/components/applicationDetails/common/tablet/NationalityDetails';
import ContactDetails from 'features/eAppV2/ph/components/applicationDetails/common/tablet/ContactDetails';
import AddressInfo from 'features/eAppV2/ph/components/applicationDetails/common/tablet/AddressInfo';
import { Control, UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import {
  contactDetailsDefaultValue,
  ContactDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/contactDetailsValidation';
import { NationalityDetailsLogic } from 'features/eAppV2/ph/hooks/useNationalityDetailsLogic';
import {
  nationalityDetailsDefaultValue,
  NationalityDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import { AddressInfoLogic } from 'features/eAppV2/ph/hooks/useAddressInfoLogic';
import {
  addressInfoDefaultValue,
  AddressInfoForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import { InsuredPersonalDetailsForm } from 'features/eAppV2/ph/validations/applicationDetails/sections/personalDetailsValidation';
import { ContactDetailsLogic } from 'features/eAppV2/ph/hooks/useContactDetailsLogic';
import { useTranslation } from 'react-i18next';
import OccupationDetails from 'features/eAppV2/ph/components/applicationDetails/common/tablet/OccupationDetails';
import {
  occupationDetailsDefaultValue,
  OccupationDetailsForm,
} from 'features/eAppV2/ph/validations/applicationDetails/sections/occupationDetailsValidation';
import { OccupationDetailsLogic } from 'features/eAppV2/ph/hooks/useOccupationDetailsLogic';
import { InsuredForm } from 'features/eAppV2/ph/validations/applicationDetails/insuredInfoValidation';
import InsuredPersonalDetails from 'features/eAppV2/ph/components/applicationDetails/ownerAndInsuredInfo/insuredInfo/tablet/InsuredPersonalDetails';
import { InsuredInfoLogic } from 'features/eAppV2/ph/hooks/useInsuredInfoLogic';
import { InsuredPersonalDetailsLogic } from 'features/eAppV2/ph/hooks/useInsuredPersonalDetailsLogic';
import { fullRemoteSellingEnabled } from 'utils/context';
import { IdentityVerification } from 'features/eAppV2/ph/components/applicationDetails/common/tablet/IdentityVerification';
import {
  EAppStore,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import useOcrLogic from 'features/eAppV2/ph/hooks/useOcrLogic';
import { PartyRole, PartyType } from 'types/party';
import { OCRForm } from 'features/eAppV2/ph/hooks/useOcrPopulatingLogic';
import { shallow } from 'zustand/shallow';
import pickAll from 'ramda/src/pickAll';
import useBoundStore from 'hooks/useBoundStore';

interface Props {
  scrollRef: ForwardedRef<KeyboardAwareScrollView>;
  control: Control<InsuredForm>;
  insuredLogicProps: InsuredInfoLogic;
  personalDetailsProps: InsuredPersonalDetailsLogic;
  contactDetailsProps: ContactDetailsLogic;
  nationalityDetailsProps: NationalityDetailsLogic;
  addressInfoProps: AddressInfoLogic;
  occupationDetailsProps: OccupationDetailsLogic;
}

export default function InsuredInfoTablet(props: Props) {
  const { t } = useTranslation(['eApp']);
  const {
    scrollRef,
    control,
    insuredLogicProps,
    personalDetailsProps,
    contactDetailsProps,
    nationalityDetailsProps,
    occupationDetailsProps,
    addressInfoProps,
  } = props;

  const {
    space,
    colors,
    isJuvenile,
    isPIEqualPO,
    isRemoteSelling,
    isRemoteSellingInsured,
    updateRemoteSellingInsured,
  } = insuredLogicProps;

  const {
    ocrImage,
    form: { setValue, getValues, reset },
  } = personalDetailsProps;

  const caseId = useBoundStore(state => state.case.caseId);
  const {
    clientType,
    entityName,
    isFaceMatched,
    isLivenessCheckVerified,
    insuredPersonalInfo,
    updateInsuredPersonalInfo,
  } = useEAppStore(
    state => ({
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
      entityName: state.policyOwnerPersonalInfo.entityDetails.entityName,
      isFaceMatched: state.insuredPersonalInfo.isFaceMatched,
      isLivenessCheckVerified:
        state.insuredPersonalInfo.isLivenessCheckVerified,
      updateInsuredPersonalInfo: state.updateInsuredPersonalInfo,
      insuredPersonalInfo: state.insuredPersonalInfo,
    }),
    shallow,
  );

  const updatePersonalInfo = useCallback<
    EAppStore['updateInsuredPersonalInfo']
  >(
    payload => {
      updateInsuredPersonalInfo(payload);
      const data = {
        ...getValues(),
        ...pickAll(
          Object.keys(contactDetailsDefaultValue),
          payload.contactDetails,
        ),
        ...pickAll(Object.keys(addressInfoDefaultValue), payload.addressInfo),
        ...pickAll(
          Object.keys(nationalityDetailsDefaultValue),
          payload.nationalityDetails,
        ),
        ...pickAll(Object.keys(occupationDetailsDefaultValue), {
          ...payload.occupationDetails,
          nameOfEmployer:
            clientType === PartyType.ENTITY
              ? entityName
              : payload.occupationDetails?.nameOfEmployer,
        }),
      } as InsuredForm;
      reset(data);
    },
    [getValues, reset, updateInsuredPersonalInfo],
  );

  const ocrLogic = useOcrLogic({
    role: PartyRole.INSURED,
    ocrImage,
    setValue: setValue as unknown as UseFormSetValue<OCRForm>,
    getValues: getValues as unknown as UseFormGetValues<OCRForm>,
    updatePersonalInfo,
    personalInfo: insuredPersonalInfo,
  });

  return (
    <>
      <KeyboardAwareScrollView
        ref={scrollRef}
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20}
        keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
        enableAutomaticScroll={true}>
        <Box pr={space[6]} pt={space[6]}>
          <Row my={space[4]}>
            <H6 fontWeight="bold">Insured's information</H6>
            <Box w={space[2]} />
          </Row>
          {!isJuvenile && (
            <Row
              backgroundColor={colors.background}
              p={space[4]}
              mb={space[4]}
              borderRadius={space[3]}
              justifyContent={'space-between'}>
              <H7 fontWeight="bold">{t('eApp:po.isRemoteSelling')}</H7>
              <Row columnGap={space[2]}>
                <Switch
                  disabled={
                    fullRemoteSellingEnabled
                      ? isRemoteSelling || !isPIEqualPO
                      : isRemoteSelling
                  }
                  value={isRemoteSellingInsured}
                  onChange={updateRemoteSellingInsured}
                />
                <LargeBody>
                  {isRemoteSellingInsured ? t('eApp:yes') : t('eApp:no')}
                </LargeBody>
              </Row>
            </Row>
          )}
          <>
            <IdentityVerification
              role={PartyRole.INSURED}
              isFaceMatched={isFaceMatched}
              onSetIsFaceMatched={isFaceMatched => {
                updateInsuredPersonalInfo({ isFaceMatched });
              }}
              isLivenessCheckVerified={isLivenessCheckVerified}
              onSetIsLivenessCheckVerified={isLivenessCheckVerified => {
                updateInsuredPersonalInfo({ isLivenessCheckVerified });
              }}
              ocrLogic={ocrLogic}
              disableFaceRecognition={isRemoteSellingInsured}
              caseId={caseId}
              partyId={insuredPersonalInfo?.id}
            />
            <InsuredPersonalDetails
              control={
                control as unknown as Control<InsuredPersonalDetailsForm>
              }
              personalDetailsProps={personalDetailsProps}
              ocrLogic={ocrLogic}
            />
            <ContactDetails
              control={control as unknown as Control<ContactDetailsForm>}
              contactDetailsProps={contactDetailsProps}
              shouldHighlight
            />
            <AddressInfo
              control={control as unknown as Control<AddressInfoForm>}
              addressInfoProps={addressInfoProps}
              shouldHighlight
            />
            <NationalityDetails
              control={control as unknown as Control<NationalityDetailsForm>}
              nationalityDetailsProps={nationalityDetailsProps}
              shouldHighlight
            />
            <OccupationDetails
              control={control as unknown as Control<OccupationDetailsForm>}
              occupationDetailsProps={occupationDetailsProps}
              shouldHighlight
            />
          </>
        </Box>
      </KeyboardAwareScrollView>
    </>
  );
}
