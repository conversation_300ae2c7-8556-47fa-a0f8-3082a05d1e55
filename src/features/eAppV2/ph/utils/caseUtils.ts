import { Party, PartyRole, PartyType } from 'types/party';
import { DoneStatus, EAppState } from '../../common/utils/store/eAppStore';
import { format, parse } from 'date-fns';
import { Gender, MaritalStatus } from 'types/person';
import { ContactDetailsForm } from 'features/eAppV2/ph/validations/applicationDetails/sections/contactDetailsValidation';
import { AddressInfoForm } from 'features/eAppV2/ph/validations/applicationDetails/sections/addressInfoValidation';
import { NationalityDetailsForm } from 'features/eAppV2/ph/validations/applicationDetails/sections/nationalityDetailsValidation';
import {
  CORRESPONDENCE_ADDRESS_OPTION,
  NEW_ADDRESS_OPTION,
  PH_COUNTRY,
  PH_DEFAULT_POLICY_DELIVERY_MODE,
  PH_MOBILE_CODE,
  PH_OFFLINE_PAYMENT,
  PH_ONLINE_PAYMENT,
  PH_OPTION_LIST,
} from 'constants/optionList';
import {
  ConsentsForm,
  ExistingPolicyForm,
} from 'features/eAppV2/ph/validations/consentsValidation';
import {
  AgentReport,
  Application,
  DirectCredit,
  File,
  PHBankDetails,
  PHInitialPayment,
  ProposerConsent,
  ReplacementInfo,
} from 'types/case';
import {
  BankDetailsForm,
  creditBankACA,
  prefixCreditCardACA,
  RenewalPaymentSetupForm,
} from 'features/eAppV2/ph/validations/renewalPaymentSetupValidation';
import {
  PaymentForm,
  PaymentOfflineForm,
} from 'features/eAppV2/ph/validations/paymentValidation';
import { ACRForm } from 'features/eAppV2/ph/validations/acrValidation';
import { DocumentCustomerType, DocumentType } from 'types/document';
import { entityDetailsDefaultValue } from '../validations/applicationDetails/sections/entityDetailsValidation';
import { authorizedRepresentativeDetailsDefaultValue } from '../validations/applicationDetails/sections/authorizedRepresentativeDetailsValidation';

type ToPartyParams = { isRemoteSelling?: boolean } & (
  | {
      role: PartyRole.PROPOSER;
      info: EAppState['policyOwnerPersonalInfo'];
    }
  | {
      role: PartyRole.INSURED;
      info: EAppState['insuredPersonalInfo'];
    }
  | {
      role: PartyRole.BENEFICIAL_OWNER;
      info: EAppState['beneficialOwnerPersonalInfo'];
    }
  | {
      role: PartyRole.PAYER;
      info: EAppState['payorPersonalInfo'];
    }
  | {
      role: PartyRole.BENEFICIARY;
      info: EAppState['beneficiariesPersonalInfo'][0];
    }
);

export const toParty = ({
  role,
  info,
  isRemoteSelling,
}: ToPartyParams): Party => {
  return {
    id: info.id || '',
    roles: [role],
    clientType:
      'customerType' in info.personalDetails
        ? (info.personalDetails.customerType as PartyType)
        : PartyType.INDIVIDUAL,
    isRemoteSelling,
    person: {
      policyDeliveryMode: info.contactDetails.deliveryMode,
      name: {
        title: info.personalDetails.title || '',
        firstName: info.personalDetails.firstName || '',
        middleName: info.personalDetails.middleName || '',
        lastName: info.personalDetails.lastName || '',
        extensionName: info.personalDetails.extensionName,
      },
      othername: {
        firstName:
          'otherLegalFirstName' in info.personalDetails
            ? info.personalDetails.otherLegalFirstName
            : '',
        middleName:
          'otherLegalMiddleName' in info.personalDetails
            ? info.personalDetails.otherLegalMiddleName
            : '',
        lastName:
          'otherLegalLastName' in info.personalDetails
            ? info.personalDetails.otherLegalLastName
            : '',
        extensionName:
          'otherLegalExtensionName' in info.personalDetails
            ? info.personalDetails.otherLegalExtensionName
            : '',
      },

      registrations: [
        {
          type: 'PRIMARY',
          idType:
            'primaryIdType' in info.personalDetails
              ? info.personalDetails.primaryIdType || ''
              : '',
          id:
            'primaryIdNumber' in info.personalDetails
              ? info.personalDetails.primaryIdNumber || ''
              : '',
          expiry:
            'expiryDate' in info.personalDetails &&
            info.personalDetails.expiryDate
              ? format(info.personalDetails.expiryDate as Date, 'yyyy-MM-dd')
              : null,
        },
        {
          type: 'DEFAULT',
          idType:
            'idType' in info.personalDetails
              ? info.personalDetails.idType || ''
              : '',
          id:
            'idNumber' in info.personalDetails
              ? info.personalDetails.idNumber || ''
              : '',
        },
      ],
      gender: (info.personalDetails.gender || Gender.MALE) as Gender,
      dateOfBirth: {
        date: info.personalDetails.dateOfBirth
          ? format(info.personalDetails.dateOfBirth as Date, 'yyyy-MM-dd')
          : '',
      },
      age: info.personalDetails.age || 0,
      maritalStatus:
        'maritalStatus' in info.personalDetails
          ? (info.personalDetails.maritalStatus as MaritalStatus)
          : undefined,
      nationality: info.nationalityDetails.nationality,
      countryOfBirth: info.nationalityDetails.countryOfBirth,
      placeOfBirth: info.nationalityDetails.placeOfBirth,
      occupation:
        'occupationDetails' in info
          ? {
              natureOfBusiness: info.occupationDetails?.natureOfBusiness || '',
              natureOfWork: info.occupationDetails.occupationType || '',
              natureOfSubWork: info.occupationDetails.occupationTypeDetail,
              nameOfEmployer: info.occupationDetails.nameOfEmployer || '',
              income: Number(info.occupationDetails.annualIncome) || 0,
            }
          : undefined,
      ...('industryAffiliation' in info
        ? {
            industryAffiliationQuestion1:
              convertFromYesNoAnswer(info.industryAffiliation.question1) || '',
            exactAffiliationOption1:
              info.industryAffiliation.question1 === 'yes'
                ? info.industryAffiliation.question1Affiliation
                : [],
            industryAffiliationQuestion2:
              convertFromYesNoAnswer(info.industryAffiliation.question2) || '',
            exactAffiliationOption2:
              info.industryAffiliation.question2 === 'yes'
                ? info.industryAffiliation.question2Affiliation
                : [],
          }
        : undefined),
      ...('additionalDetails' in info
        ? {
            purposeOfInsurance: info.additionalDetails.purposeOfInsurance,
            otherPurpose: info.additionalDetails.otherPurposeOfInsurance,
            sourceOfFund: info.additionalDetails.sourceOfFund,
            otherSource: info.additionalDetails.otherSourceOfFund,
            sourceOfPremium: info.additionalDetails.sourceOfPremium,
          }
        : undefined),
      ...('occupationDetails' in info &&
      'sourceOfFund' in info.occupationDetails
        ? {
            sourceOfFund: info.occupationDetails.sourceOfFund,
            otherSource: info.occupationDetails.otherSourceOfFund,
          }
        : undefined),
      ...(role === PartyRole.PROPOSER
        ? {
            greenCard: {
              isGreenCardHolder: getIsGreenCardHolder(
                info.usTaxDeclaration.isUSCitizen,
              ),
              passportGreenCardNo: info.usTaxDeclaration.passport,
              taxId: info.usTaxDeclaration.taxId,
              usaAddress: info.usTaxDeclaration.usAddress,
            },
          }
        : undefined),
      ...(role === PartyRole.BENEFICIAL_OWNER && info.beneficialOwnerIsUSCitizen
        ? {
            greenCard: {
              isGreenCardHolder: info.beneficialOwnerIsUSCitizen,
            },
          }
        : undefined),
      ...([
        PartyRole.PROPOSER,
        PartyRole.INSURED,
        PartyRole.PAYER,
        PartyRole.BENEFICIAL_OWNER,
        PartyRole.BENEFICIARY,
      ].includes(role)
        ? { isOcrSuccess: info.isOcrSuccess }
        : undefined),
      ...([PartyRole.PROPOSER, PartyRole.INSURED].includes(role)
        ? {
            livenessCheck:
              ('isLivenessCheckVerified' in info &&
                info.isLivenessCheckVerified) ||
              ('isFaceMatched' in info && info.isFaceMatched)
                ? {
                    isLivenessSuccess: info.isLivenessCheckVerified,
                    isFaceMatched: info.isFaceMatched,
                  }
                : undefined,
          }
        : undefined),
    },

    beneficiarySetting: {
      contactDetailSameAsPo: info.contactDetails.sameAsPo,
    },
    contacts: {
      email: info.contactDetails.email,
      phones: [
        {
          type: 'MOBILE', // primary contact
          countryCode: info.contactDetails.primaryCountryCode,
          number: info.contactDetails.primaryMobile,
        },
        {
          type: 'HOME',
          countryCode: info.contactDetails.homeCountryCode || '',
          number: info.contactDetails.homeMobile || '',
        },
        {
          type: 'WORK',
          countryCode: info.contactDetails.officeCountryCode || '',
          number: info.contactDetails.officeMobile || '',
        },
        {
          type: 'FAX',
          countryCode: info.contactDetails.faxCountryCode || '',
          number: info.contactDetails.faxMobile || '',
        },
      ],
    },
    addresses: [
      {
        addressType: 'MAIN',
        countryCode: info.addressInfo.country,
        addressNo: info.addressInfo.addressLine1,
        street: info.addressInfo.addressLine2 || '',
        province:
          (info.addressInfo.country === PH_COUNTRY
            ? info.addressInfo.province
            : info.addressInfo.addressLine3) || '',
        city: info.addressInfo.city || '',
        zipCode: info.addressInfo.postalCode || '',
        district: '',
        subDistrict: '',
        currentAddressSameAsPo: info.addressInfo.sameWithPo ? 'Yes' : 'No',
        businessAddressOpt: '',
      },
      info.addressInfo.businessAddress === CORRESPONDENCE_ADDRESS_OPTION
        ? {
            addressType: 'WORK',
            countryCode: info.addressInfo.country,
            addressNo: info.addressInfo.addressLine1,
            street: info.addressInfo.addressLine2 || '',
            province:
              (info.addressInfo.country === PH_COUNTRY
                ? info.addressInfo.province
                : info.addressInfo.addressLine3) || '',
            city: info.addressInfo.city || '',
            zipCode: info.addressInfo.postalCode || '',
            district: '',
            subDistrict: '',
            currentAddressSameAsPo: info.addressInfo.sameWithPo ? 'Yes' : 'No',
            businessAddressOpt: CORRESPONDENCE_ADDRESS_OPTION,
          }
        : {
            addressType: 'WORK',
            countryCode: info.addressInfo.businessCountry || '',
            addressNo: info.addressInfo.businessAddressLine1 || '',
            street: info.addressInfo.businessAddressLine2 || '',
            province:
              (info.addressInfo.businessCountry === PH_COUNTRY
                ? info.addressInfo.businessProvince
                : info.addressInfo.businessAddressLine3) || '',
            city: info.addressInfo.businessCity || '',
            zipCode: info.addressInfo.businessPostalCode || '',
            district: '',
            subDistrict: '',
            currentAddressSameAsPo: info.addressInfo.sameWithPo ? 'Yes' : 'No',
            businessAddressOpt: NEW_ADDRESS_OPTION,
          },
    ],
    sourceLeadId:
      'leadSource' in info.personalDetails
        ? info.personalDetails.leadSource
        : undefined,
    ...(role === PartyRole.PROPOSER &&
    info.personalDetails.customerType === PartyType.ENTITY
      ? {
          entity: {
            name: info.entityDetails.entityName || '',
            natureOfBusiness: info.entityDetails.businessNature || '',
            representative: {
              name: {
                title: info.authorizedRepresentativeDetails.title || '',
                firstName: info.authorizedRepresentativeDetails.firstName || '',
                middleName:
                  info.authorizedRepresentativeDetails.middleName || '',
                lastName: info.authorizedRepresentativeDetails.lastName || '',
                extensionName:
                  info.authorizedRepresentativeDetails.extensionName || '',
              },
              positionHeld: info.authorizedRepresentativeDetails.position || '',
            },
            purposeOfInsurance: info.additionalDetails.purposeOfInsurance || '',
            otherPurpose: info.additionalDetails.otherPurposeOfInsurance || '',
            sourceOfFund: info.additionalDetails.sourceOfFund || '',
            otherSource: info.additionalDetails.otherSourceOfFund || '',
            sourceOfPremium: info.additionalDetails.sourceOfPremium || '',
            leadSource: info.entityDetails.leadSource || '',
            greenCard: {
              isGreenCardHolder: getIsGreenCardHolder(
                info.usTaxDeclaration.isUSCitizen,
              ),
            },
          },
        }
      : undefined),
    ...(role === PartyRole.INSURED
      ? {
          relationship: info.personalDetails.relationship,
        }
      : undefined),
    ...(role === PartyRole.PAYER
      ? {
          relationship: info.personalDetails.relationshipWithPolicyOwner,
          payorSetting: {
            reasonForPayment: info.reasonForThirdPartyPayment,
          },
        }
      : undefined),
    ...(role === PartyRole.BENEFICIARY
      ? {
          relationship: info.personalDetails.relationshipWithInsured,
          beneficiarySetting: {
            beneficiaryType: info.personalDetails.beneficiaryType,
            benefitPercentage:
              'benefitPercentage' in info ? info.benefitPercentage : 0,
            designation: info.personalDetails.designation || '',
            trusteeName: info.personalDetails.nameOfTrustee || '',
            contactDetailSameAsPo: info.contactDetails.sameAsPo,
            organizationName: info.personalDetails.organizationName,
          },
        }
      : undefined),
    ...('enquiryId' in info
      ? {
          uw: {
            enquiryId: info.enquiryId,
          },
        }
      : undefined),
  };
};

type ParsePartyReturn =
  | {
      role: PartyRole.PROPOSER;
      data: EAppState['policyOwnerPersonalInfo'];
    }
  | {
      role: PartyRole.INSURED;
      data: EAppState['insuredPersonalInfo'];
    }
  | {
      role: PartyRole.BENEFICIAL_OWNER;
      data: EAppState['beneficialOwnerPersonalInfo'];
    }
  | {
      role: PartyRole.PAYER;
      data: EAppState['payorPersonalInfo'];
    }
  | {
      role: PartyRole.BENEFICIARY;
      data: EAppState['beneficiariesPersonalInfo'][0];
    };

export const parseParty = (
  party: Party,
  role: PartyRole,
  files: File[],
  index?: number,
): ParsePartyReturn | null => {
  const mobileContact = party.contacts?.phones?.find(p => p.type === 'MOBILE');
  const homeContact = party.contacts?.phones?.find(p => p.type === 'HOME');
  const officeContact = party.contacts?.phones?.find(p => p.type === 'WORK');
  const faxContact = party.contacts?.phones?.find(p => p.type === 'FAX');

  const mainAddress = party.addresses?.find(a => a.addressType === 'MAIN');
  const businessAddress = party.addresses?.find(a => a.addressType === 'WORK');

  const commonInfo: {
    id?: string;
    contactDetails: ContactDetailsForm & DoneStatus;
    addressInfo: AddressInfoForm & DoneStatus;
    nationalityDetails: NationalityDetailsForm & DoneStatus;
  } = {
    id: party.id,
    contactDetails: {
      sameAsPo: Boolean(party.beneficiarySetting?.contactDetailSameAsPo),
      email: party.contacts.email,
      deliveryMode:
        party.person?.policyDeliveryMode || PH_DEFAULT_POLICY_DELIVERY_MODE, // TODO: need to check for phil only or not?
      primaryCountryCode: mobileContact?.countryCode || PH_MOBILE_CODE,
      primaryMobile: mobileContact?.number || '',
      homeCountryCode: homeContact?.countryCode || PH_MOBILE_CODE,
      homeMobile: homeContact?.number || '',
      officeCountryCode: officeContact?.countryCode || PH_MOBILE_CODE,
      officeMobile: officeContact?.number || '',
      faxCountryCode: faxContact?.countryCode || PH_MOBILE_CODE,
      faxMobile: faxContact?.number || '',
      isEntity:
        party.clientType === PartyType.ENTITY && role === PartyRole.PROPOSER,
    },
    addressInfo: {
      sameWithPo: mainAddress?.currentAddressSameAsPo === 'Yes',
      country: mainAddress?.countryCode || PH_COUNTRY,
      addressLine1: mainAddress?.addressNo || '',
      addressLine2: mainAddress?.street || '',
      addressLine3:
        mainAddress?.countryCode !== PH_COUNTRY
          ? mainAddress?.province || ''
          : '',
      province:
        mainAddress?.countryCode === PH_COUNTRY
          ? mainAddress?.province || ''
          : '',
      city: mainAddress?.city || '',
      postalCode: mainAddress?.zipCode || '',
      businessAddress:
        // refactor address option,
        // old value: newAddress, currentAddress
        // new value: new, correspondence
        businessAddress?.businessAddressOpt === 'newAddress' ||
        businessAddress?.businessAddressOpt === 'new'
          ? NEW_ADDRESS_OPTION
          : CORRESPONDENCE_ADDRESS_OPTION,
      businessCountry: businessAddress?.countryCode || PH_COUNTRY,
      businessAddressLine1: businessAddress?.addressNo || '',
      businessAddressLine2: businessAddress?.street || '',
      businessAddressLine3:
        businessAddress?.countryCode !== PH_COUNTRY
          ? businessAddress?.province || ''
          : '',
      businessProvince:
        businessAddress?.countryCode === PH_COUNTRY
          ? businessAddress?.province || ''
          : '',
      businessCity: businessAddress?.city || '',
      businessPostalCode: businessAddress?.zipCode || '',
    },
    nationalityDetails: {
      nationality: party.person?.nationality || '',
      countryOfBirth: party.person?.countryOfBirth || '',
      placeOfBirth: party.person?.placeOfBirth || '',
    },
  };

  const defaultId = party.person?.registrations?.find(
    r => r.type === 'DEFAULT',
  );
  const primaryId = party.person?.registrations?.find(
    r => r.type === 'PRIMARY',
  );
  const ocrImage = files.find(
    f => f.fileName.includes(DocumentType.FrontID) && f.fromOcr,
  );
  const frontImage = files.find(
    f =>
      f.fileName.includes(DocumentType.FrontID) &&
      f.fileName !== ocrImage?.fileName,
  );
  const backImage = files.find(f => f.fileName.includes(DocumentType.BackID));
  const certOnBOImage = files.find(f =>
    f.fileName.includes(DocumentType.CertOnBO),
  );
  const irswFormImage = files.find(f =>
    f.fileName.includes(DocumentType.IRSWForm),
  );
  const additionalImages = files.filter(
    f =>
      ![
        ocrImage?.fileName,
        frontImage?.fileName,
        backImage?.fileName,
        certOnBOImage?.fileName,
        irswFormImage?.fileName,
      ]
        .filter(Boolean)
        .includes(f.fileName),
  );
  switch (role) {
    case PartyRole.PROPOSER:
      return {
        role: PartyRole.PROPOSER,
        data: {
          ...commonInfo,
          personalDetails: {
            customerType: party.clientType,
            title: party.person?.name.title || '',
            firstName: party.person?.name.firstName || '',
            lastName: party.person?.name.lastName || '',
            middleName: party.person?.name.middleName || '',
            extensionName: party.person?.name.extensionName || '',
            otherLegalFirstName: party.person?.othername?.firstName || '',
            otherLegalMiddleName: party.person?.othername?.middleName || '',
            otherLegalLastName: party.person?.othername?.lastName || '',
            otherLegalExtensionName:
              party.person?.othername?.extensionName || '',
            idType: defaultId?.idType || '',
            idNumber: defaultId?.id || '',
            primaryIdType: primaryId?.idType || '',
            primaryIdNumber: primaryId?.id || '',
            expiryDate: primaryId?.expiry
              ? parse(primaryId.expiry, 'yyyy-MM-dd', new Date())
              : null,
            gender: party.person?.gender || '',
            dateOfBirth: party.person?.dateOfBirth?.date
              ? parse(party.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            age: party.person?.age,
            maritalStatus: party.person?.maritalStatus || '',
            leadSource: party.sourceLeadId || '',
            document: {
              frontImage: ocrImage
                ? {
                    base64: '',
                    name: ocrImage.fileName,
                    thumbnail: ocrImage.filePath,
                    fromOcr: ocrImage.fromOcr,
                  }
                : frontImage
                ? {
                    base64: '',
                    name: frontImage.fileName,
                    thumbnail: frontImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              backImage: backImage
                ? {
                    base64: '',
                    name: backImage.fileName,
                    thumbnail: backImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              additionalDocument: additionalImages.map(i => ({
                base64: '',
                name: i.fileName,
                thumbnail: i.filePath,
              })),
            },
          },
          ...(party.clientType === PartyType.ENTITY
            ? {
                entityDetails: {
                  entityName: party.entity?.name || '',
                  businessNature: party.entity?.natureOfBusiness || '',
                  leadSource: party.entity?.leadSource || '',
                },
                authorizedRepresentativeDetails: {
                  title: party.entity?.representative?.name?.title || '',
                  firstName:
                    party.entity?.representative?.name?.firstName || '',
                  middleName:
                    party.entity?.representative?.name?.middleName || '',
                  lastName: party.entity?.representative?.name?.lastName || '',
                  extensionName:
                    party.entity?.representative?.name?.extensionName || '',
                  position: party.entity?.representative?.positionHeld || '',
                  document:
                    authorizedRepresentativeDetailsDefaultValue.document,
                },
              }
            : {
                entityDetails: entityDetailsDefaultValue,
                authorizedRepresentativeDetails:
                  authorizedRepresentativeDetailsDefaultValue,
              }),
          occupationDetails: {
            stakeholder: 'other',
            occupationType: party.person?.occupation?.natureOfWork || '',
            occupationTypeDetail:
              party.person?.occupation?.natureOfSubWork || '',
            nameOfEmployer: party.person?.occupation?.nameOfEmployer || '',
            annualIncome: String(party.person?.occupation?.income || ''),
          },
          industryAffiliation: {
            question1:
              party.person?.industryAffiliationQuestion1 === 'Y'
                ? 'yes'
                : party.person?.industryAffiliationQuestion1 === 'N'
                ? 'no'
                : '',
            question1Affiliation: party.person?.exactAffiliationOption1 || [],
            question2:
              party.person?.industryAffiliationQuestion2 === 'Y'
                ? 'yes'
                : party.person?.industryAffiliationQuestion2 === 'N'
                ? 'no'
                : '',
            question2Affiliation: party.person?.exactAffiliationOption2 || [],
            isEntity: party.clientType === PartyType.ENTITY,
          },
          additionalDetails: {
            purposeOfInsurance: party.person?.purposeOfInsurance || '',
            otherPurposeOfInsurance: party.person?.otherPurpose || '',
            sourceOfFund: party.person?.sourceOfFund || '',
            otherSourceOfFund: party.person?.otherSource || '',
            sourceOfPremium: party.person?.sourceOfPremium || '',
          },
          usTaxDeclaration: {
            isUSCitizen:
              party.person?.greenCard?.isGreenCardHolder !== null &&
              party.person?.greenCard?.isGreenCardHolder !== undefined
                ? party.person?.greenCard.isGreenCardHolder
                  ? 'yes'
                  : 'no'
                : '',
            passport: party.person?.greenCard?.passportGreenCardNo,
            taxId: party.person?.greenCard?.taxId,
            usAddress: party.person?.greenCard?.usaAddress,
            isEntity: party.clientType === PartyType.ENTITY,
          },
          enquiryId: party.uw?.enquiryId || '',
          ...(party.person?.isOcrSuccess
            ? { isOcrSuccess: party.person?.isOcrSuccess }
            : undefined),
          ...(party.person?.livenessCheck
            ? {
                isFaceMatched: party.person.livenessCheck.isFaceMatched,
                isLivenessCheckVerified:
                  party.person.livenessCheck.isLivenessSuccess,
              }
            : undefined),
        },
      };
    case PartyRole.INSURED:
      return {
        role: PartyRole.INSURED,
        data: {
          ...commonInfo,
          personalDetails: {
            customerType: party.clientType,
            title: party.person?.name.title || '',
            firstName: party.person?.name.firstName || '',
            lastName: party.person?.name.lastName || '',
            middleName: party.person?.name.middleName || '',
            extensionName: party.person?.name.extensionName || '',
            otherLegalFirstName: party.person?.othername?.firstName || '',
            otherLegalMiddleName: party.person?.othername?.middleName || '',
            otherLegalLastName: party.person?.othername?.lastName || '',
            otherLegalExtensionName:
              party.person?.othername?.extensionName || '',
            idType: defaultId?.idType || '',
            idNumber: defaultId?.id || '',
            primaryIdType: primaryId?.idType || '',
            primaryIdNumber: primaryId?.id || '',
            expiryDate: primaryId?.expiry
              ? parse(primaryId.expiry, 'yyyy-MM-dd', new Date())
              : null,
            gender: party.person?.gender || '',
            dateOfBirth: party.person?.dateOfBirth?.date
              ? parse(party.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            age: party.person?.age,
            maritalStatus: party.person?.maritalStatus || '',
            relationship: party.relationship || '',
            document: {
              frontImage: ocrImage
                ? {
                    base64: '',
                    name: ocrImage.fileName,
                    thumbnail: ocrImage.filePath,
                    fromOcr: ocrImage.fromOcr,
                  }
                : frontImage
                ? {
                    base64: '',
                    name: frontImage.fileName,
                    thumbnail: frontImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              backImage: backImage
                ? {
                    base64: '',
                    name: backImage.fileName,
                    thumbnail: backImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
            },
          },
          occupationDetails: {
            stakeholder: 'other',
            occupationType: party.person?.occupation?.natureOfWork || '',
            occupationTypeDetail:
              party.person?.occupation?.natureOfSubWork || '',
            nameOfEmployer: party.person?.occupation?.nameOfEmployer || '',
            annualIncome: String(party.person?.occupation?.income || ''),
          },
          enquiryId: party.uw?.enquiryId || '',
          ...(party.person?.isOcrSuccess
            ? { isOcrSuccess: party.person?.isOcrSuccess }
            : undefined),
          ...(party.person?.livenessCheck
            ? {
                isFaceMatched: party.person.livenessCheck.isFaceMatched,
                isLivenessCheckVerified:
                  party.person.livenessCheck.isLivenessSuccess,
              }
            : undefined),
        },
      };
    case PartyRole.BENEFICIAL_OWNER:
      return {
        role: PartyRole.BENEFICIAL_OWNER,
        data: {
          ...commonInfo,
          personalDetails: {
            title: party.person?.name.title || '',
            firstName: party.person?.name.firstName || '',
            lastName: party.person?.name.lastName || '',
            middleName: party.person?.name.middleName || '',
            extensionName: party.person?.name.extensionName || '',
            idType: defaultId?.idType || '',
            idNumber: defaultId?.id || '',
            primaryIdType: primaryId?.idType || '',
            primaryIdNumber: primaryId?.id || '',
            expiryDate: primaryId?.expiry
              ? parse(primaryId.expiry, 'yyyy-MM-dd', new Date())
              : null,
            gender: party.person?.gender,
            dateOfBirth: party.person?.dateOfBirth?.date
              ? parse(party.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            age: party.person?.age,
            document: {
              frontImage: ocrImage
                ? {
                    base64: '',
                    name: ocrImage.fileName,
                    thumbnail: ocrImage.filePath,
                    fromOcr: ocrImage.fromOcr,
                  }
                : frontImage
                ? {
                    base64: '',
                    name: frontImage.fileName,
                    thumbnail: frontImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              backImage: backImage
                ? {
                    base64: '',
                    name: backImage.fileName,
                    thumbnail: backImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              certOnBO: certOnBOImage
                ? {
                    base64: '',
                    name: certOnBOImage.fileName,
                    thumbnail: certOnBOImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              irswForm: irswFormImage
                ? {
                    base64: '',
                    name: irswFormImage.fileName,
                    thumbnail: irswFormImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
            },
          },
          ...(party.person?.greenCard?.isGreenCardHolder !== null &&
          party.person?.greenCard?.isGreenCardHolder !== undefined
            ? {
                beneficialOwnerIsUSCitizen:
                  party.person?.greenCard.isGreenCardHolder,
              }
            : { beneficialOwnerIsUSCitizen: null }),
          ...(party.person?.isOcrSuccess
            ? { isOcrSuccess: party.person?.isOcrSuccess }
            : undefined),
        },
      };
    case PartyRole.PAYER:
      return {
        role: PartyRole.PAYER,
        data: {
          ...commonInfo,
          reasonForThirdPartyPayment:
            party.payorSetting?.reasonForPayment || '',
          personalDetails: {
            title: party.person?.name.title || '',
            firstName: party.person?.name.firstName || '',
            lastName: party.person?.name.lastName || '',
            middleName: party.person?.name.middleName || '',
            extensionName: party.person?.name.extensionName || '',
            idType: defaultId?.idType || '',
            idNumber: defaultId?.id || '',
            primaryIdType: primaryId?.idType || '',
            primaryIdNumber: primaryId?.id || '',
            expiryDate: primaryId?.expiry
              ? parse(primaryId.expiry, 'yyyy-MM-dd', new Date())
              : null,
            gender: party.person?.gender,
            dateOfBirth: party.person?.dateOfBirth?.date
              ? parse(party.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            age: party.person?.age,
            relationshipWithPolicyOwner: party.relationship || '',
            document: {
              frontImage: ocrImage
                ? {
                    base64: '',
                    name: ocrImage.fileName,
                    thumbnail: ocrImage.filePath,
                    fromOcr: ocrImage.fromOcr,
                  }
                : frontImage
                ? {
                    base64: '',
                    name: frontImage.fileName,
                    thumbnail: frontImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              backImage: backImage
                ? {
                    base64: '',
                    name: backImage.fileName,
                    thumbnail: backImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
            },
          },
          occupationDetails: {
            stakeholder: 'payor',
            natureOfBusiness: party.person?.occupation?.natureOfBusiness || '',
            occupationType: party.person?.occupation?.natureOfWork || '',
            occupationTypeDetail:
              party.person?.occupation?.natureOfSubWork || '',
            nameOfEmployer: party.person?.occupation?.nameOfEmployer || '',
            annualIncome: String(party.person?.occupation?.income || ''),
            sourceOfFund: party.person?.sourceOfFund,
            otherSourceOfFund: party.person?.otherSource,
          },
          ...(party.person?.isOcrSuccess
            ? { isOcrSuccess: party.person?.isOcrSuccess }
            : undefined),
        },
      };
    case PartyRole.BENEFICIARY:
      return {
        role: PartyRole.BENEFICIARY,
        data: {
          ...commonInfo,
          index: index ?? 0,
          personalDetails: {
            title: party.person?.name.title,
            firstName: party.person?.name.firstName || '',
            lastName: party.person?.name.lastName || '',
            middleName: party.person?.name.middleName || '',
            extensionName: party.person?.name.extensionName || '',
            gender: party.person?.gender,
            dateOfBirth: party.person?.dateOfBirth?.date
              ? parse(party.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            age: party.person?.age,
            beneficiaryType: party.beneficiarySetting?.beneficiaryType || '',
            designation: party.beneficiarySetting?.designation || '',
            nameOfTrustee: party.beneficiarySetting?.trusteeName || '',
            organizationName: party.beneficiarySetting?.organizationName || '',
            relationshipWithInsured: party.relationship || '',
            document: {
              frontImage: ocrImage
                ? {
                    base64: '',
                    name: ocrImage.fileName,
                    thumbnail: ocrImage.filePath,
                    fromOcr: ocrImage.fromOcr,
                  }
                : frontImage
                ? {
                    base64: '',
                    name: frontImage.fileName,
                    thumbnail: frontImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
              backImage: backImage
                ? {
                    base64: '',
                    name: backImage.fileName,
                    thumbnail: backImage.filePath,
                  }
                : {
                    base64: '',
                    name: '',
                    thumbnail: '',
                  },
            },
          },
          benefitPercentage: party.beneficiarySetting?.benefitPercentage || 0,
          ...(party.person?.isOcrSuccess
            ? { isOcrSuccess: party.person?.isOcrSuccess }
            : undefined),
        },
      };
    default:
      return null;
  }
};

export const toApplicationConsent = (
  consent: ConsentsForm,
  existingPolicies: ExistingPolicyForm[],
  agentId: string,
): {
  replacementInfo: ReplacementInfo[];
  proposerConsent: ProposerConsent[];
} => {
  const replacementBasicInfo: Pick<
    ReplacementInfo,
    | 'agentId'
    | 'inforcePolicy'
    | 'reinstatablePolicy'
    | 'replaceWithApplyFor'
    | 'paidByPolicyLoan'
    | 'confirm'
  > = {
    agentId,
    inforcePolicy:
      consent.declaration.hasExistingInsuranceInForce === null
        ? null
        : consent.declaration.hasExistingInsuranceInForce
        ? 'Y'
        : 'N',
    reinstatablePolicy:
      consent.declaration.hasLapsedPolicyToReinstate === null
        ? null
        : consent.declaration.hasLapsedPolicyToReinstate
        ? 'Y'
        : 'N',
    replaceWithApplyFor:
      consent.declaration.willReplaceLifeInsurance === null
        ? null
        : consent.declaration.willReplaceLifeInsurance
        ? 'Y'
        : 'N',
    paidByPolicyLoan:
      consent.declaration.willPayPremiumsByLoanOrSurrender === null
        ? null
        : consent.declaration.willPayPremiumsByLoanOrSurrender
        ? 'Y'
        : 'N',
    confirm: Boolean(consent.declaration.confirmCheck),
  };
  const proposerConsent: ProposerConsent[] = [
    {
      agreement: consent.dataPrivacyAndConsents.acceptInform ? 'Y' : 'N',
      consentProcessing: consent.dataPrivacyAndConsents.acceptDataPrivacy
        ? 'Y'
        : 'N',
    },
  ];
  if (existingPolicies.length > 0) {
    return {
      replacementInfo: existingPolicies.map(policy => ({
        ...replacementBasicInfo,
        insuranceCompanyName: policy.companyName,
        replacedPolicyId: policy.policyNumber,
        sumAssured: Number(policy.amountOfCoverage),
        parentId: null,
        replaceInfo: null,
      })),
      proposerConsent,
    };
  } else {
    return {
      replacementInfo: [
        {
          ...replacementBasicInfo,
          insuranceCompanyName: null,
          replacedPolicyId: null,
          sumAssured: null,
          parentId: null,
          replaceInfo: null,
        },
      ],
      proposerConsent,
    };
  }
};

export const parseApplicationConsent = (
  application: Application,
): {
  consentsAnswers: ConsentsForm;
  existingPolicyData: ExistingPolicyForm[];
} => {
  const replacementBasicInfo = application?.replacementInfo?.[0];
  return {
    consentsAnswers: {
      declaration: {
        confirmCheck: Boolean(replacementBasicInfo?.confirm),
        hasExistingInsuranceInForce:
          replacementBasicInfo?.inforcePolicy === null ||
          replacementBasicInfo?.inforcePolicy === undefined
            ? null
            : replacementBasicInfo?.inforcePolicy === 'Y',
        hasLapsedPolicyToReinstate:
          replacementBasicInfo?.reinstatablePolicy === null ||
          replacementBasicInfo?.reinstatablePolicy === undefined
            ? null
            : replacementBasicInfo?.reinstatablePolicy === 'Y',
        willReplaceLifeInsurance:
          replacementBasicInfo?.replaceWithApplyFor === null ||
          replacementBasicInfo?.replaceWithApplyFor === undefined
            ? null
            : replacementBasicInfo?.replaceWithApplyFor === 'Y',
        willPayPremiumsByLoanOrSurrender:
          replacementBasicInfo?.paidByPolicyLoan === null ||
          replacementBasicInfo?.paidByPolicyLoan === undefined
            ? null
            : replacementBasicInfo?.paidByPolicyLoan === 'Y',
      },
      dataPrivacyAndConsents: {
        acceptDataPrivacy:
          application?.proposerConsent?.[0]?.consentProcessing === 'Y',
        acceptInform: application?.proposerConsent?.[0]?.agreement === 'Y',
      },
    },
    existingPolicyData:
      application?.replacementInfo?.map(i => ({
        amountOfCoverage: String(i.sumAssured || ''),
        companyName: i.insuranceCompanyName ?? '',
        policyNumber: i.replacedPolicyId ?? '',
      })) ?? [],
  };
};

export const toApplicationRenewalPaymentSetup = (
  renewalPaymentSetup: RenewalPaymentSetupForm,
  agentId: string,
): DirectCredit<'ph'> => {
  switch (renewalPaymentSetup.paymentMethod) {
    case 'adda':
      return {
        paymentMethod: 'adda',
        accountHolderForRenewal:
          renewalPaymentSetup?.debitAccountHolderName || '',
        accountNumberForRenewal: renewalPaymentSetup?.debitAccountNumber || '',
        agentId,
        bankNameForRenewal: renewalPaymentSetup?.debitBankName || '',
      };
    case 'aca':
      return {
        paymentMethod: 'aca',
        isSecurityBank:
          convertFromYesNoAnswer(
            renewalPaymentSetup?.creditIsIssuedBySecBankCorp,
          ) || null,
        cardHolder: renewalPaymentSetup?.creditCardHolderName || '',
        cardCompany: renewalPaymentSetup?.creditCardCompany || '',
        cardNumber:
          (renewalPaymentSetup?.creditCardCompany === creditBankACA
            ? prefixCreditCardACA
            : '') + renewalPaymentSetup?.creditCardNumber || '',
        cardExpiryDate: renewalPaymentSetup?.creditExpiryDate || '',
      };
    default:
      return {
        paymentMethod: renewalPaymentSetup?.paymentMethod as
          | 'cash'
          | 'pos'
          | 'cheque',
      };
  }
};

export const parseApplicationRenewalPaymentSetup = (
  directCredit: DirectCredit<'ph'>,
): RenewalPaymentSetupForm => {
  switch (directCredit.paymentMethod) {
    case 'adda':
      return {
        paymentMethod: 'adda',
        debitAccountHolderName: directCredit.accountHolderForRenewal,
        debitAccountNumber: directCredit.accountNumberForRenewal,
        debitBankName: directCredit.bankNameForRenewal,
      };
    case 'aca':
      return {
        paymentMethod: 'aca',
        creditAcceptDisclaimer: false,
        creditCardCompany: directCredit.cardCompany,
        creditCardHolderName: directCredit.cardHolder,
        creditCardNumber:
          directCredit.cardCompany === creditBankACA
            ? directCredit.cardNumber?.replace(prefixCreditCardACA, '')
            : directCredit.cardNumber,
        creditExpiryDate: directCredit.cardExpiryDate,
        creditIsIssuedBySecBankCorp:
          directCredit.isSecurityBank === 'Y'
            ? 'yes'
            : directCredit.isSecurityBank === 'N'
            ? 'no'
            : undefined,
      };
    default: {
      return {
        paymentMethod: directCredit.paymentMethod,
      };
    }
  }
};

export const toApplicationBankDetails = (
  bankDetails: BankDetailsForm,
  agentId: string,
): PHBankDetails => {
  return {
    accountHolder: bankDetails.accountHolderName || '',
    accountNumber: bankDetails.accountNumber || '',
    agentId,
    bankName: bankDetails.bankName || '',
    branchCode: bankDetails.branchName || '',
    proof: bankDetails.proofOfBankAccountDetails || '',
  };
};

export const parseApplicationBankDetails = (
  directCredit: DirectCredit<'ph'>,
): BankDetailsForm => {
  return {
    accountHolderName: directCredit.accountHolder || '',
    accountNumber: directCredit.accountNumber || '',
    bankName: directCredit.bankName || '',
    branchName: directCredit.branchCode || '',
    proofOfBankAccountDetails: directCredit.proof || '',
  };
};

export const toApplicationInitialPayment = (
  initialPayment: PaymentForm,
  initialPaymentOffline: PaymentOfflineForm,
): PHInitialPayment => {
  const updateDT = new Date().toISOString();
  switch (initialPayment.paymentMethod) {
    case 'dragonPayOnlineBanking':
      return {
        payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.dragonPayBanking,
        paymentOption: PH_ONLINE_PAYMENT,
        merchantId: initialPayment.merchantId,
        paymentRefNo: initialPayment.refNo,
        updateDT,
      };
    case 'dragonPayCards':
      return {
        payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.dragonPayCards,
        paymentOption: PH_ONLINE_PAYMENT,
        merchantId: initialPayment.merchantId,
        paymentRefNo: initialPayment.refNo,
        updateDT,
      };
    case 'offline':
      switch (initialPaymentOffline.paymentOfflineMethod) {
        case 'otcBillsPayment':
          switch (initialPaymentOffline.paymentOfflineDetailMethod) {
            case 'otcBillsPayment.securityBank':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment
                    .securityBank,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.bdo':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment.bdo,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.bpi':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment.bpi,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.cebuanaLhuillierBranches':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment
                    .cebuanaLhuillierBranches,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.lbc':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment.lbc,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.metroBank':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment
                    .metroBank,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'otcBillsPayment.unionBank':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.otcBillsPayment
                    .unionBank,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
          }
          break;
        case 'onlineBanking':
          switch (initialPaymentOffline.paymentOfflineDetailMethod) {
            case 'onlineBanking.bancNetOnline':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking
                    .bancNetOnline,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'onlineBanking.bdo':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking.bdo,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'onlineBanking.bpi':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking.bpi,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'onlineBanking.landBank':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking.landBank,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'onlineBanking.metroBankDirect':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking
                    .metroBankDirect,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
            case 'onlineBanking.securityBank':
              return {
                payType:
                  PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.onlineBanking
                    .securityBank,
                paymentOption: PH_OFFLINE_PAYMENT,
                updateDT,
              };
          }
          break;
        case 'gcash':
          return {
            payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.gcash,
            paymentOption: PH_OFFLINE_PAYMENT,
            updateDT,
          };
        case 'creditCardOption':
          return {
            payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.creditCardOption,
            paymentOption: PH_OFFLINE_PAYMENT,
            updateDT,
          };
        case 'fwdPosMobile':
          return {
            payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.pos,
            paymentOption: PH_OFFLINE_PAYMENT,
            updateDT,
          };
        case 'paymaya':
          return {
            payType: PH_OPTION_LIST.INITIAL_PAYMENT_OPTION.pos,
            paymentOption: PH_OFFLINE_PAYMENT,
            updateDT,
          };
      }
  }
  return {
    payType: '',
    paymentOption: '',
  };
};

export const toApplicationAgentReport = (
  acr: ACRForm,
  agentId: string,
): AgentReport => {
  return {
    agentId,
    changeInfocePolicy:
      convertFromYesNoAnswer(acr.existingInsuranceEnforceChange) || '',
    changeInstatablePolicy:
      convertFromYesNoAnswer(acr.reinstatableLapsedInsuranceChange) || '',
    employmentBusinessPropertiesInfoOfPO: acr.policyOwnerBusinessInfo,
    industryAffiliation1:
      convertFromYesNoAnswer(acr.isAffiliatedWithRestrictedEntities) || '',
    exactAffiliation1:
      (acr.restrictedEntity?.filter(Boolean) as string[]) || [],
    industryAffiliation2:
      convertFromYesNoAnswer(acr.isAffiliatedWithRestrictedIndustries) || '',
    exactAffiliation2:
      (acr.restrictedIndustry?.filter(Boolean) as string[]) || [],
    familyInfoOfPO: acr.policyOwnerFamilyInfo || '',
    pOBOIsPEPRCA: convertFromYesNoAnswer(acr.isPEPorRCA) || '',
    governementPositions: acr.governmentPosition?.value || '',
    governementPositionsTitle: acr.governmentPosition?.label || '',
    isConfirm: Boolean(acr.acceptDeclaration),
    otherRemarks: acr.otherSpecialRemarks || '',
    paidByPolicyLoan:
      convertFromYesNoAnswer(acr.policyLoanPremiumPayment) || '',
    relatedToFWPFSC: convertFromYesNoAnswer(acr.isRelatedToFWP_FSC) || '',
    relatedToPOPI: convertFromYesNoAnswer(acr.relatedToOwner) || '',
    relationship: acr.relationship || '',
  };
};

export const parseApplicationAgentReport = (
  agentReport: AgentReport,
): ACRForm => {
  return {
    relatedToOwner: convertToYesNoAnswer(agentReport.relatedToPOPI),
    relationship: agentReport.relationship,
    isRelatedToFWP_FSC: convertToYesNoAnswer(agentReport.relatedToFWPFSC),
    isPEPorRCA: convertToYesNoAnswer(agentReport.pOBOIsPEPRCA),
    governmentPosition: {
      value: agentReport.governementPositions,
      label: agentReport.governementPositionsTitle,
    },
    isAffiliatedWithRestrictedEntities: convertToYesNoAnswer(
      agentReport.industryAffiliation1,
    ),
    restrictedEntity: agentReport.exactAffiliation1,
    isAffiliatedWithRestrictedIndustries: convertToYesNoAnswer(
      agentReport.industryAffiliation2,
    ),
    restrictedIndustry: agentReport.exactAffiliation2,
    existingInsuranceEnforceChange: convertToYesNoAnswer(
      agentReport.changeInfocePolicy,
    ),
    reinstatableLapsedInsuranceChange: convertToYesNoAnswer(
      agentReport.changeInstatablePolicy,
    ),
    policyLoanPremiumPayment: convertToYesNoAnswer(
      agentReport.paidByPolicyLoan,
    ),
    policyOwnerBusinessInfo: agentReport.employmentBusinessPropertiesInfoOfPO,
    policyOwnerFamilyInfo: agentReport.familyInfoOfPO,
    otherSpecialRemarks: agentReport.otherRemarks,
    secondaryFWPCode: agentReport.secondaryAgentCode,
    secondaryFWPName: agentReport.secondaryAgentName,
    acceptDeclaration: agentReport.isConfirm,
  };
};

const convertFromYesNoAnswer = (
  answer: string | null | undefined,
  yes = 'Y',
  no = 'N',
) => {
  return answer === 'yes' ? yes : answer === 'no' ? no : answer;
};

const convertToYesNoAnswer = (answer: string) => {
  return answer === 'Y' ? 'yes' : answer === 'N' ? 'no' : '';
};

export const mapPartyRoleToDocumentCustomerType = (
  partyRole: PartyRole,
  age?: number,
): DocumentCustomerType => {
  switch (partyRole) {
    case PartyRole.INSURED:
      return DocumentCustomerType.PI;
    case PartyRole.PAYER:
      return DocumentCustomerType.PAY;
    case PartyRole.BENEFICIAL_OWNER:
      return DocumentCustomerType.BO;
    case PartyRole.BENEFICIARY:
      return age === undefined || age >= 18
        ? DocumentCustomerType.B
        : DocumentCustomerType.TT;
    case PartyRole.PROPOSER:
      return DocumentCustomerType.PO;
    default:
      return DocumentCustomerType.NA;
  }
};

const getIsGreenCardHolder = (isUSCitizen: string) => {
  if (isUSCitizen === 'yes') {
    return true;
  }
  if (isUSCitizen === 'no') {
    return false;
  }
  return false;
};
