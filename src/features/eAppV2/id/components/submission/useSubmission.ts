import { NavigationProp, useNavigation } from '@react-navigation/native';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { format } from 'date-fns';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { Plan } from 'types/quotation';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { PartyRole } from 'types/party';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import { getProductName } from 'features/eAppV2/common/utils/eAppFormat';
import { useRiders } from 'features/eAppV2/common/hooks/useRiders';
import { i18n } from 'utils';
import { CaseStatus } from 'types/case';

const useSubmission = () => {
  const { t } = useTranslation(['eApp', 'common']);
  const [loading, setLoading] = useState(true);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  useEffect(() => {
    setTimeout(() => {
      setLoading(false);
    }, 3000);
  }, []);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);

  const { data: optionList } = useGetOptionList();

  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => isRole(p, PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const insuredName = useMemo(
    () =>
      caseObj?.parties?.find(p => isRole(p, PartyRole.INSURED))?.person?.name
        .firstName,
    [caseObj?.parties],
  );

  const dateSubmitted = useMemo(() => {
    return format(new Date(), 'd LLL yyyy');
  }, []);

  const quotation = useSelectedQuotation();
  const basicPlan = quotation?.plans?.[0];
  const currency = basicPlan?.currency;
  const productName = useMemo(
    () => getProductName(basicPlan?.productName, i18n.language),
    [basicPlan],
  );

  const { basicAnnualPremium, initialPremium } = useMemo(() => {
    return {
      basicAnnualPremium: quotation?.summary?.annualPrem,
      initialPremium: quotation?.summary?.initialPrem,
    };
  }, [quotation]);

  const paymentMode = useMemo(() => {
    return (
      optionList?.PAYMENTMODE4.options.find(
        e => e.value === quotation?.basicInfo.paymentMode,
      )?.label ?? t('eApp:singlePremium')
    );
  }, [optionList?.PAYMENTMODE4.options, quotation?.basicInfo.paymentMode, t]);

  const riders = useRiders(quotation);

  const riderPairs = useMemo(() => {
    return riders.reduce<Array<Plan[]>>(function (result, value, index, array) {
      if (index % 2 === 0) result.push(array.slice(index, index + 2));
      return result;
    }, []);
  }, [riders]);

  const shouldShowWaitingForApproval =
    caseObj?.latestStatus === CaseStatus.PENDING_FOR_LEADER;

  return {
    shouldShowWaitingForApproval,
    policyOwner,
    caseObj,
    loading,
    insuredName,
    dateSubmitted,
    productName,
    basicPlan,
    paymentMode,
    basicAnnualPremium,
    initialPremium,
    riderPairs,
    riders,
    navigation,
    currency,
  };
};

export default useSubmission;
