import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import MarkdownText from 'components/MarkdownText';
import { Column, H6 } from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useOcrImage } from 'features/eAppV2/common/hooks/useOcrImage';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import IdentityVerificationTablet from 'features/eAppV2/id/components/applicationDetails/policyOwner/identityVerification/IdentityVerification.tablet';
import useOcrLogic, { OCRForm } from 'features/eAppV2/id/hooks/useOcrLogic';
import {
  partyToPolicyOwnerInfo,
  PolicyOwnerInfoForm,
  policyOwnerInfoSchema,
} from 'features/eAppV2/id/validations/applicationDetails/policyOwner/policyOwnerInfoValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetPopulateCase } from 'hooks/useGetPopulateCase';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  FormProvider,
  useForm,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Case } from 'types/case';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { usePartySubmission } from '../../../hooks/usePartySubmission';
import DukcapilValidationPopup from '../../DukcapilValidationPopup';
import ApplicationsModal from '../sections/PopulatableApplications/ApplicationsModal';
import AddressInfo from './addressInfo/AddressInfo.tablet';
import AdditionalDetails from './addtionalDetails/AdditionalDetail.tablet';
import ContactDetailsTablet from './contactDetails/ContactDetails.tablet';
import OccupationDetailsTablet from './occupationDetails/OccupationDetails.tablet';
import PersonalDetails from './personalDetails/PersonalDetails.tablet';
import PremiumPayment from './premiumPayment/PremiumPayment.tablet';
import PassionSurvey from './passionSurvey/PassionSurvey.tablet';

interface PolicyOwnerFormProps {
  onNext: () => void;
  isOwnerInsured: boolean;
}

// create the form here
export default function PolicyOwnerForm({
  onNext,
  isOwnerInsured,
}: PolicyOwnerFormProps) {
  const { space } = useTheme();
  const { t } = useTranslation('eApp');
  const [appsModalVisible, setAppsModalVisible] = useState(false);

  const [populatableApplications] = useState<Case[]>([]);
  // Fetch policy owner data from API

  const { data: optionList, isLoading: optionListIsLoading } =
    useGetOptionList<'id'>();
  const { data: agentProfile, isLoading: agentProfileIsLoading } =
    useGetAgentProfile();
  const { caseObj } = useGetActiveCase();
  const policyOwnerParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  // Initialize React Hook Form
  const resolver = useYupResolver(policyOwnerInfoSchema);
  const useFormMethods = useForm<PolicyOwnerInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () =>
        partyToPolicyOwnerInfo(
          policyOwnerParty,
          caseObj?.application,
          optionList,
        ),
      [], //eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver,
    context: {
      optionList: optionList,
      agentId: agentProfile?.person?.idNumber,
      agentMobileNumber: agentProfile?.contact?.mobilePhone,
      agentEmail: agentProfile?.contact?.email,
    },
  });
  const {
    control,
    watch,
    handleSubmit,
    trigger,
    setValue,
    getValues,
    setError,
    formState: { isValid, isSubmitting },
  } = useFormMethods;

  const { isGettingPopulatableApplications, setPopulateCaseId } =
    useGetPopulateCase({
      ownerParty: policyOwnerParty,
      formTrigger: trigger,
    });
  const isInitiating =
    optionListIsLoading ||
    agentProfileIsLoading ||
    isGettingPopulatableApplications;
  // Handle form submission

  const { onSave, dukcapilResult, resetDukcapilResult } = usePartySubmission({
    role: PartyRole.PROPOSER,
    setError,
  });

  const { saveOcrImage } = useSaveOcrImage();

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control: control,
      schema: policyOwnerInfoSchema,
      watch: watch,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition?.(0, option.y || 0, true),
    });
  useEffect(() => {
    return () => {
      // warn the user the form is not yet saved
    };
  }, []);

  // For demo liveness check
  const [isFaceMatched, setIsFaceMatched] = useState<boolean | undefined>(
    undefined,
  );
  const [isLivenessCheckVerified, setIsLivenessCheckVerified] = useState<
    boolean | undefined
  >(undefined);

  const ocrImage = useOcrImage(policyOwnerParty?.id);

  const { ocrLogic, isOCREnabled, ocrFile } = useOcrLogic({
    role: PartyRole.PROPOSER,
    getValues: getValues as unknown as UseFormGetValues<OCRForm>,
    setValue: setValue as unknown as UseFormSetValue<OCRForm>,
    ocrImage,
    isPIEqualPO: isOwnerInsured,
    defaultIsOcrSuccess: policyOwnerParty?.person?.isOcrSuccess,
  });

  const onSubmit = async () => {
    await handleSubmit(async data => {
      const id = await onSave({
        values: data,
        isOcrSuccess: ocrLogic.isOcrSuccess,
        isLivenessCheckVerified: isLivenessCheckVerified,
        isFaceMatched: isFaceMatched,
      });

      if (id) {
        if (isOCREnabled) await saveOcrImage(id, ocrFile, PartyRole.PROPOSER);
        onNext();
      }
    })();
  };

  return (
    <FormProvider {...useFormMethods}>
      <ScrollViewContainer
        ref={scrollRef}
        contentContainerStyle={{ paddingBottom: space[30] }}>
        <Column gap={space[4]}>
          <H6 fontWeight="bold">{t('policyOwnerInfo')}</H6>
          <MarkdownText>{t('policyOwnerInfo.declaration')}</MarkdownText>
        </Column>
        {/* TODO: face to face switch */}
        {isOCREnabled && (
          <IdentityVerificationTablet
            role={PartyRole.PROPOSER}
            isFaceMatched={isFaceMatched}
            onSetIsFaceMatched={setIsFaceMatched}
            isLivenessCheckVerified={isLivenessCheckVerified}
            onSetIsLivenessCheckVerified={setIsLivenessCheckVerified}
            ocrLogic={ocrLogic}
          />
        )}
        <PersonalDetails />
        <OccupationDetailsTablet />
        <ContactDetailsTablet />
        <AddressInfo role="main-party" enableCorrespondenceSelector={true} />
        <AdditionalDetails />
        <PremiumPayment />
        <PassionSurvey />
      </ScrollViewContainer>
      <EAppFooterTablet
        progressLock="appDetail-policyOwner"
        primaryDisabled={
          isOCREnabled ? !(isValid && ocrLogic.isOcrSuccess) : !isValid
        }
        primaryLabel={t('next')}
        onPrimaryPress={onSubmit}
        primaryLoading={isSubmitting}
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        isAbsolutePositioned
      />
      <ApplicationsModal
        isVisible={appsModalVisible}
        onClose={() => setAppsModalVisible(false)}
        onConfirm={setPopulateCaseId}
        applications={populatableApplications}
        owner={policyOwnerParty}
      />

      <DukcapilValidationPopup
        visible={dukcapilResult?.code === 'NOT_ALLOW_PROCEED'}
        onClose={resetDukcapilResult}
        result={dukcapilResult}
      />
    </FormProvider>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space, colors } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
    backgroundColor: colors.surface,
  }),
);
