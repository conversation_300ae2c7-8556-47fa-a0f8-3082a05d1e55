import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import {
  IdentityVerificationProps,
  LivenessCheck,
} from 'features/livenessCheck';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import HeadShotSvg from 'features/eAppV2/ph/components/applicationDetails/common/assets/HeadShotSvg';

const IdentityVerificationTablet = (props: IdentityVerificationProps) => {
  const { t } = useTranslation('livenessCheck');
  const { space } = useTheme();
  const { isOcrSuccess, ocrValidationMismatchFields } = props.ocrLogic;

  // liveness check is disabled for now
  const isOcrMatched =
    !!isOcrSuccess && Object.keys(ocrValidationMismatchFields).length === 0;

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('identityVerification.title')}
      icon={<HeadShotSvg size={40} />}
      style={{ gap: space[3] }}
      isDone={isOcrMatched}>
      <Container>
        <LivenessCheck {...props} />
      </Container>
    </ApplicationDetailsTabletSectionContainer>
  );
};

const Container = styled.View(({ theme: { space } }) => ({
  paddingHorizontal: space[6],
}));

export default IdentityVerificationTablet;
