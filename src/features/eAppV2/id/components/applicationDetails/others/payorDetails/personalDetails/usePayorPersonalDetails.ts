import { ID_COUNTRY, IDN_ID_TYPE, NEW_NRIC } from 'constants/optionList';
import { useAutoPopulateGender } from 'features/eAppV2/common/hooks/useAutoPopulateGender';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useNricForm } from 'features/eAppV2/common/hooks/useNricForm';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import {
  payorPersonalDetailsDefaultValue,
  payorPersonalDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/payorPersonalDetails';
import { useGetOptionList, useGetTitleList } from 'hooks/useGetOptionList';
import { useCallback, useEffect, useMemo } from 'react';
import { useController, useFormContext, useWatch } from 'react-hook-form';
import { Relationship } from 'types/optionList';
import { Gender } from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';

const isDigitRex = /^[0-9]+$/;

const usePayorPersonalDetails = () => {
  const { control, setValue, trigger, watch } = useFormContext();
  const primaryIdType = useWatch({
    name: 'primaryIdType',
    control: control,
  });
  const {
    field: { value: primaryId, onChange: onChangePrimaryId },
  } = useController({
    name: `primaryId`,
    control: control,
  });
  const personalDob = useWatch({
    name: 'dob',
    control: control,
  });
  const additionalIdType = useWatch({
    name: 'additionalIdType',
    control: control,
  });
  const nationality = useWatch({
    name: 'nationality',
    control: control,
  });

  const IDTypeOptions = [
    {
      value: IDN_ID_TYPE.KTP,
      label: 'KTP',
    },
    {
      value: IDN_ID_TYPE.Passport,
      label: 'Passport',
    },
  ];

  const isNricNew = primaryIdType === NEW_NRIC;
  const isIDCountry = nationality === 'RI';

  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'id'>();

  const { maleTitles, femaleTitles } = useGetTitleList(
    optionList?.CUBE_TITLE.options || [],
  );

  const isEntity = useCheckEntity();

  const relationships = useMemo(() => {
    return (
      (optionList?.RELATIONSHIP?.options ?? []) as Relationship<string, 'id'>[]
    ).filter(
      r =>
        r.role.payer === 'True' &&
        (isEntity ? r.party.entity === 'True' : r.party.individual === 'True'),
    );
  }, [isEntity, optionList?.RELATIONSHIP?.options]);

  useAutoPopulateGender({
    control,
    gender: 'gender',
    title: 'title',
  });

  useNricForm({
    control,
    trigger,
    title: 'title',
    dob: 'dob',
    gender: 'gender',
    idType: 'primaryIdType',
    id: 'primaryId',
  });

  const personalAge = useMemo(() => {
    if (personalDob) {
      return `${calculateAge(new Date(personalDob))}`;
    } else {
      return '';
    }
  }, [personalDob]);

  const onChangeAdditionalIdType = useCallback(
    (value: string | null) => {
      if (value === additionalIdType || value === '') {
        setValue('additionalId', '');
        setValue('additionalIdType', '');
      }
    },
    [additionalIdType, setValue],
  );

  const onGenderChange = useCallback(
    (value: string) => {
      setValue(
        'title',
        value === Gender.MALE ? maleTitles[0]?.value : femaleTitles[0]?.value,
      );
    },
    [maleTitles, femaleTitles, setValue],
  );

  const isValid = useSchemaValid(
    control,
    payorPersonalDetailsDefaultValue,
    payorPersonalDetailsSchema,
  );

  useEffect(() => {
    if (nationality === ID_COUNTRY) {
      if (!primaryId.match(isDigitRex)) {
        setValue('primaryId', '');
      }
      setValue('primaryIdType', IDN_ID_TYPE.KTP);
    } else {
      setValue('primaryIdType', IDN_ID_TYPE.Passport);
    }
  }, [nationality, setValue, primaryId]);

  return {
    control,
    setValue,
    trigger,
    watch,
    isValid,
    optionList,
    isLoadingOptionList,
    primaryIdType,
    onChangePrimaryId,
    isNricNew,
    personalAge,
    relationships,
    onChangeAdditionalIdType,
    additionalIdType,
    IDTypeOptions,
    nationality,
    isIDCountry,
    onGenderChange,
  };
};

export default usePayorPersonalDetails;
