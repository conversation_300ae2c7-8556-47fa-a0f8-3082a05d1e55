import { useTheme } from '@emotion/react';
import { Box, Column, LargeBody, Row, Switch } from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useOcrImage } from 'features/eAppV2/common/hooks/useOcrImage';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import useOcrLogic, { OCRForm } from 'features/eAppV2/id/hooks/useOcrLogic';
import { PayorInfoForm } from 'features/eAppV2/id/validations/applicationDetails/payor/payorValidation';
import React, { useEffect, useState } from 'react';
import {
  FormProvider,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Case } from 'types/case';
import { PartyRole } from 'types/party';
import DukcapilValidationPopup from '../../../DukcapilValidationPopup';
import IdentityVerificationTablet from '../../policyOwner/identityVerification/IdentityVerification.tablet';
import ApplicationsModal from '../../sections/PopulatableApplications/ApplicationsModal';
import AddressInfo from './addressInformation/AddressInformation.tablet';
import ContactDetails from './contactDetails/ContactDetails.tablet';
import OccupationDetails from './occupationDetails/OccupationDetails.tablet';
import PersonalDetails from './personalDetails/PersonalDetails.tablet';
import { usePayorDetails } from './usePayorDetails';
interface Props {
  onNext: () => void;
}

export default function PayorDetailsTablet({ onNext }: Props) {
  const {
    formMethods,
    hasPayor,
    setHasPayor,
    scrollRef,
    isValid,
    isSubmitting,
    focusOnNextIncompleteField,
    totalIncompleteRequiredFields,
    setPopulateCaseId,
    insured,
    onSave,
    dukcapilResult,
    resetDukcapilResult,
    isLoading,
    payorParty,
  } = usePayorDetails({ payorRole: PartyRole.PAYER });

  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const [appsModalVisible, setAppsModalVisible] = useState(false);
  const [populatableApplications] = useState<Case[]>([]);

  const { saveOcrImage } = useSaveOcrImage();

  const onSubmit = async () => {
    const id = await onSave({
      isOcrSuccess: ocrLogic?.isOcrSuccess,
      isLivenessCheckVerified,
      isFaceMatched,
    });

    if (id) {
      if (isOCREnabled) await saveOcrImage(id, ocrFile, PartyRole.PAYER);
      onNext();
    }
  };

  const [isFaceMatched, setIsFaceMatched] = useState<boolean | undefined>(
    undefined,
  );
  const [isLivenessCheckVerified, setIsLivenessCheckVerified] = useState<
    boolean | undefined
  >(undefined);

  const ocrImage = useOcrImage(payorParty?.id);
  const { ocrLogic, isOCREnabled, ocrFile } = useOcrLogic({
    role: PartyRole.PAYER,
    getValues: formMethods.getValues as unknown as UseFormGetValues<OCRForm>,
    setValue: formMethods.setValue as unknown as UseFormSetValue<OCRForm>,
    ocrImage,
    defaultIsOcrSuccess: payorParty?.person?.isOcrSuccess,
  });

  useEffect(() => {
    if (hasPayor) {
      const values = formMethods.getValues();

      const filledFieldNames = Object.entries(values)
        .filter(
          ([_, value]) => value !== '' && value !== null && value !== undefined,
        )
        .map(([key]) => key);

      formMethods.trigger(filledFieldNames as Array<keyof PayorInfoForm>);
    }
  }, [hasPayor]);

  let primaryDisabled = false;
  if (hasPayor) {
    if (isOCREnabled) {
      primaryDisabled = !(isValid && ocrLogic.isOcrSuccess);
    } else {
      primaryDisabled = !isValid;
    }
  }

  return (
    <FormProvider {...formMethods}>
      <KeyboardAwareScrollView
        ref={scrollRef}
        keyboardDismissMode="interactive"
        keyboardShouldPersistTaps="handled"
        extraScrollHeight={20}
        keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
        enableAutomaticScroll={true}
        contentContainerStyle={{ paddingBottom: space[30] }}>
        <Box pl={0} p={space[6]}>
          <Row
            alignItems="flex-start"
            pb={space[4]}
            display="flex"
            justifyContent="space-between">
            <LargeBody
              fontWeight="medium"
              children={t('eApp:other.beneficialOwnerDetails.question')}
            />
            <Switch
              label={!hasPayor ? t('eApp:yes') : t('eApp:no')}
              checked={!hasPayor}
              onChange={value => setHasPayor(!value)}
            />
          </Row>
          {hasPayor && (
            <Column>
              {/* payor should always render the ocr section */}
              <IdentityVerificationTablet
                role={PartyRole.PAYER}
                isFaceMatched={isFaceMatched}
                onSetIsFaceMatched={setIsFaceMatched}
                isLivenessCheckVerified={isLivenessCheckVerified}
                onSetIsLivenessCheckVerified={setIsLivenessCheckVerified}
                ocrLogic={ocrLogic}
                disableFaceRecognition
              />
              <PersonalDetails />
              <OccupationDetails />
              <ContactDetails />
              <AddressInfo enableCorrespondenceSelector={true} />
            </Column>
          )}
        </Box>
      </KeyboardAwareScrollView>
      <EAppFooterTablet
        progressLock="appDetail-others"
        primaryDisabled={primaryDisabled}
        primaryLabel={t('next')}
        onPrimaryPress={onSubmit}
        primaryLoading={isLoading}
        focusOnIncompleteField={focusOnNextIncompleteField}
        totalIncompleteRequiredFields={
          hasPayor ? totalIncompleteRequiredFields : 0
        }
        isAbsolutePositioned
      />
      <ApplicationsModal
        isVisible={appsModalVisible}
        onClose={() => setAppsModalVisible(false)}
        onConfirm={setPopulateCaseId}
        applications={populatableApplications}
        owner={insured}
      />

      <DukcapilValidationPopup
        visible={dukcapilResult?.code === 'NOT_ALLOW_PROCEED'}
        onClose={resetDukcapilResult}
        result={dukcapilResult}
      />
    </FormProvider>
  );
}
