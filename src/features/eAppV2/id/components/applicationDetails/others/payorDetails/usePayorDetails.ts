import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import {
  toDukcapilPayload,
  useDukcapilValidation,
} from 'features/eAppV2/id/hooks/useDukcapilValidation';
import {
  partyToPayorInfo,
  PayorInfoForm,
  payorInfoSchema,
  payorInfoToParty,
} from 'features/eAppV2/id/validations/applicationDetails/payor/payorValidation';
import { useAlert } from 'hooks/useAlert';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetPopulateCase } from 'hooks/useGetPopulateCase';
import { useDeleteParty, useSaveParty } from 'hooks/useParty';
import useToggle from 'hooks/useToggle';
import { useUpdateCase } from 'hooks/useUpdateCase';
import { useCallback, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';

interface OnSaveParams {
  isOcrSuccess?: boolean;
  isLivenessCheckVerified?: boolean;
  isFaceMatched?: boolean;
}

export const usePayorDetails = ({ payorRole }: { payorRole: PartyRole }) => {
  const { alertError } = useAlert();
  const { caseObj } = useGetActiveCase();
  const policyOwner = useMemo(
    () =>
      caseObj?.parties?.find(({ roles }) => roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );
  const renewalPayor = useMemo(
    () =>
      caseObj?.parties?.find(({ roles }) =>
        roles.includes(PartyRole.RENEWAL_PAYER),
      ),
    [caseObj?.parties],
  );

  const { t } = useTranslation(['eApp']);
  const payorParty = useMemo(
    () =>
      caseObj?.parties?.find(({ roles }) => roles.includes(PartyRole.PAYER)),
    [caseObj?.parties],
  );
  const { data: optionList } = useGetOptionList<'id'>();
  const { mutateAsync: updateCase } = useUpdateCase();
  const { saveParty } = useSaveParty();
  const { mutateAsync: deleteParty } = useDeleteParty();
  const [hasPayor, setHasPayor] = useState(() => Boolean(caseObj?.havePayer));
  const [hasRenewalPayor, setHasRenewalPayor] = useState(
    () => Boolean(renewalPayor) && renewalPayor?.id !== policyOwner?.id,
  );
  const [isLoading, showLoading, hideLoading] = useToggle();

  const {
    mutateAsync: validateDukcapil,
    data: dukcapilResult,
    reset: resetDukcapilResult,
  } = useDukcapilValidation();

  const insured = useMemo(
    () =>
      caseObj?.parties?.find(({ roles }) => roles.includes(PartyRole.INSURED)),
    [caseObj?.parties],
  );

  const ownerPrimaryId = policyOwner?.person?.registrations?.find(
    r => r.type === 'DEFAULT',
  );

  const insuredPrimaryId = insured?.person?.registrations?.find(
    r => r.type === 'DEFAULT',
  );

  const hasInsured = policyOwner?.id !== insured?.id;

  const formMethods = useForm<PayorInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () =>
        partyToPayorInfo(
          getPartiesByRole(caseObj, payorRole)?.[0],
          payorRole === PartyRole.PAYER ? hasPayor : hasRenewalPayor,
          optionList,
        ),
      [caseObj, payorRole, hasPayor, hasRenewalPayor, optionList],
    ),
    resolver: useYupResolver(payorInfoSchema),
    context: {
      optionList: optionList,
      hasInsured: hasInsured,
      policyOwnerFullName: policyOwner?.person?.name?.fullName,
      policyOwnerIdNumber: ownerPrimaryId?.id,
      policyOwnerIdType: ownerPrimaryId?.idType,
      insuredFullName: insured?.person?.name?.fullName,
      insuredIdNumber: insuredPrimaryId?.id,
      insuredIdType: insuredPrimaryId?.idType,
    },
  });

  const {
    control: payorDetailsFormControl,
    watch: watchPayorDetails,
    formState: { isValid, isSubmitting },
    setValue: setValuePayorDetails,
    handleSubmit: handleSubmitPayorDetails,
    trigger,
  } = formMethods;

  const { isGettingPopulatableApplications, setPopulateCaseId } =
    useGetPopulateCase({
      ownerParty: insured,
      formTrigger: trigger,
    });

  const payorScrollRef = useRef<KeyboardAwareScrollView>(null);

  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      schema: payorInfoSchema,
      watch: watchPayorDetails,
      control: payorDetailsFormControl,
      scrollRef: payorScrollRef,
      scrollTo: ({ y }) =>
        payorScrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  const onSave = useCallback(
    async ({
      isOcrSuccess,
      isLivenessCheckVerified,
      isFaceMatched,
    }: OnSaveParams) => {
      if (!optionList)
        throw new Error('missing option list while saving payor');
      if (!caseObj) throw new Error('missing case data while saving payor');
      showLoading();
      let id;

      try {
        const thirdPartyPayers = caseObj?.parties?.filter(
          ({ roles }) =>
            roles.includes(payorRole) && !roles.includes(PartyRole.PROPOSER),
        );
        if (thirdPartyPayers && thirdPartyPayers.length > 0) {
          for (let i = 0; i < thirdPartyPayers.length; i++) {
            await deleteParty({
              caseId: caseObj.id,
              partyId: thirdPartyPayers[i].id,
            });
          }
        }

        const shouldCreateParty =
          (payorRole === PartyRole.PAYER && hasPayor) ||
          (payorRole === PartyRole.RENEWAL_PAYER && hasRenewalPayor);

        if (shouldCreateParty) {
          await handleSubmitPayorDetails(async (values: PayorInfoForm) => {
            const dukcapilPayload = toDukcapilPayload(values);
            const result = dukcapilPayload
              ? await validateDukcapil({
                  ...dukcapilPayload,
                  caseId: caseObj.id,
                })
              : null;

            if (result?.code === 'NOT_ALLOW_PROCEED') {
              formMethods.setError('primaryId', {
                message: t('eApp:dataMismatch'),
              });
              return;
            }

            id = await saveParty({
              ...payorInfoToParty({
                form: values,
                optionList,
                role: payorRole,
                isOcrSuccess,
                isLivenessCheckVerified,
                isFaceMatched,
                dukcapilServiceStatus: result?.flag,
              }),
            });

            if (payorRole === PartyRole.PAYER) {
              await updateCase({
                caseId: caseObj.id,
                havePayer: true,
              });
            }
            if (policyOwner?.roles.includes(payorRole)) {
              await saveParty(
                {
                  ...policyOwner,
                  roles: policyOwner.roles.filter(r => r !== payorRole),
                },
                { overridingRoles: true },
              );
            }
            if (id) {
              setValuePayorDetails('id', id);
            }
          })();
        } else {
          if (policyOwner) {
            id = await saveParty(
              {
                ...policyOwner,
                roles: policyOwner.roles.concat(payorRole),
              },
              { overridingRoles: true },
            );
            if (payorRole === PartyRole.PAYER) {
              await updateCase({
                caseId: caseObj.id,
                havePayer: false,
              });
            }
          }
        }

        return id;
      } catch {
        alertError('Failed while saving payor');
        throw new Error('Failed while saving payor');
      } finally {
        hideLoading();
      }
    },
    [
      caseObj,
      deleteParty,
      handleSubmitPayorDetails,
      hasPayor,
      hasRenewalPayor,
      hideLoading,
      optionList,
      policyOwner,
      saveParty,
      setValuePayorDetails,
      showLoading,
      updateCase,
      payorRole,
      validateDukcapil,
      t,
      formMethods,
      alertError,
    ],
  );

  return {
    onSave,
    hasPayor,
    setHasPayor,
    hasRenewalPayor,
    setHasRenewalPayor,
    isLoading,
    focusOnNextIncompleteField,
    totalIncompleteRequiredFields,
    isValid,
    isSubmitting,
    formMethods,
    scrollRef: payorScrollRef,
    isGettingPopulatableApplications,
    setPopulateCaseId,
    insured,
    dukcapilResult,
    resetDukcapilResult,
    validateDukcapil,
    payorParty,
  };
};
