import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import DatePickerCalendar from 'components/DatePickerCalendar';
import IdNumberField from 'components/IdNumberField';
import Input from 'components/Input';
import NameField from 'components/NameField';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import {
  Column,
  Picker,
  PictogramIcon,
  Row,
  TextField,
} from 'cube-ui-components';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import usePayorPersonalDetails from 'features/eAppV2/id/components/applicationDetails/others/payorDetails/personalDetails/usePayorPersonalDetails';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Country,
  CubeTitle,
  MaritalStatus,
  Nationality,
  Religion,
} from 'types/optionList';
import { Gender, Title } from 'types/person';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getDateOfBirthDropdownProps from 'utils/helper/getDateOfBirthDropdownProps';

const { defaultDate, maxDate, minDate } = getDateOfBirthDropdownProps();
export default function PersonalDetails() {
  const { t } = useTranslation(['eApp']);
  const { space, sizes, colors } = useTheme();

  const {
    control,
    watch,
    isValid,
    optionList,
    isLoadingOptionList,
    primaryIdType,
    isNricNew,
    personalAge,
    relationships,
    IDTypeOptions,
    nationality,
    isIDCountry,
    onGenderChange,
  } = usePayorPersonalDetails();

  const gender = watch('gender');
  const title = watch('title');

  const titleOption = useMemo(() => {
    if (gender === Gender.MALE) {
      return optionList?.CUBE_TITLE?.options?.find(
        o => o.gender.male === 'True',
      );
    }
    if (gender === Gender.FEMALE) {
      return optionList?.CUBE_TITLE?.options?.find(
        o => o.gender.female === 'True',
      );
    }
  }, [gender, optionList?.CUBE_TITLE?.options]);

  const genderOption = useMemo(() => {
    if (title === Title.MR) {
      return optionList?.GENDER?.options?.find(o => o.value === Gender.MALE);
    }
    if (title === Title.MS) {
      return optionList?.GENDER?.options?.find(o => o.value === Gender.FEMALE);
    }
  }, [title, optionList?.CUBE_TITLE?.options]);

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:applicationDetails.personalDetails')}
      icon={<PictogramIcon.Lanyard size={sizes[10]} />}
      isDone={Boolean(isValid)}>
      <Column px={space[6]} gap={space[5]}>
        <RowBox>
          <Input
            control={control}
            as={Picker}
            name="relationship"
            type="chip"
            label={t(
              'eApp:other.beneficialOwnerDetails.relationshipToPolicyOwner',
            )}
            items={relationships}
            labelStyle={eAppCommonStyles.pickerLabel}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
            initialHighlight={true}
            preventFocus={true}
          />
        </RowBox>
        {/* 1 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<CubeTitle, string>}
            name="title"
            label={t('eApp:title')}
            value={titleOption?.value}
            data={optionList?.CUBE_TITLE?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            control={control}
            as={NameField}
            name="fullName"
            label={t('eApp:fullName')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        <RowBox>
          <Row flex={1} gap={space[3]}>
            <Input
              control={control}
              as={DatePickerCalendar}
              name="dob"
              label={t('eApp:dateOfBirth')}
              hint={t('eApp:dateFormat')}
              defaultDate={defaultDate}
              minDate={minDate}
              maxDate={maxDate}
              formatDate={val => (val ? dateFormatUtil(val) : '')}
              disabled={isNricNew}
              style={eAppCommonStyles.tabletDob}
              shouldHighlightOnUntouched={value => !value}
            />
            <TextField
              label={t('eApp:certificate.form.age')}
              value={personalAge}
              disabled
              style={eAppCommonStyles.tabletAge}
            />
          </Row>
          <Input
            control={control}
            as={Picker}
            name="gender"
            label={t('eApp:gender')}
            value={genderOption?.value}
            items={
              optionList?.GENDER?.options.map(({ value, label }) => ({
                value,
                label,
              })) || []
            }
            shouldHighlightOnUntouched={value => !value}
            style={eAppCommonStyles.tabletTextField}
            onChange={onGenderChange}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={Autocomplete<MaritalStatus, string>}
            name="maritalStatus"
            label={t('eApp:maritalStatus')}
            data={optionList?.MARITAL_STATUS?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            control={control}
            as={Autocomplete<Religion, string>}
            name="religion"
            label={t('eApp:religion')}
            data={optionList?.RELIGION?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={Autocomplete<Nationality, string>}
            name="nationality"
            label={t('eApp:nationality')}
            data={optionList?.COUNTRY?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            disabled={Boolean(nationality)}
            control={control}
            as={Picker}
            name="primaryIdType"
            type="chip"
            label={t('eApp:idType')}
            items={IDTypeOptions ?? []}
            labelStyle={eAppCommonStyles.pickerLabel}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={IdNumberField}
            idType={primaryIdType}
            name="primaryId"
            label={isIDCountry ? t('eApp:idNumber') : t('eApp:passportNumber')}
            keyboardType={isIDCountry ? 'number-pad' : 'default'}
            returnKeyType={isIDCountry ? 'done' : 'default'}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            control={control}
            as={Autocomplete<Country<string, 'id'>, string>}
            name="countryOfBirth"
            label={t('eApp:countryOfBirth')}
            data={optionList?.COUNTRY?.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
        </RowBox>

        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="placeOfBirth"
            label={t('eApp:placeOfBirth')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={value => !value}
          />
          <Input
            control={control}
            as={TextField}
            name="taxIdNumber"
            label={t('eApp:taxIdOptional')}
            style={eAppCommonStyles.tabletTextField}
          />
        </RowBox>
      </Column>
    </ApplicationDetailsTabletSectionContainer>
  );
}

const RowBox = styled(Row)(({ theme: { space } }) => ({
  flexDirection: 'row',
  gap: space[6],
}));
