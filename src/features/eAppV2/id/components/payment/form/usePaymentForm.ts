import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useMemo, useState } from 'react';
import { PartyRole } from 'types/party';
import { PaymentMethod } from './methods/paymentMethodTypes';
import { useDokuPaymentUrl } from 'hooks/useDokuPayment';
import * as WebBrowser from 'expo-web-browser';
import { useAlert } from 'hooks/useAlert';

export type AdvancePayment = {
  duration: number;
  amount: number;
};

export interface PaymentSubmissionProps {
  selectedMethod: PaymentMethod | null;
}

const usePaymentForm = ({ selectedMethod }: PaymentSubmissionProps) => {
  const { alertError } = useAlert();

  const [isTermConditionVisible, setTermConditionVisible] = useState(false);
  const showTermAndCondition = () => setTermConditionVisible(true);
  const hideTermAndCondition = () => setTermConditionVisible(false);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);

  const [advancePayment, setAdvancePayment] = useState<AdvancePayment>(() => ({
    duration: Number(caseObj?.application?.advancePayment?.duration || 0),
    amount: Number(caseObj?.application?.advancePayment?.amount || 0),
  }));

  const quotation = useSelectedQuotation();
  const paymentMode = quotation?.basicInfo?.paymentMode;
  const policyOwner = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const { mutateAsync: getDokuPaymentUrl } = useDokuPaymentUrl();

  const handleDoku = async (callbackRoute: string) => {
    if (!selectedMethod || !caseId || !caseObj || !policyOwner) return;
    const data = await getDokuPaymentUrl({
      caseId,
      callbackRoute,
    });
    if (!data.url) {
      alertError('Doku payment URL is empty');
      return;
    }
    await WebBrowser.openBrowserAsync(data.url);
  };

  return {
    paymentMode,
    showTermAndCondition,
    isTermConditionVisible,
    hideTermAndCondition,
    advancePayment,
    setAdvancePayment,
    handleDoku,
  };
};

export default usePaymentForm;
