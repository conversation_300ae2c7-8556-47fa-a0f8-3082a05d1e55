import { useCallback, useEffect, useMemo, useState } from 'react';
import { PaymentMethod } from '../form/methods/paymentMethodTypes';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { PaymentResultResponse } from 'api/dokuApi';
import * as Linking from 'expo-linking';
import usePaymentMethods from '../form/methods/usePaymentMethods';

const usePayment = () => {
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj, refetch: refetchCase } = useGetCase(caseId);
  const url = Linking.useURL();

  const { paymentMethods } = usePaymentMethods();
  const defaultPaymentMethod = useMemo(() => {
    return paymentMethods?.find(method => !method.hidden);
  }, [paymentMethods]);

  const [selectedPaymentMethod, setSelectedPaymentMethod] =
    useState<PaymentMethod | null>(
      defaultPaymentMethod
        ? (defaultPaymentMethod.value as PaymentMethod)
        : null,
    );
  const [paymentResponse, setPaymentResponse] = useState<
    PaymentResultResponse | null | undefined
  >(
    caseObj?.application?.initialPayment
      ? (
          caseObj?.application?.initialPayment as {
            paymentResponse?: PaymentResultResponse;
          }
        )?.paymentResponse
      : null,
  );

  const [proceedOfflineSubmission, setProceedOfflineSubmission] =
    useState(false);

  const onBackPress = useCallback(async () => {
    setSelectedPaymentMethod(null);
  }, []);

  useEffect(() => {
    if (selectedPaymentMethod === null && paymentResponse !== null) {
      setPaymentResponse(null);
    }
  }, [selectedPaymentMethod, paymentResponse]);

  // refetch the case when back to merchant
  useEffect(() => {
    async function getNewCase() {
      await refetchCase().then(res => {
        const newCaseObj = res.data;
        setPaymentResponse(
          newCaseObj?.application?.initialPayment
            ? (newCaseObj.application.initialPayment as PaymentResultResponse)
            : null,
        );
      });
    }
    if (url) {
      const host = Linking.parse(url).hostname;
      if (host === 'payment--') {
        getNewCase();
      }
    }
  }, [url, refetchCase]);

  return {
    selectedPaymentMethod,
    setSelectedPaymentMethod,
    paymentResponse,
    setPaymentResponse,
    onBackPress,
    proceedOfflineSubmission,
    setProceedOfflineSubmission,
  };
};

export default usePayment;
