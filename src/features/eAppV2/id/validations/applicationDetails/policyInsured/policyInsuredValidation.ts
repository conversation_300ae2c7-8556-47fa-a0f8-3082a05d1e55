import { OptionList } from 'types/optionList';
import { Party, PartyRole, PartyType } from 'types/party';
import { InferType } from 'yup';
import {
  mainPartyAddressInformationDefaultValue,
  mainPartyAddressInformationSchema,
  mainPartyAddressInformationToParty,
  partyToMainPartyAddressInformation,
} from '../sections/addressInformation';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
  contactDetailsToParty,
  partyToContactDetails,
} from '../sections/contactDetails';
import {
  mainPartyPersonalDetailsDefaultValue,
  mainPartyPersonalDetailsSchema,
  mainPartyPersonalDetailsToParty,
  partyToMainPartyPersonalDetails,
} from '../sections/mainPartyPersonalDetails';
import {
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
  occupationDetailsToParty,
  partyToOccupationDetails,
} from '../sections/occupationDetails';

export const policyInsuredInfoSchema = mainPartyPersonalDetailsSchema
  .concat(occupationDetailsSchema)
  .concat(contactDetailsSchema)
  .concat(mainPartyAddressInformationSchema);

export type PolicyInsuredInfoForm = InferType<typeof policyInsuredInfoSchema>;

export const policyInsuredDefaultValue = {
  ...mainPartyPersonalDetailsDefaultValue,
  ...occupationDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...mainPartyAddressInformationDefaultValue,
} as PolicyInsuredInfoForm;
export const partyToPolicyInsuredInfo = (
  party?: Party,
  optionList?: OptionList<string, 'id'>,
): PolicyInsuredInfoForm => {
  if (!party) return policyInsuredDefaultValue;
  return {
    ...partyToMainPartyPersonalDetails(party, optionList),
    ...partyToOccupationDetails(party, optionList),
    ...partyToContactDetails(party, optionList),
    ...partyToMainPartyAddressInformation(party),
    personalDetailsRole: 'insured',
    contactRole: 'insured',
    addressRole: 'insured',
    occupationRole: 'mainParty',
  };
};

interface PolicyInsuredInfoToPartyParams {
  form: PolicyInsuredInfoForm;
  roles: PartyRole[];
  optionList: OptionList<string, 'id'>;
  isOcrSuccess?: boolean;
  isLivenessCheckVerified?: boolean;
  isFaceMatched?: boolean;
  dukcapilServiceStatus?: string | null;
}

export const policyInsuredInfoToParty = ({
  form,
  roles,
  optionList,
  isOcrSuccess,
  isLivenessCheckVerified,
  isFaceMatched,
  dukcapilServiceStatus,
}: PolicyInsuredInfoToPartyParams): Party => {
  const { person: personForPersonalDetails, ...restOfPersonalDetails } =
    mainPartyPersonalDetailsToParty(form);
  const { person: personForOccupationDetails, ...restOfOccupationDetails } =
    occupationDetailsToParty(form, optionList);
  const contactDetails = contactDetailsToParty(form);
  const addressInformation = mainPartyAddressInformationToParty(form);
  return {
    clientType: PartyType.INDIVIDUAL,
    roles,
    ...restOfPersonalDetails,
    ...restOfOccupationDetails,
    ...contactDetails,
    ...addressInformation,
    person: {
      ...personForPersonalDetails,
      ...personForOccupationDetails,
      livenessCheck:
        isLivenessCheckVerified == null && isFaceMatched == null
          ? null
          : {
              isLivenessSuccess: isLivenessCheckVerified ?? null,
              isFaceMatched: isFaceMatched ?? null,
            },
      isOcrSuccess,
      dukcapilServiceStatus,
    } as Party['person'],
  };
};
