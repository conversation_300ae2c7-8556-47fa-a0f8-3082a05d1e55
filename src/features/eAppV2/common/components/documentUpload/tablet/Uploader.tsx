import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import { forwardRef, memo, useImperativeHandle, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import Svg, { Path } from 'react-native-svg';
import UploaderItem from './UploaderItem';
import {
  UPLOAD_COMPRESSION_CONFIG,
  UploaderProps,
} from '../DocumentUploadBase';
import { useUploader } from 'features/eAppV2/common/hooks/useUploader';
import { findNodeHandle, ScrollView, View } from 'react-native';
import ImagePickerTablet from 'components/ImagePicker/tablet/ImagePicker.tablet';
import { OcrModal } from 'features/ocr/components';

export interface UploaderRef {
  scrollToItem: (tabIndex: number, docIndex: number) => void;
}

interface CustomUploaderProps extends UploaderProps {
  scrollViewRef?: React.RefObject<ScrollView[]>;
  disabled?: boolean;
}

export const Uploader = memo(
  forwardRef<UploaderRef, CustomUploaderProps>(function Uploader(
    {
      title,
      documents,
      partyId,
      customerType,
      customerSeq,
      scrollViewRef,
      highlight,
      itemKey,
      disabled,
    }: CustomUploaderProps,
    ref,
  ) {
    const { space } = useTheme();
    const { t } = useTranslation(['eApp']);

    const {
      pickerVisible,
      showPicker,
      hidePicker,
      selectedType,
      selectedCustomerType,
      documentsWithFiles,
      onRemoveFile,
      onUploadFile,
      onUploadOCR,
      isOcrModalVisible,
      showOcrModal,
      hideOcrModal,
      handleShutter,
    } = useUploader({
      title,
      documents,
      partyId,
      customerType,
      customerSeq,
      itemKey,
    });

    const itemRefs = useRef<View[]>([]);

    const scrollToItem = (tabIndex: number, docIndex: number) => {
      if (
        itemRefs.current[docIndex] &&
        scrollViewRef &&
        scrollViewRef.current &&
        scrollViewRef.current[tabIndex]
      ) {
        const containerNode = findNodeHandle(scrollViewRef.current[tabIndex]);
        if (containerNode) {
          itemRefs.current[docIndex].measureLayout(containerNode, (x, y) => {
            scrollViewRef?.current?.[tabIndex].scrollTo?.({
              x: 0,
              y,
              animated: true,
            });
          });
        }
      }
    };

    useImperativeHandle(ref, () => ({
      scrollToItem,
    }));

    const [silentOCREnabled, setSilentOCREnabled] = useState(false);

    return (
      <Content>
        <Row alignItems="center" gap={space[2]}>
          <DocumentSubmit />
          <Typography.H6 fontWeight="bold" color="#333333">
            {title}
          </Typography.H6>
        </Row>
        <Box height={space[3]} />
        <Typography.Label color="#636566">
          {t('eApp:documentUpload.limit')}
        </Typography.Label>
        <Box height={space[3]} />
        {documentsWithFiles?.map((document, index) => (
          <UploaderItem
            disabled={disabled}
            ref={el => {
              el && (itemRefs.current[index] = el);
            }}
            title={document.title}
            files={document.files}
            type={document.type}
            key={index}
            index={index}
            onStartUploadFile={type => {
              selectedType.current = type;
              selectedCustomerType.current = document.customerType ?? '';
              setSilentOCREnabled(!!document.silentOCREnabled);
              showPicker();
            }}
            onRemoveFile={onRemoveFile}
            multiple={document.multiple}
            last={index === documentsWithFiles.length - 1}
            highlight={
              highlight && !document.optional && document.files.length === 0
            }
          />
        ))}
        {pickerVisible && (
          <ImagePickerTablet
            attachmentEnabled={!silentOCREnabled}
            config={!silentOCREnabled ? UPLOAD_COMPRESSION_CONFIG : undefined}
            title={t('eApp:documentUpload.imagePicker')}
            onOpenInAppCamera={showOcrModal}
            shouldOpenInAppCamera={!!silentOCREnabled}
            onDismiss={hidePicker}
            onDone={({ file }) =>
              silentOCREnabled
                ? onUploadOCR(
                    file,
                    selectedType.current,
                    selectedCustomerType.current,
                    true, // set from document upload to true
                  )
                : onUploadFile(
                    file,
                    selectedType.current,
                    selectedCustomerType.current,
                  )
            }
            galleryPanel={!!silentOCREnabled}
          />
        )}
        {silentOCREnabled && (
          <OcrModal
            isVisible={isOcrModalVisible}
            onShutterPress={async file => {
              hideOcrModal();
              hidePicker();
              await handleShutter(file);
            }}
            onBackPress={hideOcrModal}
          />
        )}
      </Content>
    );
  }),
);

export default Uploader;

const Content = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[6],
    paddingBottom: space[1],
    borderRadius: borderRadius.large,
  }),
);

const DocumentSubmit = memo(() => {
  const { sizes } = useTheme();
  return (
    <Svg width={sizes[10]} height={sizes[10]} fill="none">
      <Path
        fill="#E87722"
        d="M31.594 33.748H15.163a1.852 1.852 0 0 1-1.846-1.846V8.097c0-1.015.832-1.846 1.846-1.846h16.431c1.015 0 1.846.831 1.846 1.846v23.805a1.849 1.849 0 0 1-1.846 1.846Z"
      />
      <Path
        fill="#fff"
        d="M29.434 25.417H17.33c-.235 0-.423.234-.423.52 0 .289.188.52.423.52h12.105c.235 0 .423-.234.423-.52 0-.286-.191-.52-.423-.52ZM29.434 21.63H17.33c-.235 0-.423.235-.423.52 0 .29.188.52.423.52h12.105c.235 0 .423-.233.423-.52 0-.285-.191-.52-.423-.52ZM29.434 17.846H17.33c-.235 0-.423.234-.423.52 0 .288.188.52.423.52h12.105c.235 0 .423-.235.423-.52 0-.286-.191-.52-.423-.52ZM29.434 14.063H17.33c-.235 0-.423.234-.423.52 0 .288.188.52.423.52h12.105c.235 0 .423-.235.423-.52 0-.29-.191-.52-.423-.52Z"
      />
      <Path
        fill="#183028"
        d="M29.434 10.276H17.33c-.235 0-.423.235-.423.52 0 .289.188.52.423.52h12.105c.235 0 .423-.234.423-.52 0-.288-.191-.52-.423-.52Z"
      />
      <Path
        fill="#fff"
        d="M22.62 29.202h-5.197a.52.52 0 0 0 0 1.04h5.197a.52.52 0 0 0 0-1.04Z"
      />
      <Path
        fill="#F3BB90"
        fillRule="evenodd"
        d="M16.749 16.46H7.726c-.643 0-1.166.523-1.166 1.165v5.838c0 .642.523 1.165 1.166 1.165h9.023v2.112s-.009.683.26.785c.268.106.848-.425.848-.425l6.18-5.537s.652-.478.652-1.055-.812-1.128-.812-1.128l-6.02-5.395s-.566-.645-.928-.502c-.229.088-.18.725-.18.725v2.252Z"
        clipRule="evenodd"
      />
    </Svg>
  );
});
