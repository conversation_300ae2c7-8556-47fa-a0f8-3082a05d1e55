import { useCallback, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useFocusEffect } from '@react-navigation/native';
import { ImagePickerFile, resizeImage } from 'components/ImagePicker/utils';
import useToggle from 'hooks/useToggle';
import { DocumentCustomerType, DocumentType } from 'types/document';
import {
  useAddDocumentToParty,
  useGetPartyFromActiveCase,
  useSaveParty,
} from 'hooks/useParty';
import useBoundStore from 'hooks/useBoundStore';
import { useUploadDocument } from 'hooks/useUploadDocument';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import { DOCUMENT_DIR, saveFileToDocumentFolder } from 'utils/helper/fileUtils';
import { useAlert } from 'hooks/useAlert';
import {
  DocumentFile,
  UploaderProps,
} from '../components/documentUpload/DocumentUploadBase';
import * as FileSystem from 'expo-file-system';
import { useDocumentOcr } from 'hooks/useDocumentOcr';
import { useOcrDataHandler } from 'hooks/useOcrDataHandler';
import {
  applyRegionalLogicToOcrValidationResult,
  OcrValidationResult,
  validateOcr,
} from '../utils/validateOcr';
import { BuildCountry } from 'types';
import { OptionList } from 'types/optionList';
import { country } from 'utils/context';
import { Gender } from 'types/person';
import { parse } from 'date-fns';
import { useEAppStore } from '../utils/store/eAppStore';
import { OcrFile } from 'types/ocr';
import { DEFAULT_MAX_PICKER_FILE_SIZE } from 'components/ImagePicker';
import * as Crypto from 'expo-crypto';
import { PartyRole } from 'types/party';
import { shallow } from 'zustand/shallow';
import { toParty } from 'features/eAppV2/ph/utils/caseUtils';
import { AxiosError } from 'axios';
import { CubeResponse } from 'types/response';

export const useUploader = ({
  documents,
  partyId,
  customerSeq,
  customerType,
}: UploaderProps) => {
  const { t } = useTranslation(['eApp', 'common']);
  const { alertError } = useAlert();
  const [pickerVisible, showPicker, hidePicker] = useToggle();
  const [isOcrModalVisible, showOcrModal, hideOcrModal] = useToggle();
  const selectedType = useRef('');
  const selectedCustomerType = useRef('');

  const caseId = useBoundStore(state => state.case.caseId);
  const { caseObj } = useGetActiveCase();
  const { mutateAsync: uploadDocument } = useUploadDocument();
  const { mutateAsync: addDocument } = useAddDocumentToParty();

  const [files, setFiles] = useState<DocumentFile[]>([]);
  useFocusEffect(
    useCallback(() => {
      (async () => {
        const caseFiles = caseObj?.files?.filter(f => f.partyId === partyId);
        const mappedFiles: DocumentFile[] = [];
        if (caseFiles) {
          for (const file of caseFiles) {
            const fileInfo = await FileSystem.getInfoAsync(
              `${FileSystem.documentDirectory}${DOCUMENT_DIR}${file.fileName}`,
            );
            mappedFiles.push({
              base64: '',
              name: file.fileName,
              size: fileInfo.exists ? fileInfo.size / (1024 * 1024) : 0,
              status: 'uploaded',
              uri: file.filePath,
              isAttachment: false,
              fromOcr: file.fromOcr,
            });
          }
        }
        setFiles(mappedFiles);
      })();
    }, [caseObj?.files, partyId]),
  );
  const documentsWithFiles = useMemo(
    () =>
      documents?.map(i => ({
        ...i,
        files:
          files?.filter(f =>
            (f?.name ?? '').split('_').some(item => item === i.type),
          ) || [],
      })),
    [files, documents],
  );

  const onRemoveFile = useCallback((file: ImagePickerFile) => {
    setFiles(files => files.filter(curFile => curFile.name !== file.name));
  }, []);

  const onUploadFile = useCallback(
    async (
      file: ImagePickerFile | null | undefined,
      selectedType: string,
      selectedCustomerType?: string,
    ) => {
      if (!file) return;
      file = {
        ...file,
        name: `${file.name}_${selectedType}`,
      };
      setFiles(files => [
        ...files,
        {
          ...file,
          status: 'uploading',
        },
      ]);

      const { uri, base64 } = file;
      try {
        if (!caseId) throw new Error('missing case id');
        const res = await uploadDocument({
          body: {
            custType:
              (selectedCustomerType as DocumentCustomerType) || customerType,
            custSeq: customerSeq,
            docType: selectedType as DocumentType,
            fileContent: base64,
            applicationNum: caseObj?.application?.applicationNum || '',
            fileType: getFileType(uri),
          },
        });
        await saveFileToDocumentFolder(res.fileName, base64);
        if (partyId) {
          await addDocument({
            caseId,
            data: {
              partyId,
              docType: selectedType as DocumentType,
              fileName: res.fileName,
              filePath: res.filePath,
            },
          });
        }
        setFiles(files =>
          files.map(curFile => {
            if (curFile.name === file?.name) {
              return { ...curFile, status: 'uploaded' };
            }
            return curFile;
          }),
        );
      } catch (e) {
        if (e instanceof AxiosError) {
          const response: CubeResponse<unknown> =
            e?.response?.data || ({} as CubeResponse<unknown>);
          const errorMessages = response.messageList || [];
          console.log('response', response);

          if (e?.response?.status === 413) {
            alertError(t('common:upload.invalidFileSize'));
          } else if (
            e?.response?.status === 500 &&
            errorMessages.some(m =>
              m.content?.includes('File type not match pattern'),
            )
          ) {
            alertError(t('common:upload.invalidFileExtension'));
          }
        } else {
          alertError(t('eApp:documentUpload.failedToUpload'));
        }
        onRemoveFile(file);
      }
    },
    [
      addDocument,
      alertError,
      caseId,
      caseObj?.application?.applicationNum,
      customerSeq,
      customerType,
      onRemoveFile,
      partyId,
      t,
      uploadDocument,
    ],
  );

  const {
    mutateAsync: uploadAndScanDocument,
    reset: resetApi,
    isError,
  } = useDocumentOcr();

  const ocrDataHandler = useOcrDataHandler();

  const reset = useCallback(
    async (file: ImagePickerFile) => {
      resetApi();
      onRemoveFile(file);
    },
    [onRemoveFile, resetApi],
  );

  const onRetake = useCallback(
    async (file: ImagePickerFile, type: string, customerType?: string) => {
      resetApi();
      onRemoveFile(file);
      selectedType.current = type;
      selectedCustomerType.current = customerType ?? '';
      showPicker();
    },
    [onRemoveFile, resetApi, showPicker],
  );

  const {
    policyOwnerPersonalInfo,
    updatePolicyOwnerPersonalInfo,
    isRemoteSelling,
  } = useEAppStore(
    state => ({
      policyOwnerPersonalInfo: state.policyOwnerPersonalInfo,
      updatePolicyOwnerPersonalInfo: state.updatePolicyOwnerPersonalInfo,
      isRemoteSelling: state.isRemoteSelling,
    }),
    shallow,
  );

  const {
    insuredPersonalInfo,
    updateInsuredPersonalInfo,
    isRemoteSellingInsured,
  } = useEAppStore(
    state => ({
      insuredPersonalInfo: state.insuredPersonalInfo,
      updateInsuredPersonalInfo: state.updateInsuredPersonalInfo,
      isRemoteSellingInsured: state.isRemoteSellingInsured,
    }),
    shallow,
  );

  const party = useGetPartyFromActiveCase(partyId);

  const { saveParty, isLoading: isSavingParty } = useSaveParty();

  const onUploadOCR = useCallback(
    async (
      file: ImagePickerFile | null | undefined,
      selectedType: string,
      selectedCustomerType?: string,
      fromDocumentUpload?: boolean,
    ) => {
      if (!file) return;
      file = {
        ...file,
        name: `${file.name}_${selectedType}`,
      };
      setFiles(files => [
        ...files,
        {
          ...file,
          status: 'uploading',
        },
      ]);

      const { uri, base64 } = file;
      try {
        if (!caseId) throw new Error('missing case id');
        const data = await uploadAndScanDocument({
          body: {
            base64Image: base64,
            caseId,
            partyId,
          },
        });
        let documentType = '';
        const idTypeMap: Record<BuildCountry, keyof OptionList> = {
          ph: 'PRIMARY_ID_TYPE',
          ib: 'ID_TYPE',
          id: 'ID_TYPE',
          my: 'ID_TYPE',
        };
        if (idTypeMap[country]) {
          switch (data.type) {
            case 'bir':
              documentType = 'TN';
              break;
            case 'drivers_license':
              documentType = 'DL';
              break;
            case 'passport':
              documentType = 'RP';
              break;
            case 'prc':
              documentType = 'PR';
              break;
            case 'sss':
              documentType = 'SS';
              break;
            case 'umid':
              documentType = 'UD';
              break;
            default:
              documentType = data.type;
          }
        }

        const valid = await ocrDataHandler(
          data.extract,
          () => onRetake(file, selectedType, selectedCustomerType),
          () => reset(file),
        );
        if (!valid) {
          return;
        }

        const isDocumentTypeValid = documentType !== 'OTHER';

        const [result, mismatchFields] = validateOcr(
          {
            ...data.extract,
            fullName: '',
          },
          {
            firstName: party?.person?.name.firstName ?? '',
            lastName: party?.person?.name.lastName ?? '',
            fullName: '',
            dateOfBirth: party?.person?.dateOfBirth.date
              ? parse(party?.person?.dateOfBirth.date, 'yyyy-MM-dd', new Date())
              : null,
            gender: party?.person?.gender as Gender,
          },
        );

        const [newResult, newMismatchFields] =
          applyRegionalLogicToOcrValidationResult(
            documentType,
            result,
            mismatchFields,
          );

        const fromOcr =
          !fromDocumentUpload && newResult === OcrValidationResult.Match;
        const res = await uploadDocument({
          body: {
            custType:
              (selectedCustomerType as DocumentCustomerType) || customerType,
            custSeq: customerSeq,
            docType: selectedType as DocumentType,
            fileContent: base64,
            applicationNum: caseObj?.application?.applicationNum || '',
            fileType: getFileType(uri),
          },
        });
        if (!res.fileName || !res.filePath) {
          throw new Error('Missing file information');
        }
        await saveFileToDocumentFolder(res.fileName, base64);
        if (partyId) {
          await addDocument({
            caseId,
            data: {
              partyId,
              docType: selectedType as DocumentType,
              fileName: res.fileName,
              filePath: res.filePath,
              fromOcr,
            },
          });
        }

        if (customerType === DocumentCustomerType.PO) {
          const personalDetails = policyOwnerPersonalInfo.personalDetails;
          const data = {
            ...policyOwnerPersonalInfo,
            id: partyId,
            isOcrSuccess:
              isDocumentTypeValid && newResult === OcrValidationResult.Match,
            personalDetails: {
              ...personalDetails,
              document: {
                ...personalDetails.document,
                frontImage: {
                  name: res.fileName,
                  thumbnail: res.filePath,
                  base64,
                  fromOcr,
                },
              },
            },
          };
          await saveParty(
            toParty({
              role: PartyRole.PROPOSER,
              info: data,
              isRemoteSelling,
            }),
            { preventCreatingParty: true },
          );
          updatePolicyOwnerPersonalInfo(data);
        }
        if (customerType === DocumentCustomerType.PI) {
          const personalDetails = insuredPersonalInfo.personalDetails;
          const data = {
            ...insuredPersonalInfo,
            id: partyId,
            isOcrSuccess:
              isDocumentTypeValid && newResult === OcrValidationResult.Match,
            personalDetails: {
              ...personalDetails,
              document: {
                ...personalDetails.document,
                frontImage: {
                  name: res.fileName,
                  thumbnail: res.filePath,
                  base64,
                  fromOcr,
                },
              },
            },
          };
          await saveParty(
            toParty({
              role: PartyRole.INSURED,
              info: data,
              isRemoteSelling: isRemoteSelling || isRemoteSellingInsured,
            }),
            { preventCreatingParty: true },
          );
          updateInsuredPersonalInfo(data);
        }
        setFiles(files =>
          files.map(curFile => {
            if (curFile.name === file?.name) {
              return { ...curFile, status: 'uploaded' };
            }
            return curFile;
          }),
        );
      } catch {
        alertError(t('eApp:documentUpload.failedToUpload'));
        onRemoveFile(file);
      }
    },
    [
      caseId,
      uploadAndScanDocument,
      partyId,
      ocrDataHandler,
      party,
      uploadDocument,
      customerType,
      customerSeq,
      caseObj?.application?.applicationNum,
      onRetake,
      reset,
      addDocument,
      policyOwnerPersonalInfo,
      saveParty,
      isRemoteSelling,
      updatePolicyOwnerPersonalInfo,
      insuredPersonalInfo,
      isRemoteSellingInsured,
      updateInsuredPersonalInfo,
      alertError,
      t,
      onRemoveFile,
    ],
  );

  const handleShutter = async (file: OcrFile) => {
    const [resizedResult, size] = await resizeImage(
      {
        canceled: false,
        assets: [file],
      },
      {
        maxHeight: 1200,
        maxWidth: 1200,
      },
      DEFAULT_MAX_PICKER_FILE_SIZE,
    );
    if (resizedResult) {
      onUploadOCR(
        {
          base64: resizedResult.base64 || '',
          uri: file.uri,
          size: size || 0,
          name: Crypto.randomUUID(),
          isAttachment: false,
        },
        selectedType.current,
        selectedCustomerType.current,
        true,
      );
    }
  };

  return {
    pickerVisible,
    showPicker,
    hidePicker,
    selectedType,
    selectedCustomerType,
    documentsWithFiles,
    onRemoveFile,
    onUploadFile,
    onUploadOCR,
    isOcrModalVisible,
    showOcrModal,
    hideOcrModal,
    handleShutter,
  };
};
