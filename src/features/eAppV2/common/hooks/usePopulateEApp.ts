import {
  NavigationProp,
  StackActions,
  useNavigation,
} from '@react-navigation/native';
import { ApplicationProgress } from 'api/caseApi';
import { generateDeviceId } from 'api/deviceApi';
import {
  EMandateLinkResponse,
  EMandatePaymentStatusResponse,
  PaymentGatewayResponse,
  PaymentGatewayStatusResponse,
} from 'api/ipay88Api';
import { EntityRelationshipType } from 'constants/optionList';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  EAppState,
  RemoteSellingStatus,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import { getScreenFlags } from 'features/eAppV2/ib/utils/getScreenFlags';
import * as MYCaseUtils from 'features/eAppV2/my/utils/caseUtils';
import { checkAddRider, checkFatcaRequired } from 'features/eAppV2/my/utils/planUtils';
import {
  initialChildInsuredFormData,
  InsuredFormSchemaType,
} from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import * as PHCaseUtils from 'features/eAppV2/ph/utils/caseUtils';
import { checkOwnerRider } from 'features/eAppV2/ph/utils/checkOwnerRider';
import { filterIrrevocableBeneficiaries } from 'features/eAppV2/ph/utils/filterIrrevocableBeneficiaries';
import { RelationshipValue } from 'features/proposal/types';
import { useAlert } from 'hooks/useAlert';
import { useGetApplicationProgressManually } from 'hooks/useApplicationProgress';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGeneratePolicyNumber } from 'hooks/useGeneratePolicyNumber';
import { useGetAgentProfileManually } from 'hooks/useGetAgentProfile';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useGetOptionListManually } from 'hooks/useGetOptionList';
import { useGetQuotationManually } from 'hooks/useGetQuotation';
import { useUpdateCase } from 'hooks/useUpdateCase';
import round from 'lodash/round';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { DirectCredit, PHInitialPayment } from 'types/case';
import { Channels } from 'types/channel';
import { DocumentType } from 'types/document';
import { OptionList, Relationship } from 'types/optionList';
import { PartyRole, PartyType } from 'types/party';
import { country, enhancedRemoteSellingEnabled } from 'utils/context';
import {
  getDeviceId,
  increaseApplicationCount,
  saveDeviceId,
} from 'utils/storage/device';
import { shallow } from 'zustand/shallow';
import {
  PaymentMethod,
  SubPaymentMethod,
} from '../../my/components/payment/PaymentMethods';
import { NominationFormSchemaType as MYNominationFormSchemaType } from '../../my/validations/applicationDetails/nominationInfoValidation';
import { PayorDetailsSchemaType } from '../../my/validations/applicationDetails/payorInfoValidation';
import { RenewalPaymentForm } from '../../ph/validations/renewalPaymentSetupValidation';
import {
  IBCombinedRouteKey,
  IBRouteGroupKey,
  IBRouteItemKey,
  IBRouteSubgroupKey,
  IDCombinedRouteKey,
  IDRouteGroupKey,
  IDRouteItemKey,
  IDRouteSubgroupKey,
  RouteGroupKey,
  RouteItemKey,
  RouteSubgroupKey,
} from '../types/progressBarTypes';
import { getPartiesByRole } from '../utils/partyUtils';
import { checkEntity } from './useCheckEntity';
import { useInitiateQuotationStore } from './useInitiateQuotationStore';
import { RiderCode } from 'types/quotation';

type NominationFormSchemaType = MYNominationFormSchemaType;

export const usePopulateEApp = () => {
  const activeCaseId = useBoundStore(state => state.case.caseId);
  const activeAgentId = useBoundStore(state => state.auth.agentCode);
  const {
    resetEAppStoreState,
    updatePolicyOwnerPersonalInfo,
    updateInsuredPersonalInfo,
    updateBeneficialOwnerPersonalInfo,
    updatePayorPersonalInfo,
    updateBeneficiaries,
    setPIEqualPO,
    updateRemoteSelling,
    updateRemoteSellingInsured,
    setHasBeneficialOwner,
    setHasPayor,
    updateConsents,
    updateExistingPolicyData,
    updateRenewalPaymentSetup,
    updateBankDetails,
    setCompletedHealthQuestionPolicyOwner,
    setCompletedHealthQuestionInsured,
    setHasBankDetails,
    setActiveQuotationId,
    setHasOwnerRider,
    updateRemoteSellingStatus,
    // MY
    updateMY_POEnquiryId,
    updateMY_PIEnquiryIds,
    updateMY_PolicyOwnerInfo,
    updateMY_CompanyInfo,
    updateMY_AuthorizedSignatoryInfo,
    setMY_HasEmployeeInsured,
    updateMY_EmployeeInfo,
    setMY_HasSpouseInsured,
    updateMY_SpouseInfo,
    setMY_HasChildInsured,
    updateMY_ChildrenInfo,
    setMY_HasPayor,
    updateMY_PayorDetails,
    setMY_HasNomination,
    updateMY_NominationDetails,
    updateMY_DirectCredit,
    updateFatcaDeclaration,
    updateRocDeclaration,
    updateAgreePersonalInfoCollected,
    updateMY_ChequePaymentSubmitted,
    updateMY_ChequePayment,
    updateMY_DirectPaymentSubmitted,
    updateMY_DirectPayment,
    setMY_PaymentMethod,
    setMY_SubPaymentMethod,
    setMY_AdvanceContributionDuration,
    setMY_HasAdvanceContribution,
    setMY_PaymentLinkResponse,
    setMY_PaymentStatusResponse,
    setMY_RenewalPaymentForm,
    setMY_RenewalPaymentLinkResponse,
    setMY_RenewalPaymentStatusResponse,
  } = useEAppStore(
    state => ({
      resetEAppStoreState: state.resetEAppStoreState,
      updatePolicyOwnerPersonalInfo: state.updatePolicyOwnerPersonalInfo,
      updateInsuredPersonalInfo: state.updateInsuredPersonalInfo,
      updateBeneficialOwnerPersonalInfo:
        state.updateBeneficialOwnerPersonalInfo,
      updatePayorPersonalInfo: state.updatePayorPersonalInfo,
      updateBeneficiaries: state.updateBeneficiaries,
      setPIEqualPO: state.setPIEqualPO,
      updateRemoteSelling: state.updateRemoteSelling,
      updateRemoteSellingInsured: state.updateRemoteSellingInsured,
      setHasBeneficialOwner: state.setHasBeneficialOwner,
      setHasPayor: state.setHasPayor,
      updateConsents: state.updateConsents,
      updateExistingPolicyData: state.updateExistingPolicyData,
      updateRenewalPaymentSetup: state.updateRenewalPaymentSetup,
      updateBankDetails: state.updateBankDetails,
      setCompletedHealthQuestionPolicyOwner:
        state.setCompletedHealthQuestionPolicyOwner,
      setCompletedHealthQuestionInsured:
        state.setCompletedHealthQuestionInsured,
      setHasBankDetails: state.setHasBankDetails,
      setActiveQuotationId: state.setActiveQuotationId,
      setHasOwnerRider: state.setHasOwnerRider,
      updateRemoteSellingStatus: state.updateRemoteSellingStatus,
      // MY
      updateMY_POEnquiryId: state.updateMY_POEnquiryId,
      updateMY_PIEnquiryIds: state.updateMY_PIEnquiryIds,
      updateMY_PolicyOwnerInfo: state.updateMY_PolicyOwnerInfo,
      updateMY_CompanyInfo: state.updateMY_CompanyInfo,
      updateMY_AuthorizedSignatoryInfo: state.updateMY_AuthorizedSignatoryInfo,
      setMY_HasEmployeeInsured: state.setMY_HasEmployeeInsured,
      updateMY_EmployeeInfo: state.updateMY_EmployeeInfo,
      setMY_HasSpouseInsured: state.setMY_HasSpouseInsured,
      updateMY_SpouseInfo: state.updateMY_SpouseInfo,
      setMY_HasChildInsured: state.setMY_HasChildInsured,
      updateMY_ChildrenInfo: state.updateMY_ChildrenInfo,
      setMY_HasPayor: state.setMY_HasPayor,
      updateMY_PayorDetails: state.updateMY_PayorDetails,
      setMY_HasNomination: state.setMY_HasNomination,
      updateMY_NominationDetails: state.updateMY_NominationDetails,
      updateMY_DirectCredit: state.updateMY_DirectCredit,
      updateFatcaDeclaration: state.updateFatcaDeclaration,
      updateRocDeclaration: state.updateRocDeclaration,
      updateAgreePersonalInfoCollected: state.updateAgreePersonalInfoCollected,
      updateMY_ChequePaymentSubmitted: state.updateMY_ChequePaymentSubmitted,
      updateMY_ChequePayment: state.updateMY_ChequePayment,
      updateMY_DirectPaymentSubmitted:
        state.updateMY_DirectTransferPaymentSubmitted,
      updateMY_DirectPayment: state.updateMY_DirectTransferPayment,
      setMY_PaymentMethod: state.setMY_PaymentMethod,
      setMY_SubPaymentMethod: state.setMY_SubPaymentMethod,
      setMY_AdvanceContributionDuration:
        state.setMY_AdvanceContributionDuration,
      setMY_HasAdvanceContribution: state.setMY_HasAdvanceContribution,
      setMY_PaymentLinkResponse: state.setMY_PaymentLinkResponse,
      setMY_PaymentStatusResponse: state.setMY_PaymentStatusResponse,
      setMY_RenewalPaymentForm: state.setMY_RenewalPaymentForm,
      setMY_RenewalPaymentLinkResponse: state.setMY_RenewalPaymentLinkResponse,
      setMY_RenewalPaymentStatusResponse:
        state.setMY_RenewalPaymentStatusResponse,
    }),
    shallow,
  );
  const {
    resetProgressBarState,
    setProgressBarCompletedMap,
    setProgressBarState,
  } = useEAppProgressBarStore(
    state => ({
      resetProgressBarState: state.resetProgressBarState,
      setProgressBarCompletedMap: state.setProgressBarCompletedMap,
      setProgressBarState: state.setProgressBarState,
    }),
    shallow,
  );
  const { mutateAsync: getCase } = useGetCaseManually();
  const { mutateAsync: getAgent } = useGetAgentProfileManually();
  const { mutateAsync: generatePolicyNumber } = useGeneratePolicyNumber();
  const { mutateAsync: createApplication } = useCreateApplication();
  const { mutateAsync: getProgress } = useGetApplicationProgressManually();
  const { mutateAsync: getQuotation } = useGetQuotationManually();
  const { mutateAsync: getOptionList } = useGetOptionListManually();
  const { mutateAsync: updateCase } = useUpdateCase();

  const { t } = useTranslation(['common', 'eApp']);
  const { alertError } = useAlert();

  const [isLoading, setLoading] = useState(true);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const setApplicationNum = useEAppStore(state => state.setApplicationNum);
  const setPolicyNum = useEAppStore(state => state.setPolicyNum);

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { isTabletMode } = useLayoutAdoptionCheck();

  useInitiateQuotationStore();

  const populateEApp = useCallback(
    async (activeCaseId: string, activeAgentId: string) => {
      setAppLoading();
      setLoading(true);
      try {
        const caseObj = await getCase(activeCaseId);
        if (caseObj.latestStatus === 'APP_SUBMITTED') {
          setAppIdle();
          alertError(t('eApp:alreadySubmitted'));
          if (navigation.canGoBack()) {
            navigation.goBack();
          } else {
            navigation.navigate('Main', {
              screen: 'Home',
            });
          }
          return;
        }
        const agentInfo = await getAgent();
        const optionList = await getOptionList();
        const quotation = await getQuotation({
          caseId: activeCaseId,
          quotationId: caseObj.selectedQuotationId || '',
        });
        const isEntity = checkEntity(caseObj);
        let progressBarStatus: {
          [key: string]: ApplicationProgress;
        } = {};
        try {
          progressBarStatus = await getProgress(activeCaseId);
        } catch {
          setProgressBarCompletedMap({});
        }
        if (country === 'ph') {
          const quotations = await Promise.all(
            caseObj.quotations?.map(quotation =>
              getQuotation({
                caseId: caseObj.id,
                quotationId: quotation.id,
              }),
            ) || [],
          );
          const existedQuotation = quotations.find(q => q.isCounterOffer);
          const activeQuotationId =
            existedQuotation?.id || caseObj.selectedQuotationId || '';
          setActiveQuotationId(activeQuotationId);

          const beneficiaries: EAppState['beneficiariesPersonalInfo'] = [];
          let isPIEqualPO = false;
          let hasBankDetails = false;
          let hasBeneficialOwner = false;
          let hasPayor = false;
          let isJuvenile = false;
          let finishedRenewalPaymentSetup = false;
          let beneficiaryCurrentIdx = 0;
          let isKeymanEmployee = false;

          // Bank details
          const payoutFundList = optionList?.PAYOUT_FUND.options || [];
          const fundValues = payoutFundList?.map(fund => fund?.value);

          const hasPayoutFund = quotation?.funds?.fundAllocation?.some?.(
            fund => {
              return fundValues?.includes(fund?.fid);
            },
          );
          let isProductSelected = false;

          switch (quotation?.plans[0].productCode) {
            case 'US11': //All Set Higher (Peso)
            case 'US13': //Dollar All Set Higher (USD)
              isProductSelected = true;
              break;
            case 'ED01': //FWD Guaranteed 7 Endowment Life Insurance
              setHasBankDetails(true);
              return;
            case 'USP': //FWD Golden 7
              setHasBankDetails(true);
              break;
            case 'U2P': // 2Pay RPVL Product
              isProductSelected = true;
              break;
            // TODO: missing Low Premium RPVL products
          }

          if (hasPayoutFund && isProductSelected) {
            setHasBankDetails(true);
            hasBankDetails = true;
          }

          // Party
          caseObj.parties?.forEach(party => {
            const files =
              caseObj?.files?.filter(f => f.partyId === party.id) ?? [];
            party.roles.forEach(role => {
              switch (role) {
                case PartyRole.PROPOSER:
                  updatePolicyOwnerPersonalInfo(
                    PHCaseUtils.parseParty(party, PartyRole.PROPOSER, files)
                      ?.data as EAppState['policyOwnerPersonalInfo'],
                  );
                  updateRemoteSelling(Boolean(party.isRemoteSelling));
                  if (party.roles.includes(PartyRole.INSURED)) {
                    isPIEqualPO = true;
                    setPIEqualPO(true);
                  }
                  break;
                case PartyRole.INSURED: {
                  if (
                    isEntity &&
                    party.relationship ===
                      EntityRelationshipType.EMPLOYEE_KEYMAN
                  ) {
                    isKeymanEmployee = true;
                  }
                  updateInsuredPersonalInfo(
                    PHCaseUtils.parseParty(party, PartyRole.INSURED, files)
                      ?.data as EAppState['insuredPersonalInfo'],
                  );
                  if (
                    party.person?.age !== null &&
                    party.person?.age !== undefined &&
                    party.person?.age < 18
                  ) {
                    // TODO: replace by actual age calculation
                    isJuvenile = true;
                  }
                  const policyOwner = caseObj.parties?.find(p =>
                    p.roles.includes(PartyRole.PROPOSER),
                  );
                  updateRemoteSellingInsured(
                    Boolean(policyOwner?.isRemoteSelling) ||
                      Boolean(party.isRemoteSelling),
                  );
                  break;
                }
                case PartyRole.BENEFICIAL_OWNER: {
                  let ocrResult;
                  hasBeneficialOwner = true;
                  setHasBeneficialOwner(true);
                  const partyData = PHCaseUtils.parseParty(
                    party,
                    PartyRole.BENEFICIAL_OWNER,
                    files,
                  )?.data as EAppState['beneficialOwnerPersonalInfo'];
                  // If the flag is true then we can use the previous data as an evidence to check on next step
                  if (partyData.isOcrSuccess) {
                    ocrResult = {
                      data: {
                        firstName: partyData.personalDetails.firstName,
                        lastName: partyData.personalDetails.lastName,
                        dateOfBirth: partyData.personalDetails.dateOfBirth,
                        gender: partyData.personalDetails.gender,
                      },
                      type: '',
                    };
                  }
                  updateBeneficialOwnerPersonalInfo({
                    ...partyData,
                    ocrResult,
                  });
                  break;
                }
                case PartyRole.PAYER: {
                  let ocrResult;
                  hasPayor = true;
                  setHasPayor(true);
                  const partyData = PHCaseUtils.parseParty(
                    party,
                    PartyRole.PAYER,
                    files,
                  )?.data as EAppState['payorPersonalInfo'];
                  // If the flag is true then we can use the previous data as an evidence to check on next step
                  if (partyData.isOcrSuccess) {
                    ocrResult = {
                      data: {
                        firstName: partyData.personalDetails.firstName,
                        lastName: partyData.personalDetails.lastName,
                        dateOfBirth: partyData.personalDetails.dateOfBirth,
                        gender: partyData.personalDetails.gender,
                      },
                      type: '',
                    };
                  }
                  updatePayorPersonalInfo({ ...partyData, ocrResult });
                  break;
                }
                case PartyRole.BENEFICIARY: {
                  let ocrResult;
                  const partyData = PHCaseUtils.parseParty(
                    party,
                    PartyRole.BENEFICIARY,
                    files,
                    beneficiaryCurrentIdx,
                  )?.data as EAppState['beneficiariesPersonalInfo'][0];
                  // If the flag is true then we can use the previous data as an evidence to check on next step
                  if (partyData.isOcrSuccess) {
                    ocrResult = {
                      data: {
                        firstName: partyData.personalDetails.firstName,
                        lastName: partyData.personalDetails.lastName,
                        dateOfBirth: partyData.personalDetails.dateOfBirth,
                        gender: partyData.personalDetails.gender,
                      },
                      type: '',
                    };
                  }
                  beneficiaries.push({ ...partyData, ocrResult });
                  beneficiaryCurrentIdx += 1;
                }
              }
            });
            if (beneficiaries.length > 0) {
              updateBeneficiaries(beneficiaries);
            }
          });

          const hasOwnerRider = checkOwnerRider(quotation);
          setHasOwnerRider(hasOwnerRider);
          if (!hasOwnerRider && !isPIEqualPO) {
            setCompletedHealthQuestionPolicyOwner(true);
          }

          // Populate resume for the progress bar
          const screenKeys = [
            'appDetail-ownerAndInsured-policyOwner',
            !isPIEqualPO && 'appDetail-ownerAndInsured-insured',
            'appDetail-other-beneficialOwner',
            'appDetail-other-payor',
            'appDetail-other-primaryBeneficiary',
            !isKeymanEmployee && 'appDetail-other-secondaryBeneficiary',
            (isPIEqualPO || hasOwnerRider) &&
              'appDetail-healthQuestion-policyOwner',
            !isPIEqualPO && 'appDetail-healthQuestion-insured',
            'consents--declaration',
            'consents--dataPrivacy',
            'renewalPaymentSetup--renewalPayment',
            hasBankDetails && 'renewalPaymentSetup--bankDetails',
            'documentUpload--policyOwner',
            !isPIEqualPO && 'documentUpload--insured',
            hasBeneficialOwner && 'documentUpload--beneficialOwner',
            hasPayor && 'documentUpload--payor',
            ...filterIrrevocableBeneficiaries(beneficiaries).map(
              (_, idx) => `documentUpload--beneficiary${idx + 1}`,
            ),
            'review--review',
            'review--ownerSignature',
            !isPIEqualPO && !isJuvenile && 'review--insuredSignature',
            !isPIEqualPO && 'review--advisorSignature',
            'payment--payment',
            'payment--paymentOffline',
          ].filter(Boolean) as string[];
          if (Object.keys(progressBarStatus).length > 0) {
            const completedMap: { [key: string]: boolean } = {};
            Object.keys(progressBarStatus).forEach((key: string) => {
              if (progressBarStatus[key] === ApplicationProgress.COMPLETED) {
                switch (key) {
                  case 'appDetail-healthQuestion-policyOwner':
                    setCompletedHealthQuestionPolicyOwner(true);
                    break;
                  case 'appDetail-healthQuestion-insured':
                    setCompletedHealthQuestionInsured(true);
                    break;
                }
                completedMap[key] = true;
                const foundScreenKeyIdx = screenKeys.findIndex(screenKey =>
                  key.includes(screenKey),
                );
                if (foundScreenKeyIdx > -1) {
                  screenKeys.splice(foundScreenKeyIdx, 1);
                }
                if (key === 'renewalPaymentSetup--renewalPayment') {
                  finishedRenewalPaymentSetup = true;
                }
              }
              return completedMap;
            });
            setProgressBarCompletedMap(completedMap);
            if (screenKeys.length > 0) {
              const [groupKey, itemGroupKey, itemKey] =
                screenKeys[0].split('-');
              setProgressBarState({
                groupKey: (groupKey as RouteGroupKey) || undefined,
                subgroupKey: (itemGroupKey as RouteSubgroupKey) || undefined,
                itemKey: (itemKey as RouteItemKey) || undefined,
                expandedGroupKey: groupKey,
              });
            }
          }

          // Consents
          if (caseObj.application) {
            const { consentsAnswers, existingPolicyData } =
              PHCaseUtils.parseApplicationConsent(caseObj.application);
            updateConsents(consentsAnswers);
            updateExistingPolicyData(existingPolicyData);
          }

          // Renewal payment
          if (caseObj.application && caseObj.application.directCredit) {
            const renewalPaymentSetup =
              PHCaseUtils.parseApplicationRenewalPaymentSetup(
                caseObj.application.directCredit as DirectCredit<'ph'>,
              );
            if (
              renewalPaymentSetup.paymentMethod === 'aca' &&
              finishedRenewalPaymentSetup
            ) {
              renewalPaymentSetup.creditAcceptDisclaimer = true;
            }
            updateRenewalPaymentSetup(renewalPaymentSetup);
            if (hasBankDetails) {
              updateBankDetails(
                PHCaseUtils.parseApplicationBankDetails(
                  caseObj.application.directCredit as DirectCredit<'ph'>,
                ),
              );
            }
          }

          // Generate policy number
          let policyNum = '';
          let applicationNum = '';
          if (caseObj.application?.policyNum) {
            setPolicyNum(caseObj.application?.policyNum);
            setApplicationNum(
              caseObj.application?.applicationNum ??
                `01${caseObj.application.policyNum}`,
            );
          } else {
            policyNum = await generatePolicyNumber({});
            applicationNum = `01${policyNum}`;
            await createApplication({
              caseId: activeCaseId,
              data: {
                agent: {
                  agentCode: activeAgentId,
                },
                policyId: policyNum,
                policyNum,
                applicationNum,
              },
            });
            setPolicyNum(policyNum);
            setApplicationNum(`01${policyNum}`);
          }

          setAppIdle();
          if (
            screenKeys.length === 0 ||
            ((screenKeys[0]?.split('-')?.[0] as RouteGroupKey) === 'payment' &&
              (caseObj.application?.initialPayment as PHInitialPayment)
                ?.payType)
          ) {
            if (enhancedRemoteSellingEnabled && caseObj.isRemoteSelling) {
              if (caseObj.remoteSelling) {
                if (caseObj.remoteSelling.finishedAt) {
                  updateRemoteSellingStatus(RemoteSellingStatus.COMPLETED);
                } else if (
                  caseObj.remoteSelling.isPoConfirmed === false &&
                  caseObj.remoteSelling.isPiConfirmed === false
                ) {
                  updateRemoteSellingStatus(RemoteSellingStatus.PO_PI_PENDING);
                } else if (caseObj.remoteSelling.isPoConfirmed === false) {
                  updateRemoteSellingStatus(RemoteSellingStatus.PO_PENDING);
                } else if (caseObj.remoteSelling.isPiConfirmed === false) {
                  updateRemoteSellingStatus(RemoteSellingStatus.PI_PENDING);
                }
              }
              if (
                (screenKeys[0]?.split('-')?.[2] as RouteItemKey) ===
                  'paymentOffline' ||
                progressBarStatus['payment--paymentOffline'] ===
                  ApplicationProgress.COMPLETED
              ) {
                navigation.dispatch(
                  StackActions.push('PaymentResult', {
                    fromScreen: 'OfflinePayment',
                  }),
                );
              } else {
                navigation.dispatch(
                  StackActions.push('PaymentResult', {
                    fromScreen: 'OnlinePayment',
                  }),
                );
              }
            } else {
              navigation.dispatch(
                StackActions.replace('ACR', {
                  defaultValues: caseObj.application?.agentReport
                    ? PHCaseUtils.parseApplicationAgentReport(
                        caseObj.application.agentReport,
                      )
                    : undefined,
                }),
              );
            }
          }
        } else if (country === 'my') {
          let policyNum = '';
          let applicationNum = '';
          if (
            caseObj.application?.policyNum &&
            caseObj.application?.applicationNum
          ) {
            policyNum = caseObj.application?.policyNum;
            applicationNum = caseObj.application?.applicationNum;
          } else {
            const policyOwner = caseObj.parties?.find(p =>
              p.roles.includes(PartyRole.PROPOSER),
            );
            if (!caseObj.application?.policyNum) {
              const mobileContact = policyOwner?.contacts.phones.find(
                phone => phone.type === 'WORK',
              );
              const phoneNo = mobileContact
                ? `${mobileContact.countryCode}${mobileContact.number}`
                : '';

              let retryCount = 0;
              while (retryCount < 3) {
                try {
                  policyNum = await generatePolicyNumber({
                    phoneNo,
                    productType: quotation.plans[0].planCode,
                  });
                  break;
                } catch {
                  retryCount++;
                }
              }
              if (retryCount === 3 && !policyNum) {
                throw new Error('policy number was not generated');
              }
              let deviceId = await getDeviceId();
              if (deviceId === null) {
                deviceId = await generateDeviceId();
                await saveDeviceId(deviceId);
              }
              const count = await increaseApplicationCount();
              const replaceRegexp = RegExp(`[0-9]{${count.length}}$`);
              applicationNum = `${deviceId}0000`.replace(replaceRegexp, count);
              await createApplication({
                caseId: activeCaseId,
                data: {
                  agent: {
                    agentCode: activeAgentId,
                  },
                  policyId: policyNum,
                  policyNum,
                  applicationNum,
                },
              });
            }
          }
          // application number, policy number
          setPolicyNum(policyNum);
          setApplicationNum(applicationNum);
          // sync party remote selling to case remote selling
          if (
            getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0]?.isRemoteSelling
          ) {
            await updateCase({
              caseId: activeCaseId,
              isRemoteSelling: true,
            });
          }
          // Party
          const isEntity = Boolean(
            caseObj?.parties?.find(
              p =>
                p.roles.includes(PartyRole.PROPOSER) &&
                p.clientType === PartyType.ENTITY,
            ),
          );
          const hasInsured = caseObj.parties?.find(
            p =>
              p.roles.includes(PartyRole.INSURED) &&
              !p.roles.includes(PartyRole.PROPOSER),
          );
          const childInfo: InsuredFormSchemaType[] = [];
          const nominees: NominationFormSchemaType['information'] = [];
          let ownerEnquiryId = '';
          const insuredEnquiryIds: Record<string, string> = {};

          const { addRiderAnnualIncome } = checkAddRider(
            quotation,
            optionList as OptionList<string, 'my'>,
            isEntity,
          );

          caseObj.parties?.forEach(party => {
            const files =
              caseObj?.files?.filter(f => f.partyId === party.id) ?? [];
            party.roles.forEach(role => {
              const relationshipGroup = (
                optionList.RELATIONSHIP.options as Relationship<string, 'my'>[]
              ).find(i => i.value === party?.relationship)?.group;
              const annualIncome = party.isMainInsured
                ? addRiderAnnualIncome ??
                  party.person?.occupation?.incomeRange ??
                  ''
                : party.person?.occupation?.incomeRange ?? '';
              switch (role) {
                case PartyRole.PROPOSER:
                  if (isEntity) {
                    updateMY_CompanyInfo({
                      ...MYCaseUtils.parseCompanyInfo(party, optionList),
                    });
                    updateMY_AuthorizedSignatoryInfo({
                      ...MYCaseUtils.parseAuthorizedSignatoryInfo(party, files),
                      isF2F: !party.isRemoteSelling,
                    });
                  } else {
                    updateMY_PolicyOwnerInfo({
                      ...MYCaseUtils.parseParty(party, files, optionList),
                      annualIncome,
                      isF2F: !party.isRemoteSelling,
                    });
                  }
                  ownerEnquiryId = party.uw?.enquiryId || '';
                  break;
                case PartyRole.INSURED:
                  if (party.roles.includes(PartyRole.PROPOSER)) break;
                  if (hasInsured) {
                    const isEmployeeInsured =
                      relationshipGroup === RelationshipValue.EMPLOYEE;
                    if (relationshipGroup === RelationshipValue.EMPLOYEE) {
                      setMY_HasEmployeeInsured(true);
                      updateMY_EmployeeInfo({
                        ...MYCaseUtils.parseParty(
                          party,
                          files,
                          optionList,
                          isEntity,
                          isEmployeeInsured,
                        ),
                        annualIncome,
                        isF2F: !party.isRemoteSelling,
                      });
                    } else if (relationshipGroup === RelationshipValue.SPOUSE) {
                      setMY_HasSpouseInsured(true);
                      updateMY_SpouseInfo({
                        ...MYCaseUtils.parseParty(
                          party,
                          files,
                          optionList,
                          isEntity,
                          isEmployeeInsured,
                        ),
                        annualIncome,
                        isF2F: !party.isRemoteSelling,
                      });
                    } else {
                      const isChildInsured = true;
                      childInfo.push({
                        ...initialChildInsuredFormData,
                        ...MYCaseUtils.parseParty(
                          party,
                          files,
                          optionList,
                          isEntity,
                          isEmployeeInsured,
                          isChildInsured,
                        ),
                        annualIncome,
                        isF2F: !party.isRemoteSelling,
                      });
                    }
                    insuredEnquiryIds[party.id] = party.uw?.enquiryId || '';
                  }
                  break;
                case PartyRole.PAYER:
                  if (caseObj?.havePayer) {
                    setMY_HasPayor(true);
                    updateMY_PayorDetails(
                      MYCaseUtils.parseParty(
                        party,
                        files,
                        optionList,
                      ) as PayorDetailsSchemaType,
                    );
                  }
                  break;
                case PartyRole.BENEFICIARY:
                  nominees.push(
                    MYCaseUtils.parseParty(
                      party,
                      files,
                      optionList,
                    ) as NominationFormSchemaType['information'][0],
                  );
                  break;
              }
            });
          });
          if (childInfo.length > 0) {
            setMY_HasChildInsured(true);
            updateMY_ChildrenInfo(childInfo);
          }
          if (nominees.length > 0) {
            setMY_HasNomination(true);
            updateMY_NominationDetails({
              nominationType:
                (nominees[0].beneficiaryType as 'Conditional Hibah' | 'Wasi') ||
                'Conditional Hibah',
              information: nominees,
              poIdentificationNumber: '',
              piIdentificationNumbers: [],
              poFullName: '',
              piFullNames: [],
              agentFullName: '',
            });
          }
          // Declaration
          updateFatcaDeclaration(
            MYCaseUtils.fromFATCA(caseObj.application?.proposerConsent ?? []),
          );
          updateRocDeclaration(
            MYCaseUtils.fromReplacementInfo(
              caseObj.application?.replacementInfo ?? [],
            ),
          );
          // Health question
          updateMY_POEnquiryId(ownerEnquiryId);
          updateMY_PIEnquiryIds(insuredEnquiryIds);
          // Consent
          updateAgreePersonalInfoCollected(
            caseObj.application?.proposerConsent?.[0]?.pdpCheck ?? false,
          );
          // Direct credit
          updateMY_DirectCredit(
            MYCaseUtils.fromDirectCredit(caseObj.application?.directCredit),
          );
          // Cheque payment
          if (
            caseObj.application?.paymentMethod === PaymentMethod.CHEQUE &&
            caseObj.application?.chequeInfo
          ) {
            updateMY_ChequePaymentSubmitted(true);
          }
          const initialContribution = String(
            round(
              (quotation.summary.initialPrem ?? 0) +
                (caseObj.application?.advancePayment?.amount ?? 0),
              2,
            ),
          );
          updateMY_ChequePayment({
            fullNamePayor: '',
            applicationNumber: applicationNum,
            idNumber: '',
            quotationNumber: caseObj.selectedQuotationId || '',
            initialContributionAmount: initialContribution,
            chequeAmount:
              caseObj.application?.chequeInfo?.amount || initialContribution,
            chequeNumber: caseObj.application?.chequeInfo?.number || '',
            isForMultipleApplication: caseObj.application?.chequeInfo
              ? caseObj.application?.chequeInfo.forMultipleApplication
                ? 'yes'
                : 'no'
              : '',
            chequeDate: caseObj.application?.chequeInfo?.date
              ? new Date(caseObj.application?.chequeInfo.date)
              : null,
            chequeIssuerBank: caseObj.application?.chequeInfo?.issueBank || '',
            documents: [
              DocumentType.FrontCheque,
              DocumentType.BackCheque,
              DocumentType.InSlipCheque,
            ].map(type => ({
              type: type,
              files:
                caseObj.files
                  ?.filter(f => f.docType === type)
                  .map(f => ({
                    name: f.fileName,
                    uri: f.filePath,
                    size: 0,
                    base64: '',
                  })) ?? [],
            })),
          });
          // Direct payment
          if (
            caseObj.application?.paymentMethod ===
              PaymentMethod.DIRECT_TRANSFER &&
            caseObj.application?.chequeInfo
          ) {
            updateMY_DirectPaymentSubmitted(true);
          }
          updateMY_DirectPayment({
            fullNamePayor: '',
            applicationNumber: applicationNum,
            idNumber: '',
            quotationNumber: caseObj.selectedQuotationId || '',
            initialContributionAmount: initialContribution,
            transactionAmount: initialContribution,
            recipientReference: caseObj.application?.chequeInfo?.number || '',
            transferDate: caseObj.application?.chequeInfo?.date
              ? new Date(caseObj.application?.chequeInfo.date)
              : new Date(),
            transferType: caseObj.application?.chequeInfo?.bankName || '',
            documents: [DocumentType.DirectTransfer].map(type => ({
              type: type,
              files:
                caseObj.files
                  ?.filter(f => f.docType === type)
                  .map(f => ({
                    name: f.fileName,
                    uri: f.filePath,
                    size: 0,
                    base64: '',
                  })) ?? [],
            })),
          });
          // Initial payment
          setMY_PaymentMethod(
            (caseObj.application?.paymentMethod || null) as PaymentMethod,
          );
          setMY_SubPaymentMethod(
            caseObj?.application?.isInAppPayment
              ? SubPaymentMethod.IN_APP
              : SubPaymentMethod.VIA_LINK,
          );
          setMY_AdvanceContributionDuration(
            caseObj?.application?.advancePayment?.duration ?? 0,
          );
          setMY_HasAdvanceContribution(
            (caseObj?.application?.advancePayment?.duration ?? 0) > 0,
          );
          setMY_PaymentMethod(
            (caseObj.application?.paymentMethod || null) as PaymentMethod,
          );
          if (caseObj.application?.initialPayment) {
            setMY_PaymentLinkResponse(
              caseObj.application?.initialPayment as PaymentGatewayResponse,
            );
          }
          const paymentResponse = caseObj.application?.initialPayment
            ? (
                caseObj.application?.initialPayment as {
                  paymentResponse?: PaymentGatewayStatusResponse;
                }
              )?.paymentResponse
            : null;
          if (paymentResponse) {
            setMY_PaymentStatusResponse(paymentResponse);
          }
          // EMandate
          const renewalPaymentForm =
            caseObj.application?.renewalPayment &&
            typeof caseObj.application?.renewalPayment === 'object'
              ? (
                  caseObj.application?.renewalPayment as {
                    formData?: { renewalPayment?: RenewalPaymentForm };
                  }
                )?.formData?.renewalPayment
              : null;
          if (renewalPaymentForm) {
            setMY_RenewalPaymentForm(renewalPaymentForm);
          }
          const renewalPaymentLinkResponse =
            caseObj.application?.renewalPayment &&
            typeof caseObj.application?.renewalPayment === 'object'
              ? (caseObj.application?.renewalPayment as EMandateLinkResponse)
              : null;
          if (renewalPaymentLinkResponse) {
            setMY_RenewalPaymentLinkResponse(renewalPaymentLinkResponse);
          }
          const renewalPaymentStatusResponse =
            caseObj.application?.renewalPayment &&
            typeof caseObj.application?.renewalPayment === 'object'
              ? (
                  caseObj.application?.renewalPayment as {
                    eMandatePaymentStatusResponse?: EMandatePaymentStatusResponse;
                  }
                )?.eMandatePaymentStatusResponse
              : null;
          if (renewalPaymentStatusResponse) {
            setMY_RenewalPaymentStatusResponse(renewalPaymentStatusResponse);
          }

          // Progress
          const numOfInsured =
            caseObj?.parties?.filter(
              p =>
                p.roles.includes(PartyRole.INSURED) &&
                !p.roles.includes(PartyRole.PROPOSER),
            ).length ?? 0;
          let screenKeys = [
            !isEntity && 'appDetail-policyOwner-policyOwner',
            isEntity && 'appDetail-company-company',
            isEntity && 'appDetail-authorizedSignatory-authorizedSignatory',
            hasInsured && 'appDetail-insured-insured',
            'appDetail-other-payor',
            'appDetail-other-beneficiary',
            'appDetail-declaration-roc',
            checkFatcaRequired(quotation?.plans?.[0]?.pid) &&
              'appDetail-declaration-fatca',
            'appDetail-healthQuestion-policyOwner',
            ...Array.from(
              { length: numOfInsured },
              (_, i) => `appDetail-healthQuestion-insured${i + 1}`,
            ),
            'consents--',
            'directCredit--',
            'documentUpload--',
            'review--',
            'payment--',
          ].filter(Boolean) as string[];
          const completedMap: { [key: string]: boolean } = {};
          Object.keys(progressBarStatus).forEach((key: string) => {
            if (progressBarStatus[key] === ApplicationProgress.COMPLETED) {
              let comparedKey = key;
              if (/appDetail--[a-z]+/.test(key) && isTabletMode) {
                comparedKey = key.replace('--', '-') + '-';
              }
              completedMap[comparedKey] = true;
              const [foundScreenKeys, remainingScreenKeys] = screenKeys.reduce(
                (acc, screenKey) => {
                  if (screenKey.includes(comparedKey)) {
                    acc[0].push(screenKey);
                  } else {
                    acc[1].push(screenKey);
                  }
                  return acc;
                },
                [[], []] as [string[], string[]],
              );
              screenKeys = remainingScreenKeys;
              if (foundScreenKeys.length > 0) {
                foundScreenKeys.forEach(screenKey => {
                  completedMap[screenKey] = true;
                });
              }
            }
          });
          setProgressBarCompletedMap(completedMap);
          if (screenKeys.length > 0) {
            const [groupKey, itemGroupKey, itemKey] = screenKeys[0].split('-');
            setProgressBarState({
              groupKey: (groupKey as RouteGroupKey) || undefined,
              subgroupKey: (itemGroupKey as RouteSubgroupKey) || undefined,
              itemKey: (itemKey as RouteItemKey) || undefined,
              expandedGroupKey: groupKey,
            });
          }

          setAppIdle();
        } else if (country === 'ib') {
          let policyNum = '';
          let applicationNum = '';
          if (
            caseObj.application?.policyNum &&
            caseObj.application?.applicationNum
          ) {
            policyNum = caseObj.application?.policyNum;
            applicationNum = caseObj.application?.applicationNum;
          } else {
            const policyOwner = caseObj.parties?.find(p =>
              p.roles.includes(PartyRole.PROPOSER),
            );
            if (!caseObj.application?.policyNum) {
              const mobileContact = policyOwner?.contacts.phones.find(
                phone => phone.type === 'MOBILE',
              );
              const phoneNo = mobileContact
                ? `${mobileContact.countryCode}${mobileContact.number}`
                : '';

              let retryCount = 0;
              while (retryCount < 3) {
                try {
                  policyNum = await generatePolicyNumber({
                    channel: agentInfo.channel as unknown as Channels,
                    phoneNo,
                    productType: quotation?.plans?.[0]?.planCode,
                  });
                  break;
                } catch (e) {
                  console.log('failed on generating policy number');
                  console.log(e);
                  retryCount++;
                }
              }
              if (retryCount === 3 && !policyNum) {
                throw new Error('policy number was not generated');
              }

              applicationNum = `01${policyNum}`;

              await createApplication({
                caseId: activeCaseId,
                data: {
                  agent: {
                    agentCode: activeAgentId,
                  },
                  policyId: policyNum,
                  policyNum,
                  applicationNum,
                },
              });
            }
          }
          const {
            hasPayor,
            hasRenewalPayer,
            hasInsured,
            hasNomination,
            hasTrustee,
            hasParent,
            hasTakeOver,
            hasChargePremium,
          } = getScreenFlags(caseObj);

          let screenKeys = [
            isEntity
              ? 'appDetail-company-company'
              : 'appDetail-policyOwner-policyOwner',
            isEntity && 'appDetail-representative-representative',
            hasInsured && country === 'ib' && 'appDetail-insured-insured', // in IDN the owner and insured are in same screen
            'appDetail-others-payor',
            hasNomination && 'appDetail-others-beneficiary',
            hasTrustee && 'appDetail-others-trustee',
            'appDetail-others-witness',
            hasParent && 'appDetail-others-parent',
            'appDetail-declaration-fatca',
            'appDetail-declaration-rop',
            hasTakeOver && 'appDetail-declaration-takeOver',
            'appDetail-healthQuestion-policyOwner',
            hasInsured && 'appDetail-healthQuestion-insured',
            'consents--pdp',
            'consents--preContractualDutyOfDisclosure',
            'consents--theProvisionalAccidentalDeathInsuranceCoverage',
            hasChargePremium && 'consents--authorizationToChargePremium',
            'consents--ePolicyAndENotices',
            'payoutAccount--',
            'documentUpload--policyOwner',
            hasInsured && 'documentUpload--insured',
            hasPayor && 'documentUpload--payor',
            hasRenewalPayer && 'documentUpload--renewalPayer',
            'review--',
            'payment--',
          ].filter(Boolean) as IBCombinedRouteKey[];
          const completedMap: { [key: string]: boolean } = {};
          Object.keys(progressBarStatus).forEach((key: string) => {
            if (progressBarStatus[key] === ApplicationProgress.COMPLETED) {
              completedMap[key] = true;
              screenKeys = screenKeys.filter(screenKey => {
                if (
                  screenKey.includes('documentUpload') &&
                  key === 'documentUpload--'
                ) {
                  completedMap[screenKey] = true;
                  return false;
                }
                return !screenKey.includes(key);
              });
            }
          });
          setProgressBarCompletedMap(completedMap);
          if (screenKeys.length > 0) {
            const [groupKey, subgroupKey, itemKey] = screenKeys[0].split('-');
            setProgressBarState({
              groupKey: (groupKey as IBRouteGroupKey) || undefined,
              subgroupKey: (subgroupKey as IBRouteSubgroupKey) || undefined,
              itemKey: (itemKey as IBRouteItemKey) || undefined,
              expandedGroupKey: groupKey,
            });
          }
        } else if (country === 'id') {
          let policyNum = '';
          let applicationNum = '';
          if (
            caseObj.application?.policyNum &&
            caseObj.application?.applicationNum
          ) {
            policyNum = caseObj.application?.policyNum;
            applicationNum = caseObj.application?.applicationNum;
          } else {
            const policyOwner = caseObj.parties?.find(p =>
              p.roles.includes(PartyRole.PROPOSER),
            );
            if (!caseObj.application?.policyNum) {
              const mobileContact = policyOwner?.contacts.phones.find(
                phone => phone.type === 'MOBILE',
              );
              const phoneNo = mobileContact
                ? `${mobileContact.countryCode}${mobileContact.number}`
                : '';

              let retryCount = 0;
              while (retryCount < 3) {
                try {
                  policyNum = await generatePolicyNumber({
                    channel: agentInfo.channel as unknown as Channels,
                    phoneNo,
                    productType: quotation?.plans?.[0]?.planCode,
                  });
                  break;
                } catch (e) {
                  console.log('failed on generating policy number');
                  console.log(e);
                  retryCount++;
                }
              }
              if (retryCount === 3 && !policyNum) {
                throw new Error('policy number was not generated');
              }

              applicationNum = `01${policyNum}`;

              await createApplication({
                caseId: activeCaseId,
                data: {
                  agent: {
                    agentCode: activeAgentId,
                  },
                  policyId: policyNum,
                  policyNum,
                  applicationNum,
                },
              });
            }
          }
          const { hasPayor, hasRenewalPayer, hasInsured } =
            getScreenFlags(caseObj);

          let screenKeys = [
            'appDetail-policyOwner-policyOwner',
            hasInsured && 'appDetail-policyOwner-insured',
            'appDetail-others-payor',
            'appDetail-others-renewalPayor',
            'appDetail-others-beneficiary',
            'appDetail-declaration-fatca',
            hasPayor && 'appDetail-declaration-initialBeneficiaryOwner-fatca',
            hasRenewalPayer && 'appDetail-declaration-renewalPayer-fatca',
            'appDetail-declaration-passionSurvey',
            'appDetail-healthQuestion-policyOwner',
            hasInsured && 'appDetail-healthQuestion-insured',
            'consents--underwritingDecision',
            'consents--closing-agent',
            'consents--statement-and-power-of-attorney',
            'consents--statement-of-truth',
            'consents--temporary-coverage',
            'renewalPaymentSetup--renewalPremiumPayment',
            'renewalPaymentSetup--withdrawalPayment',
            'documentUpload--policyOwner',
            hasInsured && 'documentUpload--insured',
            hasPayor && 'documentUpload--payor',
            hasRenewalPayer && 'documentUpload--renewalPayer',
            'review--',
            'payment--',
          ].filter(Boolean) as IDCombinedRouteKey[];
          const completedMap: { [key: string]: boolean } = {};
          Object.keys(progressBarStatus).forEach((key: string) => {
            if (progressBarStatus[key] === ApplicationProgress.COMPLETED) {
              completedMap[key] = true;
              screenKeys = screenKeys.filter(screenKey => {
                if (
                  screenKey.includes('documentUpload') &&
                  key === 'documentUpload--'
                ) {
                  completedMap[screenKey] = true;
                  return false;
                }
                return !screenKey.includes(key);
              });
            }
          });
          setProgressBarCompletedMap(completedMap);
          if (screenKeys.length > 0) {
            const [groupKey, subgroupKey, itemKey] = screenKeys[0].split('-');
            setProgressBarState({
              groupKey: (groupKey as IDRouteGroupKey) || undefined,
              subgroupKey: (subgroupKey as IDRouteSubgroupKey) || undefined,
              itemKey: (itemKey as IDRouteItemKey) || undefined,
              expandedGroupKey: groupKey,
            });
          }
        }
        setAppIdle();
      } catch (e) {
        console.log('error', (e as Error).stack);
        setAppIdle();
        alertError(t('eApp:failedToStart'));
        if (navigation.canGoBack()) {
          navigation.goBack();
        } else {
          navigation.navigate('Main', {
            screen: 'Home',
          });
        }
      } finally {
        setLoading(false);
      }
    },
    [
      setAppLoading,
      getCase,
      getAgent,
      getOptionList,
      getQuotation,
      setAppIdle,
      alertError,
      t,
      navigation,
      getProgress,
      setProgressBarCompletedMap,
      setActiveQuotationId,
      setHasOwnerRider,
      setHasBankDetails,
      updatePolicyOwnerPersonalInfo,
      updateRemoteSelling,
      setHasBeneficialOwner,
      updateBeneficialOwnerPersonalInfo,
      setHasPayor,
      updatePayorPersonalInfo,
      setPIEqualPO,
      updateInsuredPersonalInfo,
      updateRemoteSellingInsured,
      updateBeneficiaries,
      setCompletedHealthQuestionPolicyOwner,
      setCompletedHealthQuestionInsured,
      setProgressBarState,
      updateConsents,
      updateExistingPolicyData,
      updateRenewalPaymentSetup,
      updateBankDetails,
      setPolicyNum,
      setApplicationNum,
      generatePolicyNumber,
      createApplication,
      updateRemoteSellingStatus,
      updateFatcaDeclaration,
      updateRocDeclaration,
      updateMY_POEnquiryId,
      updateMY_PIEnquiryIds,
      updateAgreePersonalInfoCollected,
      updateMY_DirectCredit,
      updateMY_ChequePayment,
      updateMY_DirectPayment,
      setMY_PaymentMethod,
      setMY_SubPaymentMethod,
      setMY_AdvanceContributionDuration,
      setMY_HasAdvanceContribution,
      updateMY_CompanyInfo,
      updateMY_AuthorizedSignatoryInfo,
      updateMY_PolicyOwnerInfo,
      setMY_HasEmployeeInsured,
      updateMY_EmployeeInfo,
      setMY_HasSpouseInsured,
      updateMY_SpouseInfo,
      setMY_HasPayor,
      updateMY_PayorDetails,
      setMY_HasChildInsured,
      updateMY_ChildrenInfo,
      setMY_HasNomination,
      updateMY_NominationDetails,
      updateMY_ChequePaymentSubmitted,
      updateMY_DirectPaymentSubmitted,
      setMY_PaymentLinkResponse,
      setMY_PaymentStatusResponse,
      setMY_RenewalPaymentForm,
      setMY_RenewalPaymentLinkResponse,
      setMY_RenewalPaymentStatusResponse,
      isTabletMode,
    ],
  );

  useEffect(() => {
    if (activeCaseId && activeAgentId) {
      resetEAppStoreState();
      resetProgressBarState();
      populateEApp(activeCaseId, activeAgentId);
    }
  }, [
    activeCaseId,
    resetEAppStoreState,
    resetProgressBarState,
    populateEApp,
    activeAgentId,
  ]);

  useEffect(() => {
    return () => {
      resetEAppStoreState();
      resetProgressBarState();
    };
  }, []);

  return { isLoading };
};
