import styled from '@emotion/native';
import { Column } from 'cube-ui-components';
import React, { MutableRefObject } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormClearErrors,
  UseFormTrigger,
  UseFormResetField,
  UseFormSetValue,
} from 'react-hook-form';
import AddressInformation from './addressInformation/AddressInformation';
import ContactDetails from './contactDetails/ContactDetails';
import NationalityDetails from './nationalityDetails/NationalityDetails';
import OccupationDetails from './occupationDetails/OccupationDetails';
import PersonalDetails from './personalDetails/PersonalDetails';
import { InsuredFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { OcrCapture, useOcr } from 'features/eAppV2/my/hooks/useOcr';

interface Props {
  index: number;
  control: Control<{ data: InsuredFormSchemaType[] }>;
  setValue: UseFormSetValue<{ data: InsuredFormSchemaType[] }>;
  resetFieldValue: UseFormResetField<{ data: InsuredFormSchemaType[] }>;
  clearErrors: UseFormClearErrors<{ data: InsuredFormSchemaType[] }>;
  getValues: UseFormGetValues<{ data: InsuredFormSchemaType[] }>;
  trigger: UseFormTrigger<{ data: InsuredFormSchemaType[] }>;
  forEntity?: boolean;
  policyOwnerAddress?: AddressInfo;
  mainInsuredAddress?: AddressInfo;
  ocrCaptures?: MutableRefObject<(OcrCapture | undefined)[]>;
  isChildMainInsured?: boolean;
}

const ChildDetails = (props: Props) => {
  const {
    index,
    control,
    setValue,
    getValues,
    resetFieldValue,
    clearErrors,
    trigger,
    forEntity,
    policyOwnerAddress,
    mainInsuredAddress,
    ocrCaptures,
    isChildMainInsured,
  } = props;

  return (
    <Wrapper>
      <PersonalDetails
        control={control}
        key={index}
        index={index}
        resetField={resetFieldValue}
        forEntity={forEntity}
        setValue={setValue}
        getValues={getValues}
        disableMaritalStatus={false}
        ocrCaptures={ocrCaptures}
      />
      <NationalityDetails
        control={control}
        key={index}
        index={index}
        resetField={resetFieldValue}
        forEntity={forEntity}
        setValue={setValue}
        clearErrors={clearErrors}
      />
      <OccupationDetails
        control={control}
        key={index}
        index={index}
        resetField={resetFieldValue}
        forEntity={forEntity}
        setValue={setValue}
        isChildMainInsured={isChildMainInsured}
      />
      <ContactDetails
        control={control}
        key={index}
        index={index}
        resetField={resetFieldValue}
        forEntity={forEntity}
        setValue={setValue}
        getValues={getValues}
      />
      <AddressInformation
        control={control}
        key={index}
        index={index}
        resetField={resetFieldValue}
        forEntity={forEntity}
        setValue={setValue}
        getValues={getValues}
        trigger={trigger}
        ownerAddress={policyOwnerAddress as unknown as AddressInfo}
        mainInsuredAddress={mainInsuredAddress as unknown as AddressInfo}
      />
    </Wrapper>
  );
};

export default ChildDetails;

const Wrapper = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    marginBottom: space[5],
    borderRadius: borderRadius.large,
    paddingBottom: space[6],
  }),
);
