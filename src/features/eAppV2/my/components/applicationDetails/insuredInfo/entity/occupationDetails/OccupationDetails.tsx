import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Input from 'components/Input';
import {
  Column,
  CurrencyTextField, PictogramIcon, Row, TextField,
  Typography
} from 'cube-ui-components';
import Autocomplete from 'components/Autocomplete';
import React, { useEffect, useMemo, useState } from 'react';
import {
  Control,
  UseFormSetValue,
  useController,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { IncomeRange, Nationality } from 'types/optionList';
import ApplicationDetailsTabletSectionContainer from '../../../../../../common/components/ApplicationDetailsTabletSectionContainer';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useOccupationClass } from 'features/customerFactFind/hooks/useOccupationClass';
import { INCOME_GREATER_THAN_200K, NON_INCOME_OCC_GROUP } from 'constants/optionList';
import { InsuredFormSchemaType } from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import AutocompletePopup from 'components/AutocompletePopup';
import AnnualIncome from '../../../AnnualIncome';
import { useHasAddRider } from 'features/eAppV2/my/hooks/useHasAddRider';

interface Props {
  control: Control<InsuredFormSchemaType>;
  setValue: UseFormSetValue<InsuredFormSchemaType>;
  isMainInsured?: boolean;
}

const OccupationDetails = (props: Props) => {
  const { control, isMainInsured } = props;
  const { t } = useTranslation(['eApp']);
  const { colors, space } = useTheme();

  const { data: optionList, isFetching: isLoadingOptionList } =
    useGetOptionList<'my'>();
  const [isDone, setDone] = useState<boolean>(false);

  const {
    field: { onChange: onChangeOccupationGroup },
  } = useController({
    name: 'occupationGroup',
    control,
  });

  const occupation = useWatch({
    name: 'occupation',
    control: control,
  });
  const nameOfBusiness = useWatch({
    name: 'nameOfBusiness',
    control: control,
  });
  const natureOfWork = useWatch({
    name: 'natureOfWork',
    control: control,
  });
  const exactDuties = useWatch({
    name: 'exactDuties',
    control: control,
  });
  const annualIncome = useWatch({
    name: 'annualIncome',
    control: control,
  });

  useEffect(() => {
    if (
      occupation &&
      nameOfBusiness &&
      natureOfWork &&
      exactDuties &&
      annualIncome
    ) {
      setDone(true);
    } else {
      setDone(false);
    }
  }, [occupation, nameOfBusiness, natureOfWork, exactDuties, annualIncome]);

  const { occupationClass, occupationGroup, occupationDescription } =
    useOccupationClass(occupation);

  const {
    field: { onChange: onChangeOccupationDescription },
  } = useController({ name: 'occupationDescription', control });

  useEffect(() => {
    onChangeOccupationDescription(occupationDescription);
    onChangeOccupationGroup(occupationGroup);
  }, [
    occupationDescription,
    occupationGroup,
    onChangeOccupationDescription,
    onChangeOccupationGroup,
  ]);

  const annualIncomeValue = useWatch({
    name: 'annualIncome',
    control: control,
  });

  const {
    field: {
      onChange: onChangeAnnualIncomeAmount,
      onBlur: onBlurAnnualIncomeAmount,
    },
  } = useController({ name: 'annualIncomeAmount', control });

  useEffect(() => {
    if (annualIncomeValue !== INCOME_GREATER_THAN_200K) {
      onChangeAnnualIncomeAmount('');
      onBlurAnnualIncomeAmount();
    }
  }, [annualIncomeValue, onBlurAnnualIncomeAmount, onChangeAnnualIncomeAmount]);

  const shouldHighlight = useMemo(() => {
    return occupationGroup !== NON_INCOME_OCC_GROUP;
  }, [occupationGroup]);

  const { hasAddRider } = useHasAddRider();

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('eApp:certificate.occupationTitle')}
      icon={<PictogramIcon.Work3 size={40} />}
      isDone={isDone}>
      <Content>
        <RowInfo>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.form.occupation')}
            </Typography.SmallLabel>

            <Typography.Body>
              {
                optionList?.OCCUPATION.options.find(e => occupation == e.value)
                  ?.label
              }
            </Typography.Body>
          </Column>
          <Column flex={1} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationClass')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationClass?.label.en}</Typography.Body>
          </Column>

          <Column flex={4} gap={space[2]}>
            <Typography.SmallLabel color={colors.placeholder}>
              {t('eApp:certificate.occupationDescription')}
            </Typography.SmallLabel>

            <Typography.Body>{occupationDescription}</Typography.Body>
          </Column>
        </RowInfo>

        {/* 2 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="nameOfBusiness"
            label={t('eApp:certificate.form.nameOfBusiness')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
          <Input
            control={control}
            as={AutocompletePopup<Nationality, string>}
            name="natureOfWork"
            label={t('eApp:certificate.form.natureOfWork')}
            data={optionList?.NATURE_OF_WORK_BUSINESS.options ?? []}
            disabled={isLoadingOptionList}
            getItemLabel={item => item.label}
            getItemValue={item => item.value}
            style={{ flex: 1, marginTop: 7 }}
            searchable
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
        </RowBox>

        {/* 3 */}
        <RowBox>
          <Input
            control={control}
            as={TextField}
            name="exactDuties"
            label={t('eApp:certificate.form.exactDuties')}
            style={{ flex: 1, marginTop: 7 }}
            shouldHighlightOnUntouched={value => shouldHighlight && !value}
          />
          <Input
            control={control}
            as={AnnualIncome}
            name="annualIncome"
            disabled={isMainInsured && hasAddRider}
            shouldHighlightOnUntouched={value =>
              shouldHighlight && !value && !hasAddRider
            }
          />
        </RowBox>

        {/* 4 */}
        <RowBox>
          <Input
            control={control}
            as={CurrencyTextField}
            name="annualIncomeAmount"
            label={t('eApp:certificate.form.annualIncomeAmount')}
            style={{ flex: 1, marginTop: 7 }}
            disabled={annualIncomeValue !== INCOME_GREATER_THAN_200K}
            shouldHighlightOnUntouched={value => !value}
          />
          <Column flex={1} />
        </RowBox>
      </Content>
    </ApplicationDetailsTabletSectionContainer>
  );
};

export default OccupationDetails;

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

const RowBox = styled(Row)(({ theme: { space } }) => ({
  gap: space[6],
  flex: 1,
  marginTop: space[5],
}));

const RowInfo = styled(Row)(({ theme: { space, colors } }) => ({
  gap: space[8],
  flex: 1,
  borderRadius: space[2],
  borderWidth: 1,
  borderColor: colors.palette.fwdGrey[100],
  padding: space[5],
  marginTop: space[5],
}));
