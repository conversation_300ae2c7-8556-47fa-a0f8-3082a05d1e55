import styled from '@emotion/native';
import { Row } from 'cube-ui-components';
import {
  ChildDependentDetailForm,
  childDependentDetailFormValidationSchema,
  InsuredFormSchemaType,
  insuredFormValidationSchema,
} from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  UseFormTrigger,
  useForm,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import TabletSections, {
  flattenSectionNameList,
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSections';
import InsuredInfoHeader from '../InsuredInfoHeader';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import EAppFooterTablet from '../../../../../common/components/footer/EAppFooter.tablet';
import { toParty } from 'features/eAppV2/my/utils/caseUtils';
import { PartyRole } from 'types/party';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import PersonalDetails from './personalDetails/PersonalDetails';
import NationalityDetails from './nationalityDetails/NationalityDetails';
import OccupationDetails from './occupationDetails/OccupationDetails';
import ContactDetails from './contactDetails/ContactDetails';
import { ValidationError } from 'yup';
import AddressInformation from './addressInformation/AddressInformation';
import ChildDetails from '../childDetails/ChildDetails';
import { useEAppAlert } from 'features/eAppV2/common/hooks/useEAppAlert';
import { OcrCapture, useSaveOcr } from 'features/eAppV2/my/hooks/useOcr';
import { useGetActiveCase } from 'hooks/useGetActiveCase';

export default function EntityInsuredInfo() {
  const { t } = useTranslation(['eApp']);
  const { alertError } = useEAppAlert();
  const {
    my_policyOwnerPersonalInfo,
    my_authorizedSignatoryInfo,
    my_companyInfo,
    my_hasEmployeeInsured,
    my_employeePersonalInfo,
    updateMY_EmployeeInfo,
    my_hasSpouseInsured,
    my_spousePersonalInfo,
    updateMY_SpouseInfo,
    my_hasChildInsured,
    my_childrenPersonalInfo,
    updateMY_ChildrenInfo,
  } = useEAppStore(
    state => ({
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_authorizedSignatoryInfo: state.my_authorizedSignatoryInfo,
      my_companyInfo: state.my_companyInfo,
      my_hasEmployeeInsured: state.my_hasEmployeeInsured,
      my_employeePersonalInfo: state.my_employeePersonalInfo,
      updateMY_EmployeeInfo: state.updateMY_EmployeeInfo,
      my_hasSpouseInsured: state.my_hasSpouseInsured,
      my_spousePersonalInfo: state.my_spousePersonalInfo,
      updateMY_SpouseInfo: state.updateMY_SpouseInfo,
      my_hasChildInsured: state.my_hasChildInsured,
      my_childrenPersonalInfo: state.my_childrenPersonalInfo,
      updateMY_ChildrenInfo: state.updateMY_ChildrenInfo,
    }),
    shallow,
  );
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);
  const employeeOcrCapture = useRef<OcrCapture>();
  const spouseOcrCapture = useRef<OcrCapture>();
  const childOcrCaptures = useRef<OcrCapture[]>(
    Array(my_childrenPersonalInfo.length).fill(undefined),
  );
  const { caseObj } = useGetActiveCase();
  const isEmployeeMainInsured = useMemo(
    () =>
      caseObj?.parties?.find(p => p.id === my_employeePersonalInfo.id)
        ?.isMainInsured,
    [caseObj?.parties, my_employeePersonalInfo.id],
  );
  const isSpouseMainInsured = useMemo(
    () =>
      caseObj?.parties?.find(p => p.id === my_spousePersonalInfo.id)
        ?.isMainInsured,
    [caseObj?.parties, my_spousePersonalInfo.id],
  );
  const isChildMainInsured = useMemo(
    () =>
      my_childrenPersonalInfo.map(
        child => caseObj?.parties?.find(p => p.id === child.id)?.isMainInsured,
      ),
    [caseObj?.parties, my_childrenPersonalInfo],
  );

  const employeeDetailsResolver = useYupResolver(insuredFormValidationSchema);

  const {
    control: employeeDetailFormControl,
    setValue: setValueEmployeeDetail,
    getValues: getValueEmployeeDetail,
    trigger: triggerEmployeeDetail,
    handleSubmit: handleEmployeeDetailFormSubmit,
    formState: { isValid: isEmployeeValid },
    clearErrors: clearErrorsEmployeeDetail,
    watch: watchEmployeeDetail,
  } = useForm<InsuredFormSchemaType>({
    mode: 'onBlur',
    defaultValues: my_employeePersonalInfo,
    resolver: employeeDetailsResolver,
  });

  const spouseDetailsResolver = useYupResolver(insuredFormValidationSchema);

  const {
    control: spouseDetailFormControl,
    setValue: setValueSpouseDetail,
    getValues: getValueSpouseDetail,
    trigger: triggerSpouseDetail,
    handleSubmit: handleSpouseDetailFormSubmit,
    formState: { isValid: isSpouseValid },
    clearErrors: clearErrorsSpouseDetail,
    watch: watchSpouseDetail,
  } = useForm<InsuredFormSchemaType>({
    mode: 'onBlur',
    defaultValues: my_spousePersonalInfo,
    resolver: spouseDetailsResolver,
  });

  const childDetailsResolver = useYupResolver(
    childDependentDetailFormValidationSchema,
  );

  const {
    control: childDetailFormControl,
    setValue: setValueChildInfo,
    getValues: getValueChildInfo,
    handleSubmit: handleChildInfoFormSubmit,
    formState: { isValid: isChildValid },
    reset: resetChildDependentValue,
    resetField: resetChildDependentField,
    clearErrors: clearErrorsChildDependent,
    watch: watchChildDependent,
    trigger: triggerChildDependent,
  } = useForm<ChildDependentDetailForm>({
    mode: 'onBlur',
    defaultValues: { data: my_childrenPersonalInfo },
    resolver: childDetailsResolver,
  });

  const { data: optionList } = useGetOptionList<'my'>();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { saveOcr, isLoading: isSavingOcr } = useSaveOcr();

  const onContinue = async () => {
    const saveEmployeeInfo = () => {
      return handleEmployeeDetailFormSubmit(async data => {
        if (!optionList) return;
        const id = await saveParty(
          toParty(data, PartyRole.INSURED, optionList),
          {
            overridingRoles: false,
            preventCreatingParty: true,
          },
        );
        await saveOcr(PartyRole.INSURED, id, employeeOcrCapture.current);
        setValueEmployeeDetail('id', id);
        updateMY_EmployeeInfo({ ...data, id });
      })();
    };
    const saveSpouseInfo = () => {
      return handleSpouseDetailFormSubmit(async data => {
        if (!optionList) return;
        const id = await saveParty(
          toParty(data, PartyRole.INSURED, optionList),
          {
            overridingRoles: false,
            preventCreatingParty: true,
          },
        );
        await saveOcr(PartyRole.INSURED, id, spouseOcrCapture.current);
        setValueSpouseDetail('id', id);
        updateMY_SpouseInfo({ ...data, id });
      })();
    };
    const saveChildrenInfo = () => {
      return handleChildInfoFormSubmit(async data => {
        if (!optionList) return;
        const ids: string[] = [];
        for (let i = 0; i < data.data.length; i++) {
          const childDetails = data.data[i];
          const id = await saveParty(
            {
              ...toParty(childDetails, PartyRole.INSURED, optionList),
            },
            {
              overridingRoles: false,
              preventCreatingParty: true,
            },
          );
          await saveOcr(PartyRole.INSURED, id, childOcrCaptures.current[i]);
          ids.push(id || '');
        }
        data.data.forEach((_, idx) => {
          setValueChildInfo(`data.${idx}.id`, ids[idx]);
        });
        updateMY_ChildrenInfo(
          data.data.map((childDetail, idx) => ({
            ...childDetail,
            id: ids[idx],
          })),
        );
      })();
    };
    try {
      const flattenPaths = flattenSectionNameList(sections);
      const activePathIndex = flattenPaths.findIndex(
        path => path === activePath,
      );
      const isOnLastSection = activePathIndex === flattenPaths.length - 1;
      if (isOnLastSection) {
        await saveEmployeeInfo();
        if (my_hasSpouseInsured) await saveSpouseInfo();
        if (my_hasChildInsured) await saveChildrenInfo();
        nextSubgroup(true);
      } else {
        setActivePath(flattenPaths[activePathIndex + 1]);
      }
    } catch {
      alertError(onContinue);
    }
  };

  const childScrollRefs = useRef<(KeyboardAwareScrollView | null)[]>(
    my_childrenPersonalInfo.map(() => null),
  );
  const activeChildTabIdx = activePath?.includes('childInfo')
    ? Number(activePath.split('.').slice(-1)[0])
    : -1;
  const {
    totalIncompleteRequiredFields: totalChildDetailsIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextChildDetailsIncompleteField,
  } = useIncompleteFields({
    control: childDetailFormControl,
    watch: watchChildDependent,
    schema: childDependentDetailFormValidationSchema,
    scrollRef:
      activeChildTabIdx >= 0
        ? childScrollRefs.current[activeChildTabIdx]
        : null,
    scrollTo: option =>
      childScrollRefs.current[activeChildTabIdx]?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
    filterError: (e: ValidationError) => {
      return Boolean(e.path?.includes(`data[${activeChildTabIdx}]`));
    },
  });

  const spouseScrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    totalIncompleteRequiredFields: totalSpouseDetailsIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextSpouseDetailsIncompleteField,
  } = useIncompleteFields({
    control: spouseDetailFormControl,
    watch: watchSpouseDetail,
    schema: insuredFormValidationSchema,
    scrollRef: spouseScrollRef,
    scrollTo: option =>
      spouseScrollRef.current?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
  });

  const employeeScrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    totalIncompleteRequiredFields: totalEmployeeDetailsIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextEmployeeDetailsIncompleteField,
  } = useIncompleteFields({
    control: employeeDetailFormControl,
    watch: watchEmployeeDetail,
    schema: insuredFormValidationSchema,
    scrollRef: employeeScrollRef,
    scrollTo: option =>
      employeeScrollRef.current?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
  });

  const focusOnIncompleteField = () => {
    if (activePath === 'employeeInfo') {
      focusOnNextEmployeeDetailsIncompleteField();
    } else if (activePath === 'spouseInfo') {
      focusOnNextSpouseDetailsIncompleteField();
    } else {
      focusOnNextChildDetailsIncompleteField();
    }
  };

  const companyAddress = useMemo(
    () => ({
      businessAddress: my_companyInfo.correspondenceAddress,
      businessAddressLine1: my_companyInfo.correspondenceAddressLine1,
      businessAddressLine2: my_companyInfo.correspondenceAddressLine2,
      businessAddressLine3: my_companyInfo.correspondenceAddressLine3,
      businessPostCode: my_companyInfo.correspondencePostCode,
      businessCity: my_companyInfo.correspondenceCity,
      businessState: my_companyInfo.correspondenceState,
      businessCountry: my_companyInfo.correspondenceCountry,
    }),
    [
      my_companyInfo.correspondenceAddress,
      my_companyInfo.correspondenceAddressLine1,
      my_companyInfo.correspondenceAddressLine2,
      my_companyInfo.correspondenceAddressLine3,
      my_companyInfo.correspondenceCity,
      my_companyInfo.correspondenceCountry,
      my_companyInfo.correspondencePostCode,
      my_companyInfo.correspondenceState,
    ],
  );

  useEffect(() => {
    setValueEmployeeDetail(
      'authorizedSignatoryId',
      my_authorizedSignatoryInfo.idNumber,
    );
    setValueSpouseDetail(
      'authorizedSignatoryId',
      my_authorizedSignatoryInfo.idNumber,
    );
    setValueChildInfo(
      'authorizedSignatoryId',
      my_authorizedSignatoryInfo.idNumber,
    );
  }, [
    my_authorizedSignatoryInfo.idNumber,
    setValueChildInfo,
    setValueEmployeeDetail,
    setValueSpouseDetail,
  ]);

  const employeeAddressValues = watchEmployeeDetail([
    'correspondenceAddress',
    'correspondenceAddressLine1',
    'correspondenceAddressLine2',
    'correspondenceAddressLine3',
    'correspondencePostCode',
    'correspondenceCity',
    'correspondenceState',
    'correspondenceCountry',
    'residentialAddress',
    'residentialAddressLine1',
    'residentialAddressLine2',
    'residentialAddressLine3',
    'residentialPostCode',
    'residentialCity',
    'residentialState',
    'residentialCountry',
    'businessAddress',
    'businessAddressLine1',
    'businessAddressLine2',
    'businessAddressLine3',
    'businessPostCode',
    'businessCity',
    'businessState',
    'businessCountry',
  ]);

  const employeeAddress = useMemo(
    () => ({
      correspondenceAddress: employeeAddressValues[0],
      correspondenceAddressLine1: employeeAddressValues[1],
      correspondenceAddressLine2: employeeAddressValues[2],
      correspondenceAddressLine3: employeeAddressValues[3],
      correspondencePostCode: employeeAddressValues[4],
      correspondenceCity: employeeAddressValues[5],
      correspondenceState: employeeAddressValues[6],
      correspondenceCountry: employeeAddressValues[7],
      residentialAddress: employeeAddressValues[8],
      residentialAddressLine1: employeeAddressValues[9],
      residentialAddressLine2: employeeAddressValues[10],
      residentialAddressLine3: employeeAddressValues[11],
      residentialPostCode: employeeAddressValues[12],
      residentialCity: employeeAddressValues[13],
      residentialState: employeeAddressValues[14],
      residentialCountry: employeeAddressValues[15],
      businessAddress: employeeAddressValues[16],
      businessAddressLine1: employeeAddressValues[17],
      businessAddressLine2: employeeAddressValues[18],
      businessAddressLine3: employeeAddressValues[19],
      businessPostCode: employeeAddressValues[20],
      businessCity: employeeAddressValues[21],
      businessState: employeeAddressValues[22],
      businessCountry: employeeAddressValues[23],
    }),
    [
      employeeAddressValues[0],
      employeeAddressValues[1],
      employeeAddressValues[2],
      employeeAddressValues[3],
      employeeAddressValues[4],
      employeeAddressValues[5],
      employeeAddressValues[6],
      employeeAddressValues[7],
      employeeAddressValues[8],
      employeeAddressValues[9],
      employeeAddressValues[10],
      employeeAddressValues[11],
      employeeAddressValues[12],
      employeeAddressValues[13],
      employeeAddressValues[14],
      employeeAddressValues[15],
      employeeAddressValues[16],
      employeeAddressValues[17],
      employeeAddressValues[18],
      employeeAddressValues[19],
      employeeAddressValues[20],
      employeeAddressValues[21],
      employeeAddressValues[22],
      employeeAddressValues[23],
    ],
  );

  const sections = useMemo(
    () =>
      [
        my_hasEmployeeInsured && {
          name: 'employeeInfo',
          title: t('eApp:insured'),
          subtitle: getValueEmployeeDetail('fullName'),
          content: (
            <ScrollViewContainer ref={employeeScrollRef} key={'EmployeeInfo'}>
              <InsuredInfoHeader role="employee" />
              <PersonalDetails
                control={employeeDetailFormControl}
                getValues={getValueEmployeeDetail}
                setValue={setValueEmployeeDetail}
                disableMaritalStatus={my_hasSpouseInsured}
                isMainInsured
                ocrCapture={employeeOcrCapture}
              />
              <NationalityDetails
                control={employeeDetailFormControl}
                setValue={setValueEmployeeDetail}
                clearErrors={clearErrorsEmployeeDetail}
                isMainInsured
              />
              <OccupationDetails
                control={employeeDetailFormControl}
                setValue={setValueEmployeeDetail}
                isMainInsured={isEmployeeMainInsured}
              />
              <ContactDetails
                control={employeeDetailFormControl}
                setValue={setValueEmployeeDetail}
                getValues={getValueEmployeeDetail}
              />
              <AddressInformation
                control={
                  employeeDetailFormControl as unknown as Control<AddressInfo>
                }
                setValue={
                  setValueEmployeeDetail as unknown as UseFormSetValue<AddressInfo>
                }
                getValues={
                  getValueEmployeeDetail as unknown as UseFormGetValues<AddressInfo>
                }
                trigger={
                  triggerEmployeeDetail as unknown as UseFormTrigger<AddressInfo>
                }
                ownerAddress={companyAddress as unknown as AddressInfo}
                mainInsuredAddress={employeeAddress}
                isMainInsured
              />
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
        my_hasSpouseInsured && {
          name: 'spouseInfo',
          title: `${t('eApp:insured')} (${t(
            'eApp:personCovered.menu.spouse',
          )})`,
          subtitle: getValueSpouseDetail('fullName'),
          content: (
            <ScrollViewContainer ref={spouseScrollRef} key={'SpouseInfo'}>
              <InsuredInfoHeader role="spouse" />
              <PersonalDetails
                control={spouseDetailFormControl}
                getValues={getValueSpouseDetail}
                setValue={setValueSpouseDetail}
                disableMaritalStatus={true}
                ocrCapture={spouseOcrCapture}
              />
              <NationalityDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                clearErrors={clearErrorsSpouseDetail}
              />
              <OccupationDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                isMainInsured={isSpouseMainInsured}
              />
              <ContactDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                getValues={getValueSpouseDetail}
              />
              <AddressInformation
                control={
                  spouseDetailFormControl as unknown as Control<AddressInfo>
                }
                setValue={
                  setValueSpouseDetail as unknown as UseFormSetValue<AddressInfo>
                }
                getValues={
                  getValueSpouseDetail as unknown as UseFormGetValues<AddressInfo>
                }
                trigger={
                  triggerSpouseDetail as unknown as UseFormTrigger<AddressInfo>
                }
                ownerAddress={companyAddress as unknown as AddressInfo}
                mainInsuredAddress={employeeAddress}
              />
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
        my_hasChildInsured && {
          name: 'childInfo',
          title: t('eApp:personCovered.menu.child'),
          subItems: my_childrenPersonalInfo.map((childInfo, idx) => ({
            name: String(idx),
            title: childInfo.fullName ?? '',
            content: (
              <ScrollViewContainer
                ref={ref => (childScrollRefs.current[idx] = ref)}
                key={'ChildDetail'}>
                <InsuredInfoHeader role="child" />
                <ChildDetails
                  index={idx}
                  forEntity={true}
                  control={childDetailFormControl}
                  setValue={setValueChildInfo}
                  resetFieldValue={resetChildDependentField}
                  clearErrors={clearErrorsChildDependent}
                  getValues={getValueChildInfo}
                  trigger={triggerChildDependent}
                  mainInsuredAddress={employeeAddress}
                  ocrCaptures={childOcrCaptures}
                  isChildMainInsured={isChildMainInsured[idx]}
                />
                <BoxSpace />
              </ScrollViewContainer>
            ),
          })),
        },
      ].filter(Boolean) as TabletSectionsProps['items'],
    [
      my_hasEmployeeInsured,
      t,
      getValueEmployeeDetail,
      employeeDetailFormControl,
      setValueEmployeeDetail,
      my_hasSpouseInsured,
      clearErrorsEmployeeDetail,
      isEmployeeMainInsured,
      triggerEmployeeDetail,
      companyAddress,
      employeeAddress,
      getValueSpouseDetail,
      spouseDetailFormControl,
      setValueSpouseDetail,
      clearErrorsSpouseDetail,
      isSpouseMainInsured,
      triggerSpouseDetail,
      my_hasChildInsured,
      my_childrenPersonalInfo,
      childDetailFormControl,
      setValueChildInfo,
      resetChildDependentField,
      clearErrorsChildDependent,
      getValueChildInfo,
      triggerChildDependent,
      isChildMainInsured,
    ],
  );

  const childDetail = useWatch({
    control: childDetailFormControl,
  });

  const isEnabledButton = useMemo(() => {
    if (activePath === 'spouseInfo') return isSpouseValid;
    if (activePath === 'employeeInfo') return isEmployeeValid;
    if (activePath?.includes('childInfo')) {
      const values = childDetail?.data
        ? childDetail?.data[activeChildTabIdx]
        : {};
      return insuredFormValidationSchema.isValidSync(values);
    }
    return true;
  }, [
    activePath,
    isSpouseValid,
    isEmployeeValid,
    childDetail,
    activeChildTabIdx,
  ]);

  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock="appDetail-insured"
        onPrimaryPress={onContinue}
        primaryDisabled={!isEnabledButton}
        primaryLoading={isSavingParty || isSavingOcr}
        totalIncompleteRequiredFields={
          activePath === 'spouseInfo'
            ? totalSpouseDetailsIncompleteRequiredFields
            : activePath === 'employeeInfo'
            ? totalEmployeeDetailsIncompleteRequiredFields
            : totalChildDetailsIncompleteRequiredFields
        }
        focusOnIncompleteField={focusOnIncompleteField}
      />
    </>
  );
}

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
