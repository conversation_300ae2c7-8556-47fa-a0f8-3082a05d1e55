import styled from '@emotion/native';
import { Row } from 'cube-ui-components';
import { useCFFValidationResolver } from 'features/customerFactFind/hooks/useCFFValidationResolver';
import {
  ChildDependentDetailForm,
  childDependentDetailFormValidationSchema,
  InsuredFormSchemaType,
  insuredFormValidationSchema,
} from 'features/eAppV2/my/validations/applicationDetails/insuredInfoValidation';
import React, { memo, useMemo, useRef, useState } from 'react';
import {
  Control,
  useForm,
  UseFormTrigger,
  UseFormGetValues,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import TabletSections, {
  flattenSectionNameList,
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSections';
import InsuredInfoHeader from '../InsuredInfoHeader';
import AddressInformation from '../../policyOwnerInfo/components/addressInformation/AddressInformation';
import ChildDetails from '../childDetails/ChildDetails';
import ContactDetails from '../../policyOwnerInfo/components/contactDetails/ContactDetails';
import NationalityDetails from '../../policyOwnerInfo/components/nationalityDetails/NationalityDetails';
import OccupationDetails from '../../policyOwnerInfo/components/occupationDetails/OccupationDetails';
import PersonalDetails from '../../policyOwnerInfo/components/personalDetails/PersonalDetails';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { shallow } from 'zustand/shallow';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import EAppFooterTablet from '../../../../../common/components/footer/EAppFooter.tablet';
import { toParty } from 'features/eAppV2/my/utils/caseUtils';
import { PartyRole } from 'types/party';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useAlert } from 'hooks/useAlert';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useEAppAlert } from 'features/eAppV2/common/hooks/useEAppAlert';
import { ValidationError } from 'yup';
import {
  OcrCapture,
  useOcr,
  useSaveOcr,
} from 'features/eAppV2/my/hooks/useOcr';
import { useGetActiveCase } from 'hooks/useGetActiveCase';

const IndividualInsuredInfo = memo(() => {
  const { t } = useTranslation(['eApp']);
  const { alertError } = useEAppAlert();
  const {
    my_policyOwnerPersonalInfo,
    my_hasSpouseInsured,
    my_spousePersonalInfo,
    updateMY_SpouseInfo,
    my_hasChildInsured,
    my_childrenPersonalInfo,
    updateMY_ChildrenInfo,
  } = useEAppStore(
    state => ({
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_hasSpouseInsured: state.my_hasSpouseInsured,
      my_spousePersonalInfo: state.my_spousePersonalInfo,
      updateMY_SpouseInfo: state.updateMY_SpouseInfo,
      my_hasChildInsured: state.my_hasChildInsured,
      my_childrenPersonalInfo: state.my_childrenPersonalInfo,
      updateMY_ChildrenInfo: state.updateMY_ChildrenInfo,
    }),
    shallow,
  );
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);
  const spouseOcrCapture = useRef<OcrCapture>();
  const childOcrCaptures = useRef<OcrCapture[]>(
    Array(my_childrenPersonalInfo.length).fill(undefined),
  );
  const { caseObj } = useGetActiveCase();
  const isSpouseMainInsured = useMemo(
    () =>
      caseObj?.parties?.find(p => p.id === my_spousePersonalInfo.id)
        ?.isMainInsured,
    [caseObj?.parties, my_spousePersonalInfo.id],
  );
  const isChildMainInsured = useMemo(
    () =>
      my_childrenPersonalInfo.map(
        child => caseObj?.parties?.find(p => p.id === child.id)?.isMainInsured,
      ),
    [caseObj?.parties, my_childrenPersonalInfo],
  );

  const spouseDetailsResolver = useCFFValidationResolver(
    insuredFormValidationSchema,
  );

  const childDetailsResolver = useYupResolver(
    childDependentDetailFormValidationSchema,
  );

  const {
    control: spouseDetailFormControl,
    setValue: setValueSpouseDetail,
    getValues: getValueSpouseDetail,
    trigger: triggerSpouseDetail,
    handleSubmit: handleSpouseDetailFormSubmit,
    formState: { isValid: isSpouseValid },
    clearErrors: clearErrorsSpouseDetail,
    watch: watchSpouseDetail,
    setFocus: setFocusSpouseDetail,
  } = useForm<InsuredFormSchemaType>({
    mode: 'onBlur',
    defaultValues: my_spousePersonalInfo,
    resolver: spouseDetailsResolver,
  });

  const {
    control: childDetailFormControl,
    setValue: setValueChildInfo,
    getValues: getValueChildInfo,
    handleSubmit: handleChildInfoFormSubmit,
    formState: { isValid: isChildValid },
    reset: resetChildDependentValue,
    resetField: resetChildDependentField,
    watch: watchChildDependent,
    setFocus: setFocusChildDependent,
    clearErrors: clearErrorsChildDependent,
    trigger: triggerChildDependent,
  } = useForm<ChildDependentDetailForm>({
    mode: 'onBlur',
    defaultValues: { data: my_childrenPersonalInfo },
    resolver: childDetailsResolver,
  });

  const { data: optionList } = useGetOptionList<'my'>();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { saveOcr, isLoading: isSavingOcr } = useSaveOcr();

  const onContinue = async () => {
    const saveSpouseInfo = () => {
      return handleSpouseDetailFormSubmit(async data => {
        if (!optionList) return;
        const id = await saveParty(
          toParty(data, PartyRole.INSURED, optionList),
          {
            overridingRoles: false,
            preventCreatingParty: true,
          },
        );
        await saveOcr(PartyRole.INSURED, id, spouseOcrCapture.current);
        setValueSpouseDetail('id', id);
        updateMY_SpouseInfo({ ...data, id });
      })();
    };
    const saveChildrenInfo = () => {
      return handleChildInfoFormSubmit(async data => {
        if (!optionList) return;
        const ids: string[] = [];
        for (let i = 0; i < data.data.length; i++) {
          const childDetails = data.data[i];
          const id = await saveParty(
            {
              ...toParty(childDetails, PartyRole.INSURED, optionList),
            },
            {
              overridingRoles: false,
              preventCreatingParty: true,
            },
          );
          await saveOcr(PartyRole.INSURED, id, childOcrCaptures.current[i]);
          ids.push(id || '');
        }
        data.data.forEach((_, idx) => {
          setValueChildInfo(`data.${idx}.id`, ids[idx]);
        });
        updateMY_ChildrenInfo(
          data.data.map((childDetail, idx) => ({
            ...childDetail,
            id: ids[idx],
          })),
        );
      })();
    };
    try {
      const flattenPaths = flattenSectionNameList(sections);
      const activePathIndex = flattenPaths.findIndex(
        path => path === activePath,
      );
      const isOnLastSection = activePathIndex === flattenPaths.length - 1;
      if (isOnLastSection) {
        if (my_hasSpouseInsured) await saveSpouseInfo();
        if (my_hasChildInsured) await saveChildrenInfo();
        nextSubgroup(true);
      } else {
        setActivePath(flattenPaths[activePathIndex + 1]);
      }
    } catch {
      alertError(onContinue);
    }
  };

  const childScrollRefs = useRef<(KeyboardAwareScrollView | null)[]>(
    my_childrenPersonalInfo.map(() => null),
  );
  const activeChildTabIdx = activePath?.includes('childInfo')
    ? Number(activePath.split('.').slice(-1)[0])
    : -1;
  const {
    totalIncompleteRequiredFields: totalChildDetailsIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextChildDetailsIncompleteField,
  } = useIncompleteFields({
    control: childDetailFormControl,
    watch: watchChildDependent,
    schema: childDependentDetailFormValidationSchema,
    scrollRef:
      activeChildTabIdx >= 0
        ? childScrollRefs.current[activeChildTabIdx]
        : null,
    scrollTo: option =>
      childScrollRefs.current[activeChildTabIdx]?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
    filterError: (e: ValidationError) => {
      return Boolean(e.path?.includes(`data[${activeChildTabIdx}]`));
    },
  });

  const spouseScrollRef = useRef<KeyboardAwareScrollView>(null);
  const {
    totalIncompleteRequiredFields: totalSpouseDetailsIncompleteRequiredFields,
    focusOnNextIncompleteField: focusOnNextSpouseDetailsIncompleteField,
  } = useIncompleteFields({
    control: spouseDetailFormControl,
    watch: watchSpouseDetail,
    schema: insuredFormValidationSchema,
    scrollRef: spouseScrollRef,
    scrollTo: option =>
      spouseScrollRef.current?.scrollToPosition(
        0,
        option.y || 0,
        option.animated,
      ),
  });

  const focusOnIncompleteField = () => {
    if (activePath === 'spouseInfo') {
      focusOnNextSpouseDetailsIncompleteField();
    } else {
      focusOnNextChildDetailsIncompleteField();
    }
  };

  const spouseOccupationGroup = watchSpouseDetail('occupationGroup');

  const sections = useMemo(
    () =>
      [
        my_hasSpouseInsured && {
          name: 'spouseInfo',
          title: t('eApp:personCovered.menu.spouse'),
          subtitle: getValueSpouseDetail('fullName'),
          content: (
            <ScrollViewContainer ref={spouseScrollRef} key={'InsuredInfo'}>
              <InsuredInfoHeader role="spouse" />
              <PersonalDetails
                control={spouseDetailFormControl}
                getValues={getValueSpouseDetail}
                setValue={setValueSpouseDetail}
                ocrCapture={spouseOcrCapture}
              />
              <NationalityDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                clearErrors={clearErrorsSpouseDetail}
              />
              <OccupationDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                isMainInsured={isSpouseMainInsured}
              />
              <ContactDetails
                control={spouseDetailFormControl}
                setValue={setValueSpouseDetail}
                getValues={getValueSpouseDetail}
              />
              <AddressInformation
                isFromSI
                ownerAddress={my_policyOwnerPersonalInfo}
                control={
                  spouseDetailFormControl as unknown as Control<AddressInfo>
                }
                setValue={
                  setValueSpouseDetail as unknown as UseFormSetValue<AddressInfo>
                }
                getValues={
                  getValueSpouseDetail as unknown as UseFormGetValues<AddressInfo>
                }
                trigger={
                  triggerSpouseDetail as unknown as UseFormTrigger<AddressInfo>
                }
                occupationGroup={spouseOccupationGroup}
              />
              <BoxSpace />
            </ScrollViewContainer>
          ),
        },
        my_hasChildInsured && {
          name: 'childInfo',
          title: t('eApp:personCovered.menu.child'),
          subItems: my_childrenPersonalInfo.map((childInfo, idx) => ({
            name: String(idx),
            title: childInfo.fullName ?? '',
            content: (
              <ScrollViewContainer
                ref={ref => (childScrollRefs.current[idx] = ref)}
                key={'ChildDetail'}>
                <InsuredInfoHeader role="child" />
                <ChildDetails
                  index={idx}
                  control={childDetailFormControl}
                  setValue={setValueChildInfo}
                  resetFieldValue={resetChildDependentField}
                  clearErrors={clearErrorsChildDependent}
                  getValues={getValueChildInfo}
                  trigger={triggerChildDependent}
                  policyOwnerAddress={my_policyOwnerPersonalInfo}
                  ocrCaptures={childOcrCaptures}
                  isChildMainInsured={isChildMainInsured[idx]}
                />
                <BoxSpace />
              </ScrollViewContainer>
            ),
          })),
        },
      ].filter(Boolean) as TabletSectionsProps['items'],
    [
      my_hasSpouseInsured,
      t,
      getValueSpouseDetail,
      spouseDetailFormControl,
      setValueSpouseDetail,
      clearErrorsSpouseDetail,
      isSpouseMainInsured,
      my_policyOwnerPersonalInfo,
      triggerSpouseDetail,
      spouseOccupationGroup,
      my_hasChildInsured,
      my_childrenPersonalInfo,
      childDetailFormControl,
      setValueChildInfo,
      resetChildDependentField,
      clearErrorsChildDependent,
      getValueChildInfo,
      triggerChildDependent,
      isChildMainInsured,
    ],
  );

  const childDetail = useWatch({
    control: childDetailFormControl,
  });

  const isEnabledButton = useMemo(() => {
    if (activePath === 'spouseInfo') return isSpouseValid;
    if (activePath?.includes('childInfo')) {
      const values = childDetail?.data
        ? childDetail?.data[activeChildTabIdx]
        : {};
      return insuredFormValidationSchema.isValidSync(values);
    }
    return true;
  }, [activePath, isSpouseValid, childDetail?.data, activeChildTabIdx]);

  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock="appDetail-insured"
        onPrimaryPress={onContinue}
        primaryDisabled={!isEnabledButton}
        primaryLoading={isSavingParty || isSavingOcr}
        totalIncompleteRequiredFields={
          activePath === 'spouseInfo'
            ? totalSpouseDetailsIncompleteRequiredFields
            : totalChildDetailsIncompleteRequiredFields
        }
        focusOnIncompleteField={focusOnIncompleteField}
      />
    </>
  );
});

export default IndividualInsuredInfo;

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
