import styled from '@emotion/native';
import { Row } from 'cube-ui-components';
import { useCFFValidationResolver } from 'features/customerFactFind/hooks/useCFFValidationResolver';

import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { useEAppAlert } from 'features/eAppV2/common/hooks/useEAppAlert';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { toParty } from 'features/eAppV2/my/utils/caseUtils';
import {
  PolicyOwnerFormSchemaType,
  policyOwnerFormValidationSchema,
} from 'features/eAppV2/my/validations/applicationDetails/policyOwnerInfoValidation';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';
import {
  Control,
  useForm,
  UseFormTrigger,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { CHANNELS } from 'types/channel';
import { PartyRole } from 'types/party';
import GATracking from 'utils/helper/gaTracking';
import { shallow } from 'zustand/shallow';
import TabletSections from '../../../../common/components/TabletSections';
import EAppFooterTablet from '../../../../common/components/footer/EAppFooter.tablet';
import PolicyOwnerInfoHeader from './components/PolicyOwnerInfoHeader';
import AddressInformation from './components/addressInformation/AddressInformation';
import ContactDetails from './components/contactDetails/ContactDetails';
import NationalityDetails from './components/nationalityDetails/NationalityDetails';
import OccupationDetails from './components/occupationDetails/OccupationDetails';
import PersonalDetails from './components/personalDetails/PersonalDetails';
import SourceOfFundDetails from './components/sourceOfFundDetails/SourceOfFundDetails';
import {
  OcrCapture,
  useOcr,
  useSaveOcr,
} from 'features/eAppV2/my/hooks/useOcr';
import { useGetActiveCase } from 'hooks/useGetActiveCase';

const PolicyOwnerInfo = memo(() => {
  const { t } = useTranslation(['eApp']);
  const { alertError } = useEAppAlert();
  const { my_policyOwnerPersonalInfo, updateMY_PolicyOwnerInfo } = useEAppStore(
    state => ({
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      updateMY_PolicyOwnerInfo: state.updateMY_PolicyOwnerInfo,
    }),
    shallow,
  );
  const { caseObj } = useGetActiveCase();
  const isMainInsured = useMemo(
    () =>
      caseObj?.parties?.find(p => p.id === my_policyOwnerPersonalInfo.id)
        ?.isMainInsured,
    [caseObj?.parties, my_policyOwnerPersonalInfo.id],
  );
  const resolverPer = useCFFValidationResolver(policyOwnerFormValidationSchema);
  const [activePath, setActivePath] = useState<string | undefined>(undefined);
  const nextSubgroup = useEAppProgressBarStore(state => state.nextSubgroup);

  const channel = useGetCubeChannel();
  const isBanca = channel === CHANNELS.BANCA;
  const quotation = useSelectedQuotation();
  const shouldShowSourceOfFundWealth =
    quotation?.plans[0].showSourceOfFundWealth;

  const ocrCapture = useRef<OcrCapture>();
  const { saveOcr, isLoading: isSavingOcr } = useSaveOcr();

  const {
    control: policyOwnerFormControl,
    setValue: setValuePolicyOwner,
    getValues: getValuePolicyOwner,
    trigger: triggerPolicyOwnerValidation,
    handleSubmit: handlePolicyOwnerInfoFormSubmit,
    watch: watchOwnerInfoFormSubmit,
    reset: resetValuePolicyOwner,
    clearErrors: clearErrorsPolicyOwner,
    formState: { isValid },
    setFocus,
  } = useForm<PolicyOwnerFormSchemaType>({
    mode: 'onBlur',
    defaultValues: my_policyOwnerPersonalInfo,
    resolver: resolverPer,
  });

  useEffect(() => {
    resetValuePolicyOwner(my_policyOwnerPersonalInfo);
  }, [my_policyOwnerPersonalInfo, resetValuePolicyOwner]);

  useEffect(() => {
    setValuePolicyOwner(
      'isBancaSourceOfFundWealthRequired',
      isBanca && shouldShowSourceOfFundWealth,
    );
  }, [channel, isBanca, setValuePolicyOwner, shouldShowSourceOfFundWealth]);

  const { data: optionList } = useGetOptionList<'my'>();
  const { saveParty, isLoading: isSavingParty } = useSaveParty();

  const onContinue = async () => {
    handlePolicyOwnerInfoFormSubmit(async data => {
      if (!optionList) return;
      const id = await saveParty(
        toParty(data, PartyRole.PROPOSER, optionList),
        {
          overridingRoles: false,
          preventCreatingParty: true,
        },
      );
      setValuePolicyOwner('id', id);
      updateMY_PolicyOwnerInfo({ ...data, id });
      await saveOcr(PartyRole.PROPOSER, id, ocrCapture.current);

      GATracking.logCustomEvent('application', {
        action_type: 'eapp_submit_details',
        application_type: 'F2F',
      });
      nextSubgroup(true);
    })().catch(() => alertError(onContinue));
  };

  const focusOnIncompleteField = () => {
    focusOnNextIncompleteField();
  };

  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
    useIncompleteFields({
      watch: watchOwnerInfoFormSubmit,
      schema: policyOwnerFormValidationSchema,
      control: policyOwnerFormControl,
      scrollRef,
      scrollTo: option => {
        scrollRef?.current?.scrollToPosition(0, option.y || 0, option.animated);
      },
    });

  const ownerAddressValues = watchOwnerInfoFormSubmit([
    'correspondenceAddress',
    'correspondenceAddressLine1',
    'correspondenceAddressLine2',
    'correspondenceAddressLine3',
    'correspondencePostCode',
    'correspondenceCity',
    'correspondenceState',
    'correspondenceCountry',
    'residentialAddress',
    'residentialAddressLine1',
    'residentialAddressLine2',
    'residentialAddressLine3',
    'residentialPostCode',
    'residentialCity',
    'residentialState',
    'residentialCountry',
    'businessAddress',
    'businessAddressLine1',
    'businessAddressLine2',
    'businessAddressLine3',
    'businessPostCode',
    'businessCity',
    'businessState',
    'businessCountry',
  ]);

  const ownerAddress = useMemo(
    () => ({
      correspondenceAddress: ownerAddressValues[0],
      correspondenceAddressLine1: ownerAddressValues[1],
      correspondenceAddressLine2: ownerAddressValues[2],
      correspondenceAddressLine3: ownerAddressValues[3],
      correspondencePostCode: ownerAddressValues[4],
      correspondenceCity: ownerAddressValues[5],
      correspondenceState: ownerAddressValues[6],
      correspondenceCountry: ownerAddressValues[7],
      residentialAddress: ownerAddressValues[8],
      residentialAddressLine1: ownerAddressValues[9],
      residentialAddressLine2: ownerAddressValues[10],
      residentialAddressLine3: ownerAddressValues[11],
      residentialPostCode: ownerAddressValues[12],
      residentialCity: ownerAddressValues[13],
      residentialState: ownerAddressValues[14],
      residentialCountry: ownerAddressValues[15],
      businessAddress: ownerAddressValues[16],
      businessAddressLine1: ownerAddressValues[17],
      businessAddressLine2: ownerAddressValues[18],
      businessAddressLine3: ownerAddressValues[19],
      businessPostCode: ownerAddressValues[20],
      businessCity: ownerAddressValues[21],
      businessState: ownerAddressValues[22],
      businessCountry: ownerAddressValues[23],
    }),
    [
      ownerAddressValues[0],
      ownerAddressValues[1],
      ownerAddressValues[2],
      ownerAddressValues[3],
      ownerAddressValues[4],
      ownerAddressValues[5],
      ownerAddressValues[6],
      ownerAddressValues[7],
      ownerAddressValues[8],
      ownerAddressValues[9],
      ownerAddressValues[10],
      ownerAddressValues[11],
      ownerAddressValues[12],
      ownerAddressValues[13],
      ownerAddressValues[14],
      ownerAddressValues[15],
      ownerAddressValues[16],
      ownerAddressValues[17],
      ownerAddressValues[18],
      ownerAddressValues[19],
      ownerAddressValues[20],
      ownerAddressValues[21],
      ownerAddressValues[22],
      ownerAddressValues[23],
    ],
  );

  const ownerOccupationGroup = watchOwnerInfoFormSubmit('occupationGroup');

  const sections = useMemo(
    () => [
      {
        name: 'policyOwnerInfo',
        title: t('eApp:certificate.menu.certificateOwner'),
        subtitle: getValuePolicyOwner('fullName'),
        content: (
          <ScrollViewContainer ref={scrollRef} key={'PolicyOwner'}>
            <PolicyOwnerInfoHeader />
            <PersonalDetails
              control={policyOwnerFormControl}
              getValues={getValuePolicyOwner}
              setValue={setValuePolicyOwner}
              clearErrors={clearErrorsPolicyOwner}
              isForPO={true}
              ocrCapture={ocrCapture}
            />
            <NationalityDetails
              control={policyOwnerFormControl}
              setValue={setValuePolicyOwner}
              clearErrors={clearErrorsPolicyOwner}
            />
            <OccupationDetails
              control={policyOwnerFormControl}
              setValue={setValuePolicyOwner}
              isOwner
              isMainInsured={isMainInsured}
            />
            <ContactDetails
              control={policyOwnerFormControl}
              setValue={setValuePolicyOwner}
              getValues={getValuePolicyOwner}
            />
            <AddressInformation
              isFromSI
              ownerAddress={ownerAddress}
              control={
                policyOwnerFormControl as unknown as Control<AddressInfo>
              }
              setValue={
                setValuePolicyOwner as unknown as UseFormSetValue<AddressInfo>
              }
              getValues={
                getValuePolicyOwner as unknown as UseFormGetValues<AddressInfo>
              }
              trigger={
                triggerPolicyOwnerValidation as unknown as UseFormTrigger<AddressInfo>
              }
              isOwner
              occupationGroup={ownerOccupationGroup}
            />
            {isBanca && shouldShowSourceOfFundWealth && (
              <SourceOfFundDetails control={policyOwnerFormControl} />
            )}
            <BoxSpace />
          </ScrollViewContainer>
        ),
      },
    ],
    [
      isMainInsured,
      clearErrorsPolicyOwner,
      getValuePolicyOwner,
      isBanca,
      ownerAddress,
      ownerOccupationGroup,
      policyOwnerFormControl,
      setValuePolicyOwner,
      shouldShowSourceOfFundWealth,
      t,
      triggerPolicyOwnerValidation,
    ],
  );

  return (
    <>
      <TabletSections
        items={sections}
        activePath={activePath}
        setActivePath={setActivePath}
      />
      <EAppFooterTablet
        progressLock="appDetail-policyOwner"
        onPrimaryPress={onContinue}
        primaryDisabled={!isValid}
        primaryLoading={isSavingParty || isSavingOcr}
        totalIncompleteRequiredFields={totalIncompleteRequiredFields}
        focusOnIncompleteField={focusOnIncompleteField}
      />
    </>
  );
});

export default PolicyOwnerInfo;

const BoxSpace = styled(Row)(({ theme: { space } }) => ({
  height: space[6],
}));

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
  }),
);
