import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import DatePickerCalendar from 'components/DatePickerCalendar';
import Input from 'components/Input';
import { NEW_NRIC } from 'constants/optionList';
import {
  Box,
  Column,
  H7,
  Icon,
  LargeBody,
  Picker,
  Row,
  TextField,
} from 'cube-ui-components';
import { Cheque } from 'cube-ui-components/dist/cjs/icons';
import ReadOnlyField from 'features/eAppV2/common/components/ReadOnlyField';
import SectionWithTitle from 'features/eAppV2/common/components/SectionWithTitle';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import {
  DirectTransferPaymentForm,
  directTransferPaymentSchema,
} from 'features/eAppV2/my/validations/directTransferPaymentValidation';
import { useAlert } from 'hooks/useAlert';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { StyleSheet } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { CHANNELS } from 'types/channel';
import { DocumentType } from 'types/document';
import { formatCurrencyWithMask } from 'utils';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import GATracking from 'utils/helper/gaTracking';
import { formatNewNricNumber } from 'utils/helper/idNumberUtils';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { shallow } from 'zustand/shallow';
import EAppFooterTablet from '../../../../common/components/footer/EAppFooter.tablet';
import PaymentDocumentUpload, {
  DocumentTypeItem,
} from '../PaymentDocumentUpload';
import { PaymentMethod } from '../PaymentMethods';
import CancelPaymentConfirmationDialog from '../dialog/CancelPaymentConfirmationDialog';

const AGENCY_ACCOUNT_NUMBER = '************';
const BANCA_ACCOUNT_NUMBER = '************';
const TRANSFER_APPROVAL_REQUIRED_AMOUNT = 30000;

interface DirectPaymentProps {
  onBackPress: () => Promise<void>;
}

export default function DirectTransferPayment(props: DirectPaymentProps) {
  const { space, colors, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { alertError } = useAlert();
  const { data: optionList } = useGetOptionList<'my'>();
  const isEntity = useCheckEntity();
  const {
    my_directPaymentForm,
    updateMY_DirectPaymentSubmitted,
    updateMY_DirectPayment,
    my_hasPayor,
    my_payorDetails,
    my_policyOwnerPersonalInfo,
    my_companyInfo,
  } = useEAppStore(
    state => ({
      activeQuotationId: state.activeQuotationId,
      my_hasPayor: state.my_hasPayor,
      my_payorDetails: state.my_payorDetails,
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_directPaymentForm: state.my_directTransferPaymentForm,
      updateMY_DirectPayment: state.updateMY_DirectTransferPayment,
      updateMY_DirectPaymentSubmitted:
        state.updateMY_DirectTransferPaymentSubmitted,
      my_companyInfo: state.my_companyInfo,
    }),
    shallow,
  );

  const resolverDirectTransferPayment = useYupResolver(
    directTransferPaymentSchema,
  );

  const {
    control: control,
    handleSubmit: handleFormSubmit,
    formState: { isValid: isDirectValid },
    reset,
    watch,
    setFocus,
  } = useForm<DirectTransferPaymentForm>({
    mode: 'onBlur',
    defaultValues: my_directPaymentForm,
    resolver: resolverDirectTransferPayment,
  });

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { totalIncompleteRequiredFields, focusOnNextIncompleteField } =
    useIncompleteFields({
      control,
      watch: watch,
      schema: directTransferPaymentSchema,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition(0, option.y || 0, option.animated),
    });

  const focusOnIncompleteField = () => {
    focusOnNextIncompleteField();
  };

  const fullNamePayor = useWatch({
    name: 'fullNamePayor',
    control,
  });

  const applicationNumber = useWatch({
    name: 'applicationNumber',
    control,
  });

  const idNumber = useWatch({
    name: 'idNumber',
    control,
  });

  const initialContributionAmount = useWatch({
    name: 'initialContributionAmount',
    control,
  });

  useEffect(() => {
    if (my_hasPayor) {
      reset({
        ...my_directPaymentForm,
        fullNamePayor: my_payorDetails.fullName,
        idNumber: my_payorDetails.identificationNumber,
      });
    } else if (isEntity) {
      reset({
        ...my_directPaymentForm,
        fullNamePayor: my_companyInfo.companyName,
        idNumber: my_companyInfo.regNumber,
      });
    } else {
      reset({
        ...my_directPaymentForm,
        fullNamePayor: my_policyOwnerPersonalInfo.fullName,
        idNumber: my_policyOwnerPersonalInfo.identificationNumber,
      });
    }
  }, [
    isEntity,
    my_companyInfo.companyName,
    my_companyInfo.regNumber,
    my_directPaymentForm,
    my_hasPayor,
    my_payorDetails.fullName,
    my_payorDetails.identificationNumber,
    my_policyOwnerPersonalInfo.fullName,
    my_policyOwnerPersonalInfo.identificationNumber,
    reset,
  ]);

  const [cancelConfirmationVisible, setCancelConfirmationVisible] =
    useState(false);
  const [isGoingBack, setIsGoingBack] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isDocumentValid, setIsDocumentValid] = useState<boolean>(false);
  const [documents, setDocuments] = useState<DocumentTypeItem[]>();
  const isSubmitDisabled = !isDirectValid || !isDocumentValid;

  const channel = useGetCubeChannel();
  const isAgency = channel === CHANNELS.AGENCY;

  const quotation = useSelectedQuotation();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();

  const idType = my_hasPayor
    ? my_payorDetails.primaryIdType
    : my_policyOwnerPersonalInfo.primaryIdType;

  const initDocuments = useMemo(() => {
    const isTransferApprovalRequired =
      Number(initialContributionAmount) <= TRANSFER_APPROVAL_REQUIRED_AMOUNT;
    const documents: DocumentTypeItem[] = [
      {
        type: DocumentType.DirectTransfer,
        title: t('eApp:payment.direct.uploadImage.slip'),
        files: [],
        required: true,
      },
      {
        type: DocumentType.DirectDebitAuthorization,
        title: t('eApp:payment.direct.uploadImage.debitAuthorization'),
        files: [],
        required: true,
      },
      {
        type: DocumentType.DirectTransferApproval,
        title: isTransferApprovalRequired
          ? t('eApp:payment.direct.uploadImage.directTransferApproval')
          : t('eApp:documentUpload.optional', {
              document: t(
                'eApp:payment.direct.uploadImage.directTransferApproval',
              ),
            }),
        files: [],
        required: isTransferApprovalRequired,
      },
    ].map(section => {
      const files =
        my_directPaymentForm.documents?.find(doc => doc.type === section.type)
          ?.files ?? [];
      return {
        ...section,
        files: files.map(f => ({ ...f, status: 'uploaded' })),
      };
    });
    return documents;
  }, [initialContributionAmount, my_directPaymentForm.documents, t]);

  const onSubmit = async () => {
    setIsSubmitting(true);
    try {
      GATracking.logCustomEvent('application', {
        action_type: 'eapp_submit_payment',
        application_type: 'F2F',
      });
      await handleFormSubmit(
        async data => {
          if (!caseObj || !caseId) return;
          await saveApplication({
            caseId,
            data: {
              ...caseObj.application,
              paymentMethod: PaymentMethod.DIRECT_TRANSFER,
              initialPayment: null,
              renewalPayment: null,
              chequeInfo: {
                number: data.recipientReference,
                date: data.transferDate ? data.transferDate.toISOString() : '',
                bankName: data.transferType,
                amount: data.transactionAmount,
                currency: quotation?.basicInfo.currency || '',
                issueBank: '',
                forMultipleApplication: false,
              },
            },
          });
          updateMY_DirectPayment({
            ...data,
            documents: documents,
          });
          updateMY_DirectPaymentSubmitted(true);
        },
        errors => {
          console.log('direct payment form errors: ', errors);
        },
      )();
    } catch {
      alertError(t('eApp:failedToSaveData'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const identificationNumber = useMemo(() => {
    return idType === NEW_NRIC ? formatNewNricNumber(idNumber) : idNumber;
  }, [idNumber, idType]);

  const transferTypes = useMemo(
    () =>
      optionList?.DIRECT_TRANSFER_TYPE.options.map(i => ({
        value: i.value,
        text: i.label,
      })),
    [optionList?.DIRECT_TRANSFER_TYPE.options],
  );

  return (
    <Column flex={1} backgroundColor={colors.surface}>
      <ScrollViewContainer ref={scrollRef} key={'direct'}>
        <SectionWithTitle
          title={t('eApp:payment.direct.title')}
          icon={<Cheque fill={colors.palette.white} />}>
          <Column
            backgroundColor={colors.background}
            padding={space[6]}
            borderRadius={borderRadius.large}>
            <Row
              padding={space[4]}
              gap={space[1]}
              backgroundColor={colors.primaryVariant3}>
              <Icon.InfoCircle fill={colors.primary} />
              <Column>
                <LargeBody color={colors.palette.fwdGreyDarkest}>
                  {t('eApp:payment.cheque.note')}
                </LargeBody>

                <Box height={sizes[2]} />
                <H7 fontWeight="bold">
                  {t('eApp:payment.cheque.note.bank', {
                    bankName: 'HSBC Amanah Malaysia Berhad',
                  })}
                </H7>
                <H7 fontWeight="bold">
                  {t('eApp:payment.cheque.note.accountNumber', {
                    accountNumber: isAgency
                      ? AGENCY_ACCOUNT_NUMBER
                      : BANCA_ACCOUNT_NUMBER,
                  })}
                </H7>
              </Column>
            </Row>
            <Box h={space[6]} />
            <Row gap={space[6]}>
              <Input
                control={control}
                as={TextField}
                name="recipientReference"
                label={t('eApp:payment.direct.recipient_reference')}
                style={{ flex: 1 }}
                returnKeyType="done"
                shouldHighlightOnUntouched={value => !value}
              />
              <Input
                control={control}
                as={DatePickerCalendar}
                name="transferDate"
                label={t('eApp:payment.direct.transfer_date')}
                hint={t('eApp:dateFormat')}
                style={{ flex: 1 }}
                formatDate={val => (val ? dateFormatUtil(val) : '')}
                shouldHighlightOnUntouched={value => !value}
              />
              <Box flex={1}>
                <Input
                  control={control}
                  as={Picker}
                  name="transferType"
                  type="chip"
                  items={transferTypes}
                  label={t('eApp:payment.direct.direct_transfer_type')}
                  style={{ marginLeft: space[4] }}
                  itemStyle={styles.pickerItem}
                  labelStyle={styles.pickerLabel}
                  shouldHighlightOnUntouched={value => !value}
                />
              </Box>
            </Row>
            <Box h={space[6]} />
            <Row gap={space[6]}>
              <ReadOnlyField
                value={fullNamePayor}
                label={t('eApp:payment.cheque.fullNamePayor')}
              />
              <ReadOnlyField
                value={applicationNumber}
                label={t('eApp:review.applicationNumber')}
              />
              <ReadOnlyField
                value={identificationNumber}
                label={t('eApp:payment.cheque.identificationNumber')}
              />
            </Row>
            <Box h={space[6]} />
            <Row gap={space[6]}>
              <ReadOnlyField
                withRowHasInput
                value={formatCurrencyWithMask(
                  Number(initialContributionAmount),
                  2,
                )}
                label={t('eApp:payment.direct.initialContributionAmount')}
              />
              <ReadOnlyField
                withRowHasInput
                value={formatCurrencyWithMask(
                  Number(initialContributionAmount),
                  2,
                )}
                label={t('eApp:payment.direct.transaction_amount')}
              />
              <Box flex={1} />
            </Row>
          </Column>
        </SectionWithTitle>
        <PaymentDocumentUpload
          title={t('eApp:payment.direct.uploadImage')}
          initDocuments={initDocuments}
          onValidChanged={isValid => setIsDocumentValid(isValid)}
          onDocumentUploaded={documents => setDocuments(documents)}
        />
        <Box height={sizes[12]} />
      </ScrollViewContainer>
      <EAppFooterTablet
        progressLock="payment-"
        primaryLoading={isSubmitting}
        secondaryLoading={isGoingBack}
        secondaryLabel={t('eApp:back')}
        onPrimaryPress={onSubmit}
        onSecondaryPress={() => {
          setCancelConfirmationVisible(true);
        }}
        primaryLabel={t('eApp:next')}
        primaryDisabled={isSubmitDisabled}
        totalIncompleteRequiredFields={
          totalIncompleteRequiredFields +
          (initDocuments.filter(doc => doc.required).length -
            (documents
              ?.filter(doc => doc.required)
              .reduce((acc, doc) => acc + (doc.files.length > 0 ? 1 : 0), 0) ??
              0))
        }
        focusOnIncompleteField={focusOnIncompleteField}
      />
      <CancelPaymentConfirmationDialog
        title={t('eApp:payment.direct.cancel.title')}
        desc={t('eApp:payment.direct.cancel.description')}
        visible={cancelConfirmationVisible}
        isLoading={isSavingApplication}
        onDismiss={() => setCancelConfirmationVisible(false)}
        onConfirm={() => {
          setIsGoingBack(true);
          props.onBackPress().finally(() => setIsGoingBack(false));
        }}
      />
    </Column>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space } }) => ({
    flex: 1,
    paddingHorizontal: space[8],
    paddingTop: space[6],
  }),
);

const styles = StyleSheet.create({
  pickerItem: {
    marginRight: 4,
  },
  pickerLabel: {
    fontSize: 16,
    lineHeight: 20,
  },
});
