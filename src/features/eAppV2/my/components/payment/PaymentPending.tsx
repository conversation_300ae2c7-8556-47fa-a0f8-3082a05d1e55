import { useTheme } from '@emotion/react';
import { Body, Box, Button, H6 } from 'cube-ui-components';
import React from 'react';
import { useTranslation } from 'react-i18next';
import PaymentRequestingIcon from './icons/PaymentRequestingIcon';
import { generateShareEMandateTemplate } from 'features/eAppV2/ib/constants/emailTemplates/paymentEmailTemplate';
import PaymentShare from 'features/eAppV2/ib/components/payment/share/PaymentShare';

const PaymentPending = ({
  paymentUrl,
  onCheckRegStatus,
  onRedo,
}: {
  paymentUrl: string | undefined;
  onCheckRegStatus: () => void;
  onRedo: () => void;
}) => {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  return (
    <Box
      flexDirection={'row'}
      flex={1}
      alignItems="center"
      justifyContent="space-around">
      <Box justifyContent="flex-end" alignItems="flex-end" flex={1}>
        <Box alignItems="center">
          <PaymentRequestingIcon />
          <Box h={sizes[4]} />
          <H6 fontWeight="bold">{t('eApp:eMandate.inProgress')}</H6>
          <Box h={sizes[6]} />
          <Button
            style={{ maxWidth: 230 }}
            variant="primary"
            text={t('eApp:eMandate.checkStatus')}
            onPress={onCheckRegStatus}
          />
          <Box h={sizes[4]} />
          <Button
            style={{ maxWidth: 230 }}
            variant="secondary"
            text={t('eApp:eMandate.registration')}
            onPress={onRedo}
          />
        </Box>
      </Box>
      <Box
        alignItems="center"
        alignSelf="stretch"
        mx={space[17]}
        my={52}
        gap={space[2]}>
        <Box flex={1} w={1} backgroundColor={colors.palette.fwdGreyDark} />
        <Body color={colors.palette.fwdGreyDarker}>
          {t('eApp:payment.resendPaymentLink.or')}
        </Body>
        <Box flex={1} w={1} backgroundColor={colors.palette.fwdGreyDark} />
      </Box>
      <PaymentShare
        paymentLink={paymentUrl || ''}
        emailTemplateGenerator={generateShareEMandateTemplate}
        title={t('eApp:payment.resendPaymentLinkEmandate.title')}
        message={t('eApp:payment.resendPaymentLinkEmandate.scanToRegister.tablet')}
      />
    </Box>
  );
};
export default PaymentPending;
