import { useAddDocumentToParty } from 'hooks/useParty';
import { getFileType } from 'features/eAppV2/common/utils/documentUtils';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useBoundStore from 'hooks/useBoundStore';
import useLatest from 'hooks/useLatest';
import { useUploadDocument } from 'hooks/useUploadDocument';
import {
  Fragment,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { DocumentCustomerType, DocumentType } from 'types/document';
import { saveFileToDocumentFolder } from 'utils/helper/fileUtils';
import { shallow } from 'zustand/shallow';
import Uploader from '../documentUpload/Uploader';
import { useAlert } from 'hooks/useAlert';
import { useTranslation } from 'react-i18next';
import ImagePicker from 'components/ImagePicker';
import { ImagePickerFile } from 'components/ImagePicker/utils';

export type File = Omit<ImagePickerFile, 'isAttachment'> & {
  status: 'uploading' | 'uploaded';
};
export interface DocumentTypeItem {
  type: string;
  title: string;
  files: File[];
  required?: boolean;
  hidden?: boolean;
}
interface PaymentDocumentUploadProps {
  title: string;
  initDocuments: DocumentTypeItem[];
  onValidChanged: (isValid: boolean) => void;
  onDocumentUploaded: (documents: DocumentTypeItem[]) => void;
}

export const PaymentDocumentUpload = memo(function PaymentDocumentUpload({
  title,
  initDocuments,
  onValidChanged,
  onDocumentUploaded,
}: PaymentDocumentUploadProps) {
  const { t } = useTranslation(['eApp']);
  const [visibleDocumentUpload, setVisibleDocumentPicker] = useState(false);
  const [selectedType, setSelectedType] = useState<string>('');
  const { applicationNum } = useEAppStore(
    state => ({
      applicationNum: state.applicationNum,
    }),
    shallow,
  );
  const { alertError } = useAlert();

  const getDocuments = (initial = false) => {
    const documents = initDocuments;

    //sync uploaded files
    const uploadedFileMap: Record<string, File[]> = {};
    if (!initial) {
      latestDocuments.current?.forEach(item => {
        uploadedFileMap[item.type] = item.files;
      });

      documents.forEach(item => {
        item.files = (uploadedFileMap[item.type] || []).map(i => ({
          ...i,
          status: 'uploaded',
        }));
      });
    }
    return documents;
  };

  const latestUpdateDocuments = useLatest(() => setDocuments(getDocuments()));

  const [documents, setDocuments] = useState<DocumentTypeItem[]>(() =>
    getDocuments(true),
  );
  const latestDocuments = useLatest(documents);

  useEffect(() => {
    latestUpdateDocuments.current();
  }, [latestUpdateDocuments]);

  const onStartUploadAsset = (type: string) => {
    setSelectedType(type);
    setVisibleDocumentPicker(true);
  };

  const { mutateAsync: uploadDocument } = useUploadDocument();
  const caseId = useBoundStore(state => state.case.caseId);
  const { mutateAsync: addDocument } = useAddDocumentToParty();
  const onUploadAsset = (file: File) => {
    const newPathDocuments = [...documents].map(e => {
      if (e.type === selectedType) {
        const newFile = [...e.files];
        newFile.push(file);
        return {
          ...e,
          files: newFile,
        };
      } else {
        return e;
      }
    });
    setDocuments(newPathDocuments);
  };

  const onRemoveAsset = useCallback(
    (asset: File, type: string) => {
      let newPathDocuments = documents;

      newPathDocuments = (newPathDocuments ? [...newPathDocuments] : []).map(
        e => {
          if (e.type === type) {
            let newFile = [...e.files];
            newFile = newFile.filter(e => e.uri !== asset.uri);
            return {
              ...e,
              files: newFile,
            };
          } else {
            return e;
          }
        },
      );

      setDocuments(newPathDocuments);
    },
    [documents],
  );

  const isValid = useMemo(
    () =>
      documents &&
      documents
        ?.filter(doc => doc.required)
        .every(
          doc =>
            doc.files.length > 0 &&
            doc.files.every(f => f.status === 'uploaded'),
        ),
    [documents],
  );

  useEffect(() => {
    onDocumentUploaded(documents);
  }, [documents, onDocumentUploaded]);

  useEffect(() => {
    onValidChanged(isValid);
  }, [isValid, onValidChanged]);

  return (
    <Fragment>
      <Uploader
        title={title}
        items={documents}
        onStartUploadAsset={(type: string) => onStartUploadAsset(type)}
        onRemoveAsset={(asset: File, type: string) =>
          onRemoveAsset(asset, type)
        }
      />
      <ImagePicker
        visible={visibleDocumentUpload}
        attachmentEnabled
        onDismiss={() => setVisibleDocumentPicker(false)}
        config={{
          compression: 0.5,
          maxHeight: 800,
          maxWidth: 800,
        }}
        onDone={async ({ file: asset }) => {
          if (!asset) return;
          onUploadAsset({ ...asset, status: 'uploading' });
          const customerType =
            selectedType === DocumentType.DirectTransfer
              ? DocumentCustomerType.PAY
              : DocumentCustomerType.PO;
          const customerSeq = '1';

          const { uri, base64 } = asset;
          try {
            if (!caseId) throw new Error('missing case id');
            const res = await uploadDocument({
              body: {
                custType: customerType,
                custSeq: customerSeq,
                docType: selectedType as DocumentType,
                fileContent: base64,
                applicationNum: applicationNum,
                fileType: getFileType(uri),
              },
            });
            await saveFileToDocumentFolder(res.fileName, base64);
            await addDocument({
              caseId,
              data: {
                partyId: '',
                docType: selectedType as DocumentType,
                fileName: res.fileName,
                filePath: res.filePath,
              },
            });

            onUploadAsset({
              ...asset,
              name: res.fileName,
              status: 'uploaded',
            });
          } catch (e) {
            alertError(t('eApp:documentUpload.failedToUpload'));
            onRemoveAsset({ ...asset, status: 'uploading' }, selectedType);
          }
        }}
      />
    </Fragment>
  );
});
export default PaymentDocumentUpload;
