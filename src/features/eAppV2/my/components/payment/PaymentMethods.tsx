import { useTheme } from '@emotion/react';
import { Box, H6, H7, Label, PictogramIcon, Row } from 'cube-ui-components';
import { getDaysToBirthday } from 'features/eAppV2/my/utils/getDaysToBirthday';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import React, { ReactElement, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import PaymentMethodItem from './PaymentMethodItem';
import { PaymentMode } from 'types/proposal';
import { CHANNELS } from 'types/channel';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import usePaymentInfo from 'features/eAppV2/ib/components/payment/info/usePaymentInfo';
import { getPremByPaymentMode } from '../../utils/planUtils';

export enum PaymentMethod {
  CREDIT = 'CC',
  FPX = 'FPX',
  CHEQUE = 'CHQ',
  BIRO = 'BIRO',
  DIRECT_TRANSFER = 'DBT',
}

export enum SubPaymentMethod {
  IN_APP = 'INAPP',
  VIA_LINK = 'VIALINK',
}
type PaymentProps = {
  onPressTC: () => void;
  paymentMethod: PaymentMethod | null;
  subPaymentMethod: SubPaymentMethod | null;
  setPaymentMethod: (method: PaymentMethod | null) => void;
  setSubPaymentMethod?: (subMethod: SubPaymentMethod | null) => void;
};

export type PaymentMethodsProps = {
  value: string;
  label: string;
  desc?: string;
  icon?: ReactElement;
  hidden?: boolean;
  disabled?: boolean;
};

const CHEQUE_MIN_AMOUNT = 10000;
const DIRECT_TRANSFER_MIN_AMOUNT = 50000;

const PaymentMethods = ({
  onPressTC,
  paymentMethod,
  subPaymentMethod,
  setPaymentMethod,
  setSubPaymentMethod,
}: PaymentProps) => {
  const { t } = useTranslation(['eApp']);
  const { space, sizes, colors, borderRadius } = useTheme();
  const {
    my_policyOwnerPersonalInfo,
    my_authorizedSignatoryInfo,
    my_hasAdvanceContribution,
  } = useEAppStore(state => {
    return {
      my_policyOwnerPersonalInfo: state.my_policyOwnerPersonalInfo,
      my_authorizedSignatoryInfo: state.my_authorizedSignatoryInfo,
      my_hasAdvanceContribution: state.my_hasAdvanceContribution,
    };
  }, shallow);

  const channel = useGetCubeChannel();
  const quotation = useSelectedQuotation();
  const isEntity = useCheckEntity();

  const advanceContributionDuration = useEAppStore(
    state => state.my_advanceContributionDuration,
  );
  const { initialPremium } = usePaymentInfo({
    advancePayment: {
      duration: advanceContributionDuration,
      amount:
        advanceContributionDuration * (getPremByPaymentMode(quotation) ?? 0),
    },
  });

  const paymentMethods: PaymentMethodsProps[] = useMemo(() => {
    const isAgency = channel === CHANNELS.AGENCY;
    const isBanca = channel === CHANNELS.BANCA;
    return [
      {
        value: PaymentMethod.CREDIT,
        label: t('eApp:payment.method.credit'),
        desc: t('eApp:payment.method.credit.desc'),
        icon: <PictogramIcon.CreditCard size={sizes[10]} />,
        hidden: false,
      },
      {
        value: PaymentMethod.FPX,
        label: t('eApp:payment.method.online'),
        desc: t('eApp:payment.method.online.desc'),
        icon: <PictogramIcon.Bank2 size={sizes[10]} />,
        hidden: false,
      },
      {
        value: PaymentMethod.CHEQUE,
        label: t('eApp:payment.method.cheque'),
        desc: t('eApp:payment.method.cheque.desc'),
        icon: <PictogramIcon.Capital size={sizes[10]} />,
        hidden: isBanca || initialPremium < CHEQUE_MIN_AMOUNT,
      },
      {
        value: PaymentMethod.BIRO,
        label: t('eApp:payment.method.biro'),
        desc: t('eApp:payment.method.biro.desc'),
        icon: <PictogramIcon.Cash size={sizes[10]} />,
        hidden:
          isEntity ||
          my_hasAdvanceContribution ||
          !(
            isAgency &&
            quotation?.basicInfo.paymentMode === PaymentMode.MONTHLY &&
            my_policyOwnerPersonalInfo?.dob &&
            getDaysToBirthday(my_policyOwnerPersonalInfo?.dob) >= 90
          ),
      },
      {
        value: PaymentMethod.DIRECT_TRANSFER,
        label: t('eApp:payment.method.direct'),
        desc: isAgency
          ? t('eApp:payment.method.direct.desc.agency')
          : t('eApp:payment.method.direct.desc.banca'),
        icon: <PictogramIcon.Transaction size={sizes[10]} />,
        hidden: isAgency
          ? initialPremium <= DIRECT_TRANSFER_MIN_AMOUNT
          : false,
      },
    ].filter(o => !o.hidden);
  }, [
    channel,
    initialPremium,
    isEntity,
    my_hasAdvanceContribution,
    my_policyOwnerPersonalInfo?.dob,
    quotation?.basicInfo.paymentMode,
    sizes,
    t,
  ]);

  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);

  return (
    <Box
      bgColor={colors.background}
      borderRadius={borderRadius.large}
      p={space[6]}>
      <H6 fontWeight="bold">{t('eApp:payment.method.select')}</H6>
      <Row marginTop={space[8]} justifyContent="space-between">
        <Row alignItems="center" flex={1}>
          <H7 fontWeight="bold">{t('eApp:payment.method.no')}</H7>
          <Box w={sizes[2]} />
          <H7 fontWeight="bold" color={colors.primary}>
            {caseObj?.application?.applicationNum}
          </H7>
        </Row>
        <Row alignItems="flex-start" flex={1} gap={space[2]}>
          <H7 fontWeight="bold">{t('eApp:payment.method.cert.no')}</H7>
          <H7 fontWeight="bold" color={colors.primary}>
            {caseObj?.application?.policyNum}
          </H7>
        </Row>
      </Row>
      <Box marginTop={space[4]}>
        {paymentMethods.map(methodItem => {
          const shouldRenderSubPayment =
            paymentMethod === methodItem.value &&
            (paymentMethod === PaymentMethod.CREDIT ||
              paymentMethod === PaymentMethod.FPX);

          return (
            <Box key={methodItem.value} marginBottom={space[4]}>
              <PaymentMethodItem
                {...methodItem}
                setPaymentMethod={setPaymentMethod}
                selectedPaymentMethod={paymentMethod}
                selectedSubPaymentMethod={null}
                setSubPaymentMethod={setSubPaymentMethod}
              />
              {shouldRenderSubPayment && (
                <Row marginLeft={space[10]} gap={space[4]} marginTop={space[3]}>
                  <Box flex={1}>
                    <PaymentMethodItem
                      setPaymentMethod={setPaymentMethod}
                      selectedPaymentMethod={paymentMethod}
                      label={t('eApp:payment.submethod.inapp')}
                      value={SubPaymentMethod.IN_APP}
                      isSubPayment
                      setSubPaymentMethod={setSubPaymentMethod}
                      selectedSubPaymentMethod={subPaymentMethod}
                      disabled={caseObj?.isRemoteSelling}
                    />
                  </Box>
                  <Box flex={1}>
                    <PaymentMethodItem
                      setPaymentMethod={setPaymentMethod}
                      selectedPaymentMethod={paymentMethod}
                      label={t('eApp:payment.submethod.vialink')}
                      value={SubPaymentMethod.VIA_LINK}
                      isSubPayment
                      setSubPaymentMethod={setSubPaymentMethod}
                      selectedSubPaymentMethod={subPaymentMethod}
                    />
                  </Box>
                </Row>
              )}
            </Box>
          );
        })}
      </Box>
      <Row alignItems="center">
        <Label
          onPress={onPressTC}
          fontWeight="medium"
          color={colors.primary}
          style={{ textDecorationLine: 'underline' }}>
          {t('eApp:payment.tc')}
        </Label>
      </Row>
    </Box>
  );
};

export default PaymentMethods;
