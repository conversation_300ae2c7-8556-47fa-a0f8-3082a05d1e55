import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import CubeWebView from 'components/CubeWebView';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { Icon, Row, Typography } from 'cube-ui-components';
import CFFModal from 'features/customerFactFind/components/modals/CFFModal';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { WebViewNavigation } from 'react-native-webview';

const InAppPaymentModal = ({
  paymentUrl,
  visible,
  onDismiss,
}: {
  onDismiss: () => void;
  visible: boolean;
  paymentUrl?: string | null;
}) => {
  const { t } = useTranslation(['eApp']);
  const { colors, space, sizes } = useTheme();

  const onHandleNavigationChange = async (e: WebViewNavigation) => {
    if (e.canGoBack) {
      // onDismiss();
    }
  };
  const handleClosePayment = async () => {
    onDismiss();
  };

  return (
    <CFFModal
      visible={visible}
      backdropColor={colors.onPrimary}
      contentContainerStyle={{ flex: 1, alignSelf: 'stretch' }}>
      <Container>
        <Row
          alignItems="center"
          padding={space[4]}
          gap={space[5]}
          borderBottomWidth={1}
          borderColor={colors.palette.fwdGrey[100]}>
          <TouchableOpacity
            hitSlop={ICON_HIT_SLOP}
            onPress={handleClosePayment}>
            <Icon.Close size={sizes[6]} fill={colors.onBackground} />
          </TouchableOpacity>
          <Typography.H6
            numberOfLines={1}
            fontWeight="bold"
            color={colors.onBackground}>
            {t('eApp:app')}
          </Typography.H6>
        </Row>
        <WebViewContainer
          source={{
            uri: paymentUrl || '',
          }}
          onNavigationStateChange={onHandleNavigationChange}
        />
      </Container>
    </CFFModal>
  );
};

const Container = styled.View(({ theme: { colors } }) => {
  const insets = useSafeAreaInsets();
  return {
    flex: 1,
    alignSelf: 'stretch',
    backgroundColor: colors.onPrimary,
    marginTop: insets.top,
  };
});

const WebViewContainer = styled(CubeWebView)(() => ({
  flex: 1,
}));

export default InAppPaymentModal;
