import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import Svg, { Path } from 'react-native-svg';
import UploaderItem from './UploaderItem';
import { DocumentTypeItem, File } from '../payment/PaymentDocumentUpload';

export const Uploader = memo(function Uploader({
  title,
  items,
  onStartUploadAsset,
  onRemoveAsset,
}: {
  title: string;
  items: DocumentTypeItem[] | undefined;
  onStartUploadAsset: (type: string) => void;
  onRemoveAsset: (asset: File, type: string) => void;
}) {
  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);
  return (
    <Content>
      <Row alignItems="center">
        <DocumentSubmit />
        <Box w={space[2]} />
        <Typography.H6 fontWeight="bold" color="#333333">
          {title}
        </Typography.H6>
      </Row>
      <Box height={space[3]} />
      <Typography.Label color="#636566">
        {t('eApp:documentUpload.acceptedFormats')}
      </Typography.Label>
      <Box height={space[3]} />
      {items?.map((item, index) => (
        <UploaderItem
          title={item.title}
          files={item.files}
          type={item.type}
          key={index}
          index={index}
          onStartUploadAsset={onStartUploadAsset}
          onRemoveAsset={asset => onRemoveAsset(asset, item.type)}
        />
      ))}
    </Content>
  );
});

export default Uploader;

const Content = styled(Column)(
  ({ theme: { space, colors, borderRadius } }) => ({
    backgroundColor: colors.background,
    padding: space[6],
    paddingBottom: space[2],
    borderRadius: borderRadius.large,
  }),
);

const DocumentSubmit = memo(() => {
  const { sizes } = useTheme();
  return (
    <Svg width={sizes[10]} height={sizes[10]} fill="none">
      <Path
        fill="#E87722"
        d="M31.594 33.748H15.163a1.852 1.852 0 0 1-1.846-1.846V8.097c0-1.015.832-1.846 1.846-1.846h16.431c1.015 0 1.846.831 1.846 1.846v23.805a1.849 1.849 0 0 1-1.846 1.846Z"
      />
      <Path
        fill="#fff"
        d="M29.434 25.417H17.33c-.235 0-.423.234-.423.52 0 .289.188.52.423.52h12.105c.235 0 .423-.234.423-.52 0-.286-.191-.52-.423-.52ZM29.434 21.63H17.33c-.235 0-.423.235-.423.52 0 .29.188.52.423.52h12.105c.235 0 .423-.233.423-.52 0-.285-.191-.52-.423-.52ZM29.434 17.846H17.33c-.235 0-.423.234-.423.52 0 .288.188.52.423.52h12.105c.235 0 .423-.235.423-.52 0-.286-.191-.52-.423-.52ZM29.434 14.063H17.33c-.235 0-.423.234-.423.52 0 .288.188.52.423.52h12.105c.235 0 .423-.235.423-.52 0-.29-.191-.52-.423-.52Z"
      />
      <Path
        fill="#183028"
        d="M29.434 10.276H17.33c-.235 0-.423.235-.423.52 0 .289.188.52.423.52h12.105c.235 0 .423-.234.423-.52 0-.288-.191-.52-.423-.52Z"
      />
      <Path
        fill="#fff"
        d="M22.62 29.202h-5.197a.52.52 0 0 0 0 1.04h5.197a.52.52 0 0 0 0-1.04Z"
      />
      <Path
        fill="#F3BB90"
        fillRule="evenodd"
        d="M16.749 16.46H7.726c-.643 0-1.166.523-1.166 1.165v5.838c0 .642.523 1.165 1.166 1.165h9.023v2.112s-.009.683.26.785c.268.106.848-.425.848-.425l6.18-5.537s.652-.478.652-1.055-.812-1.128-.812-1.128l-6.02-5.395s-.566-.645-.928-.502c-.229.088-.18.725-.18.725v2.252Z"
        clipRule="evenodd"
      />
    </Svg>
  );
});
