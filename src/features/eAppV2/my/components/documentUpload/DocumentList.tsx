import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import { Fragment, memo } from 'react';
import { ScrollView } from 'react-native';
import DocumentItem from './DocumentItem';
import { File } from '../payment/PaymentDocumentUpload';

export const DocumentList = memo(function DocumentList({
  files,
  onRemoveAsset,
}: {
  files: File[];
  onRemoveAsset: (asset: File) => void;
}) {
  const { space } = useTheme();
  if (files.length === 0) {
    return null;
  }
  return (
    <Fragment>
      <Box height={space[1]} />
      <ScrollView horizontal>
        <Row style={{ gap: space[6] }}>
          {files.map((file, index) => (
            <DocumentItem
              key={index}
              file={file}
              index={index}
              onRemoveAsset={onRemoveAsset}
            />
          ))}
        </Row>
      </ScrollView>
    </Fragment>
  );
});

export default DocumentList;
