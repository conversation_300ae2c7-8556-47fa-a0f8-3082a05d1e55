import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Column, Icon, Row, Typography } from 'cube-ui-components';
import { memo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import DocumentList from './DocumentList';
import { File } from '../payment/PaymentDocumentUpload';

export const UploaderItem = memo(function UploaderItem({
  index,
  files,
  title,
  type,
  onStartUploadAsset,
  onRemoveAsset,
}: {
  index: number;
  title: string;
  type: string;
  files: File[];
  onStartUploadAsset: (type: string) => void;
  onRemoveAsset: (asset: File) => void;
}) {
  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);

  const onPress = useCallback(() => {
    onStartUploadAsset(type);
  }, [onStartUploadAsset]);

  return (
    <Container index={index}>
      <Row alignItems="center">
        <Typography.H7 fontWeight="medium" style={{ flex: 1 }}>
          {title}
        </Typography.H7>
        <Box width={space[3]} />
        <Button
          size="small"
          icon={Icon.Upload}
          text={t('eApp:documentUpload.upload')}
          variant="secondary"
          onPress={onPress}
        />
      </Row>
      <DocumentList files={files} onRemoveAsset={onRemoveAsset} />
    </Container>
  );
});
export default UploaderItem;

const Container = styled(Column)<{ index: number }>(
  ({ theme: { space, colors }, index }) => ({
    paddingVertical: space[4],
    borderTopWidth: index > 0 ? 1 : 0,
    borderTopColor: colors.palette.fwdGrey[100],
  }),
);
