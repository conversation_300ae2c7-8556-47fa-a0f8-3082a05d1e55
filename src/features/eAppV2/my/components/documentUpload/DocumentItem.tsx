import { useTheme } from '@emotion/react';
import {
  Box,
  Button,
  H6,
  H7,
  Icon,
  LoadingIndicator,
  PictogramIcon,
  Row,
  Toast,
  Typography
} from 'cube-ui-components';
import { memo, useCallback, useEffect, useState } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import styled from '@emotion/native';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import { File } from '../payment/PaymentDocumentUpload';
import { DOCUMENT_DIR, getDocumentUri } from 'utils/helper/fileUtils';
import { useDeleteDocument } from 'hooks/useParty';
import useBoundStore from 'hooks/useBoundStore';
import { useTranslation } from 'react-i18next';
import DialogPhone from 'components/Dialog.phone';
import useToggle from 'hooks/useToggle';
import { useAlert } from 'hooks/useAlert';
import * as FileSystem from 'expo-file-system';

export const DocumentItem = memo(function DocumentItem({
  file,
  onRemoveAsset,
}: {
  file: File;
  index: number;
  onRemoveAsset: (asset: File) => void;
}) {
  const { sizes, colors, borderRadius } = useTheme();
  const { t } = useTranslation(['common', 'eApp']);
  const { alertError } = useAlert();
  const caseId = useBoundStore(state => state.case.caseId);
  const { mutateAsync: deleteDocument, isLoading: isDeleting } =
    useDeleteDocument();
  const [
    deleteConfirmationVisible,
    showDeleteConfirmation,
    hideDeleteConfirmation,
  ] = useToggle();

  const [fileInfo, setFileInfo] = useState<FileSystem.FileInfo>();

  const load = useCallback(async () => {
    const info = await FileSystem.getInfoAsync(
      `${FileSystem.documentDirectory}${DOCUMENT_DIR}${file.name}`,
    );
    setFileInfo(info);
  }, [file.name]);

  useEffect(() => {
    load();
  }, [load]);

  const onDelete = useCallback(async () => {
    hideDeleteConfirmation();
    if (!caseId) return;
    try {
      const response = await deleteDocument({
        caseId,
        fileName: file.name || '',
      });
      Toast.show(
        [
          {
            message: t('eApp:documentUpload.deleted'),
            IconLeft: <Icon.Tick />,
          },
        ],
        {
          type: 'success',
          duration: Toast.durations.SHORT,
        },
      );
      if (response) {
        onRemoveAsset(file);
      }
    } catch {
      alertError(t('eApp:documentUpload.failedToDelete'));
    }
  }, [
    alertError,
    caseId,
    deleteDocument,
    file,
    hideDeleteConfirmation,
    onRemoveAsset,
    t,
  ]);

  return (
    <View
      style={{ opacity: file.status === 'uploading' ? 0.5 : 1 }}
      renderToHardwareTextureAndroid>
      <Box
        w={sizes[20]}
        h={sizes[20]}
        borderRadius={borderRadius.small}
        bgColor={colors.palette.fwdGrey[100]}>
        {/* Show image and attachment here */}
        {file.name?.endsWith('.pdf') ? (
          <PictogramIcon.DocumentPdf size={sizes[20]} />
        ) : (
          <ImageItem
            source={{
              uri:
                file.status === 'uploading'
                  ? file.uri
                  : getDocumentUri(file.name),
            }}
          />
        )}
        <Box
          style={StyleSheet.absoluteFill}
          borderRadius={borderRadius.small}
          borderColor={colors.palette.fwdGrey[100]}
          borderWidth={1}
        />
      </Box>
      <Row mt={sizes[1]} alignItems="center">
        <Typography.SmallBody
          color={colors.palette.fwdGreyDarkest}
          style={{ flex: 1 }}>
          {`${Number(
            fileInfo?.exists ? fileInfo.size / 1e6 : file.size,
          ).toFixed(2)}mb`}
        </Typography.SmallBody>
        <Box w={sizes[1]} />
        {!file.fromOcr && (
          <TouchableOpacity
            disabled={file.status === 'uploading' || isDeleting}
            hitSlop={ICON_HIT_SLOP}
            onPress={showDeleteConfirmation}>
            {file.status === 'uploading' || isDeleting ? (
              <LoadingIndicator color={colors.primary} size={sizes[4]} />
            ) : (
              <Icon.Delete fill={colors.onBackground} size={sizes[4]} />
            )}
          </TouchableOpacity>
        )}
      </Row>
      <DeleteConfirmationModal
        visible={deleteConfirmationVisible}
        onAgree={onDelete}
        onDismiss={hideDeleteConfirmation}
      />
    </View>
  );
});
export default DocumentItem;

const ImageItem = styled.Image(({ theme: { space, borderRadius } }) => {
  return {
    width: space[20],
    height: space[20],
    borderRadius: borderRadius.small,
  };
});

const DeleteConfirmationModal = ({
  visible,
  onDismiss,
  onAgree,
}: {
  visible: boolean;
  onDismiss: () => void;
  onAgree: () => void;
}) => {
  const { t } = useTranslation(['eApp']);
  const { space } = useTheme();
  return (
    <DialogPhone visible={visible}>
      <Box p={space[6]}>
        <H6 fontWeight="bold">{t('eApp:documentUpload.deleteTitle')}</H6>
        <Box height={space[4]} />
        <H7 fontWeight="normal">{t('eApp:documentUpload.deleteSubtitle')}</H7>
        <Row
          marginTop={space[6]}
          gap={space[4]}
          width={'100%'}
          justifyContent="center">
          <Button
            variant="secondary"
            text={t('eApp:cancel')}
            style={{ flex: 1, maxWidth: 200 }}
            onPress={onDismiss}
            size="medium"
          />
          <Button
            onPress={onAgree}
            style={{ flex: 1, maxWidth: 200 }}
            variant="primary"
            text={t('eApp:remove')}
            size="medium"
          />
        </Row>
      </Box>
    </DialogPhone>
  );
};
