import { OptionList } from 'types/optionList';
import { Quotation, RiderCode } from 'types/quotation';

export const getPremByPaymentMode = (quotation?: Quotation) => {
  const prem: {
    [key: string]: number | undefined;
  } = {
    A: quotation?.summary?.annualPrem,
    S: quotation?.summary?.semiAnnualPrem,
    Q: quotation?.summary?.quarterlyPrem,
    M: quotation?.summary?.monthlyPrem,
    L: quotation?.summary?.totalPrem,
  };
  return prem[quotation?.basicInfo?.paymentMode?.toString() ?? ''];
};

export const checkFatcaRequired = (pid: string | undefined) => {
  switch (pid) {
    case 'EN01': // 'FWD Income First'
    case 'ILB': // 'FWD Invest First'
    case 'ILBP': // 'FWD Invest First Plus',
    case 'OFB': // 'FWD Life First'
    case 'ILM': // 'FWD Maqbul Link'
    case 'EN3': // 'Takaful Future Wealth'
    case 'EN4': // 'Takaful Lifestyle Protector Plus'
    case 'EN5': // 'Takaful Lifestyle Protector Plus (Premier)'
    case 'EN6': // 'Takaful Future Defender'
    case 'EN7': // 'FWD Protect First'
    case 'EN8': // 'Takaful Future Wealth Plus'
    case 'EN9': // 'FWD Majestic'
    case 'IL4': // 'Takaful Future Education'
    case 'IL8': // 'Takaful Future Select Plus'
    case 'IP1': // 'Takaful FutureMax'
    case 'IP2': // 'Takaful Future Prime'
    case 'IP1SIO': // 'Takaful FutureMax (Campaign)'
    case 'LVS': // 'Takaful FutureSecure'
    case 'TSI': // 'Takaful LifeSelect Single'
      return true;
    case 'NGC': // 'FWD CI First'
    case 'TM1': // 'FWD Future First'
    default:
      return false;
  }
};

export const checkAddRider = (
  quotation?: Quotation,
  optionList?: OptionList<string, 'my'>,
  isEntity?: boolean,
) => {
  // TODO: this is a temporary solution to disable income prepopulation for entity
  if (isEntity) {
    return {
      hasAddRider: false,
      addRiderAnnualIncome: undefined,
    };
  }
  const addRider = quotation?.plans?.find(plan => plan?.pid === RiderCode.ADIA);
  const hasAddRider = addRider !== null && addRider !== undefined;
  const addRiderAnnualIncome = hasAddRider
    ? (optionList as OptionList<string, 'my'>).INCOME_RANGE?.options?.find(
        e => e.annualIncomeValue === addRider?.annualIncome,
      )?.value
    : undefined;
  return {
    hasAddRider,
    addRiderAnnualIncome,
  };
};
