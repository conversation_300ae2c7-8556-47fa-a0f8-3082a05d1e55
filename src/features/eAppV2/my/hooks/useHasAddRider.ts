import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import { checkAddRider } from '../utils/planUtils';
import { useMemo } from 'react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';

export const useHasAddRider = () => {
  const quotation = useSelectedQuotation();
  const { data: optionList } = useGetOptionList<'my'>();
  const isEntity = useCheckEntity();
  return useMemo(
    () => checkAddRider(quotation, optionList, isEntity),
    [quotation, optionList, isEntity],
  );
};
