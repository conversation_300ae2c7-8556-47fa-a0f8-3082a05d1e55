import { useCallback } from 'react';
import { SocialMarketingRatio } from '../types';
import { useTheme } from '@emotion/react';
import { useWindowDimensions } from 'react-native';

const useCalculatePostReviewSize = () => {
  const { width: screenWidth } = useWindowDimensions();
  const { space } = useTheme();

  const calculatePostReviewSize = useCallback(
    (ratio: SocialMarketingRatio) => {
      let calculatedWidth = screenWidth - space[4] * 2; // Subtracting padding
      let calculatedHeight = calculatedWidth; // Default to square

      if (ratio === SocialMarketingRatio.SocialPost) {
        calculatedWidth = screenWidth - space[8] * 2; // Subtracting padding
        // Maintain 4:5 aspect ratio
        calculatedHeight = calculatedWidth * (5 / 4);
      } else if (ratio === SocialMarketingRatio.SocialStory) {
        calculatedWidth = screenWidth - space[12] * 2; // Subtracting padding
        // Maintain 9:16 aspect ratio
        calculatedHeight = calculatedWidth * (16 / 9);
      }

      return {
        width: calculatedWidth,
        height: calculatedHeight,
      };
    },
    [screenWidth, space],
  );

  return calculatePostReviewSize;
};

export default useCalculatePostReviewSize;
