import { useTheme } from '@emotion/react';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface LayoutState {
  isOnboardSocial: boolean;
  tabBarHeight: number;
  setTabBarHeight: (height: number) => void;
  setOnboardSocial: (data: boolean) => void;
  setPromptReviewFooterHeight: (height: number) => void;
  promptReviewFooterHeight: number;
}

export const useLayoutStore = create(
  persist<LayoutState>(
    set => ({
      isOnboardSocial: false,
      tabBarHeight: 0,
      setTabBarHeight: height => set({ tabBarHeight: height }),
      setOnboardSocial: data => set({ isOnboardSocial: data }),
      setPromptReviewFooterHeight: height =>
        set({ promptReviewFooterHeight: height }),
      promptReviewFooterHeight: 0,
    }),
    {
      name: 'layout-social-storage',
      storage: createJSONStorage(() => localStorage),
    },
  ),
);

export const useGetTabBarHeight = () => {
  const tabBarHeight = useLayoutStore(state => state.tabBarHeight);
  return tabBarHeight;
};

export const useSetTabBarHeight = () => {
  const setTabBarHeight = useLayoutStore(state => state.setTabBarHeight);
  return setTabBarHeight;
};

export const usePromptReviewFooterHeight = () => {
  const { space } = useTheme();
  const promptReviewFooterHeight = useLayoutStore(
    state => state.promptReviewFooterHeight,
  );
  return promptReviewFooterHeight + space[20]; // Addjusting for additional space
};

export const useSetPromptReviewFooterHeight = () => {
  const setPromptReviewFooterHeight = useLayoutStore(
    state => state.setPromptReviewFooterHeight,
  );
  return setPromptReviewFooterHeight;
};
