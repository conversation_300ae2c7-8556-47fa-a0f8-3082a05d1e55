import { useCallback } from 'react';

import socialMarketing<PERSON>pi from 'api/socialMarketingApi';
import { create } from 'zustand';
import {
  SocialMarketingCreatePostPayload,
  SocialMarketingCreatePostResponse,
  SocialMarketingRegeneratePostPayload,
} from '../types';

// Zustand store for managing post creation state
interface PostCreationState {
  isLoading: boolean;
  isRegenerating: boolean;
  createdPost: SocialMarketingCreatePostResponse | null;
  createPost: (payload: SocialMarketingCreatePostPayload) => Promise<void>;
  regeneratePost: (
    postId: string,
    payload: SocialMarketingRegeneratePostPayload,
  ) => Promise<void>;
}

const usePostCreationStore = create<PostCreationState>(set => ({
  isLoading: false,
  isRegenerating: false,
  createdPost: null,
  createPost: async payload => {
    set({ isLoading: true });
    try {
      const createdPost = await socialMarketingApi.createPost(payload);
      set({ createdPost });
    } finally {
      set({ isLoading: false });
    }
  },
  regeneratePost: async (postId, payload) => {
    set({ isRegenerating: true });
    try {
      const createdPost = await socialMarketingApi.regeneratePost(
        postId,
        payload,
      );
      set({ createdPost });
    } finally {
      set({ isRegenerating: false });
    }
  },
}));

export const useCreatePost = () => {
  const createPost = usePostCreationStore(state => state.createPost);

  const handleCreatePost = useCallback(
    async (payload: SocialMarketingCreatePostPayload) => {
      await createPost(payload);
    },
    [createPost],
  );

  return handleCreatePost;
};

export const useRegeneratePost = () => {
  const regeneratePost = usePostCreationStore(state => state.regeneratePost);

  const handleRegeneratePost = useCallback(
    async (postId: string, payload: SocialMarketingRegeneratePostPayload) => {
      await regeneratePost(postId, payload);
    },
    [regeneratePost],
  );

  return handleRegeneratePost;
};

export const usePostCreationState = () => {
  const isLoading = usePostCreationStore(state => state.isLoading);
  const isRegenerating = usePostCreationStore(state => state.isRegenerating);
  const createdPost = usePostCreationStore(state => state.createdPost);

  return {
    isLoading,
    isRegenerating,
    createdPost,
  };
};
