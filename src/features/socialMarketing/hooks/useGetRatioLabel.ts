import { SocialMarketingRatio } from '../types';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

const useGetRatioLabel = (ratio: SocialMarketingRatio) => {
  const { t } = useTranslation('socialMarketing');

  return useMemo(() => {
    let label = t('createPost.imageRatio.square');
    switch (ratio) {
      default:
      case SocialMarketingRatio.Square:
        label = t('createPost.imageRatio.square');
        break;
      case SocialMarketingRatio.SocialPost:
        label = t('createPost.imageRatio.socialPost');
        break;
      case SocialMarketingRatio.SocialStory:
        label = t('createPost.imageRatio.socialStory');
        break;
    }

    return label;
  }, [ratio, t]);
};

export default useGetRatioLabel;
