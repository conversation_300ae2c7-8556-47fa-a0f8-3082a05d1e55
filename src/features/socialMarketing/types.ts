export type MediaContentType =
  | 'video/mp4'
  | 'video/quicktime'
  | 'image/png'
  | 'image/jpeg';

export enum SocialMarketingPostType {
  Image = 'image',
  ShortVideo = 'video', // Following API convention, 'video' is used for short videos
  AvatarVideo = 'avatar', // Following API convention, 'avatar' is used for avatar videos
}

export interface SocialMarketingMedia {
  uid: string;
  title: string;
  url: string;
  content_type: MediaContentType;
  created_at?: string;
  updated_at?: string;
}

export interface SocialMarketingTemplate {
  template_name: string;
  _metadata: unknown;
  post_caption?: string;
  video?: SocialMarketingMedia;
  image?: SocialMarketingMedia;
  search_tags?: string;
}

export interface SocialMarketingCategory {
  id: string;
  title: string;
}

export interface SocialMarketingTopic {
  id: string;
  title: string;
}

export enum SocialMarketingPlatform {
  Instagram = 'instagram',
  Facebook = 'facebook',
  TikTok = 'tiktok',
}

export enum SocialMarketingRatio {
  Square = '1:1',
  // 4:5 for social post
  SocialPost = '4:5',
  // 9:16 for social story
  SocialStory = '9:16',
}

export interface SocialMarketingCreatePostPayload {
  mediaType: SocialMarketingPostType;
  prompt: string;
  topic: string;
  ratio: SocialMarketingRatio;
  platform?: SocialMarketingPlatform;
}

export interface SocialMarketingRegeneratePostPayload {
  prompt: string;
  topic?: string | null;
}

export interface SocialMarketingCreatePostResponse {
  id: string;
  agentId: string;
  caption?: string | null;
  createdAt?: string | null;
  facebookPostId?: string | null;
  mediaType?: SocialMarketingPostType | null;
  mediaUrl?: string | null;
  localUrl?: string | null;
  prompt?: string | null;
  ratio?: SocialMarketingRatio | null;
  topic?: string | null;
}
