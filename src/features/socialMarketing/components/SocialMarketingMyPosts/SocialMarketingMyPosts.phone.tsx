import { type SocialMarketingMedia } from 'features/socialMarketing/types';
import React from 'react';
import { useTranslation } from 'react-i18next';
import SocialMarketingFlatList from '../SocialMarketingFlatList';

const mockData: SocialMarketingMedia[] = [
  {
    uid: '1',
    title: 'Template 1',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczNcbBKfMtyGTQLjQK0eZETnJQqj0Hg5FafTkSaCO40imZES0lmjCvGMUMPAKee2D_h2Utj8CAo7_UwSPQjth__E5b84sMg4yTKpbnjLCVO_tfFw3rzJ5USHTFEJgsZ-m5FslxIS-9KC618IfCReCVo=w166-h208-s-no-gm',
    content_type: 'image/png',
  },
  {
    uid: '2',
    title: 'Template 2',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczOWy7BBvJsi5j8uX_RjeTNNpyX6FIMLOx6bx1IF0B80XMNv7Q6duG0e_rezersoQhbtUCmDI4FrQtszL3Aja9s_CIUCPC21EgXGBIURGO0C2Hwq2QXK5VTK6lyzoiTTwjvOGq22HQ-gWUBGHI08is4=w166-h295-s-no-gm',
    content_type: 'image/png',
  },
  {
    uid: '3',
    title: 'Template 3',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczP0rCeGO1GMyv2ownt415j_WQjB328uFjm7wWqtQmuu5yAy-gcA9OEgUTpGllEbJf2zeKQlUZQs90xY9XEr1grdY2OrTXtNQ9WB04_-mB2GfEgirNNLzbr_KiYo4WQyIVscnLcgz4jdGQtPOXPtcOE=w166-h295-s-no-gm',
    content_type: 'image/png',
  },
  {
    uid: '4',
    title: 'Template 4',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczPKGBKzq9W07iG_PTEe3W8SQ3S3ZJV-t35mI5QBY3SjSBtgzxCQefrc9ZGRwQhOu7expU_byw0WuU9m9XHdKn7sujrJWfMxcP6N-6J5ZnE5Rl6v8gsAaDA7N4_yzK6kDynQsv99Vo14lAyced5IXug=w166-h295-s-no-gm',
    content_type: 'image/png',
  },
  {
    uid: '5',
    title: 'Template 5',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczOz8_LkDdVjGJzavHskowL9bCdF6qEcnU2xbtf1tyXAlNI8HE61SddVagE98IjBZOMDI4_5AfHstp8jn-jGARux_NcU00PFAAzr49rJylqumeFerZyGmMGflg7SPw521cWCrFml3JZzM1d4dkuSC2c=w166-h295-s-no-gm',
    content_type: 'image/png',
  },
  {
    uid: '6',
    title: 'Template 6',
    url: 'https://lh3.googleusercontent.com/pw/AP1GczOLJC285_rwcc5i3OBNm2MfiKn_fkuRqyY2LfdNArbm2rwcyyKjekfnljju6tqlXusY6pzyRmv1mDfb9ek6ZAU09-y-uezD9C0__bVbGQNXt-TzegXJXA31FKPLYiRCdRYrAe72vcFMRqXGB8H-Ahg=w166-h208-s-no-gm',
    content_type: 'image/png',
  },
];

export default function SocialMarketingMyPostsPhone() {
  const { t } = useTranslation('socialMarketing');

  return <SocialMarketingFlatList data={mockData} title={t('myPosts.title')} />;
}
