import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import React from 'react';
import { Image, View } from 'react-native';
import { LoadingSocialProps } from './types';

const ContainerSvg = styled(View)(({ theme }) => ({
  justifyContent: 'center',
  alignItems: 'center',
  paddingHorizontal: theme.space[10],
  paddingVertical: theme.space[32],
}));

const Percentage = styled(View)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.space[2],
}));

const Title = styled(Typography.H7_2)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  marginBottom: theme.space[1],
  marginTop: theme.space[6],
  fontWeight: 'bold',
}));

const Description = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  textAlign: 'center',
}));

export default function LoadingSocialPhone({
  title,
  description,
}: LoadingSocialProps) {
  return (
    <View
      style={{
        flex: 1,
      }}>
      <Image
        source={require('assets/loading-bg.jpg')}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
        }}
        resizeMode="cover"
      />
      <ContainerSvg>
        <Image
          source={require('assets/loading-temp.png')}
          style={{
            width: 180,
            height: 165,
          }}
          resizeMode="cover"
        />
        <Percentage>
          <Title>{title}</Title>
        </Percentage>
        {description && <Description>{description}</Description>}
      </ContainerSvg>
    </View>
  );
}
