import React, { useCallback, useState } from 'react';
import { useWindowDimensions } from 'react-native';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Image, ImageLoadEventData } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import { GalleryMediaCardProps } from './types';

const GalleryMediaContainer = styled.TouchableOpacity<{
  width?: number;
  height?: number;
}>(({ theme: { colors, space }, width, height }) => ({
  flex: 1,
  ...(width ? { width } : {}),
  ...(height ? { height } : {}),
  borderRadius: space[4],
  overflow: 'hidden',
  marginHorizontal: space[1],
  backgroundColor: colors.palette.fwdOrange[20],
}));

export default function GalleryMediaCardPhone({
  media,
  width,
  height,
  onPress,
  onLayout,
}: GalleryMediaCardProps) {
  const { space } = useTheme();
  const { width: screenWidth } = useWindowDimensions();
  const [ratio, setRatio] = useState<number | null>(null);

  const isVideoType = media.content_type?.startsWith('video');

  const player = useVideoPlayer(isVideoType ? media.url : '', player => {
    player.muted = true;
    player.currentTime = 0;
    player.loop = true;
    player.play();
  });

  const handleImageLoad = useCallback(({ source }: ImageLoadEventData) => {
    if (source?.width && source?.height) {
      setRatio(source.height / source.width);
    }
  }, []);

  const renderMediaContent = useCallback(() => {
    const mediaWidth = (screenWidth - space[8]) / 2;
    const calculatedHeight = ratio ? mediaWidth * ratio : 300;

    if (isVideoType && player) {
      return (
        <VideoView
          player={player}
          style={{
            width: width || mediaWidth,
            height: height || calculatedHeight,
          }}
          nativeControls={false}
          contentFit="cover"
        />
      );
    }

    return (
      <Image
        source={{ uri: media.url, cache: 'force-cache' }}
        onLoad={handleImageLoad}
        style={{
          flex: 1,
          width: width || mediaWidth,
          height: height || calculatedHeight,
        }}
        contentFit="cover"
        cachePolicy="memory-disk"
      />
    );
  }, [
    screenWidth,
    space,
    ratio,
    media.content_type,
    media.url,
    handleImageLoad,
    width,
    height,
    player,
  ]);

  return (
    <GalleryMediaContainer
      onLayout={onLayout}
      onPress={() => onPress?.(media)}
      width={width}
      height={height}>
      {renderMediaContent()}
    </GalleryMediaContainer>
  );
}
