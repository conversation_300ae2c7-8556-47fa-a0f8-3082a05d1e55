import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  ListRenderItem,
  MasonryFlashList,
  MasonryFlashListProps,
} from '@shopify/flash-list';
import { SocialMarketingMedia } from '../types';
import { Typography } from 'cube-ui-components';
import React, { useCallback } from 'react';
import { View } from 'react-native';
import { useGetTabBarHeight } from '../hooks/useLayoutStore';
import GalleryMediaCard from './GalleryMediaCard';

const ListHeaderContainer = styled.View(({ theme: { space } }) => ({
  paddingVertical: space[2],
  paddingHorizontal: space[1],
}));
const MediaSeprator = styled.View(({ theme: { space } }) => ({
  width: space[2],
  height: space[2],
}));

type SocialMarketingFlatListProps = Omit<
  MasonryFlashListProps<SocialMarketingMedia>,
  'renderItem' | 'onEndReached' | 'ListHeaderComponent'
> & {
  ExtraListHeaderComponent?: React.ReactNode;
  title?: string;
  description?: string;
  renderItem?: ListRenderItem<SocialMarketingMedia>;
  onLoadMore?: () => void;
};

export default function SocialMarketingFlatList({
  ExtraListHeaderComponent,
  title,
  description,
  ...props
}: SocialMarketingFlatListProps) {
  const tabBarHeight = useGetTabBarHeight();
  const { colors, space } = useTheme();

  const handleRenderMediaItem = useCallback(
    ({ item }: { item: SocialMarketingMedia }) => (
      <GalleryMediaCard media={item} onPress={() => {}} />
    ),
    [],
  );

  return (
    <MasonryFlashList
      numColumns={2}
      keyExtractor={item => item.uid}
      renderItem={handleRenderMediaItem}
      {...props}
      style={{ flex: 1 }}
      contentContainerStyle={{
        backgroundColor: colors.background,
        paddingVertical: space[1],
        paddingHorizontal: space[3],
        paddingBottom: tabBarHeight + space[2], // Adjust for bottom tab bar height
      }}
      onEndReached={props.onLoadMore}
      ItemSeparatorComponent={() => <MediaSeprator />}
      ListHeaderComponent={
        <View>
          <ListHeaderContainer>
            {!!title && (
              <Typography.H5 fontWeight="bold">{title}</Typography.H5>
            )}
            {!!description && (
              <Typography.Label>{description}</Typography.Label>
            )}
          </ListHeaderContainer>
          {React.isValidElement(ExtraListHeaderComponent)
            ? ExtraListHeaderComponent
            : null}
        </View>
      }
    />
  );
}
