import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import TutorialArrowSVG from 'features/socialMarketing/assets/TutorialArrowSVG';
import React from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, LinearTransition } from 'react-native-reanimated';
import GalleryMediaCard from '../../GalleryMediaCard';
import {
  HandDrawText,
  TransformSVG,
  TutorialGalleryHighlight,
  TutorialGrid,
  TutorialGridContainer,
  TutorialGridItem,
  TutorialGridTextItem,
} from './styled';

export default function TutorialExplainerStepTwoPhone({
  onNextStep,
}: {
  onNextStep: () => void;
}) {
  const { colors, space, animation } = useTheme();
  const { t } = useTranslation('socialMarketing');

  return (
    <>
      <Typography.H6 color={colors.palette.fwdOrange[100]} fontWeight="bold">
        {t('tutorial.createPost')}
      </Typography.H6>
      <HandDrawText>{t('tutorial.chooseATemplate')}</HandDrawText>
      <TutorialGridContainer>
        <TutorialGrid>
          <TutorialGridTextItem>
            <Animated.View
              layout={LinearTransition.duration(animation.duration)}
              entering={FadeIn}>
              <TransformSVG>
                <TutorialArrowSVG width={80} height={100} />
              </TransformSVG>
              <Typography.LargeBody
                color={colors.palette.fwdOrange[100]}
                fontWeight="medium"
                style={{
                  paddingHorizontal: space[4],
                  paddingVertical: space[2],
                  direction: 'rtl',
                }}>
                {t('tutorial.customizeYourVideo')}
              </Typography.LargeBody>
            </Animated.View>
          </TutorialGridTextItem>
          <TutorialGridItem>
            <TutorialGalleryHighlight height={328}>
              <GalleryMediaCard
                media={{
                  uid: '2',
                  title: 'Template 2',
                  url: 'https://lh3.googleusercontent.com/pw/AP1GczOWy7BBvJsi5j8uX_RjeTNNpyX6FIMLOx6bx1IF0B80XMNv7Q6duG0e_rezersoQhbtUCmDI4FrQtszL3Aja9s_CIUCPC21EgXGBIURGO0C2Hwq2QXK5VTK6lyzoiTTwjvOGq22HQ-gWUBGHI08is4=w166-h295-s-no-gm',
                  content_type: 'image/png',
                }}
                onPress={onNextStep}
              />
            </TutorialGalleryHighlight>
          </TutorialGridItem>
        </TutorialGrid>
      </TutorialGridContainer>
    </>
  );
}
