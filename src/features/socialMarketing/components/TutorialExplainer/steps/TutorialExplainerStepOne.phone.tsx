import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import TutorialArrowSVG from 'features/socialMarketing/assets/TutorialArrowSVG';
import React from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, LinearTransition } from 'react-native-reanimated';
import GalleryMediaCard from '../../GalleryMediaCard';
import {
  HandDrawText,
  TutorialGalleryHighlight,
  TutorialGrid,
  TutorialGridContainer,
  TutorialGridItem,
  TutorialGridTextItem,
} from './styled';

export default function TutorialExplainerStepOnePhone({
  onNextStep,
}: {
  onNextStep: () => void;
}) {
  const { colors, space, animation } = useTheme();
  const { t } = useTranslation('socialMarketing');

  return (
    <>
      <Typography.H6 color={colors.palette.fwdOrange[100]} fontWeight="bold">
        {t('tutorial.createPost')}
      </Typography.H6>
      <HandDrawText>{t('tutorial.chooseATemplate')}</HandDrawText>
      <TutorialGridContainer>
        <TutorialGrid>
          <TutorialGridItem>
            <TutorialGalleryHighlight height={231}>
              <GalleryMediaCard
                media={{
                  uid: '1',
                  title: 'Template 1',
                  url: 'https://lh3.googleusercontent.com/pw/AP1GczNcbBKfMtyGTQLjQK0eZETnJQqj0Hg5FafTkSaCO40imZES0lmjCvGMUMPAKee2D_h2Utj8CAo7_UwSPQjth__E5b84sMg4yTKpbnjLCVO_tfFw3rzJ5USHTFEJgsZ-m5FslxIS-9KC618IfCReCVo=w166-h208-s-no-gm',
                  content_type: 'image/png',
                }}
                onPress={onNextStep}
              />
            </TutorialGalleryHighlight>
          </TutorialGridItem>
          <TutorialGridTextItem>
            <Animated.View
              layout={LinearTransition.duration(animation.duration)}
              entering={FadeIn}>
              <TutorialArrowSVG width={80} height={100} />
              <Typography.LargeBody
                color={colors.palette.fwdOrange[100]}
                fontWeight="medium"
                style={{
                  paddingHorizontal: space[4],
                  paddingVertical: space[2],
                }}>
                {t('tutorial.customizeYourImage')}
              </Typography.LargeBody>
            </Animated.View>
          </TutorialGridTextItem>
        </TutorialGrid>
      </TutorialGridContainer>
    </>
  );
}
