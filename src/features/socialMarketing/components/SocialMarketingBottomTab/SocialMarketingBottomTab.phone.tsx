import styled from '@emotion/native';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Icon } from 'cube-ui-components';
import BottomMenuBarSVG from 'features/socialMarketing/assets/BottomMenuBarSVG';
import { useSetTabBarHeight } from 'features/socialMarketing/hooks/useLayoutStore';
import React, { useCallback, useState } from 'react';
import { LayoutChangeEvent } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { SocialMarketingParamList } from 'types';
import { TabButton } from '../TabButton';
import { SocialMarketingBottomTab } from './types';

const Container = styled(SafeAreaView)({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
});
const TabButtonContainer = styled.View(({ theme: { space } }) => ({
  flexDirection: 'row',
  alignItems: 'flex-end',
  paddingHorizontal: space[2],
  paddingVertical: space[1],
}));

export default function SocialMarketingBottomTabPhone() {
  const setTabBarHeight = useSetTabBarHeight();
  const navigation =
    useNavigation<NavigationProp<SocialMarketingParamList>>();
  const [layoutHeight, setLayoutHeight] = useState<number | undefined>();
  const [selectedTab, setSelectedTab] =
    useState<SocialMarketingBottomTab>('templates');

  // Adjust the height of the BottomMenuBarSVG based on the layout height
  const handleLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      setLayoutHeight(height);
      setTabBarHeight(height);
    },
    [setTabBarHeight],
  );
  const handleTabPress = useCallback(
    (selectedTab: SocialMarketingBottomTab) => () => {
      // Navigate to the corresponding screen based on the selected tab
      switch (selectedTab) {
        case 'templates':
          setSelectedTab(selectedTab);
          navigation.navigate('SocialMarketingTemplates');
          break;
        case 'myPosts':
          setSelectedTab(selectedTab);
          navigation.navigate('SocialMarketingMyPosts');
          break;
        case 'createNew':
          navigation.navigate('SocialMarketingCreateNew');
          break;
      }
    },
    [navigation],
  );

  return (
    <Container edges={['bottom']}>
      <BottomMenuBarSVG height={layoutHeight} />
      <TabButtonContainer onLayout={handleLayout}>
        <TabButton
          name="templates"
          Icon={Icon.MultipleMedia}
          isFocused={selectedTab === 'templates'}
          onPress={handleTabPress('templates')}
        />
        <TabButton
          name="myPosts"
          Icon={Icon.DocumentCopy}
          isFocused={selectedTab === 'myPosts'}
          onPress={handleTabPress('myPosts')}
        />
        <TabButton
          name="createNew"
          Icon={Icon.Plus}
          isFocused={selectedTab === 'createNew'}
          onPress={handleTabPress('createNew')}
        />
      </TabButtonContainer>
    </Container>
  );
}
