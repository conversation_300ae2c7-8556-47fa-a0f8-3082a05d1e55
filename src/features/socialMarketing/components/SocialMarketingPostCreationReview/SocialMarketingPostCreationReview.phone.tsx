import React, { useState } from 'react';

import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Button, Icon, Typography } from 'cube-ui-components';
import { usePromptReviewFooterHeight } from 'features/socialMarketing/hooks/useLayoutStore';
import { usePostCreationState } from 'features/socialMarketing/hooks/usePosts';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import SocialMarketingImageReview from './SocialMarketingImageReview';
import SocialMarketingPromptInputReview from './SocialMarketingPromptInputReview';
import LoadingSocial from '../LoadingSocial';

const Container = styled(SafeAreaView)(({ theme: { colors } }) => ({
  flex: 1,
  backgroundColor: colors.palette.fwdGrey[20],
}));
const Header = styled(SafeAreaView)(({ theme: { colors, space } }) => ({
  flexDirection: 'row',
  backgroundColor: colors.background,
  paddingHorizontal: space[4],
  paddingVertical: space[3],
}));
const FooterWrapper = styled.View(({ theme: { space } }) => ({
  flexGrow: 1,
  justifyContent: 'flex-end',
  padding: space[4],
}));

export default function SocialMarketingPostCreationReviewPhone() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('socialMarketing');

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { isLoading } = usePostCreationState();
  const promptFooterHeight = usePromptReviewFooterHeight();

  const [isPromptFocused, setIsPromptFocused] = useState(false);

  const handlePromptFocus = (focused: boolean) => {
    setIsPromptFocused(focused);
  };

  const handleGoBackPress = () => {
    navigation.goBack();
  };

  return (
    <Container edges={['bottom']}>
      {/* Different background color */}
      <Header edges={['top']}>
        <TouchableOpacity onPress={handleGoBackPress}>
          <Icon.ArrowLeft size={space[6]} fill={colors.palette.black} />
        </TouchableOpacity>
      </Header>

      {isLoading ? (
        <LoadingSocial title={t('createPost.generating')} />
      ) : (
        <>
          <KeyboardAwareScrollView
            bottomOffset={promptFooterHeight + space[4]} // Adjust for footer height and padding with keyboard
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
            contentContainerStyle={{
              flexGrow: 1,
              padding: space[4],
            }}>
            <SocialMarketingImageReview blur={isPromptFocused} />

            <SocialMarketingPromptInputReview onFocus={handlePromptFocus} />
          </KeyboardAwareScrollView>
          <FooterWrapper>
            <Button text={t('createPost.cta.postCaption')} />
          </FooterWrapper>
        </>
      )}
    </Container>
  );
}
