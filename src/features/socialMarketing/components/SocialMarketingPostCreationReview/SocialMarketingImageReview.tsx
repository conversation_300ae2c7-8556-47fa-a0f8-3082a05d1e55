import styled from '@emotion/native';
import GlowBorderBoxIndicator from 'components/GlowBorderBoxIndicator';
import { Image } from 'expo-image';
import useCalculatePostReviewSize from 'features/socialMarketing/hooks/useCalculatePostReviewSize';
import { usePostCreationState } from 'features/socialMarketing/hooks/usePosts';
import { SocialMarketingRatio } from 'features/socialMarketing/types';
import React, { useMemo } from 'react';
import { View } from 'react-native';

const Container = styled.View<{
  width: number;
  height: number;
  blur?: boolean;
}>(({ theme: { colors, space }, width, height, blur }) => ({
  width,
  height,
  alignSelf: 'center',
  backgroundColor: colors.palette.fwdOrange[20],
  borderRadius: space[1],
  overflow: 'hidden',
  opacity: blur ? 0.5 : 1,
}));

type SocialMarketingImageReviewProps = {
  blur?: boolean;
};

export default function SocialMarketingImageReview(
  props: SocialMarketingImageReviewProps,
) {
  const calculatePostReviewSize = useCalculatePostReviewSize();
  const { createdPost, isRegenerating } = usePostCreationState();

  const imageUrl = createdPost?.localUrl || createdPost?.mediaUrl;
  const { width, height } = useMemo(() => {
    const ratio = createdPost?.ratio || SocialMarketingRatio.Square;

    return calculatePostReviewSize(ratio);
  }, [createdPost]);

  if (!imageUrl) {
    return null;
  }

  return (
    <Container width={width} height={height} blur={props.blur}>
      <Image
        source={{ uri: imageUrl }}
        style={{ width: '100%', height: '100%' }}
        contentFit="cover"
      />
      {isRegenerating && <GlowBorderBoxIndicator />}
    </Container>
  );
}
