import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { <PERSON>ton, Icon, TextField, Typography } from 'cube-ui-components';
import { useSetPromptReviewFooterHeight } from 'features/socialMarketing/hooks/useLayoutStore';
import {
  usePostCreationState,
  useRegeneratePost,
} from 'features/socialMarketing/hooks/usePosts';
import { SocialMarketingTopic } from 'features/socialMarketing/types';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FlatList,
  Keyboard,
  LayoutChangeEvent,
  Pressable,
  TextInput,
  View,
} from 'react-native';

const Container = styled.View<{ focused: boolean }>(
  ({ theme: { colors, space }, focused }) => ({
    marginTop: space[10],
    paddingVertical: space[2],
    borderRadius: space[4],
    borderWidth: 1,
    borderColor: focused
      ? colors.palette.fwdOrange[100]
      : colors.palette.fwdGrey[100],
  }),
);
const TopicItemContainer = styled.TouchableOpacity(
  ({ theme: { colors, space } }) => ({
    marginVertical: space[2],
    paddingVertical: space[1],
    paddingHorizontal: space[2],
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.palette.fwdOrange[100],
    borderRadius: space[6],
  }),
);
const TopicItemSeprator = styled.View(({ theme: { space } }) => ({
  width: space[2],
}));
const MagicWandButton = styled(Pressable)(({ theme: { colors, space } }) => ({
  position: 'absolute',
  top: -space[3],
  right: space[4],
  zIndex: 1,
  backgroundColor: colors.palette.fwdGrey[20],
}));

const mockTopics: SocialMarketingTopic[] = [
  {
    id: '1',
    title: 'Medical benefit',
  },
  {
    id: '2',
    title: 'Family protection',
  },
  {
    id: '3',
    title: 'Accident benefit',
  },
  {
    id: '4',
    title: 'Accidents',
  },
];

type SocialMarketingPromptInputReviewProps = {
  onFocus?: (focused: boolean) => void;
};

export default function SocialMarketingPromptInputReview(
  props: SocialMarketingPromptInputReviewProps,
) {
  const promptInputRef = useRef<TextInput>(null);
  const { colors, space } = useTheme();
  const { t } = useTranslation('socialMarketing');
  const { isRegenerating, createdPost } = usePostCreationState();
  const regeneratePost = useRegeneratePost();
  const setPromptReviewFooterHeight = useSetPromptReviewFooterHeight();

  const [prompt, setPrompt] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [isPromptInputFocused, setIsPromptInputFocused] = useState(false);

  const handleFooterLayout = useCallback(
    (event: LayoutChangeEvent) => {
      const { height } = event.nativeEvent.layout;
      if (height > 0) {
        setPromptReviewFooterHeight(height);
      }
    },
    [setPromptReviewFooterHeight],
  );

  const handlePromptFocus = (focused: boolean) => {
    setIsPromptInputFocused(focused);
    props.onFocus?.(!!focused);
  };

  const handleMagicWandPress = () => {
    promptInputRef.current?.focus();
  };

  const handleRenderTopicItem = useCallback(
    ({ item }: { item: SocialMarketingTopic }) => {
      return (
        <TopicItemContainer
          onPress={() => {
            setSelectedTopic(item.title);
          }}>
          <Typography.SmallLabel
            fontWeight="medium"
            color={colors.palette.fwdOrange[100]}>
            {item.title}
          </Typography.SmallLabel>
        </TopicItemContainer>
      );
    },
    [],
  );

  const handleRegeneratePost = useCallback(async () => {
    if (isRegenerating || !prompt || !createdPost) {
      return;
    }

    Keyboard.dismiss();

    await regeneratePost(createdPost.id || '', {
      prompt,
      topic: selectedTopic || createdPost.topic,
    });
  }, [createdPost?.id, createdPost?.topic, selectedTopic, prompt]);

  useEffect(() => {
    // Update caption in the post creation state when createdPost changes
    if (!isRegenerating && createdPost && createdPost.prompt !== prompt) {
      setPrompt(createdPost.prompt || '');
    }
  }, [createdPost, isRegenerating]);

  return (
    <Container focused={isPromptInputFocused}>
      <Typography.SmallLabel
        fontWeight="medium"
        color={
          isPromptInputFocused
            ? colors.palette.fwdOrange[100]
            : colors.palette.fwdDarkGreen[50]
        }
        style={{
          position: 'absolute',
          paddingHorizontal: space[1],
          backgroundColor: colors.palette.fwdGrey[20],
          top: -space[2],
          left: space[3],
        }}>
        {t('createPost.describeImage')}
      </Typography.SmallLabel>

      <TextField
        ref={promptInputRef}
        textarea
        placeholder={t('createPost.describeImagePlaceholder')}
        inputContainerStyle={{
          borderRadius: space[4],
          height: space[25],
          borderWidth: 0,
          paddingRight: space[1],
          backgroundColor: colors.palette.fwdGrey[20],
        }}
        value={prompt}
        onChangeText={setPrompt}
        onFocus={() => handlePromptFocus(true)}
        onBlur={() => handlePromptFocus(false)}
      />

      {isPromptInputFocused && (
        <View onLayout={handleFooterLayout}>
          <Typography.SmallLabel
            fontWeight="medium"
            style={{
              marginHorizontal: space[4],
              marginTop: space[2],
            }}>
            {t('createPost.tryTheseTopics')}
          </Typography.SmallLabel>
          <FlatList
            horizontal
            data={mockTopics}
            showsHorizontalScrollIndicator={false}
            keyboardShouldPersistTaps="always"
            contentContainerStyle={{
              paddingHorizontal: space[4],
              paddingBottom: space[2],
            }}
            keyExtractor={item => `topic-${item.id}`}
            renderItem={handleRenderTopicItem}
            ItemSeparatorComponent={() => <TopicItemSeprator />}
          />
          <Button
            mini
            size="small"
            text={t('createPost.cta.regenerate')}
            icon={<Icon.MagicWand />}
            onPress={handleRegeneratePost}
            style={{
              flexGrow: 1,
              alignSelf: 'flex-end',
              marginBottom: space[2],
              marginHorizontal: space[4],
            }}
          />
        </View>
      )}

      {!isPromptInputFocused && (
        <MagicWandButton onPress={handleMagicWandPress}>
          <Icon.MagicWand size={space[5]} />
        </MagicWandButton>
      )}
    </Container>
  );
}
