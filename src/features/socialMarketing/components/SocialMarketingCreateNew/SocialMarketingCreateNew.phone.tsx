import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import {
  Button,
  Icon,
  Row,
  TextField,
  Typography,
  Card,
} from 'cube-ui-components';
import {
  mediaTypeAvatarVideoMP4,
  mediaTypeImagePNG,
  mediaTypeShortVideoMP4,
} from 'features/socialMarketing/assets/mediaTypes';
import { useCreatePost } from 'features/socialMarketing/hooks/usePosts';
import {
  SocialMarketingPlatform,
  SocialMarketingPostType,
  SocialMarketingRatio,
  SocialMarketingTopic,
} from 'features/socialMarketing/types';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList, View } from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import MediaTypeCardPhone from './MediaTypeCard.phone';
import RatioSelection from './RatioSelection';
import SocialPlatformIcon from './SocialPlatformIcon';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';

const Container = styled(SafeAreaView)(({ theme: { colors, space } }) => ({
  flex: 1,
  backgroundColor: colors.background,
  paddingHorizontal: space[4],
}));
const SheetIndicator = styled(View)(({ theme: { colors, space } }) => ({
  alignSelf: 'center',
  width: space[10],
  height: space[1],
  borderRadius: space[1],
  backgroundColor: colors.palette.fwdGrey[100],
  marginTop: space[2],
  marginBottom: space[8],
}));
const Label = styled(Typography.H7)(({ theme: { space } }) => ({
  marginVertical: space[2],
}));
const AITextBoxWrapper = styled(View)(({ theme: { colors, space } }) => ({
  flex: 1,
  backgroundColor: colors.background,
  borderRadius: space[4],
}));
const TopicItemContainer = styled.TouchableOpacity(
  ({ theme: { colors, space } }) => ({
    marginVertical: space[2],
    paddingVertical: space[1],
    paddingHorizontal: space[2],
    backgroundColor: colors.background,
    borderWidth: 1,
    borderColor: colors.palette.fwdOrange[100],
    borderRadius: space[6],
  }),
);
const TopicItemSeprator = styled.View(({ theme: { space } }) => ({
  width: space[2],
}));
const OptionContainer = styled(Row)(({ theme: { space } }) => ({
  alignItems: 'center',
  justifyContent: 'space-between',
  marginVertical: space[2],
  paddingVertical: space[2],
}));

const mockTopics: SocialMarketingTopic[] = [
  {
    id: '1',
    title: 'Medical benefit',
  },
  {
    id: '2',
    title: 'Family protection',
  },
  {
    id: '3',
    title: 'Accident benefit',
  },
  {
    id: '4',
    title: 'Accidents',
  },
];

export default function SocialMarketingCreateNewPhone() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { colors, space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { t } = useTranslation('socialMarketing');
  const createPost = useCreatePost();

  const [prompt, setPrompt] = useState('');
  const [selectedTopic, setSelectedTopic] = useState('');
  const [selectedMediaType, setSelectedMediaType] =
    useState<SocialMarketingPostType | null>(null);
  const [selectedPlatform, setSelectedPlatform] =
    useState<SocialMarketingPlatform>();
  const [selectedRatio, setSelectedRatio] = useState<SocialMarketingRatio>(
    SocialMarketingRatio.Square,
  );

  const isFormComplete = useMemo(() => {
    return (
      prompt.trim() !== '' &&
      selectedTopic.trim() !== '' &&
      selectedMediaType?.trim() !== '' &&
      !!selectedPlatform &&
      !!selectedRatio
    );
  }, [
    prompt,
    selectedTopic,
    selectedMediaType,
    selectedPlatform,
    selectedRatio,
  ]);

  const handleRenderTopicItem = useCallback(
    ({ item }: { item: SocialMarketingTopic }) => {
      return (
        <TopicItemContainer
          onPress={() => {
            setSelectedTopic(item.title);
          }}>
          <Typography.SmallLabel
            fontWeight="medium"
            color={colors.palette.fwdOrange[100]}>
            {item.title}
          </Typography.SmallLabel>
        </TopicItemContainer>
      );
    },
    [],
  );

  const handleCreatePost = useCallback(async () => {
    if (!selectedMediaType || !isFormComplete) {
      return;
    }

    // Close the current modal (presented as a stack screen)
    // This ensures that the screen appears on top of the base stack
    navigation.goBack();
    // Navigate to the review screen for post creation
    navigation.navigate('SocialMarketingPostCreationReview');

    await createPost({
      mediaType: selectedMediaType,
      prompt,
      topic: selectedTopic,
      ratio: selectedRatio,
      platform: selectedPlatform,
    });
  }, [
    selectedMediaType,
    prompt,
    selectedPlatform,
    selectedRatio,
    selectedTopic,
    navigation,
  ]);

  return (
    <BottomSheetModalProvider>
      <Container edges={['bottom']}>
        <SheetIndicator />
        <KeyboardAwareScrollView showsVerticalScrollIndicator={false}>
          <Typography.H6 fontWeight="bold">
            {t('createPost.title')}
          </Typography.H6>
          <View
            style={{
              marginVertical: space[3],
            }}>
            <Label fontWeight="medium">{t('createPost.chooseMediaType')}</Label>
            <Row alignItems="center" justifyContent="space-between">
              <MediaTypeCardPhone
                type={SocialMarketingPostType.Image}
                title={t('mediaType.image')}
                thumbnail={mediaTypeImagePNG}
                onSelect={setSelectedMediaType}
                isSelected={selectedMediaType === SocialMarketingPostType.Image}
              />
              <MediaTypeCardPhone
                type={SocialMarketingPostType.ShortVideo}
                title={t('mediaType.shortVideo')}
                thumbnail={mediaTypeShortVideoMP4}
                onSelect={setSelectedMediaType}
                isSelected={
                  selectedMediaType === SocialMarketingPostType.ShortVideo
                }
              />
              <MediaTypeCardPhone
                type={SocialMarketingPostType.AvatarVideo}
                title={t('mediaType.avatarVideo')}
                thumbnail={mediaTypeAvatarVideoMP4}
                onSelect={setSelectedMediaType}
                isSelected={
                  selectedMediaType === SocialMarketingPostType.AvatarVideo
                }
              />
            </Row>
          </View>
          <Label fontWeight="medium">{t('createPost.describeImage')}</Label>
          {/* TODO: Neon Linear gradient box will be implemented when it's added in cube-ui-components by Ryan */}
          <Card
            variant="gradient"
            style={{
              marginBottom: space[4],
              padding: space[1],
            }}>
            <AITextBoxWrapper>
              <TextField
                textarea
                value={prompt}
                onChangeText={setPrompt}
                placeholder={t('createPost.describeImagePlaceholder')}
                inputContainerStyle={{
                  borderRadius: space[4],
                  height: space[30],
                  borderWidth: 0,
                  paddingVertical: space[2],
                  paddingRight: space[1],
                }}
              />
              <Typography.SmallLabel
                fontWeight="medium"
                style={{
                  marginHorizontal: space[4],
                  marginTop: space[2],
                }}>
                {t('createPost.tryTheseTopics')}
              </Typography.SmallLabel>
              <FlatList
                horizontal
                data={mockTopics}
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  paddingHorizontal: space[4],
                  paddingBottom: space[2],
                }}
                keyExtractor={item => `topic-${item.id}`}
                renderItem={handleRenderTopicItem}
                ItemSeparatorComponent={() => <TopicItemSeprator />}
              />
            </AITextBoxWrapper>
          </Card>
          <OptionContainer>
            <Label fontWeight="medium">{t('createPost.platforms')}</Label>
            <Row alignItems="center" justifyContent="center">
              <SocialPlatformIcon
                onPress={setSelectedPlatform}
                platform={SocialMarketingPlatform.Instagram}
                isSelected={
                  selectedPlatform === SocialMarketingPlatform.Instagram
                }
              />
              <SocialPlatformIcon
                onPress={setSelectedPlatform}
                platform={SocialMarketingPlatform.Facebook}
                isSelected={
                  selectedPlatform === SocialMarketingPlatform.Facebook
                }
              />
              <SocialPlatformIcon
                onPress={setSelectedPlatform}
                platform={SocialMarketingPlatform.TikTok}
                isSelected={selectedPlatform === SocialMarketingPlatform.TikTok}
              />
            </Row>
          </OptionContainer>
          <OptionContainer>
            <Label fontWeight="medium">{t('createPost.imageRatio')}</Label>
            <RatioSelection
              onSelect={setSelectedRatio}
              selectedRatio={selectedRatio}
            />
          </OptionContainer>
        </KeyboardAwareScrollView>
        <KeyboardStickyView
          offset={{
            opened: bottom,
          }}
          style={{
            backgroundColor: colors.background,
          }}>
          <Button
            variant="primary"
            size="medium"
            disabled={!isFormComplete}
            icon={<Icon.AIStar />}
            onPress={handleCreatePost}
            style={{
              marginBottom: space[4],
            }}
            text={t('createPost.cta.generate')}
          />
        </KeyboardStickyView>
      </Container>
    </BottomSheetModalProvider>
  );
}
