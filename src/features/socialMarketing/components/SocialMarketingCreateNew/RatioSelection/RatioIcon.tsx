import React, { useMemo } from 'react';

import SocialPostRatioIconSVG from 'features/socialMarketing/assets/SocialPostRatioIconSVG';
import SocialStoryRatioIconSVG from 'features/socialMarketing/assets/SocialStoryRatioIconSVG';
import SquareRatioIconSVG from 'features/socialMarketing/assets/SquareRatioIconSVG';
import { SocialMarketingRatio } from 'features/socialMarketing/types';

const RatioIcon = ({ ratio }: { ratio: SocialMarketingRatio }) => {
  const IconComponent = useMemo(() => {
    switch (ratio) {
      case SocialMarketingRatio.Square:
        return SquareRatioIconSVG;
      case SocialMarketingRatio.SocialPost:
        return SocialPostRatioIconSVG;
      case SocialMarketingRatio.SocialStory:
        return SocialStoryRatioIconSVG;
      default:
        return null;
    }
  }, [ratio]);

  if (!IconComponent) {
    return null;
  }

  return <IconComponent />;
};

export default RatioIcon;
