import React from 'react';

import { useTheme } from '@emotion/react';
import { Icon, Typography } from 'cube-ui-components';
import useGetRatioLabel from 'features/socialMarketing/hooks/useGetRatioLabel';
import { SocialMarketingRatio } from 'features/socialMarketing/types';
import { TouchableOpacity, View } from 'react-native';
import RatioIcon from './RatioIcon';

export default function RatioSelectionItem({
  ratio,
  isSelected,
  onPress,
}: {
  isSelected: boolean;
  ratio: SocialMarketingRatio;
  onPress: (ratio: SocialMarketingRatio) => void;
}) {
  const label = useGetRatioLabel(ratio);
  const { colors, space } = useTheme();

  return (
    <TouchableOpacity
      onPress={() => onPress(ratio)}
      style={{
        paddingVertical: space[3],
        marginHorizontal: space[4],
        flexDirection: 'row',
        alignItems: 'center',
        borderBottomColor: colors.palette.fwdGrey[100],
        borderBottomWidth: 1,
      }}>
      <View
        style={{
          flex: 0.1,
          paddingHorizontal: space[1],
          alignSelf: 'center',
          alignItems: 'center',
        }}>
        <RatioIcon ratio={ratio} />
      </View>
      <Typography.LargeLabel
        style={{
          flex: 0.8,
          marginLeft: space[2],
        }}>
        {label}
      </Typography.LargeLabel>
      <View
        style={{
          flex: 0.1,
        }}>
        {isSelected && <Icon.Tick />}
      </View>
    </TouchableOpacity>
  );
}
