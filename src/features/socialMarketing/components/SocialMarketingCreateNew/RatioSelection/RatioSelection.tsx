import React, { useCallback, useRef } from 'react';

import { useTheme } from '@emotion/react';
import { BottomSheetBackdrop, BottomSheetModal } from '@gorhom/bottom-sheet';
import { Icon, Row, Typography } from 'cube-ui-components';
import useGetRatioLabel from 'features/socialMarketing/hooks/useGetRatioLabel';
import { SocialMarketingRatio } from 'features/socialMarketing/types';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import RatioIcon from './RatioIcon';
import RatioSelectionItem from './RatioSelectionItem';
import { RatioSelectionProps } from './types';

const SNAP_POINTS = ['50%'];

export default function RatioSelection(props: RatioSelectionProps) {
  const ref = useRef<BottomSheetModal>(null);
  const { t } = useTranslation('socialMarketing');
  const { colors, space } = useTheme();

  const label = useGetRatioLabel(props.selectedRatio);

  const handleOpenRatioSelector = useCallback(() => {
    ref.current?.present();
  }, []);

  const ratioOptions = [
    SocialMarketingRatio.Square,
    SocialMarketingRatio.SocialPost,
    SocialMarketingRatio.SocialStory,
  ];

  const handleDismissModal = useCallback(() => {
    ref.current?.dismiss();
  }, [ref]);

  const handleSelectRatio = useCallback(
    (ratio: SocialMarketingRatio) => {
      props.onSelect?.(ratio);
      handleDismissModal();
    },
    [props, handleDismissModal],
  );

  const renderBackdrop = useCallback(
    (backdropProps: any) => (
      <BottomSheetBackdrop
        {...backdropProps}
        onPress={handleDismissModal}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        pressBehavior="close"
      />
    ),
    [handleDismissModal],
  );

  return (
    <>
      <TouchableOpacity onPress={handleOpenRatioSelector}>
        <Row alignItems="center">
          <RatioIcon ratio={props.selectedRatio} />
          <Typography.LargeLabel
            style={{
              marginHorizontal: space[1],
            }}>
            {label}
          </Typography.LargeLabel>
          <Icon.ChevronRight
            fill={colors.palette.fwdGreyDarkest}
            size={space[4]}
          />
        </Row>
      </TouchableOpacity>

      <BottomSheetModal
        ref={ref}
        index={0}
        snapPoints={SNAP_POINTS}
        backdropComponent={renderBackdrop}>
        <Row
          style={{
            padding: space[4],
          }}
          justifyContent="space-between"
          alignItems="center">
          <TouchableOpacity onPress={handleDismissModal}>
            <Icon.ArrowLeft
              size={space[6]}
              fill={colors.palette.fwdDarkGreen[100]}
            />
          </TouchableOpacity>
          <Typography.H6 fontWeight="bold">
            {t('createPost.imageRatio')}
          </Typography.H6>
          <View style={{ width: space[6] }} />
        </Row>
        {ratioOptions.map(option => (
          <RatioSelectionItem
            key={option}
            ratio={option}
            isSelected={option === props.selectedRatio}
            onPress={handleSelectRatio}
          />
        ))}
      </BottomSheetModal>
    </>
  );
}
