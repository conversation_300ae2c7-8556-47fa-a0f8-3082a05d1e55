import React, { useCallback } from 'react';

import { useFocusEffect } from '@react-navigation/native';
import { setStatusBarStyle } from 'expo-status-bar';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import SocialMarketingCreateNewPhone from './SocialMarketingCreateNew.phone';

export default function SocialMarketingCreateNew() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  useFocusEffect(
    useCallback(() => {
      setStatusBarStyle('light');
      return () => {
        setStatusBarStyle('dark');
      };
    }, []),
  );

  return isTabletMode ? <></> : <SocialMarketingCreateNewPhone />;
}
