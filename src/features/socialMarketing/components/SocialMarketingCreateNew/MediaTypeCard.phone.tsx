import {
  View,
  Text,
  Dimensions,
  useWindowDimensions,
  TouchableOpacity,
} from 'react-native';
import React, { useCallback } from 'react';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { Image } from 'expo-image';
import { useVideoPlayer, VideoView } from 'expo-video';
import { SocialMarketingPostType } from 'features/socialMarketing/types';

type MediaTypeCardPhoneProps = {
  title: string;
  onSelect: (type: SocialMarketingPostType) => void;
  isSelected?: boolean;
  type: SocialMarketingPostType;
  thumbnail: string;
};

const Container = styled(TouchableOpacity)(({ theme: { colors, space } }) => ({
  flex: 1,
  justifyContent: 'center',
  alignItems: 'center',
}));
const CardContainer = styled(View)<{
  width: number;
  height: number;
  isSelected?: boolean;
}>(({ theme: { colors, space }, isSelected, width, height }) => ({
  width,
  height,
  backgroundColor: colors.palette.fwdOrange[50],
  borderRadius: space[4],
  borderWidth: 3,
  borderColor: isSelected ? colors.palette.fwdOrange[100] : 'transparent',
  justifyContent: 'center',
  alignItems: 'center',
  marginBottom: space[1],
  overflow: 'hidden',
}));

export default function MediaTypeCardPhone(props: MediaTypeCardPhoneProps) {
  const { space } = useTheme();
  const { width: screenWidth } = useWindowDimensions();

  const cardWidth = (screenWidth - space[14]) / 3; // 3 cards per row, adjusting for space
  const cardHeight = cardWidth * 0.9; // Maintain a 4:3 aspect ratio
  const isVideoType =
    props.type === SocialMarketingPostType.ShortVideo ||
    props.type === SocialMarketingPostType.AvatarVideo;
  const isImageType = props.type === SocialMarketingPostType.Image;

  const player = useVideoPlayer(isVideoType ? props.thumbnail : '', player => {
    player.muted = true;
    player.currentTime = 0;
    player.loop = true;
    player.play();
  });

  const renderMediaContent = useCallback(() => {
    if (isImageType) {
      return (
        <Image
          source={props.thumbnail}
          style={{
            width: cardWidth,
            height: cardHeight,
          }}
          contentFit="cover"
          cachePolicy="memory-disk"
        />
      );
    }

    if (isVideoType) {
      return (
        <VideoView
          player={player}
          nativeControls={false}
          style={{
            width: cardWidth,
            height: cardHeight,
          }}
          contentFit="cover"
        />
      );
    }

    return null;
  }, [
    screenWidth,
    space,
    isVideoType,
    isImageType,
    props.thumbnail,
    cardHeight,
    cardWidth,
  ]);

  return (
    <Container onPress={() => props.onSelect(props.type)} activeOpacity={1}>
      <CardContainer
        width={cardWidth}
        height={cardHeight}
        isSelected={props.isSelected}>
        {renderMediaContent()}
      </CardContainer>
      <Typography.Body>{props.title}</Typography.Body>
    </Container>
  );
}
