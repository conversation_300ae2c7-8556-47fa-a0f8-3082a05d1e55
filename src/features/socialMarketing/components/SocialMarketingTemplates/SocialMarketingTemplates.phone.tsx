import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import socialMarketingApi from 'api/socialMarketingApi';
import {
  SocialMarketingTemplate,
  type SocialMarketingCategory,
  type SocialMarketingMedia,
} from 'features/socialMarketing/types';
import { Typography } from 'cube-ui-components';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FlatList } from 'react-native';
import SocialMarketingFlatList from '../SocialMarketingFlatList';

const mockCategories: SocialMarketingCategory[] = [
  {
    id: '1',
    title: 'Seasonal',
  },
  {
    id: '2',
    title: 'Motivation',
  },
  {
    id: '3',
    title: 'Sales',
  },
  {
    id: '4',
    title: 'Life hacks',
  },
  {
    id: '5',
    title: 'Recruitment',
  },
];

const CategoryItemContainer = styled.TouchableOpacity<{ isSelected?: boolean }>(
  ({ theme: { colors, space }, isSelected }) => ({
    marginVertical: space[2],
    paddingVertical: space[1],
    paddingHorizontal: space[2],
    backgroundColor: isSelected
      ? colors.palette.fwdOrange[20]
      : colors.background,
    borderWidth: 1,
    borderColor: colors.palette.fwdOrange[100],
    borderRadius: 24,
  }),
);
const CategoryItemSeprator = styled.View(({ theme: { space } }) => ({
  width: space[2],
}));

export default function SocialMarketingTemplatesPhone() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('socialMarketing');

  const [isLoading, setIsLoading] = useState(false);
  const [mediaList, setMediaList] = useState<SocialMarketingMedia[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<
    SocialMarketingCategory[]
  >([]);
  const handleRenderCategoryItem = useCallback(
    ({ item }: { item: SocialMarketingCategory }) => (
      <CategoryItemContainer
        isSelected={selectedCategories.some(cat => cat.id === item.id)}
        onPress={() => {
          setSelectedCategories(prev =>
            prev.some(cat => cat.id === item.id)
              ? prev.filter(cat => cat.id !== item.id)
              : [...prev, item],
          );
        }}>
        <Typography.Body
          fontWeight="medium"
          color={colors.palette.fwdOrange[100]}>
          {item.title}
        </Typography.Body>
      </CategoryItemContainer>
    ),
    [selectedCategories],
  );

  useEffect(() => {
    setIsLoading(true);
    socialMarketingApi
      .getTemplates()
      .then(templates => {
        const mappedMediaList = templates
          .flatMap(
            (template: SocialMarketingTemplate) =>
              template.video || template.image,
          )
          .filter(media =>
            Boolean(media?.uid && media?.url),
          ) as SocialMarketingMedia[];

        setMediaList(mappedMediaList);
        setIsLoading(false);
      })
      .catch(error => {
        console.error('Failed to fetch social marketing templates:', error);
        setIsLoading(false);
      });
  }, []);

  return (
    <SocialMarketingFlatList
      data={mediaList}
      title={t('templates.title')}
      description={t('templates.description')}
      ExtraListHeaderComponent={
        <FlatList
          horizontal
          data={mockCategories}
          renderItem={handleRenderCategoryItem}
          ItemSeparatorComponent={() => <CategoryItemSeprator />}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{
            paddingHorizontal: space[4],
            paddingBottom: space[2],
          }}
          style={{
            marginHorizontal: -space[3],
          }}
        />
      }
    />
  );
}
