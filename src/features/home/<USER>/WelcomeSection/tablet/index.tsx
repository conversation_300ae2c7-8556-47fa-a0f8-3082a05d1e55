import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Row, Typography } from 'cube-ui-components';
import AddNewLeadIconV2SVG from 'features/home/<USER>/overview/AddLeadIconV2SVG';
import CreateSiShortcutV2SVG from 'features/home/<USER>/overview/CreateSiShortcutV2SCG';
import FNAShortcutV2SVG from 'features/home/<USER>/overview/FNAShortcutV2SVG';
import ReorderButton from 'features/home/<USER>/WelcomeSection/tablet/ReorderButton';
import AddNewLeadModal from 'features/lead/components/AddNewLeadModal';
import AddNewEntityForm from 'features/lead/tablet/components/AddNewEntityForm';
import AddNewLeadForm from 'features/lead/tablet/components/AddNewLeadForm';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import {
  MainTabParamList,
  RootStackParamList,
  TypesOfNewLeadForm,
} from 'types';
import { ShortcutsConfigArray } from 'types/home';
import GATracking from 'utils/helper/gaTracking';
import { countryModuleFnaConfig } from 'utils/config/module';
import { userPrivacyDialog } from 'features/lead/hooks/usePrivacyDialog';
import useToggle from 'hooks/useToggle';

export default function WelcomeSectionTablet() {
  const { colors, space, borderRadius, sizes } = useTheme();

  const { t } = useTranslation(['common', 'home', 'lead']);
  const { data: agentProfile } = useGetAgentProfile();
  const navigation =
    useNavigation<NavigationProp<RootStackParamList & MainTabParamList>>();

  const [addNewLeadModalVisible, showLeadModal, hideLeadModal] =
    useToggle(false);

  const [leadType, setLeadType] = useState<TypesOfNewLeadForm>('INDIVIDUAL');

  const onCloseAddLeadModal = () => {
    hideLeadModal();
  };

  const hasPermission = useHasPermission();
  const canWorkOnSi = hasPermission('si');

  const showLeadModalFn = userPrivacyDialog(showLeadModal);

  const SHORTCUTS_CONFIG: ShortcutsConfigArray = [
    {
      type: 'addNewLead',
      Icon: AddNewLeadIconV2SVG,
      IconInactive: AddNewLeadIconV2SVG,
      isDisabled: !hasPermission('lead'),
      label: t('home:home.shortcutSection.addNewLead.normal'),
      tabletLabel: t('home:home.shortcutSection.addNewLead.tablet'),
      onShortcutPress: () => {
        showLeadModalFn();
      },
    },
    {
      type: 'fna',
      Icon: FNAShortcutV2SVG,
      IconInactive: FNAShortcutV2SVG,
      isDisabled: !canWorkOnSi || !countryModuleFnaConfig.hasShortcut,
      label: t('home:home.shortcutSection.fna.normalV2'),
      tabletLabel: t('home:home.shortcutSection.fna.tabletV2'),
      onShortcutPress: () => {
        navigation.navigate('Fna');
        GATracking.logCustomEvent('fn_assessment', {
          form_source: 'homepage_cta',
          action_type: 'fna_open_form',
        });
      },
    },
    {
      type: 'createQuickQuote',
      Icon: CreateSiShortcutV2SVG,
      IconInactive: CreateSiShortcutV2SVG,
      isDisabled: !canWorkOnSi,
      label: t('home:home.shortcutSection.createQuickQuote.normal'),
      tabletLabel: t('home:home.shortcutSection.createQuickQuote.tablet'),
      onShortcutPress: () => navigation.navigate('CoverageDetailsScreen'),
    },
  ];

  return (
    <>
      <Box
        gap={space[3]}
        paddingY={space[4]}
        paddingX={space[6]}
        backgroundColor={colors.background}
        borderRadius={borderRadius['large']}>
        <Row justifyContent="space-between">
          <Typography.H4
            fontWeight="bold"
            numberOfLines={2}
            ellipsizeMode="tail"
            style={{
              flex: 1,
            }}>
            {t('common:home.welcome.hi', {
              name: agentProfile?.person?.firstName ?? '',
            })}
          </Typography.H4>
          <ReorderButton color={colors.primary} />
        </Row>
        <Typography.H7>{t('common:home.welcome.question')}</Typography.H7>
        <Row gap={space[5]}>
          {SHORTCUTS_CONFIG.map(sc => {
            return (
              <Box key={sc.type} flex={1} alignItems="center" gap={space[2]}>
                <TouchableOpacity
                  disabled={sc.isDisabled}
                  onPress={sc.onShortcutPress}
                  style={{
                    padding: space[2],
                    width: 72,
                    height: 72,
                    borderRadius: borderRadius['x-large'],
                    backgroundColor: colors.palette.fwdOrange[20],
                    opacity: sc.isDisabled ? 0.5 : 1,
                  }}>
                  {sc.Icon && <sc.Icon />}
                </TouchableOpacity>
                <Typography.Label
                  style={{
                    textAlign: 'center',
                  }}>
                  {sc.tabletLabel}
                </Typography.Label>
              </Box>
            );
          })}
        </Row>
      </Box>

      <AddNewLeadModal
        visible={addNewLeadModalVisible}
        leadType={leadType}
        IndividualForm={
          <AddNewLeadForm
            onClose={onCloseAddLeadModal}
            setLeadType={setLeadType}
          />
        }
        EntityForm={
          <AddNewEntityForm
            onClose={onCloseAddLeadModal}
            setLeadType={setLeadType}
          />
        }
      />
    </>
  );
}
