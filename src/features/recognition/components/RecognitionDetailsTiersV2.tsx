import { View } from 'react-native';
import React from 'react';
import { useTheme } from '@emotion/react';
import { Row, Typography, Icon, Box } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { MDRTTierRequirement } from 'features/recognition/type';
import { country } from 'utils/context';

export default function RecognitionDetailsTiersV2({
  tiersArray,
  activeYear,
}: {
  tiersArray: MDRTTierRequirement[];
  activeYear?: number;
}) {
  const { sizes, colors, borderRadius } = useTheme();

  const { t } = useTranslation('performance');

  return (
    <Box
      backgroundColor={colors.background}
      borderRadius={borderRadius.large}
      px={sizes[4]}
      py={sizes[6]}>
      <Typography.H7 fontWeight={'bold'} style={{ marginBottom: sizes[5] }}>
        {t('performance.recognition.achievedTiersWithYeah', {
          year: activeYear,
        })}
      </Typography.H7>
      <View style={{ gap: sizes[5] }}>
        {tiersArray.map((tier, index) => (
          <RecognitionTierWithRequirement
            key={'RecognitionTier' + tier.title + index}
            {...tier}
          />
        ))}
      </View>
    </Box>
  );
}

const RecognitionTier = ({
  icon,
  title,
  isComplete,
  isCurrentTier,
}: {
  icon: React.ReactNode;
  title: string;
  isComplete: boolean;
  isCurrentTier: boolean;
}) => {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation('performance');

  return (
    <Row style={{ alignItems: 'center', justifyContent: 'space-between' }}>
      <Row>
        {icon}
        <View
          style={{
            marginLeft: sizes[3],
            gap: sizes[1],
            justifyContent: 'center',
          }}>
          {isCurrentTier && (
            <Typography.H8 color={colors.palette.fwdGreyDarkest}>
              {t('performance.recognition.currentTier')}
            </Typography.H8>
          )}
          <Typography.H7 fontWeight="bold">{title}</Typography.H7>
        </View>
      </Row>
      {isComplete ? (
        <Row style={{ gap: 5 }}>
          <Icon.TickCircle fill={colors.palette.alertGreen} />
          <Typography.H7 color={colors.palette.alertGreen}>
            {t('performance.recognition.complete')}
          </Typography.H7>
        </Row>
      ) : (
        <Row style={{ gap: 5 }}>
          <Icon.TickCircle fill={colors.palette.fwdGreyDark} />
          <Typography.H7 color={colors.palette.fwdGreyDark}>
            {t('performance.recognition.Incomplete')}
          </Typography.H7>
        </Row>
      )}
    </Row>
  );
};

const RecognitionTierWithRequirement = ({
  icon,
  title,
  isComplete,
  isCurrentTier,
  requirement,
  isInProgress,
}: MDRTTierRequirement) => {
  const { sizes, colors } = useTheme();
  const { t } = useTranslation(['performance', 'common']);
  return (
    <>
      <Row alignItems="center" justifyContent="space-between">
        <Row>
          {icon}
          <Box ml={sizes[3]} gap={sizes[1]} justifyContent="center">
            {/* {country === 'ph' && isCurrentTier && (
              <Typography.LargeLabel color={colors.palette.fwdGreyDarkest}>
                {t('performance:performance.recognition.currentTier')}
              </Typography.LargeLabel>
            )} */}

            <Typography.LargeLabel fontWeight="bold">
              {title == 'FWDElite' ? 'FWD Elite' : title}
            </Typography.LargeLabel>

            {country !== 'ph' && (
              <Typography.LargeLabel>
                {'FYP: ' +
                  t('common:currencySymbol') +
                  ' ' +
                  numberToThousandsFormat(requirement ?? null)}
              </Typography.LargeLabel>
            )}
          </Box>
        </Row>
        {isComplete ? (
          <Row gap={sizes[1]} alignItems="center">
            <Icon.TickCircle fill={colors.palette.alertGreen} />
            <Typography.H7 color={colors.palette.alertGreen}>
              {t('performance:performance.recognition.complete')}
            </Typography.H7>
          </Row>
        ) : isCurrentTier && isInProgress ? (
          <Row gap={sizes[1]} alignItems="center">
            <Icon.Progress fill={colors.primary} />
            <Typography.H7 color={colors.primary}>
              {t('performance:performance.recognition.inprogress')}
            </Typography.H7>
          </Row>
        ) : (
          <Row gap={sizes[1]} alignItems="center">
            <Icon.TickCircle fill={colors.palette.fwdGreyDark} />
            <Typography.H7 color={colors.palette.fwdGreyDark}>
              {t('performance:performance.recognition.Incomplete')}
            </Typography.H7>
          </Row>
        )}
      </Row>
    </>
  );
};
