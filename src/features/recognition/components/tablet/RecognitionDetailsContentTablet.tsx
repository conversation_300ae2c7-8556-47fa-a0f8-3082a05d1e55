import { <PERSON>rollView } from 'react-native';
import React, { useCallback, useMemo, useState } from 'react';
import { Theme, useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRootStackNRoute } from 'hooks/useRootStack';
import {
  ProcPerformanceResponse,
  useGetAllPerformance,
  useGetTeamIndividualMDRT,
} from 'hooks/useGetPerformance';
import { MDRTSmallIcon } from 'features/recognition/assets/MDRTIcons';
import RecognitionDetailsMainSection from './RecognitionDetailsMainSection';
import RecognitionDetailsTiersV2 from '../RecognitionDetailsTiersV2';
import { Column, Row } from 'cube-ui-components';
import RecognitionDetailsRequirementTablet from './RecognitionDetailsRequirementTablet';
import { build, country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import RecognitionDetailsMainSectionFIB from 'features/recognition/ib/tablet/RecognitionDetailsMainSection';
import { Image } from 'expo-image';
import {
  MDRTTierRequirement,
  MdrtDetailsDataItem,
} from 'features/recognition/type';
import { MDRTTiers } from 'types/performance';
import FWDEliteImage from 'features/recognition/assets/FWDElite.png';
import TabButton from 'features/lead/tablet/components/TabButton';
import { RecognitionCategoryType } from 'features/recognition/RecognitionDetailsContent';
import {
  useIBInProgressTier,
  useTeamBasedMdrtHandler,
} from 'features/recognition/hooks/useMDRTTierCalculations';
import { getFIBMDRTTiersConfig } from '../MDRTTiersConfigs';

export default function RecognitionDetailsContentTablet() {
  const theme = useTheme();
  const { sizes, colors, borderRadius } = theme;
  const { bottom } = useSafeAreaInsets();
  const { isTabletMode } = useLayoutAdoptionCheck();
  const isMY = country === 'my';
  const isIB = country === 'ib';
  const isIbOrMy = isIB || isMY;
  const isIbOrIdOrMy = isIB || country === 'id' || isMY;

  const { isLoading, data } = useGetAllPerformance();
  const {
    params: { title: inProgressTier, agentCode, agentName },
  } = useRootStackNRoute<'RecognitionDetails'>();
  const { isLoading: isTeamMDRTLoading, data: teamMdrtDataObj } =
    useGetTeamIndividualMDRT({
      params: { isGetTeam: false },
      agentCode,
    });

  const [activeYearTabIndex, setActiveYearTabIndex] = useState(0);
  const isHistoricalAchievement = useMemo(() => {
    return activeYearTabIndex > 0;
  }, [activeYearTabIndex]);

  // title is inProgressTier
  const title = isIB
    ? useIBInProgressTier(teamMdrtDataObj, activeYearTabIndex)
    : inProgressTier;

  //fib
  const categoryByTiers = useCallback((): RecognitionCategoryType => {
    const mappings: Record<string, RecognitionCategoryType> =
      isHistoricalAchievement
        ? {
            MDRT: 'FWDElite',
            COT: 'MDRT',
            TOT: 'COT',
            '': 'TOT', // Completed 100% & Achieved TOT
            FWDElite: null, // No Achievement
            'FWD Elite': null, // No Achievement
          }
        : {
            MDRT: 'MDRT',
            COT: 'COT',
            TOT: 'TOT',
            '': 'TOT', // Completed 100% & Achieved TOT
            FWDElite: 'FWDElite',
            'FWD Elite': 'FWDElite',
          };
    return mappings[title] ?? null;
  }, [title, activeYearTabIndex]);

  //idn
  const categoryByTiersID = useMemo(() => {
    switch (title) {
      case 'MDRT':
      case 'COT':
      case 'TOT':
        return title; // this is the difference betewen id and ib
      case 'FWD Elite':
        return 'FWDElite';
      default:
        return null;
    }
  }, [title]);

  const isAtTopTier = title === 'TOT' || title === '';

  const teamMdrtData = teamMdrtDataObj?.mdrtRanking?.find((item, idx) => {
    if (isIB) return idx === activeYearTabIndex;

    const currentYear = new Date().getFullYear();
    // 2025 has no mdrt data yet
    const currentYearDForDev = new Date().getFullYear() - 1;

    //  Takaful use 1 year data while IB use 2 years data
    const targetYear = isMY
      ? currentYear
      : build === 'dev' || build === 'sit'
      ? currentYearDForDev
      : currentYear;

    return item.year === String(targetYear);
  });

  const mdrtDetailsData = mdrtDetailsDataHandler({
    data,
    title: title as MDRTTiers,
  });

  const mdrtDetailsDataTeam = useTeamBasedMdrtHandler({
    teamMdrtData,
    title: title as MDRTTiers,
  });

  // IB using mdrtDetailsData, Takaful using mdrtDetailsDataTeam, not sure what other countries are using
  const percent = isIB
    ? mdrtDetailsData?.percent ?? 0
    : isMY
    ? mdrtDetailsDataTeam?.percent
    : mdrtDetailsData.percent;

  const currentYear =
    parseInt(teamMdrtDataObj?.mdrtRanking?.[activeYearTabIndex]?.year ?? '') ||
    new Date().getFullYear();
  const nextYear = currentYear + 1;

  // phl?
  const MDRTTiersConfig = [
    {
      icon: <MDRTTierIcon />,
      title: 'MDRT',
      isComplete: title === 'COT' || title === 'TOT',
      isCurrentTier: title === 'MDRT',
      requirement: data?.mdrt?.mdrtLevel ?? 0,
    },
    {
      icon: <MDRTTierIcon />,
      title: 'COT',
      isComplete: title === 'TOT',
      isCurrentTier: title === 'COT',
      requirement: data?.mdrt?.cotLevel ?? 0,
    },
    {
      icon: <MDRTTierIcon />,
      title: 'TOT',
      isComplete: title === 'TOT' && percent >= 100,
      isCurrentTier: title === 'TOT',
      requirement: data?.mdrt?.totLevel ?? 0,
    },
  ] satisfies MDRTTierRequirement[];

  //idn
  const IDMDRTTiersConfig = [
    {
      icon: (
        <Image
          source={FWDEliteImage}
          contentFit={'contain'}
          style={{
            height: sizes[12],
            width: sizes[12],
            borderRadius: borderRadius['x-small'],
            borderWidth: 1,
            borderColor: colors.palette.fwdGrey[100],
          }}
        />
      ),
      title: 'FWDElite',
      isComplete: title === 'MDRT' || title === 'COT' || title === 'TOT',
      isCurrentTier: title === 'FWDElite',
      requirement: data?.eliteAgent?.level ?? 0,
    },
    ...MDRTTiersConfig.map(e => ({
      ...e,
      isInProgress:
        e.title === data?.mdrt?.nextTier &&
        data?.mdrt?.shortfallToNextTierPercent > 0,
    })),
  ] satisfies MDRTTierRequirement[];

  return (
    <ScrollView
      contentContainerStyle={{
        paddingTop: sizes[4],
        paddingBottom: bottom + sizes[4],
        paddingHorizontal: sizes[8],
      }}>
      {isIB && (
        <ScrollView horizontal style={{ paddingBottom: sizes[4] }}>
          <Row style={{ gap: sizes[2] }}>
            {teamMdrtDataObj?.mdrtRanking?.map((item, idx) => (
              <TabButton
                key={idx}
                isActive={activeYearTabIndex === idx}
                isActiveColor={colors.secondary}
                label={String(parseInt(item?.year) + 1)}
                onPress={() => {
                  setActiveYearTabIndex(idx);
                }}
              />
            ))}
          </Row>
        </ScrollView>
      )}
      <Row style={{ gap: sizes[6] }}>
        <Column style={{ flex: 6 }}>
          <RecognitionDetailsMainSectionFIB
            isAtTopTier={isAtTopTier}
            category={country === 'id' ? categoryByTiersID : categoryByTiers()}
            mdrtDetailsData={
              country === 'id'
                ? { ...mdrtDetailsData, rank: 0, totalAgent: 0 }
                : mdrtDetailsDataTeam
            }
            activeYear={currentYear}
            title={title}
            isHistoricalAchievement={isHistoricalAchievement}
            agentName={agentName}
          />
        </Column>

        <Column style={{ flex: 4, gap: sizes[3] }}>
          <RecognitionDetailsTiersV2
            tiersArray={
              isIbOrMy
                ? getFIBMDRTTiersConfig({
                    teamMdrtData,
                    title,
                    percent,
                  }).map(item => ({
                    ...item,
                    isInProgress:
                      Boolean(teamMdrtData?.mdrt?.fypCompleted) &&
                      !isHistoricalAchievement,
                  }))
                : country === 'id'
                ? IDMDRTTiersConfig
                : MDRTTiersConfig
            }
            activeYear={nextYear}
          />

          {isIbOrIdOrMy ? null : (
            <RecognitionDetailsRequirementTablet
              MDRTRequirementConfig={mdrtRequirementConfigHandler({
                data,
                isTabletMode,
                theme,
              })}
            />
          )}
        </Column>
      </Row>
    </ScrollView>
  );
}

const MDRTTierIcon = () => {
  const { colors, borderRadius } = useTheme();

  return (
    <MDRTSmallIcon
      containerStyle={{
        borderWidth: 1,
        borderColor: colors.palette.fwdGrey[100],
        borderRadius: borderRadius['x-small'],
      }}
    />
  );
};

const MDRTRequirementIcon = ({
  customIconSize,
}: {
  customIconSize: number;
}) => {
  const { colors, borderRadius } = useTheme();

  return (
    <MDRTSmallIcon
      containerStyle={{
        borderWidth: 1,
        borderColor: colors.palette.fwdGrey[100],
        borderRadius: borderRadius['x-small'],
      }}
      customIconSize={customIconSize}
    />
  );
};

const mdrtDetailsDataHandler = ({
  data,
  title,
}: {
  data: ProcPerformanceResponse | undefined;
  title: MDRTTiers;
}): MdrtDetailsDataItem => {
  switch (title) {
    case 'MDRT':
      return {
        percent: data?.mdrt?.shortfallToMDRTPercent
          ? 100 - data?.mdrt?.shortfallToMDRTPercent
          : 0,
        completion: data?.mdrt?.total ?? 0,
        shortfall: data?.mdrt?.shortfallToMDRT ?? 0,
        target: data?.mdrt?.mdrtLevel ?? 0,
      };
    case 'COT':
      return {
        percent: data?.mdrt?.shortfallToCOTPercent
          ? 100 - data?.mdrt?.shortfallToCOTPercent
          : 0,
        completion: data?.mdrt?.total ?? 0,
        shortfall: data?.mdrt?.shortfallToCOT ?? 0,
        target: data?.mdrt?.cotLevel ?? 0,
      };
    case 'TOT':
      return {
        percent: data?.mdrt?.shortfallToTOTPercent
          ? 100 - data?.mdrt?.shortfallToTOTPercent
          : 0,
        completion: data?.mdrt?.total ?? 0,
        shortfall: data?.mdrt?.shortfallToTOT ?? 0,
        target: data?.mdrt?.totLevel ?? 0,
      };
    default:
      return {
        percent: 0,
        completion: 0,
        shortfall: 0,
        target: 0,
      };
  }
};

function mdrtRequirementConfigHandler({
  data,
  isTabletMode,
  theme,
}: {
  data: ProcPerformanceResponse | undefined;
  isTabletMode: boolean;
  theme: Theme;
}) {
  const { borderRadius, colors, space } = theme;

  const iconSize = isTabletMode ? space[11] : space[6];

  const TakafulMDRTRequirementConfig = [
    {
      icon: <MDRTRequirementIcon customIconSize={iconSize} />,
      title: 'TOT',
      fyp: data?.mdrt?.totLevelFYP ?? 0,
      fyc: data?.mdrt?.totLevelFYC ?? 0,
    },
    {
      icon: <MDRTRequirementIcon customIconSize={iconSize} />,
      title: 'COT',
      fyp: data?.mdrt?.cotLevelFYP ?? 0,
      fyc: data?.mdrt?.cotLevelFYC ?? 0,
    },
    {
      icon: <MDRTRequirementIcon customIconSize={iconSize} />,
      title: 'MDRT',
      fyp: data?.mdrt?.mdrtLevelFYP ?? 0,
      fyc: data?.mdrt?.mdrtLevelFYC ?? 0,
    },
  ];

  switch (country) {
    case 'my':
      return TakafulMDRTRequirementConfig;
    default:
      return TakafulMDRTRequirementConfig.reverse();
  }
}
