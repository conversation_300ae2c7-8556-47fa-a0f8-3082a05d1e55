import { View } from 'react-native';
import React from 'react';
import { Row, Typography, H6, Box } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { RecognitionCategory } from 'features/recognition/RecognitionDetailsContent';
import {
  MDRTMediumIcon,
  NoAchievementIcon,
} from 'features/recognition/assets/MDRTIcons';
import { useTranslation } from 'react-i18next';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import AchievementBar from '../AchievementBar';
import RecognitionAchievedV2 from 'features/recognition/assets/RecognitionAchievedV2';
import RecognitionInProgressV2 from 'features/recognition/assets/RecognitionInProgressV2';
import { Image } from 'expo-image';
import { MdrtDetailsDataItem, MdrtRankData } from 'features/recognition/type';
import FWDEliteImage from 'features/recognition/assets/FWDElite.png';
import { country } from 'utils/context';
import HistoricalAchievementBox from './HistoricalAchievementBox';
import CurrentRankSection from '../CurrentRankSection';
import RecognitionAchievedSuccessMY from 'features/recognition/assets/RecognitionAchievedMYSuccess';
import RecognitionAchievedMY from 'features/recognition/assets/RecognitionAchievedMY';

export default function RecognitionDetailsMainSection({
  isAtTopTier,
  category,
  mdrtDetailsData,
  activeYear,
  title,
  isHistoricalAchievement,
  agentName,
}: {
  mdrtDetailsData: MdrtDetailsDataItem & MdrtRankData;
  isAtTopTier: boolean;
  activeYear?: number;
  title: string;
  isHistoricalAchievement: boolean;
  agentName?: string | null;
} & RecognitionCategory) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('performance');

  const percent = mdrtDetailsData?.percent ?? 0;

  const isAchieved = percent >= 100;
  const isAllDone = isAchieved && isAtTopTier;

  const agentRank = mdrtDetailsData.rank;
  const totalActiveAgents = mdrtDetailsData.totalAgent;

  const isRankShown = agentRank != totalActiveAgents && Boolean(percent);

  return (
    <ContentContainer>
      <Row alignItems="center" minHeight={sizes[21]} marginBottom={space[4]}>
        <HeaderIcon category={category} isAllDone={isAllDone} />
        <Box pl={space[4]} mr={28} flex={3}>
          {isHistoricalAchievement ? (
            <Typography.ExtraLargeBody
              fontWeight={isAllDone ? 'bold' : 'medium'}
              color={colors.palette.fwdDarkGreen[100]}>
              {t('performance.recognition.achieved')}
            </Typography.ExtraLargeBody>
          ) : (
            <Typography.ExtraLargeBody
              fontWeight={isAllDone ? 'bold' : 'medium'}
              color={colors.palette.fwdDarkGreen[100]}>
              {agentName}
              {isAllDone
                ? t('performance.recognition.congratulation')
                : t('performance.recognition.yourNextTierWillBe')}
            </Typography.ExtraLargeBody>
          )}

          <Typography.H4
            fontWeight="bold"
            color={
              category
                ? colors.palette.fwdDarkGreen[100]
                : colors.palette.fwdGreyDarker
            }>
            {category
              ? t(`performance.recognition.${category}`)
              : t(`performance.recognition.NoAchievement`)}
          </Typography.H4>
        </Box>
        <Box flex={1} h={'100%'}>
          <AchievementIcon isAchieved={isAchieved} />
        </Box>
      </Row>
      <AchievementText fontWeight="bold">
        {t('performance.recognition.overAllAchievementWithYear', {
          year: activeYear,
        })}
      </AchievementText>

      {isHistoricalAchievement ? (
        <HistoricalAchievementBox completion={mdrtDetailsData.completion} />
      ) : (
        <AchievementBar
          percent={percent}
          completion={mdrtDetailsData.completion}
          shortfall={mdrtDetailsData.shortfall}
          target={mdrtDetailsData.target}
        />
      )}
      {/* DMS not ready */}
      {/* {country === 'ib' &&
        !isHistoricalAchievement &&
        mdrtDetailsData?.completion > 0 && (
          <CurrentRankSection
            isRankShown={isRankShown}
            agentRank={agentRank}
            totalActiveAgents={totalActiveAgents}
          />
        )} */}
    </ContentContainer>
  );
}

const HeaderIcon = ({
  category,
  isAllDone,
}: {
  category: RecognitionCategory['category'];
  isAllDone: boolean;
}) => {
  if (category === 'FWDElite') {
    return (
      <Image
        source={FWDEliteImage}
        contentFit={'contain'}
        style={{
          height: 100,
          width: 100,
        }}
      />
    );
  }
  if (
    category === 'MDRT' ||
    category === 'COT' ||
    category === 'TOT' ||
    isAllDone
  ) {
    return <MDRTMediumIcon customIconSize={96} />;
  }
  return <NoAchievementIcon customIconSize={96} />;
};

const AchievementIcon: React.FC<{ isAchieved: boolean }> = ({ isAchieved }) => {
  const isPH = country === 'ph';
  const isMY = country === 'my';
  return (
    <Box>
      <Box
        pos="absolute"
        right={isAchieved ? -28 : 0}
        top={isAchieved ? -48 : isMY ? -40 : isPH ? -40 : -56}
        zIndex={10}>
        {isMY ? (
          isAchieved ? (
            <RecognitionAchievedSuccessMY />
          ) : (
            <RecognitionAchievedMY />
          )
        ) : isAchieved ? (
          <RecognitionAchievedV2 />
        ) : (
          <RecognitionInProgressV2 />
        )}
      </Box>
    </Box>
  );
};

const HeaderContentRow = styled(Row)(({ theme: { sizes, colors } }) => ({
  alignItems: 'center',
  justifyContent: 'space-between',
}));

const ContentContainer = styled(View)(({ theme: { sizes, colors } }) => ({
  paddingHorizontal: sizes[7],
  paddingTop: sizes[8],
  paddingBottom: sizes[6],
  backgroundColor: colors.background,
  borderRadius: sizes[4],
}));

const AchievementText = styled(H6)(({ theme: { sizes } }) => ({}));
