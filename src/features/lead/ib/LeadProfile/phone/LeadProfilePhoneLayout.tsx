import { useTheme } from '@emotion/react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { NavigationProp, RouteProp } from '@react-navigation/native';
import AppTopTabBar from 'components/AppTopTabBar';
import { addToast } from 'cube-ui-components';
import ExistingPolicyScreen from 'features/lead/components/LeadPolicies/ExistingPolicyScreen';
import LeadInfoSection, {
  ShowLeadInfoHandle,
} from 'features/lead/components/LeadProfile/LeadInfoSection';
import LeadProfileBottomBar from 'features/lead/components/LeadProfile/utils/LeadProfileBottomBar';
import useClearCaseFocusEffect from 'features/lead/hooks/useClearCaseFocusEffect';
import ScrollScreenWrapper from 'features/lead/ph/LeadProfile/components/ScrollScreenWrapper';
import ActivitiesScreen from 'features/lead/ph/LeadProfile/phone/ActivitiesScreen';
import OpportunitiesScreen from 'features/lead/ph/LeadProfile/phone/OpportunitiesScreen';
import ProposalsScreen from 'features/lead/ph/LeadProfile/phone/ProposalsScreen';
import RpqScreen from 'features/lead/ph/LeadProfile/phone/RpqScreen';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCustomerDetailsByCustomerId } from 'hooks/useGetCustomerDetails';
import { useGetLeadByCustomerId, useGetLeadByLeadId } from 'hooks/useGetLeads';
import { t } from 'i18next';
import { headerDefaultTitleMap } from 'navigation/components/ScreenHeader/constant';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { useEffect, useRef } from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LeadProfileTabParamList, RootStackParamList } from 'types';
import { PartyType } from 'types/party';
import { build, country } from 'utils/context';
import CustomerInfoSection from './CustomerInfoSection';
import FnaScreen from './FnaScreen';
import LeadInfoSectionV2 from 'features/lead/ph/LeadProfile/phone/LeadInfoSectionV2';

export default function LeadProfilePhoneLayout({
  navigation,
  route,
}: {
  navigation: NavigationProp<RootStackParamList, 'LeadProfile'>;
  route: RouteProp<RootStackParamList, 'LeadProfile'>;
}) {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const LeadProfileTab =
    createMaterialTopTabNavigator<LeadProfileTabParamList>();

  const updateInquiringLeadId = useBoundStore(
    store => store.leadProfileActions.updateInquiringLeadId,
  );

  const { setAppLoading, setAppIdle } = useBoundStore(
    store => store.appActions,
  );

  const showLeadInfoRef = useRef<ShowLeadInfoHandle>(null);
  const showLeadInfo = () => showLeadInfoRef.current?.showComponent();
  const hideLeadInfo = () => showLeadInfoRef.current?.hideComponent();

  const {
    id: leadId,
    customerId: customerIdParam,
    isIndividualLead,
  } = route.params;

  /*
    need to reset the active case when re-entering the lead profile
    to avoid using it to create a new proposal
  */
  useClearCaseFocusEffect();

  // Set lead ID to Zustand
  useEffect(() => {
    updateInquiringLeadId(leadId);
    return () => updateInquiringLeadId('');
  }, [leadId]);

  const {
    isLoading: isLoadingLead,
    isError: isErrorLead,
    data: dataLead,
  } = useGetLeadByCustomerId(customerIdParam);

  useEffect(() => {
    if (dataLead && dataLead?.data[0]) {
      navigation.setParams({
        id: dataLead?.data[0].id,
      });
    }
  }, [dataLead]);

  const { data: customerProfile } =
    useGetCustomerDetailsByCustomerId(customerIdParam);

  const { isLoading, isError, data: lead } = useGetLeadByLeadId(leadId);

  // Add toast for new lead
  useEffect(() => {
    if (!customerIdParam && lead?.createdAt) {
      const createdAtDate = new Date(lead?.createdAt);
      const now = new Date();
      const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      const isNewLead = createdAtDate >= twentyFourHoursAgo;

      isNewLead &&
        addToast(
          [{ message: 'Brand new lead added with in 24 hours' }],
          false,
          true,
        );
    }
  }, [lead]);

  useEffect(() => {
    isLoading ? setAppLoading() : setAppIdle();
  }, [isLoading]);

  // * Customer profile data
  const customerFullName = customerProfile?.fullName ?? '--';

  // * Lead profile data
  const leadFirstName = lead?.firstName ? lead?.firstName + ' ' : '';
  const leadMiddleName = lead?.middleName ? lead?.middleName + ' ' : '';
  const leadLastName = lead?.lastName ? lead?.lastName + ' ' : '';
  const leadFullName = leadFirstName + leadMiddleName + leadLastName;

  const customerProfileTabs: Array<{
    key: keyof LeadProfileTabParamList;
    label: string;
    Screen: JSX.Element;
  }> = [
    {
      key: 'ExistingPolicy',
      label: 'Existing Policy',
      Screen: (
        <ScrollScreenWrapper
          screen={<ExistingPolicyScreen customerID={customerIdParam} />}
          showLeadInfo={showLeadInfo}
          hideLeadInfo={hideLeadInfo}
        />
      ),
    },
  ];

  if (isLoading) return <></>;

  return (
    <View
      style={{
        flex: 1,
        paddingBottom: insets.bottom,
        backgroundColor: theme.colors.background,
      }}>
      <View style={{ zIndex: 999 }}>
        <ScreenHeader
          route={'LeadProfile'}
          customTitle={
            customerIdParam
              ? customerFullName
                ? customerFullName
                : headerDefaultTitleMap['CustomerProfileDetails']
              : t('leadProfile:leadProfile.leadDetail')
          }
          isLeftArrowBackShown
          showBottomSeparator={false}
          customTitleStyle={{ maxWidth: '70%' }}
        />
      </View>

      {customerIdParam ? (
        <CustomerInfoSection
          ref={showLeadInfoRef}
          customerProfile={customerProfile}
        />
      ) : (
        <LeadInfoSectionV2
          ref={showLeadInfoRef}
          isIndividualLead={isIndividualLead}
          isCustomer={false}
          customerId={undefined}
          isMarketingLead={true}
        />
      )}

      <LeadProfileTab.Navigator
        tabBar={props => {
          if (!!customerIdParam && customerProfileTabs.length == 1) {
            return <></>;
          }
          return <AppTopTabBar variant="scrollable" {...props} />;
        }}>
        {/* //* Tabs shown for Customer Profile only */}
        {customerIdParam ? (
          customerProfileTabs.map((tab, idx) => (
            <LeadProfileTab.Screen
              key={tab.key + idx}
              name={tab.key}
              children={() => tab.Screen}
              options={{
                tabBarLabel: tab.label,
              }}
            />
          ))
        ) : (
          <></>
        )}
        {/* Tabs shown for Lead Profile only */}
        {/* Some tabs may have data available later */}
        {customerIdParam ? (
          <></>
        ) : (
          <>
            {build === 'dev' && lead?.isIndividual && country === 'ph' && (
              <LeadProfileTab.Screen
                name="Opportunities"
                children={() => (
                  <ScrollScreenWrapper
                    screen={<OpportunitiesScreen leadId={leadId} />}
                    showLeadInfo={showLeadInfo}
                    hideLeadInfo={hideLeadInfo}
                  />
                )}
              />
            )}
            {lead?.isIndividual && country !== 'id' && (
              <LeadProfileTab.Screen
                name="FNA"
                options={{ title: t('navigation:tabScreen.FNA') }}
                children={() => (
                  <ScrollScreenWrapper
                    screen={<FnaScreen leadId={leadId} />}
                    showLeadInfo={showLeadInfo}
                    hideLeadInfo={hideLeadInfo}
                  />
                )}
              />
            )}
            {country !== 'id' && (
              <LeadProfileTab.Screen
                name="RPQ"
                children={() => (
                  <ScrollScreenWrapper
                    screen={<RpqScreen leadId={leadId} />}
                    showLeadInfo={showLeadInfo}
                    hideLeadInfo={hideLeadInfo}
                  />
                )}
              />
            )}
            {country !== 'id' && (
              <LeadProfileTab.Screen
                name="Proposals"
                children={() => (
                  <ScrollScreenWrapper
                    screen={
                      <ProposalsScreen
                        leadId={leadId}
                        clientType={
                          lead && !lead.isIndividual
                            ? PartyType.ENTITY
                            : PartyType.INDIVIDUAL
                        }
                      />
                    }
                    showLeadInfo={showLeadInfo}
                    hideLeadInfo={hideLeadInfo}
                  />
                )}
              />
            )}
            <LeadProfileTab.Screen
              name="Activities"
              children={() => (
                <ScrollScreenWrapper
                  screen={<ActivitiesScreen leadId={leadId} />}
                  showLeadInfo={showLeadInfo}
                  hideLeadInfo={hideLeadInfo}
                />
              )}
            />
          </>
        )}
      </LeadProfileTab.Navigator>
      {customerIdParam ? (
        <LeadProfileBottomBar customerId={customerIdParam} />
      ) : (
        <LeadProfileBottomBar leadId={leadId} />
      )}
    </View>
  );
}
