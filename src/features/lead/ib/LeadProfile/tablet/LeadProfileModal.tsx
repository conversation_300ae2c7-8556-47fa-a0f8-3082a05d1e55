import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import React, { memo, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, TouchableOpacity, View } from 'react-native';
import { TransactionsInLead } from 'types';
import {
  Box,
  Column,
  Row,
  Typography,
  Text,
  PictogramIcon,
} from 'cube-ui-components';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { Close } from 'cube-ui-components/dist/cjs/icons';

type DetailData = {
  label?: string;
  content?: string;
};

export interface LeadProfileModalProps {
  isModalVisible: boolean;
  personalInfoData: DetailData[];
  contactDetailData: DetailData[];
  referralDetailData?:
    | {
        label?: string;
        infoModel?: JSX.Element;
        content?: string | JSX.Element;
      }[]
    | undefined;
  nationalityDetailData: DetailData[];
  occupationDetailData: DetailData[];
  addressInfoData: DetailData[];
  activityList: TransactionsInLead[] | undefined;
  onPressClose: () => void;
}

export const LeadProfileModal = memo(function LeadProfileModal({
  isModalVisible,
  personalInfoData,
  contactDetailData,
  nationalityDetailData,
  occupationDetailData,
  addressInfoData,
  activityList,
  onPressClose,
}: LeadProfileModalProps) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);

  const sections = useMemo<DetailsItemProps[]>(() => {
    return [
      {
        title: t('leadProfile:leadProfile.profileDetails.personalDetails'),
        icon: <PictogramIcon.ManWithShield size={36} />,
        data: personalInfoData,
      },
      {
        title: t('leadProfile:leadProfile.profileDetails.nationalityDetails'),
        icon: <PictogramIcon.Earth size={36} />,
        data: nationalityDetailData,
      },
      {
        title: t('leadProfile:leadProfile.profileDetails.occupationDetails'),
        icon: <PictogramIcon.Briefcase size={36} />,
        data: occupationDetailData,
      },
      {
        title: t('leadProfile:leadProfile.profileDetails.contactDetails'),
        icon: <PictogramIcon.ActivePhoneCall size={36} />,
        data: contactDetailData,
      },
      {
        title: t('leadProfile:leadProfile.profileDetails.addressInformation'),
        icon: <PictogramIcon.House2 size={36} />,
        data: addressInfoData,
      },
    ].filter(i => i.data.length > 0);
  }, [
    addressInfoData,
    contactDetailData,
    nationalityDetailData,
    occupationDetailData,
    personalInfoData,
    t,
  ]);

  return (
    <Modal visible={isModalVisible} transparent={true} animationType="fade">
      <ModalMainContainer>
        <ModalBody>
          <Row justifyContent="flex-end">
            <TouchableOpacity onPress={onPressClose}>
              <Close size={sizes[6]} fill={colors.secondary} />
            </TouchableOpacity>
          </Row>
          <ScrollView>
            <Box px={space[6]} flex={1}>
              {sections.map((section, index) => (
                <DetailsItem {...section} key={index} index={index} />
              ))}
            </Box>
          </ScrollView>
        </ModalBody>
      </ModalMainContainer>
    </Modal>
  );
});
export default LeadProfileModal;

interface DetailsItemProps {
  title: string | undefined;
  icon: JSX.Element | undefined;
  data: DetailData[];
}
const DetailsItem = memo(
  ({ icon, title, data, index }: DetailsItemProps & { index: number }) => {
    const { space } = useTheme();
    return (
      <>
        {index > 0 && <Divider />}
        <Box style={{ flexBasis: 'auto' }}>
          <Column>
            {(!!icon || !!title) && (
              <Row gap={space[2]} mb={space[6]} alignItems="center">
                {!!icon && <Row alignItems="center">{icon}</Row>}
                {!!title && <Title fontWeight="bold">{title}</Title>}
              </Row>
            )}
            <Row flexWrap="wrap">
              {data?.map((item, index) => (
                <ProfileDetailField
                  columnNum={4}
                  label={item.label}
                  key={item.label ? item.label : index}
                  dataLength={data?.length}
                  content={item.content ? item.content : ' '}
                  idx={index}
                />
              ))}
            </Row>
          </Column>
        </Box>
      </>
    );
  },
);

export const ModalMainContainer = styled(View)(({ theme: { space } }) => ({
  flex: 1,
  backgroundColor: '#000000aa',
  paddingHorizontal: space[20],
  paddingVertical: space[24],
  justifyContent: 'center',
}));

export const ModalBody = styled(View)(
  ({ theme: { borderRadius, space, colors } }) => ({
    flex: 1,
    backgroundColor: colors.palette.white,
    borderRadius: borderRadius.large,
    paddingHorizontal: space[6],
    paddingTop: space[6],
    paddingBottom: space[12],
  }),
);

const Divider = styled(View)(({ theme: { colors, space } }) => ({
  marginBottom: space[6],
  height: 1,
  backgroundColor: colors.palette.fwdGreyDark,
}));

const ProfileDetailField = ({
  label,
  content,
  columnNum,
  dataLength,
  idx,
}: {
  label?: string;
  content: string;
  columnNum: number;
  dataLength: number;
  idx: number;
}) => {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);

  return (
    <Column
      width={
        idx == dataLength - 1 && dataLength % columnNum != 0
          ? `${
              (100 / columnNum) *
              (round(dataLength, columnNum) - (dataLength - 1))
            }%`
          : `${100 / columnNum}%`
      }
      // height={
      //   dataLength % columnNum != 0
      //     ? `${(100 / round(dataLength, columnNum)) * columnNum}%`
      //     : `${(100 / dataLength) * columnNum}%`
      // }
      paddingBottom={space[6]}>
      <Typography.Body color={colors.placeholder}>{label}</Typography.Body>

      <Typography.Body color={colors.secondary}>
        {content ? content : '--'}
      </Typography.Body>
    </Column>
  );
};

const round = (number: number, step: number) => {
  return Math.ceil(number / step) * step;
};

const Title = styled(Text)({
  fontSize: 22,
  lineHeight: 28,
});
