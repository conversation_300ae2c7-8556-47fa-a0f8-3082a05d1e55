import useGetNatureOfBusinessOption from 'features/lead/hooks/useGetNatureOfBusinessOption';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Lead } from 'types';
import { CustomerDetails } from 'types/customerDetails';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import getLabelFromValue from 'utils/helper/getLabelFromValue';
import { differenceInYears } from 'date-fns';
import { country } from 'utils/context';
import LeadScore from 'features/lead/components/LeadProfile/LeadScore';
import { getSourceLabels } from 'features/lead/utils';
import _ from 'lodash';
import ScoreInfoModal from 'features/lead/components/LeadProfile/ScoreInfoModal';

export function useMappedLeadProfile({
  lead,
  customerDetails,
  isIndividualLead,
  dobTypes = 'withAge',
  mode = 'normal',
}: {
  lead: Lead | undefined;
  customerDetails?: CustomerDetails | undefined;
  isIndividualLead: boolean | undefined;
  dobTypes?: 'withAge' | 'withoutAge' | 'standaloneAgeField';
  mode?: 'normal' | 'full';
}) {
  if (customerDetails == undefined) {
    console.log('--- useMappedLeadProfile ---- customerDetails is undefined');
  }
  const { t } = useTranslation(['lead', 'leadProfile', 'proposal']);
  const { data: optionList } = useGetOptionList();
  const natureOfBusinessOptions = useGetNatureOfBusinessOption();

  const leadSource = lead?.sourceIds ?? [];
  const mappedLeadSource = getSourceLabels(leadSource)
    .map(label => t(`lead:source.${label}`))
    .join(', ');
  const interestProduct = !_.isEmpty(lead?.interestedProducts)
    ? lead?.interestedProducts?.join(',\n')
    : !_.isEmpty(lead?.interestedCategories)
    ? lead?.interestedCategories?.join(', ')
    : undefined;

  const occupation = lead
    ? lead?.occupationCode ?? lead?.occupationGroupCode
    : customerDetails?.occupationDesc;

  const mappedOccupationFromGroup = useMemo(
    () =>
      optionList?.OCCUPATION_GROUP?.options.find(
        occuGrp => occuGrp.value === occupation,
      )?.label,
    [occupation, optionList?.OCCUPATION_GROUP?.options],
  );
  const mappedOccupationFromSubGroup = useMemo(
    () =>
      optionList?.OCCUPATION_SUBGROUP?.options.find(
        occuSubGrp => occuSubGrp.value === occupation,
      )?.label,
    [occupation, optionList?.OCCUPATION_SUBGROUP?.options],
  );

  const mappedOccupation = useMemo(
    () =>
      optionList?.OCCUPATION?.options.find(
        occuGrp => occuGrp.value === occupation,
      )?.label,
    [occupation, optionList?.OCCUPATION?.options],
  );

  const generalOccupationLabel =
    occupation ??
    mappedOccupationFromGroup ??
    mappedOccupationFromSubGroup ??
    '--';
  const occupationLabel =
    country === 'id'
      ? mappedOccupation ?? generalOccupationLabel
      : generalOccupationLabel;

  const natureOfBusinessLabel = useMemo(() => {
    return (
      natureOfBusinessOptions.find(
        item => item.value === lead?.occupationIndustryCode,
      )?.label ?? '--'
    );
  }, [natureOfBusinessOptions, lead?.occupationIndustryCode]);

  //-----------------------  contactDetailData -----------------------
  const normalLeadContactDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: lead?.mobilePhoneCountryCode
        ? `+${lead?.mobilePhoneCountryCode} ${lead?.mobilePhoneNumber}`
        : lead?.mobilePhoneNumber ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: lead?.email || '--',
    },
  ];

  const fullLeadContactDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: lead?.mobilePhoneCountryCode
        ? `+${lead?.mobilePhoneCountryCode} ${lead?.mobilePhoneNumber}`
        : lead?.mobilePhoneNumber ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: lead?.email || '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.preferredContactMode'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.correspondenceViaEmail'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.homePhone'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.officePhone'),
      content: '--',
    },
  ];

  const leadContactDetailData =
    mode === 'full' ? fullLeadContactDetailData : normalLeadContactDetailData;

  const customerContactDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: customerDetails?.mobileNumber
        ? formatPhoneNumber(customerDetails?.mobileNumber)
        : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: customerDetails?.emailAddress ?? '--',
    },
  ];

  const entityContactDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.businessPhoneNumber'),
      content: lead?.mobilePhoneCountryCode
        ? `+${lead?.mobilePhoneCountryCode} ${lead?.mobilePhoneNumber}`
        : lead?.mobilePhoneNumber ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: lead?.email ?? '--',
    },
    // {
    //   label: t('leadProfile:leadProfile.profileDetails.preferredContactMode'),
    //   content: '--',
    // },
    // {
    //   label: t(
    //     'leadProfile:leadProfile.profileDetails.preferredDocumentLanguage',
    //   ),
    //   content: '--',
    // },
  ];

  const contactDetailData =
    lead && isIndividualLead
      ? leadContactDetailData
      : lead && !isIndividualLead
      ? entityContactDetailData
      : customerContactDetailData;

  //-----------------------  personalInfoData -----------------------

  const rawLeadPersonalInfoData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.salutation'),
      content:
        getSalutation({
          genderCode: lead?.genderCode,
          maritalStatusCode: lead?.maritalStatusCode,
        }) ?? '--',
    },
    {
      label:
        country === 'id'
          ? t('leadProfile:leadProfile.profileDetails.fullName')
          : t('leadProfile:leadProfile.profileDetails.name'),
      content: lead?.middleName
        ? `${lead?.firstName} ${lead?.middleName} ${lead?.lastName}`
        : `${lead?.firstName} ${lead?.lastName}` ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.dob'),
      content: `${lead?.birthDate ? dateFormatUtil(lead?.birthDate) : '--'}${
        dobTypes === 'withAge' && lead?.birthDate
          ? ` (${calculateAge(new Date(lead?.birthDate))} y.o.)`
          : ''
      }`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.gender'),
      content:
        getLabelFromValue(optionList?.GENDER?.options, lead?.genderCode) ??
        '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),
      content:
        getLabelFromValue(
          country === 'id'
            ? optionList?.MARITAL_STATUS?.options
            : optionList?.MARITAL?.options,
          lead?.maritalStatusCode,
        ) ?? '--',
    },
  ];
  // .filter(data => data.content !== undefined); // Filter out items with undefined content

  const ageField = {
    label: t('leadProfile:leadProfile.profileDetails.age'),
    content: `${
      lead?.birthDate ? calculateAge(new Date(lead?.birthDate)) : '--'
    } y.o.`,
  };

  const normalLeadPersonalInfoData =
    dobTypes === 'standaloneAgeField'
      ? rawLeadPersonalInfoData.concat(ageField)
      : rawLeadPersonalInfoData;

  const fullLeadPersonalInfoData = [
    ...normalLeadPersonalInfoData,
    country === 'id'
      ? undefined
      : {
          label: t('leadProfile:leadProfile.profileDetails.smokingHabit'),
          content: '--',
        },
    {
      label: t('leadProfile:leadProfile.profileDetails.religion'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.motherMaidenName'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.idType'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.idNumber'),
      content: '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.taxId'),
      content: '--',
    },
  ].filter(Boolean) as DataPoint[];
  const leadPersonalInfoData =
    mode === 'full' ? fullLeadPersonalInfoData : normalLeadPersonalInfoData;

  const customerPersonalInfoData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.salutation'),
      content: customerDetails?.title ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.name'),
      content: customerDetails?.fullName ? customerDetails?.fullName : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.dob'),
      content: `${
        customerDetails?.dateOfBirth
          ? dateFormatUtil(customerDetails?.dateOfBirth)
          : '--'
      }${
        dobTypes === 'withAge' && customerDetails?.dateOfBirth
          ? ` (${differenceInYears(
              new Date(),
              new Date(customerDetails?.dateOfBirth),
            )} y.o.)`
          : ''
      }`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.gender'),
      content:
        getLabelFromValue(
          optionList?.GENDER?.options,
          customerDetails?.gender,
        ) ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),
      content: customerDetails?.maritalStatus ?? '--',
    },
    country === 'id'
      ? undefined
      : {
          label: t('leadProfile:leadProfile.profileDetails.smokingHabit'),
          content:
            customerDetails?.smoker === 'S'
              ? 'Smoker'
              : customerDetails?.smoker === 'N'
              ? 'Non-smoker'
              : '--',
        },
    {
      label: t('leadProfile:leadProfile.profileDetails.religion'),
      content: customerDetails?.religion ?? '--',
    },

    {
      label: t('leadProfile:leadProfile.profileDetails.nationality'),
      content: customerDetails?.nationality ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.countryOfResidence'),
      content: customerDetails?.address?.countryCode ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.leadInfoSection.occupation'),
      content: occupationLabel,
    },
  ].filter(Boolean) as DataPoint[];

  const entityPersonalInfoData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.companyName'),
      content: lead?.companyName ?? '--',
    },
    {
      label: t(
        'leadProfile:leadProfile.profileDetails.authorisedSignatoryName',
      ),
      content: lead?.firstName ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.dob'),
      content: `${lead?.birthDate ? dateFormatUtil(lead?.birthDate) : '--'}${
        dobTypes === 'withAge' && lead?.birthDate
          ? ` (${calculateAge(new Date(lead?.birthDate))} y.o.)`
          : ''
      }`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.gender'),
      content:
        getLabelFromValue(optionList?.GENDER?.options, lead?.genderCode) ??
        '--',
    },
    // {
    //   label: t(
    //     'leadProfile:leadProfile.profileDetails.registrationNumberLatest',
    //   ),
    //   content: '--',
    // },
    // {
    //   label: t('leadProfile:leadProfile.profileDetails.registrationNumberOld'),
    //   content: '--',
    // },
    // {
    //   label: t('leadProfile:leadProfile.profileDetails.dateOfRegistration'),
    //   content: '--',
    // },
    {
      label: t('leadProfile:leadProfile.profileDetails.natureOfBusiness'),
      content: natureOfBusinessLabel,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.registrationDate'),
      content: lead?.extra?.['registration_date']
        ? dateFormatUtil(lead?.extra?.['registration_date'])
        : '--',
    },
  ];

  const rolePersonalInfoData =
    lead && isIndividualLead
      ? leadPersonalInfoData
      : lead && !isIndividualLead
      ? entityPersonalInfoData
      : customerPersonalInfoData;

  //-----------------------  profileDetailData  -----------------------

  // CUBEFIB-7187 integrated lead score, campaign, etc.
  const leadProfileV2Data = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content:
        lead?.mobilePhoneCountryCode || lead?.mobilePhoneNumber
          ? `+${lead?.mobilePhoneCountryCode ?? '--'} ${
              lead?.mobilePhoneNumber ?? '--'
            }`
          : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: lead?.email ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),
      content:
        getLabelFromValue(
          optionList?.MARITAL?.options,
          lead?.maritalStatusCode,
        ) ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.leadScore'),
      infoModel: (
        <ScoreInfoModal version="v2" isVisible={false} onClose={() => {}} />
      ),
      content: lead?.extra?.engagement_score
        ? LeadScore({ leadScore: lead?.extra?.engagement_score })
        : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.leadOrigin'),
      content: mappedLeadSource ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.leadSource'),
      content: lead?.extra?.contact_history ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.interestProduct'),
      content: interestProduct ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.campaignName'),
      content: lead?.campaignName ?? '--',
    },
  ]
  // .filter(data => data.content !== undefined); // Filter out items with undefined content

  const leadProfileDetailData =
    country === 'ib'
      ? leadProfileV2Data
      : [
          {
            label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
            content: `+${lead?.mobilePhoneCountryCode ?? '--'} ${
              lead?.mobilePhoneNumber ?? '--'
            }`,
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.email'),
            content: lead?.email ?? '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),

            content:
              getLabelFromValue(
                country === 'id'
                  ? optionList?.MARITAL_STATUS?.options
                  : optionList?.MARITAL?.options,
                lead?.maritalStatusCode,
              ) ?? '--',
          },
        ];

  const customerProfileDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.mobileNumber'),
      content: customerDetails?.mobileNumber
        ? formatPhoneNumber(customerDetails?.mobileNumber)
        : '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: customerDetails?.emailAddress ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.maritalStatus'),
      content: customerDetails?.maritalStatus ?? '--',
    },
  ];

  const entityProfileDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.businessPhoneNumber'),
      content: `+${lead?.mobilePhoneCountryCode ?? '--'} ${
        lead?.mobilePhoneNumber ?? '--'
      }`,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.email'),
      content: lead?.email ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.natureOfBusiness'),
      content: natureOfBusinessLabel,
    },
    // {
    //   label: t('leadProfile:leadProfile.profileDetails.preferredContactMode'),
    //   content: 'Email' ?? '--',
    // },
    // {
    //   label: t(
    //     'leadProfile:leadProfile.profileDetails.preferredDocumentLanguage',
    //   ),
    //   content: 'English' ?? '--',
    // },
  ];

  const roleProfileDetailData = (
    lead && isIndividualLead
      ? leadProfileDetailData
      : lead && !isIndividualLead
      ? entityProfileDetailData
      : customerProfileDetailData
  ) as typeof leadProfileDetailData;

  const referralDetailData = [
    {
      label: t('leadProfile:leadProfile.profileDetails.leadScore'),
      infoModel: (
        <ScoreInfoModal version="v2" isVisible={false} onClose={() => {}} />
      ),
      content: lead?.extra?.engagement_score
        ? LeadScore({ leadScore: lead?.extra?.engagement_score })
        : undefined,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.leadOrigin'),
      content: mappedLeadSource ?? '--',
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.leadSource'),
      content: lead?.extra?.contact_history,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.interestProduct'),
      content: interestProduct,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.campaignName'),
      content: lead?.campaignName,
    },
    {
      label: t('leadProfile:leadProfile.profileDetails.notes'),
      content: lead?.extra?.notes,
    },
  ]
    //
    .filter(data => data.content !== undefined); // Filter out items with undefined content
  //
  const nationalityDetailData =
    mode === 'full'
      ? [
          {
            label: t('leadProfile:leadProfile.profileDetails.pob.country'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.pob.state'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.pob.city'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.cityName'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.nationality'),
            content: '--',
          },
        ]
      : [];

  const occupationDetailData =
    mode === 'full'
      ? [
          {
            label: t('leadProfile:leadProfile.profileDetails.industry'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.occupation'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.occupationClass'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.companyName'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.occupationSector'),
            content: '--',
          },
          {
            label: t(
              'leadProfile:leadProfile.profileDetails.occupationPosition',
            ),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.annualIncome'),
            content: '--',
          },
          {
            label: t(
              'leadProfile:leadProfile.profileDetails.occupationDetails',
            ),
            content: '--',
          },
        ]
      : [];

  const addressInfoData =
    mode === 'full'
      ? [
          {
            label: t('leadProfile:leadProfile.profileDetails.address'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.country'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.province'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.city'),
            content: '--',
          },
          {
            label: t('leadProfile:leadProfile.profileDetails.postCode'),
            content: '--',
          },
        ]
      : [];

  return {
    contactDetailData,
    // Lead:
    // mobileNumber, email
    //
    // customer:
    // mobileNumber, email
    //
    // entity:
    // businessPhoneNumber, email
    //
    rolePersonalInfoData,
    // Lead:
    // salutation, name, dob, gender,maritalStatus
    //
    // customer:
    // salutation, name, dob, gender,maritalStatus,smokingHabit, religion,
    // nationality, countryOfResidence, occupation
    //
    // entity:
    // companyName, authorisedSignatoryName, dob, gender,natureOfBusiness

    roleProfileDetailData,
    // Lead:
    // mobile number, email, occupation
    // customer:
    // mobile number, email, occupation
    // entity:
    // businessPhoneNumber, email, natureOfBusiness
    referralDetailData,
    nationalityDetailData,
    occupationDetailData,
    addressInfoData,
  } satisfies {
    contactDetailData: DataPoint[];
    rolePersonalInfoData: DataPoint[];
    roleProfileDetailData: DataPoint[];
    referralDetailData: DataPoint[];
    nationalityDetailData: DataPoint[];
    occupationDetailData: DataPoint[];
    addressInfoData: DataPoint[];
  };
}

type DataPoint = {
  label: string;
  infoModel?: React.ReactNode | JSX.Element | undefined;
  content: string | JSX.Element | undefined;
};

function formatPhoneNumber(phoneNumber: string) {
  return phoneNumber.slice(0, 3) + ' ' + phoneNumber.slice(3);
}

const getSalutation = ({
  genderCode,
  gender,
  maritalStatusCode,
}: SalutationProps) => {
  if (!genderCode && !gender) return '--';
  if (genderCode === 'M' || gender == 'Male') return 'Mr';
  if (maritalStatusCode === 'M') return 'Mrs';
  return 'Miss';
};

type SalutationProps = {
  genderCode?: string;
  gender?: string;
  maritalStatusCode?: string;
};

const test = {
  id: '1ebb2300-b077-4c3a-b31f-d725693145de',
  createdAt: '2025-02-17T02:27:53.044Z',
  updatedAt: '2025-02-17T02:46:08.702Z',
  opportunityUpdatedAt: '2025-02-17T02:27:53.059Z',
  agentId: 'AA10027',
  agentAssignedAt: '2025-02-17T02:27:53.059Z',
  sourceIds: ['CUBE'],
  caseIds: ['67b29f666926e76668436d9b'],
  policyIds: ['********'],
  isIndividual: true,
  salutation: 'MRS',
  firstName: 'Crystal leadsprofile',
  lastName: ' ',
  email: '<EMAIL>',
  mobilePhoneCountryCode: '60',
  mobilePhoneNumber: '*********',
  addressCity: 'Kangar',
  addressPostalCode: '01000',
  addressCountryCode: 'MYS',
  acceptPhoneCall: true,
  acceptEmail: true,
  genderCode: 'F',
  birthDate: '2000-01-01',
  maritalStatusCode: 'M',
  companyName: 'Fwd',
  occupationIndustryCode: 'ACCT',
  occupationGroupCode: 'Accountant/Accounting Professional',
  occupationClassCode: '1',
  extra: {},
  interestedCategories: ['Education'],
  interestedProducts: [],
  recommendedProducts: [],
  status: 'submitted',
  isWinning: true,
  isLose: false,
  winAt: '2025-02-17T02:46:07.451Z',
  isContacted: true,
  lastPhoneCallAt: '2025-02-17T02:31:13.882Z',
  leads: [
    {
      id: '1ebb2300-b077-4c3a-b31f-d725693145de',
      createdAt: '2025-02-17T02:27:53.044795+00:00',
      sourceId: 'CUBE',
      sourceLeadId: '_CUBE_67b29ea72610bf66b5069bd0',
      isIndividual: true,
      firstName: 'Crystal leadsprofile',
      lastName: ' ',
      email: '<EMAIL>',
      mobilePhoneCountryCode: '60',
      mobilePhoneNumber: '*********',
      acceptPhoneCall: true,
      acceptEmail: true,
      genderCode: 2,
      birthDate: '2000-01-01',
      maritalStatusCode: 'U',
      extra: {},
      interestedCategories: ['Education'],
      interestedProducts: [],
      recommendedProducts: [],
      isArchived: 'N',
    },
  ],
  transactions: [
    {
      action: 'create',
      actionAt: '2025-02-17T02:27:53.059Z',
      sourceId: 'CUBE',
      rawLeadId: '_CUBE_67b29ea72610bf66b5069bd0',
      agentId: 'AA10027',
    },
    {
      action: 'assign',
      actionAt: '2025-02-17T02:27:53.059Z',
      sourceId: 'CUBE',
      rawLeadId: '_CUBE_67b29ea72610bf66b5069bd0',
      agentId: 'AA10027',
    },
    {
      action: 'contact',
      actionAt: '2025-02-17T02:31:13.882Z',
      sourceId: 'CUBE',
      success: 'true',
      agentId: 'AA10027',
      contactMethod: 'phone',
      extra: {
        feedback: 'interested',
      },
    },
    {
      action: 'illustrate',
      actionAt: '2025-02-17T02:31:15.202Z',
      sourceId: 'CUBE',
      agentId: 'AA10027',
      productCode: 'GS1',
      quotationId: '67b29f716926e76668436dac',
      extra: {
        is_pdf_generated: 'true',
        is_quick_quote: 'true',
      },
    },
    {
      action: 'illustrate',
      actionAt: '2025-02-17T02:31:41.445Z',
      sourceId: 'CUBE',
      agentId: 'AA10027',
      productCode: 'GS1',
      quotationId: '67b29f8d6926e76668436dd2',
      extra: {
        is_pdf_generated: 'false',
        is_quick_quote: 'true',
      },
    },
    {
      action: 'appointment',
      actionAt: '2025-02-17T02:35:49.419Z',
      sourceId: 'CUBE',
      success: 'true',
      agentId: 'AA10027',
    },
    {
      action: 'illustrate',
      actionAt: '2025-02-17T02:37:44.22Z',
      sourceId: 'CUBE',
      agentId: 'AA10027',
      productCode: 'GS1',
      quotationId: '67b2a0f861d9d90b4424a74d',
      extra: {
        is_pdf_generated: 'true',
        is_quick_quote: 'false',
      },
    },
    {
      action: 'illustrate',
      actionAt: '2025-02-17T02:40:51.342Z',
      sourceId: 'CUBE',
      agentId: 'AA10027',
      productCode: 'GS1',
      quotationId: '67b2a0f861d9d90b4424a74d',
      extra: {
        is_pdf_generated: 'false',
        is_quick_quote: 'false',
      },
    },
    {
      action: 'submit',
      actionAt: '2025-02-17T02:46:07.451Z',
      sourceId: 'CUBE',
      agentId: 'AA10027',
      productCode: 'GS1',
      policyId: '********',
    },
    {
      action: 'update',
      actionAt: '2025-02-17T02:46:08.702Z',
      sourceId: 'CUBE',
    },
  ],
};
