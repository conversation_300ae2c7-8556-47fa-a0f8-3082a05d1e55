import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { RouteProp, useRoute } from '@react-navigation/native';
import {
  AppointmentRequestBody,
  ContactRequestBody,
} from 'api/leadActivityApi';
import DatePickerCalendar from 'components/DatePickerCalendar';
import Input from 'components/Input';
import {
  addToast,
  Box,
  Button,
  Column,
  H7,
  Icon,
  Row,
  SmallLabel,
  TextField,
  Typography,
} from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { parse, set } from 'date-fns';
import {
  APPOINTMENT,
  CONTACTED,
  DEFERRED,
  INTERESTED,
  LogActivityFormData,
  NOT_INTERESTED,
} from 'features/lead/components/LeadProfile/LogActivityForm/logActivityFormUtils';
import useLogActivityForm from 'features/lead/components/LeadProfile/LogActivityForm/useLogActivityForm';
import {
  usePostAppointmentLeadActivity,
  usePostContactLeadActivity,
} from 'features/lead/hooks/usePostLeadActivity';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Pressable } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { RootStackParamList } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import GATracking from 'utils/helper/gaTracking';
import { useValidationYupResolver } from 'utils/validation';

export default function LogActivityModal({
  setIsFormModalActive,
}: {
  setIsFormModalActive: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const { t: ct } = useTranslation('common');
  const { t } = useTranslation('leadProfile');
  const { colors, space, sizes } = useTheme();
  const route = useRoute<RouteProp<RootStackParamList, 'LeadProfile'>>();

  const { logActivityFormDefaultValue, logActivityFormFormSchema } =
    useLogActivityForm();

  const leadId = route?.params?.id;
  const mutateLeadId = leadId;

  const { data: leadProfile } = useGetLeadByLeadId(leadId);
  const { mutate: contactedMutate } = usePostContactLeadActivity();
  const { mutate: appointmentMutate } = usePostAppointmentLeadActivity();

  const [feedbackValue, setFeedbackValue] = useState('');

  const LEADS_ACTIVITY_TOGGLE_CONFIG = [
    {
      type: 'contacted',
      label: t('leadProfile.logActivityForm.contacted'),
      value: CONTACTED,
    },
    {
      type: 'appointment',
      label: t('leadProfile.logActivityForm.appointment'),
      value: APPOINTMENT,
    },
  ];

  const LEADS_FEEDBACK_TOGGLE_CONFIG = [
    {
      type: 'interested',
      label: t('leadProfile.logActivityForm.interested'),
      value: INTERESTED,
      icon: <Icon.Interested size={sizes[5]} />,
      onPress: () => setFeedbackValue(INTERESTED),
    },
    {
      type: 'deferred',
      label: t('leadProfile.logActivityForm.deferred'),
      value: DEFERRED,
      icon: <Icon.Thinking size={sizes[5]} />,
      onPress: () => setFeedbackValue(DEFERRED),
    },
    {
      type: 'notInterested',
      label: t('leadProfile.logActivityForm.notInterested'),
      value: NOT_INTERESTED,
      icon: <Icon.NotInterested size={sizes[5]} />,
      onPress: () => setFeedbackValue(NOT_INTERESTED),
    },
  ];

  const NOT_BUYING_RADIO_BTN_CONFIG = [
    {
      type: 'noResponse',
      label: t('leadProfile.logActivityForm.notInterested.noResponse'),
      value: 'No response from client',
    },
    {
      type: 'noFunds',
      label: t('leadProfile.logActivityForm.notInterested.noFunds'),
      value: 'No funds',
    },
    {
      type: 'alreadyHasIes',
      label: t('leadProfile.logActivityForm.notInterested.alreadyHasIes'),
      value: 'Already has insurance policy(ies)',
    },
    {
      type: 'undecided',
      label: t('leadProfile.logActivityForm.notInterested.undecided'),
      value: 'Undecided',
    },
    {
      type: 'interestedButNotReady',
      label: t(
        'leadProfile.logActivityForm.notInterested.interestedButNotReady',
      ),
      value: 'Interested but not ready as of the moment',
    },
    {
      type: 'quoteTooHigh',
      label: t('leadProfile.logActivityForm.notInterested.quoteTooHigh'),
      value: 'Quote too high',
    },
  ];

  const {
    handleSubmit,
    control,
    formState: { isValid },
  } = useForm({
    defaultValues: logActivityFormDefaultValue,
    resolver: useValidationYupResolver(logActivityFormFormSchema),
  });

  const onSubmitSuccessAction = () => {
    addToast(
      [
        {
          message: t('leadProfile.logActivityForm.logSuccessToast'),
          IconLeft: <Icon.Tick fill={colors.background} />,
        },
      ],
      false,
    );
    setIsFormModalActive(false);
  };

  const onSubmitErrorAction = () => {
    addToast(
      [
        {
          message: t('leadProfile.logActivityForm.logFailedToast'),
          IconLeft: <Icon.Warning fill={colors.background} />,
        },
      ],
      false,
    );
    setIsFormModalActive(false);
  };

  const onSubmit = (data: LogActivityFormData) => {
    const {
      activityDate,
      feedback,
      notInterestedReason,
      typeOfActivity,
      yourFeedbackOptional,
    } = data;

    const clonedDate = new Date(activityDate);

    const currentTimeHHMMSS = new Date().toLocaleTimeString([], {
      hourCycle: 'h23',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
    const currentTime = parse(currentTimeHHMMSS, 'HH:mm:ss', new Date());

    const combinedDateTime = set(clonedDate, {
      hours: currentTime.getHours(),
      minutes: currentTime.getMinutes(),
      seconds: currentTime.getSeconds(),
    });

    const selectedDateWithSubmittedTime = combinedDateTime.toISOString();
    const isInterested = feedback === INTERESTED;

    // Type of activity - Contacted
    const contactedRequestData: ContactRequestBody = {
      contactMethod: 'phone',
      success: true,
      reason: isInterested ? null : notInterestedReason,
      actionAt: selectedDateWithSubmittedTime,
      extra: {
        feedback: feedback,
        ...(!isInterested && { feedbackDetails: yourFeedbackOptional }),
      },
    };

    // Type of activity - Appointment
    const appointmentRequestData: AppointmentRequestBody = {
      reason: isInterested ? null : notInterestedReason,
      actionAt: selectedDateWithSubmittedTime,
      extra: {
        feedback: feedback,
        ...(!isInterested && { feedbackDetails: yourFeedbackOptional }),
      },
    };

    if (typeOfActivity === CONTACTED) {
      GATracking.logCustomEvent('lead_contacted', {
        feedback,
        reason: notInterestedReason,
      });

      contactedMutate(
        { leadId: mutateLeadId, data: contactedRequestData },
        {
          onSuccess: () => onSubmitSuccessAction(),
          onError: () => onSubmitErrorAction(),
        },
      );
    }

    if (typeOfActivity === APPOINTMENT) {
      GATracking.logCustomEvent('lead_appointment', {
        feedback,
        reason: notInterestedReason,
      });

      appointmentMutate(
        { leadId: mutateLeadId, data: appointmentRequestData },
        {
          onSuccess: () => onSubmitSuccessAction(),
          onError: () => onSubmitErrorAction(),
        },
      );
    }
  };

  return (
    <Column
      justifyContent={'center'}
      alignItems={'center'}
      style={{
        minHeight:
          feedbackValue === DEFERRED || feedbackValue === NOT_INTERESTED
            ? 648
            : 'auto',
      }}>
      <KeyboardAwareScrollView
        enableOnAndroid
        extraHeight={space[30]}
        // ! Setting ContentContainerStyle may cause keyboard avoiding view not working
        style={{
          backgroundColor: colors.background,
          padding: space[12],
          width: '100%',
          borderRadius: 16,
        }}>
        <Column gap={space[4]}>
          <Typography.H6 fontWeight="bold" style={{ marginBottom: 10 }}>
            {t('leadProfile.logActivityForm.title')}
          </Typography.H6>

          <Row gap={space[4]}>
            <Column flex={1}>
              <Input
                control={control}
                as={DatePickerCalendar}
                name="activityDate"
                label={'Date'}
                hint={'MM/DD/YYYY'}
                formatDate={val => (val ? dateFormatUtil(val) : '')}
                minDate={
                  leadProfile?.createdAt
                    ? new Date(leadProfile?.createdAt)
                    : undefined
                }
              />
            </Column>
            <Column flex={1}>
              <SmallLabel style={{ bottom: space[2] }}>
                {t('leadProfile.logActivityForm.typeOfActivity')}
              </SmallLabel>
              <Controller
                control={control}
                name={'typeOfActivity'}
                render={({ field: { onChange, value: hookFormValue } }) => (
                  <Row
                    flex={1}
                    bottom={20}
                    gap={space[2]}
                    alignItems="flex-end">
                    {LEADS_ACTIVITY_TOGGLE_CONFIG.map(
                      ({ type, label, value }) => (
                        <ChipButton
                          key={'typeOfActivity' + type}
                          label={label}
                          onSelect={() => {
                            onChange(value); // react-hook-form onChange
                          }}
                          isActive={value === hookFormValue}
                        />
                      ),
                    )}
                  </Row>
                )}
              />
            </Column>
          </Row>

          <H7 fontWeight="bold">
            {t('leadProfile.logActivityForm.leadsFeedback')}
          </H7>

          <Controller
            control={control}
            name={'feedback'}
            render={({ field: { onChange, value: hookFormValue } }) => (
              <Row style={{ gap: space[2] }}>
                {LEADS_FEEDBACK_TOGGLE_CONFIG.map(
                  ({ type, label, value, icon, onPress }) => (
                    <ChipButton
                      key={'LEADS_FEEDBACK_BTN_' + type}
                      label={label}
                      icon={icon}
                      onSelect={() => {
                        onPress(); // button config onPress
                        onChange(value); // react-hook-form onChange
                      }}
                      isActive={value === hookFormValue}
                    />
                  ),
                )}
              </Row>
            )}
          />

          {(feedbackValue === NOT_INTERESTED || feedbackValue === DEFERRED) && (
            <Animated.View entering={FadeIn} exiting={FadeOut}>
              <Box gap={space[4]}>
                <H7 fontWeight="bold">
                  {t('leadProfile.logActivityForm.notInterested.question')}
                </H7>

                <Controller
                  control={control}
                  name={'notInterestedReason'}
                  render={({ field: { onChange, value: hookFormValue } }) => (
                    <Row
                      style={{ gap: space[2] }}
                      alignItems="center"
                      flexWrap="wrap"
                      flex={1}>
                      {NOT_BUYING_RADIO_BTN_CONFIG.map(
                        ({ type, label, value }) => (
                          <ChipButton
                            key={'NOT_BUYING_RADIO_BTN_CONFIG' + type}
                            label={label}
                            onSelect={() => {
                              onChange(value); // react-hook-form onChange
                            }}
                            isActive={value === hookFormValue}
                          />
                        ),
                      )}
                    </Row>
                  )}
                />

                <Column gap={space[2]}>
                  <Input
                    control={control}
                    as={TextField}
                    name="yourFeedbackOptional"
                    label={t(
                      'leadProfile.logActivityForm.otherFeedbackOptional',
                    )}
                    multiline
                    autoExpand
                    numberOfLines={3}
                  />
                  <SubText>{t('leadProfile.logActivityForm.subText')}</SubText>
                </Column>
              </Box>
            </Animated.View>
          )}
          <Row
            marginTop={space[8]}
            gap={space[4]}
            alignItems="center"
            justifyContent="center">
            <Button
              onPress={() => setIsFormModalActive(false)}
              text={ct('cancel')}
              variant="secondary"
              style={{ width: space[30] }}
            />
            <Button
              disabled={!isValid}
              text={ct('save')}
              style={{ width: space[30] }}
              onPress={handleSubmit(data =>
                onSubmit(data as LogActivityFormData),
              )}
            />
          </Row>
        </Column>
      </KeyboardAwareScrollView>
    </Column>
  );
}

function ChipButton({
  label,
  icon,
  onSelect,
  isActive,
}: {
  type?: string;
  label: string;
  value?: string;
  icon?: JSX.Element;
  onSelect: () => void;
  isActive: boolean;
}) {
  return (
    <ButtonContainer isActive={isActive} onPress={onSelect}>
      {icon && icon}
      {isActive ? (
        <H7 fontWeight="bold" color={colors.fwdOrange[100]}>
          {label}
        </H7>
      ) : (
        <H7>{label}</H7>
      )}
    </ButtonContainer>
  );
}

const ButtonContainer = styled(Pressable)<{ isActive: boolean; theme?: Theme }>(
  ({ isActive, theme }) => ({
    height: theme.sizes[10],
    flexDirection: 'row',
    backgroundColor: isActive
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    paddingHorizontal: theme.space[4],
    paddingVertical: theme.space[2],
    alignItems: 'center',
    borderWidth: isActive ? 2 : 1,
    borderColor: isActive
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
    borderRadius: theme.borderRadius['full'],
    gap: theme.space[2],
  }),
);

const SubText = styled(SmallLabel)(({ theme }) => ({
  color: theme.colors.placeholder,
  lineHeight: theme.space[4],
}));
