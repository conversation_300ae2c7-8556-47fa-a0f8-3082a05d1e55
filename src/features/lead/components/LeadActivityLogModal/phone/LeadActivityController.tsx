import styled from '@emotion/native';
import { addToast, Icon } from 'cube-ui-components';
import { usePostContactLeadActivity } from 'features/lead/hooks/usePostLeadActivity';
import useBoundStore from 'hooks/useBoundStore';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Linking, Modal, Platform, View } from 'react-native';
import { Lead } from 'types';
import GATracking from 'utils/helper/gaTracking';
import LeadActivityResponsePanel from '../../LeadActivityResponsePanel';
import { Feedback, LeadClone, Page } from '../type';
import LeadActivityFirstPrompt from './LeadActivityFirstPrompt';
import LeadActivityModal from './LeadActivityModal';

export default function LeadActivityController({
  expiredLeadData,
}: {
  expiredLeadData: Lead[];
}) {
  const { t } = useTranslation(['lead']);
  const { mutateAsync: mutateContactLeadActivity } =
    usePostContactLeadActivity();
  const [leadsCount, setLeadsCount] = useState(0);

  const {
    expiredLeadActions: { setCheckedExpiredLeads },
  } = useBoundStore();

  const [isLoadingNext, setIsLoadingNext] = useState(false);
  const handlePanelClose = () => console.log('Panel closed');

  const [currentModal, setCurrentModal] = useState(Page.BEFORE_CALL);
  const [currentLeadResponse, setCurrentLeadResponse] = useState<
    Feedback | string
  >('');

  const [expiredLeadsDataClone, setExpiredLeadsClone] = useState(
    [...expiredLeadData].map(lead => ({
      ...lead,
      isChecked: false,
    })),
  );

  const [currentLead, setCurrentLead] = useState<LeadClone>(
    expiredLeadsDataClone[0],
  );

  const [contactedLeadsCount, setContactedLeadsCount] = useState(0);

  const [firstPromptValue, setFirstPromptValue] = useState<Feedback | string>(
    '',
  );

  useEffect(() => {
    if (leadsCount === expiredLeadsDataClone.length) {
      onSkipAll();
    } else {
      for (const lead of expiredLeadsDataClone) {
        if (!lead.isChecked) {
          setIsLoadingNext(false);
          setCurrentLead(lead);
          setLeadsCount(leadsCount + 1);
          break;
        }
      }
    }
  }, [JSON.stringify(expiredLeadsDataClone)]);

  useEffect(() => {
    setCurrentModal(Page.BEFORE_CALL);
  }, [currentLead]);

  const updateLeadCheckedStatus = (leadId: string) => {
    setExpiredLeadsClone(prevState =>
      prevState.map(lead =>
        lead.id === leadId ? { ...lead, isChecked: true } : lead,
      ),
    );
  };

  // Handle negative feedback for leads (Not interested - NI or Deferred - D)
  const handleNegativeFeedback = async (option: string, feedback: string) => {
    const notInterested = firstPromptValue === Feedback.NI;
    try {
      const result = await mutateContactLeadActivity({
        leadId: currentLead?.id,
        data: {
          contactMethod: 'phone',
          success: true,
          reason: option,
          extra: {
            feedback: notInterested ? 'notInterested' : 'deferred',
            ...(Boolean(feedback) && { feedbackDetails: feedback }),
          },
          actionAt: new Date().toISOString(),
        },
      });

      if (result !== null || result !== undefined) {
        setContactedLeadsCount(contactedLeadsCount + 1);
      }
    } catch (e) {
      console.log('error', e);
    }
    onSkip(notInterested ? Feedback.NI : Feedback.D);
  };

  const onSkip = (value: Feedback | string) => {
    setCurrentLeadResponse(value);
    setIsLoadingNext(true);
    updateLeadCheckedStatus(currentLead?.id);
  };

  const onSelectResponse = async (value: Feedback | string) => {
    setFirstPromptValue(value);

    if (value === Feedback.I && currentLead?.id) {
      GATracking.logCustomEvent('lead_contacted', {
        feedback: 'interested',
      });

      try {
        const result = await mutateContactLeadActivity({
          leadId: currentLead?.id,
          data: {
            contactMethod: 'phone',
            success: true,
            reason: null,
            extra: { feedback: 'interested' },
            actionAt: new Date().toISOString(),
          },
        });
        if (result !== null || result !== undefined) {
          setContactedLeadsCount(contactedLeadsCount + 1);
        }
      } catch (e) {
        console.log('error', e);
      }
      return onSkip(value);
    } else if (value === Feedback.D || value === Feedback.NI) {
      return setCurrentModal(Page.SURVEY);
    } else {
      return onSkip(value);
    }
  };

  const onSkipAll = () => {
    setCheckedExpiredLeads();
    if (contactedLeadsCount > 0) {
      addToast([
        {
          IconLeft: Icon.Tick,
          message: t('lead:activity.toast', { count: contactedLeadsCount }),
        },
      ]);
    }
  };

  const onAfterCall = async () => {
    const contactLink =
      (Platform.OS === 'android' ? 'tel' : 'telprompt') +
      `:${currentLead?.mobilePhoneCountryCode}${currentLead?.mobilePhoneNumber}`;

    await Linking.canOpenURL(contactLink)
      .then(supported => {
        if (!supported) {
          if (Platform.OS === 'android') {
            Linking.openURL(contactLink); // canOpenURL() fails in android
          }
          if (Platform.OS === 'ios') {
            Alert.alert(
              t('lead:activity.callFailed.title'),
              t('lead:activity.callFailed.message'),
              [{ text: 'OK', style: 'cancel' }],
            );
          }
        } else {
          return Linking.openURL(contactLink);
        }
      })
      .catch(err => {
        console.log('error:', err);
        Alert.alert(
          t('lead:activity.callFailed.title'),
          t('lead:activity.callFailed.message'),
          [{ text: 'OK', style: 'cancel' }],
        );
      });

    setCurrentModal(Page.AFTER_CALL);
  };

  const leadName = !currentLead?.isIndividual
    ? currentLead?.companyName ?? '--'
    : currentLead?.lastName?.trim() === ''
    ? `${currentLead?.firstName}`
    : `${currentLead?.firstName} ${currentLead?.lastName}`;

  let firstPromptTitle;

  switch (currentLeadResponse) {
    case Feedback.I:
      firstPromptTitle = t('lead:activity.interested', { leadName });
      break;
    case Feedback.D:
      firstPromptTitle = t('lead:activity.notInterested', { leadName });
      break;
    case Feedback.NI:
      firstPromptTitle = t('lead:activity.notInterested', { leadName });
      break;
    case Feedback.N:
      firstPromptTitle = t('lead:activity.notInterested', { leadName });
      break;
    default:
      firstPromptTitle = t('lead:activity.noAnswer', { leadName });
      break;
  }

  return (
    <Modal animationType="fade" transparent={true}>
      <Container>
        {currentModal === Page.BEFORE_CALL && !isLoadingNext && (
          <LeadActivityFirstPrompt
            title={firstPromptTitle}
            onSelect={onSelectResponse}
            isSkipAllAllowed={
              leadsCount === expiredLeadsDataClone.length || leadsCount >= 4
            }
            onSkip={() => onSkip('')}
            onSkipAll={onSkipAll}
            onCall={onAfterCall}
          />
        )}

        {currentModal === Page.AFTER_CALL && !isLoadingNext && (
          <LeadActivityModal onSelect={onSelectResponse} />
        )}

        <LeadActivityResponsePanel
          visible={currentModal === Page.SURVEY && !isLoadingNext}
          onSuccess={handleNegativeFeedback}
          closeHandler={handlePanelClose}
        />
      </Container>
    </Modal>
  );
}

const Container = styled(View)(() => {
  return {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 10,
  };
});
