import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import ResponsiveText from 'components/ResponsiveTypography';
import Skeleton from 'components/Skeleton';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { add, isPast, parseISO } from 'date-fns';
import { getSourceLabels } from 'features/lead/utils';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View, useWindowDimensions } from 'react-native';
import { Lead } from 'types';
import { DateUtil } from 'utils';
import { country } from 'utils/context';
import { TimeObj } from 'utils/helper/dateUtil';
import StatusTag from '../../../components/StatusTag';
import ContactLeadModal from './ContactLeadModal';
import LeadFeedback from './LeadFeedback';
import LeadStatusTag from './LeadStatusTag';
import _ from 'lodash';
import LeadScore from './LeadProfile/LeadScore';

interface LeadItemProps extends Lead {
  onPress?: () => void;
  isToday?: boolean;
  isLoading?: boolean;
}

export const LeadItem = ({
  firstName,
  lastName,
  sourceIds,
  interestedCategories,
  mobilePhoneCountryCode,
  mobilePhoneNumber,
  email,
  opportunityUpdatedAt,
  createdAt,
  onPress,
  isToday,
  status,
  id,
  customerId,
  isIndividual,
  companyName,
  campaignName,
  interestedProducts,
  extra,
  isLoading = false,
}: LeadItemProps) => {
  const { colors, sizes, space } = useTheme();
  const { width } = useWindowDimensions();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation(['customer', 'lead']);

  const opportunityTime = opportunityUpdatedAt || createdAt;
  const expireTime = add(parseISO(opportunityTime), { hours: 24 });
  const expireSoonTime = add(parseISO(opportunityTime), { hours: 23 });
  const expired = isPast(expireTime);
  const expireSoon = isPast(expireSoonTime);
  const isNew =
    isToday &&
    !isPast(add(parseISO(createdAt), { hours: 24 })) &&
    status === 'created';

  const [visible, setVisible] = useState(false);
  const [isCalled, setIsCalled] = useState(false);

  const isEntity = !isIndividual ? <Icon.Office /> : <Icon.Account />;

  // combine mobile phone number with country code
  const mobilePhoneNumberWithCountryCode = `${mobilePhoneCountryCode}${mobilePhoneNumber}`;

  if (isLoading) {
    return <LeadSkeleton />;
  }

  return (
    <>
      <LeadTouchable
        style={
          isWideScreen && {
            paddingLeft: space[5],
            paddingVertical: space[4],
          }
        }
        onPress={onPress}>
        <LeadLeftSection>
          <Box flex={1}>
            <Row alignItems="center" gap={space[1]}>
              {isEntity}
              <LeadName
                TypographyDefault={Typography.LargeLabel}
                TypographyWide={Typography.ExtraLargeBody}
                style={isWideScreen && { lineHeight: 20 }}
                color={colors.primary}>
                {!isIndividual
                  ? companyName
                  : firstName + (lastName ? ` ${lastName}` : '')}
              </LeadName>
            </Row>
            {country === 'ib' ? (
              <IBLeadListCardSource
                customerId={customerId}
                sourceIds={sourceIds}
                campaignName={campaignName}
                interestedProducts={interestedProducts}
                interestedCategories={interestedCategories}
                extra={extra}
              />
            ) : (
              <>
                <Row>
                  <LeadDetail
                    style={{ maxWidth: width * 0.7 }}
                    TypographyDefault={Typography.Label}
                    TypographyWide={Typography.LargeLabel}
                    color={colors.secondary}>
                    {t(customerId ? 'customer:customerSource' : 'lead:from') +
                      ' ' +
                      getSourceLabels(sourceIds)
                        .map(label => t(`lead:source.${label}`))
                        .join(', ')}
                  </LeadDetail>
                </Row>
                <Row>
                  {interestedCategories && (
                    <LeadDetail
                      TypographyDefault={Typography.Label}
                      TypographyWide={Typography.LargeLabel}
                      color={colors.secondaryVariant}>
                      {t('lead:interestedIn') +
                        ' ' +
                        (interestedCategories?.length > 0
                          ? interestedCategories?.join(', ')
                          : '--')}
                    </LeadDetail>
                  )}
                  {/* {opportunityTime && (
              <LeadDetail
                TypographyDefault={Typography.Label}
                TypographyWide={Typography.LargeLabel}
                color={colors.secondaryVariant}>
                {t('lead:createdDate') +
                  ': ' +
                  dateFormatUtil(parseISO(opportunityTime))}
              </LeadDetail>
            )} */}
                </Row>
              </>
            )}
          </Box>
          <Row marginTop={space[1]} alignItems="center" minHeight={20}>
            {isNew && (
              <StatusTag
                text={'New'}
                backgroundColor={colors.primaryVariant2}
                textColor={colors.primary}
                containerStyle={{
                  paddingHorizontal: space[1],
                  paddingVertical: 2,
                }}
              />
            )}
            {country === 'ib' || country === 'id' ? (
              <LeadStatusTag.Ib status={status} />
            ) : (
              <LeadStatusTag.Ph status={status} />
            )}
          </Row>
        </LeadLeftSection>
        <LeadRightSection>
          {isToday && !expired && (
            <TimeLeft
              timeLeft={DateUtil.calculateTimeLeft(new Date(), expireTime)}
              expireSoon={expireSoon}
            />
          )}
          <CallTabArea
            onPress={() => {
              setVisible(true);
            }}>
            <Icon.Call
              size={sizes[isWideScreen ? 7 : 6]}
              fill={colors.palette.fwdAlternativeOrange[100]}
            />
            <ContactLeadModal
              firstName={firstName}
              visible={visible}
              onClose={() => {
                setVisible(false);
              }}
              ableIsCalled={() => setIsCalled(true)}
              phoneMobile={mobilePhoneNumberWithCountryCode}
              emailAddress={email}
            />
            <LeadFeedback
              id={id}
              isCalled={isCalled}
              disableIsCalled={() => setIsCalled(false)}
            />
          </CallTabArea>
          <Icon.ChevronRight
            width={sizes[5]}
            height={sizes[5]}
            fill={colors.secondaryVariant}
          />
        </LeadRightSection>
      </LeadTouchable>
    </>
  );
};

const IBLeadListCardSource = ({
  sourceIds,
  campaignName,
  interestedProducts,
  interestedCategories,
  extra,
}: Partial<LeadItemProps>) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['lead', 'customer']);
  const { width } = useWindowDimensions();

  const interestProduct = !_.isEmpty(interestedProducts)
    ? interestedProducts?.join(', ')
    : !_.isEmpty(interestedProducts)
    ? interestedCategories?.join(', ')
    : undefined;

  const sourcesLabel = getSourceLabels(sourceIds)
    .map(label => t(`lead:source.${label}`))
    .join(', ');
  return (
    <>
      {/* //* Row 2: LeadScore */}
      {extra?.engagement_score && (
        <Row alignItems="center">
          <LeadDetail
            style={{ maxWidth: width * 0.7 }}
            TypographyDefault={Typography.Label}
            TypographyWide={Typography.LargeLabel}
            color={colors.secondary}>
            {t('lead:leadScore') + ': '}
          </LeadDetail>
          <Box mt={space[1]}>
            <LeadScore leadScore={extra?.engagement_score} />
          </Box>
        </Row>
      )}
      {/* //* Row 3: LeadOrigin */}
      <Row>
        <LeadDetail
          style={{ maxWidth: width * 0.7 }}
          TypographyDefault={Typography.Label}
          TypographyWide={Typography.LargeLabel}
          color={colors.secondary}>
          {t('lead:leadOrigin') + ':' + ' ' + sourcesLabel || '--'}
        </LeadDetail>
      </Row>
      {/* //* Row 4: Campaign */}
      <Row>
        {campaignName && (
          <LeadDetail
            TypographyDefault={Typography.Label}
            TypographyWide={Typography.LargeLabel}
            color={colors.secondary}>
            {t('lead:campaign') + ':'} {campaignName}
          </LeadDetail>
        )}
      </Row>
      {/* //* Row 5: InterestedProducts */}
      <Row>
        {interestProduct && (
          <LeadDetail
            TypographyDefault={Typography.Label}
            TypographyWide={Typography.LargeLabel}
            color={colors.secondaryVariant}>
            {t('lead:interestedIn') + ' ' + interestProduct}
          </LeadDetail>
        )}
      </Row>
    </>
  );
};

const TimeLeft = ({
  timeLeft,
  expireSoon,
}: {
  timeLeft: TimeObj;
  expireSoon: boolean;
}) => {
  const { colors, sizes } = useTheme();
  const { t } = useTranslation(['lead']);

  return (
    <TimeLeftContainer expireSoon={expireSoon}>
      <Row alignItems="center">
        <Icon.OperatingHours
          size={sizes[4]}
          fill={
            expireSoon ? colors.palette.alertRed : colors.palette.fwdBlue[100]
          }
        />
        <Typography.SmallBody
          style={{
            color: expireSoon
              ? colors.palette.alertRed
              : colors.palette.fwdBlue[100],
            marginLeft: sizes[1],
          }}>
          {Math.floor(timeLeft.value)} {timeLeft.unit} {t('lead:time.left')}
        </Typography.SmallBody>
      </Row>
    </TimeLeftContainer>
  );
};

const LeadSkeleton = () => {
  const { colors, sizes, borderRadius } = useTheme();

  return (
    <LeadTouchable
      disabled
      style={{
        height: sizes[28],
      }}>
      <Column
        style={{
          flex: 1,
          justifyContent: 'space-between',
        }}>
        <Skeleton
          height={sizes[4]}
          width={'100%'}
          radius={borderRadius['x-small']}
        />
        <Skeleton
          height={sizes[4]}
          width={'50%'}
          radius={borderRadius['x-small']}
        />
        <Skeleton
          height={sizes[4]}
          width={'50%'}
          radius={borderRadius['x-small']}
        />
        <Skeleton
          height={sizes[4]}
          width={'25%'}
          radius={borderRadius['x-small']}
        />
      </Column>
      <Column
        style={{
          width: sizes[5],
          justifyContent: 'center',
        }}>
        <View style={{ right: -10 }}>
          <Icon.ChevronRight
            width={sizes[5]}
            height={sizes[5]}
            fill={colors.palette.fwdGrey[100]}
          />
        </View>
      </Column>
    </LeadTouchable>
  );
};

const TimeLeftContainer = styled.View<{ expireSoon: boolean }>(
  ({ theme, expireSoon }) => ({
    position: 'absolute',
    right: theme.space[7],
    bottom: 0,
    backgroundColor: expireSoon
      ? theme.colors.palette.alertRedLight
      : theme.colors.palette.alertGreenLight,
    borderRadius: theme.borderRadius.full,
    paddingHorizontal: theme.space[2],
    paddingVertical: 2,
  }),
);

const CallTabArea = styled.TouchableOpacity(({ theme }) => ({
  height: theme.sizes[8],
  width: theme.sizes[8],
  justifyContent: 'center',
  alignItems: 'center',
  marginRight: theme.space[4],
}));

const LeadName = styled(ResponsiveText)(({ theme }) => ({
  flex: 1,
  marginRight: 2,
  minHeight: theme.sizes[5],
}));

const LeadDetail = styled(ResponsiveText)(() => ({
  marginTop: 4,
}));

LeadName.defaultProps = {
  fontWeight: 'bold',
  numberOfLines: 1,
  ellipsizeMode: 'tail',
};

const LeadTouchable = styled.TouchableOpacity(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  paddingVertical: theme.space[3],
  paddingHorizontal: theme.space[4],
  flexDirection: 'row',
  marginBottom: theme.space[3],
  position: 'relative',
  minHeight: theme.sizes[28],
}));

const LeadLeftSection = styled.View(() => ({
  flex: 1,
  justifyContent: 'flex-start',
  overflow: 'hidden',
}));

const LeadRightSection = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  alignSelf: 'center',
  position: 'absolute',
  right: theme.sizes[2],
  height: '100%',
}));
