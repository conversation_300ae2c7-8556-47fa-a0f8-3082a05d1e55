import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  ActionPanel,
  ActionPanelProps,
  Box,
  Checkbox,
  Chip,
  Column,
  Icon,
  Row,
  Typography,
} from 'cube-ui-components';
import FormAction from 'features/eApp/components/phone/common/FormAction';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import {
  useGetLeadsOtherFilters,
  useGetLeadsTodayFilters,
} from 'hooks/useGetLeads';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import _ from 'lodash';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  ScrollView,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  LeadCampaignStats,
  LeadsFilters,
  LeadSource,
  LeadSourceStats,
  LeadStatus,
  LeadStatusStats,
  LeadType,
  LeadTypeStats,
  MARKETING_LEAD_SOURCES,
  MyLeadParamList,
  NOT_CONTACTED_LEAD_STATUS,
  SELF_GEN_LEAD_SOURCES,
} from 'types';
import { ObjectUtil } from 'utils';
import { build, country } from 'utils/context';
import { clearFilters } from '../../utils';

const IS_PH = country === 'ph';
const displayLeadOriginLabel = IS_PH;

type LeadChipsFilterPanelProps = ActionPanelProps & {
  visible: boolean;
  curTab: keyof MyLeadParamList;
};

export default function LeadChipsFilterPanel({
  curTab,
  visible,
  handleClose,
}: LeadChipsFilterPanelProps) {
  const { t } = useTranslation('lead');
  const { space } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const { height: screenHeight } = useWindowDimensions();

  const leadStoreKey = curTab === 'TodayLeads' ? 'today' : 'others';

  const { filters } = useBoundStore(store => store.lead)[leadStoreKey];
  const { updateFilter } = useBoundStore(store => store.leadActions)[
    leadStoreKey
  ];

  const [tempFilters, setTempFilters] = useState<LeadsFilters>(filters);

  useEffect(() => {
    setTempFilters(filters);
  }, [filters, leadStoreKey]);

  const updateTempFilters = (filters: LeadsFilters) => {
    setTempFilters(filters);
  };

  const onPressApply = () => {
    updateFilter(tempFilters);
    handleClose();
  };

  const { data: todayFilters } = useGetLeadsTodayFilters(
    curTab === 'TodayLeads',
  );
  const { data: otherFilters } = useGetLeadsOtherFilters(
    curTab === 'OtherLeads',
  );

  const curTabFilters = (curTab === 'TodayLeads'
    ? todayFilters
    : otherFilters) || {
    typeStats: [],
    sourceStats: [],
    campaignStats: [],
    statusStats: [],
  };

  return (
    <ActionPanel
      visible={visible}
      handleClose={() => {
        handleClose();
        setTempFilters(filters);
      }}
      title={t('filter.panel.title')}
      contentContainerStyle={{
        maxHeight: screenHeight * 0.75,
        padding: 0,
        paddingBottom: Platform.select({
          android: space[isWideScreen ? 5 : 4] + bottom,
          ios: 0,
        }),
      }}>
      <ScrollView>
        <FiltersView
          tempFilters={tempFilters}
          updateTempFilters={updateTempFilters}
          typeStats={curTabFilters.typeStats}
          sourceStats={curTabFilters.sourceStats}
          campaignStats={curTabFilters.campaignStats}
          statusStats={curTabFilters.statusStats}
        />
      </ScrollView>

      <FormAction
        primaryLabel={'Apply'}
        onPrimaryPress={onPressApply}
        secondaryLabel={t('filter.panel.reset')}
        onSecondaryPress={() => setTempFilters(clearFilters(tempFilters))}
      />
    </ActionPanel>
  );
}

export const FiltersView = ({
  tempFilters,
  updateTempFilters,
  typeStats = [],
  sourceStats = [],
  campaignStats = [],
  statusStats = [],
}: {
  tempFilters: LeadsFilters;
  updateTempFilters: (filters: LeadsFilters) => void;
  typeStats?: LeadTypeStats;
  sourceStats?: LeadSourceStats;
  campaignStats?: LeadCampaignStats;
  statusStats?: LeadStatusStats;
}) => {
  const { t } = useTranslation('lead');
  const { colors, space, sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const [showAllCampaigns, setShowAllCampaigns] = useState(false);

  const typeFilters = tempFilters['type'];
  const sourceFilters = tempFilters['source'];
  const campaignFilters = tempFilters['campaignCode'];
  const statusFilters = tempFilters['status'];

  const typeArr: LeadType[] = ['individual', 'entity'];

  const sourcesArr: LeadSource[] = [
    'SELF',
    'BLTS',
    'ALTS',
    'Marketing',
    'Affiliate',
  ];

  const fullCampaignArr = campaignStats?.map(({ type }) => type) ?? [];
  const slicedCampaignArr = fullCampaignArr.slice(0, 3);
  const campaignArr = showAllCampaigns ? fullCampaignArr : slicedCampaignArr;

  const statusArr: LeadStatus[] = [
    'not_contacted',
    'contacted',
    'appointment',
    'illustration',
    'submitted',
    'not_interested',
    'deferred',
    'cancelled',
    'declined',
    'issued',
  ];

  const LeadTypeBox = isTabletMode ? Box : Row;

  return (
    <Container>
      {!!typeStats?.length && (
        <>
          <LeadTypeBox
            style={
              !isTabletMode && {
                alignItems: 'center',
                justifyContent: 'space-between',
              }
            }>
            <Typography.H7
              fontWeight="bold"
              children={t('filter.panel.section.leadType')}
              style={isTabletMode && { marginBottom: space[4] }}
            />

            <Row flexWrap="wrap" columnGap={space[2]} rowGap={space[3]}>
              {typeArr.map(typeKey => {
                const filterHasItem =
                  typeStats?.findIndex(typeInfo => {
                    const { type } = typeInfo;
                    return typeKey === type;
                  }) >= 0;
                if (filterHasItem) {
                  const iconColor = typeFilters[typeKey]
                    ? colors.primary
                    : colors.onBackground;
                  const icon =
                    typeKey === 'individual' ? (
                      <Icon.Account size={sizes[5]} fill={iconColor} />
                    ) : (
                      <Icon.Office size={sizes[5]} fill={iconColor} />
                    );
                  return (
                    <Chip
                      key={typeKey}
                      icon={() => icon}
                      textPosition="right"
                      label={t(`source.${typeKey}`)}
                      focus={typeFilters?.[typeKey]}
                      onPress={() => {
                        const newFilters = ObjectUtil.cloneDeep(tempFilters);
                        newFilters.type[typeKey] = !typeFilters?.[typeKey];
                        updateTempFilters(newFilters);
                      }}
                    />
                  );
                }
                return null;
              })}
            </Row>
          </LeadTypeBox>

          <SeparatorLine />
        </>
      )}

      <Typography.H7
        fontWeight="bold"
        children={
          displayLeadOriginLabel
            ? t('filter.panel.section.leadOrigin')
            : t('filter.panel.section.sourceOfLead')
        }
        style={{ marginBottom: space[4] }}
      />
      <Row flexWrap="wrap" columnGap={space[2]} rowGap={space[3]}>
        {sourcesArr.map(sourceKey => {
          const filterHasItem =
            sourceStats.findIndex(sourceInfo => {
              const { sourceId } = sourceInfo;
              if (sourceKey === 'SELF') {
                return SELF_GEN_LEAD_SOURCES.includes(sourceId);
              }
              if (sourceKey === 'Marketing') {
                return MARKETING_LEAD_SOURCES.includes(sourceId);
              }
              return sourceKey === sourceId;
            }) >= 0;
          if (filterHasItem) {
            return (
              <Chip
                key={sourceKey}
                label={t(`source.${sourceKey}`)}
                focus={sourceFilters?.[sourceKey]}
                onPress={() => {
                  const newFilters = ObjectUtil.cloneDeep(tempFilters);
                  newFilters.source[sourceKey] = !sourceFilters?.[sourceKey];

                  //  Clear campaign filters if 'Marketing' unchecked
                  if (!newFilters.source['Marketing']) {
                    newFilters.campaignCode = {};
                  }

                  updateTempFilters(newFilters);
                }}
              />
            );
          }
          return null;
        })}
      </Row>

      <SeparatorLine />

      {(build === 'dev' || build === 'sit') && // ref. CUBEPH-4940 Pending for release
        !_.isEmpty(campaignArr) && (
          <>
            <Typography.H7
              fontWeight="bold"
              children={t('filter.panel.section.campaignName')}
              style={{ marginBottom: space[4] }}
            />
            <Column gap={space[4]}>
              {campaignArr.map(campaignKey => {
                return (
                  <Checkbox
                    key={campaignKey}
                    label={campaignKey}
                    value={campaignFilters?.[campaignKey]}
                    onChange={checked => {
                      const newFilters = ObjectUtil.cloneDeep(tempFilters);
                      newFilters.source['Marketing'] = true; // Ensure 'Marketing' is checked
                      newFilters.campaignCode[campaignKey] = checked;
                      updateTempFilters(newFilters);
                    }}
                  />
                );
              })}

              {fullCampaignArr?.length > 3 && (
                <TouchableOpacity
                  style={{ flexDirection: 'row', alignItems: 'center' }}
                  onPress={() => setShowAllCampaigns(!showAllCampaigns)}>
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={
                      showAllCampaigns
                        ? t('filter.panel.showLess')
                        : t('filter.panel.showAll')
                    }
                    color={colors.palette.fwdAlternativeOrange[100]}
                    style={{ marginRight: space[2] }}
                  />
                  {showAllCampaigns ? (
                    <Icon.ChevronUp
                      size={sizes[4]}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  ) : (
                    <Icon.ChevronDown
                      size={sizes[4]}
                      fill={colors.palette.fwdAlternativeOrange[100]}
                    />
                  )}
                </TouchableOpacity>
              )}
            </Column>

            <SeparatorLine />
          </>
        )}

      <Typography.H7
        fontWeight="bold"
        children={t('filter.panel.section.statusOfLead')}
        style={{ marginBottom: space[4] }}
      />
      <Row flexWrap="wrap" columnGap={space[2]} rowGap={space[3]}>
        {statusArr.map(statusKey => {
          const filterHasItem =
            statusStats.findIndex(statusInfo => {
              const { status } = statusInfo;
              if (statusKey === 'not_contacted') {
                return NOT_CONTACTED_LEAD_STATUS.includes(status);
              }
              return statusInfo.status === statusKey;
            }) >= 0;

          if (filterHasItem) {
            return (
              <Chip
                key={statusKey}
                focus={statusFilters?.[statusKey]}
                label={t(`status.${statusKey}`)}
                onPress={() => {
                  const newFilters = ObjectUtil.cloneDeep(tempFilters);
                  newFilters.status[statusKey] = !statusFilters?.[statusKey];
                  updateTempFilters(newFilters);
                }}
              />
            );
          }
          return null;
        })}
      </Row>

      <View style={{ height: sizes[3] }} />
    </Container>
  );
};

const Container = styled.View(({ theme }) => ({
  padding: theme.sizes[4],
}));

const SeparatorLine = styled.View(({ theme }) => ({
  height: 1,
  backgroundColor: theme?.colors.palette.fwdGrey[50],
  marginVertical: theme.space[5],
}));
