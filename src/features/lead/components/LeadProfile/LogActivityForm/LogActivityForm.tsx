import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import {
  AppointmentRequestBody,
  ContactRequestBody,
} from 'api/leadActivityApi';
import Input from 'components/Input';
import {
  addToast,
  Button,
  DatePicker,
  Dropdown,
  H7,
  Icon,
  RadioButton,
  RadioButtonGroup,
  SmallLabel,
  TextField,
} from 'cube-ui-components';
import { parse, set } from 'date-fns';
import {
  usePostAppointmentLeadActivity,
  usePostContactLeadActivity,
} from 'features/lead/hooks/usePostLeadActivity';
import useBoundStore from 'hooks/useBoundStore';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Pressable, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LeadProfileParamList } from 'types';
import { country } from 'utils/context';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import GATracking from 'utils/helper/gaTracking';
import {
  APPOINTMENT,
  CONTACTED,
  DEFERRED,
  INTERESTED,
  LogActivityFormData,
  NOT_INTERESTED,
} from './logActivityFormUtils';
import useLogActivityForm from './useLogActivityForm';

export default function LogActivityForm() {
  const { t: ct } = useTranslation('common');
  const { t } = useTranslation('leadProfile');
  const { colors, space, sizes } = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation<NavigationProp<LeadProfileParamList>>();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const [isSubmitLoading, setIsSubmitLoading] = useState(false);

  const { logActivityFormDefaultValue, logActivityFormFormSchema } =
    useLogActivityForm();

  const leadId = useBoundStore.getState().lead.inquiringLeadId;

  const { data: leadProfile } = useGetLeadByLeadId(leadId);

  const { mutate: contactedMutate } = usePostContactLeadActivity();
  const { mutate: appointmentMutate } = usePostAppointmentLeadActivity();

  const [feedbackValue, setFeedbackValue] = useState('');

  const LEADS_FEEDBACK_TOGGLE_CONFIG = [
    {
      type: 'interested',
      label: t('leadProfile.logActivityForm.interested'),
      value: INTERESTED,
      icon: <Icon.VeryInterested size={sizes[8]} />,
      onPress: () => setFeedbackValue(INTERESTED),
    },
    {
      type: 'deferred',
      label: t('leadProfile.logActivityForm.deferred'),
      value: DEFERRED,
      icon: <Icon.Thinking size={sizes[8]} />,
      onPress: () => setFeedbackValue(DEFERRED),
    },
    {
      type: 'notInterested',
      label: t('leadProfile.logActivityForm.notInterested'),
      value: NOT_INTERESTED,
      icon: <Icon.NotInterested size={sizes[8]} />,
      onPress: () => setFeedbackValue(NOT_INTERESTED),
    },
  ].filter(
    ({ type }) => country === 'ph' || country === 'id' || type !== 'deferred',
  );

  const NOT_BUYING_RADIO_BTN_CONFIG = [
    {
      type: 'noResponse',
      label: t('leadProfile.logActivityForm.notInterested.noResponse'),
      value: 'No response from client',
    },
    {
      type: 'noFunds',
      label: t('leadProfile.logActivityForm.notInterested.noFunds'),
      value: 'No funds',
    },
    {
      type: 'alreadyHasIes',
      label: t('leadProfile.logActivityForm.notInterested.alreadyHasIes'),
      value: 'Already has insurance policy(ies)',
    },
    {
      type: 'undecided',
      label: t('leadProfile.logActivityForm.notInterested.undecided'),
      value: 'Undecided',
    },
    {
      type: 'interestedButNotReady',
      label: t(
        'leadProfile.logActivityForm.notInterested.interestedButNotReady',
      ),
      value: 'Interested but not ready as of the moment',
    },
    {
      type: 'quoteTooHigh',
      label: t('leadProfile.logActivityForm.notInterested.quoteTooHigh'),
      value: 'Quote too high',
    },
  ];

  const {
    handleSubmit,
    control,
    formState: { isValid },
  } = useForm({
    defaultValues: logActivityFormDefaultValue,
    resolver: yupResolver(logActivityFormFormSchema),
  });

  const onSubmitSuccessAction = () => {
    navigation.navigate('Activities');

    addToast(
      [
        {
          message: t('leadProfile.logActivityForm.logSuccessToast'),
          IconLeft: <Icon.Tick fill={colors.background} />,
        },
      ],
      false,
    );
  };

  const onSubmitErrorAction = () => {
    setIsSubmitLoading(false);
    addToast(
      [
        {
          message: t('leadProfile.logActivityForm.logFailedToast'),
          IconLeft: <Icon.Warning fill={colors.background} />,
        },
      ],
      false,
    );
  };

  const onSubmit = (data: LogActivityFormData) => {
    setIsSubmitLoading(true);
    const {
      activityDate,
      feedback,
      notInterestedReason,
      typeOfActivity,
      yourFeedbackOptional,
    } = data;

    const clonedDate = new Date(activityDate);

    const currentTimeHHMMSS = new Date().toLocaleTimeString([], {
      hourCycle: 'h23',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
    const currentTime = parse(currentTimeHHMMSS, 'HH:mm:ss', new Date());

    const combinedDateTime = set(clonedDate, {
      hours: currentTime.getHours(),
      minutes: currentTime.getMinutes(),
      seconds: currentTime.getSeconds(),
    });

    const selectedDateWithSubmittedTime = combinedDateTime.toISOString();
    const isInterested = feedback === INTERESTED;

    // Type of activity - Contacted
    const contactedRequestData: ContactRequestBody = {
      contactMethod: 'phone',
      success: true,
      reason: isInterested ? null : notInterestedReason,
      actionAt: selectedDateWithSubmittedTime,
      extra: {
        feedback: feedback,
        ...(!isInterested && { feedbackDetails: yourFeedbackOptional }),
      },
    };

    // Type of activity - Appointment
    const appointmentRequestData: AppointmentRequestBody = {
      reason: isInterested ? null : notInterestedReason,
      actionAt: selectedDateWithSubmittedTime,
      extra: {
        feedback: feedback,
        ...(!isInterested && { feedbackDetails: yourFeedbackOptional }),
      },
    };

    if (typeOfActivity === CONTACTED) {
      GATracking.logCustomEvent('lead_contacted', {
        feedback,
        reason: notInterestedReason,
      });

      contactedMutate(
        { leadId: leadId, data: contactedRequestData },
        {
          onSuccess: () => onSubmitSuccessAction(),
          onError: () => onSubmitErrorAction(),
        },
      );
      return;
    }

    if (typeOfActivity === APPOINTMENT) {
      GATracking.logCustomEvent('lead_appointment', {
        feedback,
        reason: notInterestedReason,
      });

      appointmentMutate(
        { leadId: leadId, data: appointmentRequestData },
        {
          onSuccess: () => onSubmitSuccessAction(),
          onError: () => onSubmitErrorAction(),
        },
      );
      return;
    }
  };

  return (
    <>
      <KeyboardAwareScrollView
        bounces={false}
        style={{ backgroundColor: colors.background }}
        contentContainerStyle={[
          {
            paddingTop: sizes[4],
            paddingBottom: sizes[12],
            paddingHorizontal: sizes[4],
          },
          isWideScreen && {
            paddingTop: sizes[5],
            paddingBottom: sizes[15],
            paddingHorizontal: sizes[4],
          },
          isNarrowScreen && {
            paddingTop: sizes[4],
            paddingBottom: sizes[8],
            paddingHorizontal: sizes[3],
          },
        ]}>
        <View style={{ gap: isWideScreen ? sizes[7] : sizes[5] }}>
          <Input
            control={control}
            as={DatePicker}
            name="activityDate"
            label={t('leadProfile.logActivityForm.activityDate')}
            formatDate={val => (val ? dateFormatUtil(val) : '')}
            modalTitle={t('leadProfile.logActivityForm.activityDate')}
            // maxDate={new Date()}
            minDate={
              leadProfile?.createdAt
                ? new Date(leadProfile?.createdAt)
                : undefined
            }
          />

          <Input
            control={control}
            as={Dropdown<string, string>}
            name="typeOfActivity"
            label={t('leadProfile.logActivityForm.typeOfActivity')}
            modalTitle={t('leadProfile.logActivityForm.typeOfActivity')}
            data={[CONTACTED, APPOINTMENT]}
            getItemValue={item => item}
            getItemLabel={item => item}
          />
        </View>

        <View
          style={{
            marginTop: space[8],
            gap: isWideScreen ? sizes[7] : sizes[5],
          }}>
          <H7 fontWeight="bold">
            {t('leadProfile.logActivityForm.leadsFeedback')}
          </H7>

          <View style={{ gap: space[2] }}>
            <Controller
              control={control}
              name={'feedback'}
              render={({ field: { onChange, value: hookFormValue } }) => (
                <>
                  {LEADS_FEEDBACK_TOGGLE_CONFIG.map(
                    ({ type, label, value, icon, onPress }) => (
                      <LeadsFeedbackButton
                        key={'LEADS_FEEDBACK_BTN_' + type}
                        label={label}
                        icon={icon}
                        onSelect={() => {
                          onPress(); // button config onPress
                          onChange(value); // react-hook-form onChange
                        }}
                        isActive={value === hookFormValue}
                      />
                    ),
                  )}
                </>
              )}
            />
          </View>
        </View>

        {(feedbackValue === NOT_INTERESTED || feedbackValue === DEFERRED) && (
          <Animated.View entering={FadeIn} exiting={FadeOut}>
            <View
              style={{
                marginTop: space[8],
                gap: isWideScreen ? sizes[7] : sizes[5],
              }}>
              <H7 fontWeight="bold">
                {t('leadProfile.logActivityForm.notInterested.question')}
              </H7>

              <View style={{ gap: space[5], marginBottom: space[2] }}>
                <Input
                  control={control}
                  as={RadioButtonGroup}
                  name={'notInterestedReason'}>
                  {NOT_BUYING_RADIO_BTN_CONFIG.map(({ type, label, value }) => (
                    <RadioButton
                      key={'NOT_BUYING_RADIO_BTN_' + type}
                      label={label}
                      value={value}
                      style={{ flex: 1 }}
                    />
                  ))}
                </Input>
              </View>

              <Input
                control={control}
                as={TextField}
                name="yourFeedbackOptional"
                label={
                  country === 'id'
                    ? t('leadProfile.logActivityForm.otherFeedbackOptional')
                    : t('leadProfile.logActivityForm.yourFeedbackOptional')
                }
                multiline
                autoExpand
                scrollEnabled
              />

              <SubText>{t('leadProfile.logActivityForm.subText')}</SubText>
            </View>
          </Animated.View>
        )}
      </KeyboardAwareScrollView>

      <View
        style={[
          {
            backgroundColor: colors.background,
            paddingTop: space[4],
            paddingBottom: space[4] + insets.bottom,
          },
          isNarrowScreen && {
            paddingTop: space[3],
            paddingBottom: space[3] + insets.bottom,
          },
        ]}>
        <Button
          disabled={!isValid}
          loading={isSubmitLoading}
          text={ct('save')}
          style={[
            {
              width: '100%',
              maxWidth: 400,
              alignSelf: 'center',
              paddingHorizontal: space[4],
            },
            isNarrowScreen && {
              paddingHorizontal: space[3],
            },
          ]}
          onPress={handleSubmit(data => onSubmit(data as LogActivityFormData))}
        />
      </View>
    </>
  );
}

function LeadsFeedbackButton({
  label,
  icon,
  onSelect,
  isActive,
}: {
  type?: string;
  label: string;
  value?: string;
  icon: React.ReactNode;
  onSelect: () => void;
  isActive: boolean;
}) {
  return (
    <ButtonContainer isActive={isActive} onPress={onSelect}>
      {icon}
      <H7>{label}</H7>
    </ButtonContainer>
  );
}

const ButtonContainer = styled(Pressable)<{ isActive: boolean; theme?: Theme }>(
  ({ isActive, theme }) => ({
    flexDirection: 'row',
    width: '100%',
    backgroundColor: isActive
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    padding: theme.space[4],
    alignItems: 'center',
    borderWidth: isActive ? 2 : 1,
    borderColor: isActive
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
    borderRadius: theme.borderRadius['x-small'],
    gap: theme.space[2],
  }),
);

const SubText = styled(SmallLabel)(({ theme }) => ({
  color: theme.colors.placeholder,
}));
