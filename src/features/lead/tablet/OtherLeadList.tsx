import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Chip, Icon, Row, Typography } from 'cube-ui-components';
import AddNewLeadButton from 'features/lead/tablet/AddNewLeadButton';
import AnimatedViewWrapper from 'features/lead/tablet/components/AnimatedViewWrapper';
import LeadList from 'features/lead/tablet/components/LeadList';
import { LeadTodayLeadToolTip } from 'features/lead/tablet/components/LeadTodayLeadTooltip';
import NoLead from 'features/lead/tablet/components/NoLead';
import { checkHasFilter } from 'features/lead/tablet/helpers';
import LeadFilterModalButton from 'features/lead/tablet/LeadFilterModalButton';
import useBoundStore from 'hooks/useBoundStore';
import { useGetLeadsOther } from 'hooks/useGetLeads';
import React, { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import Animated, {
  FadeIn,
  FadeOut,
  LinearTransition,
} from 'react-native-reanimated';
import { CurrentLeadTabKeys, LeadTypeStats } from 'types';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import EmptyLeadListWithFilters from './EmptyLeadListWithFilters';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { CHANNELS } from 'types/channel';
import { country } from 'utils/context';

export default function OtherLeadList({
  setCurrentTab,
}: {
  setCurrentTab: React.Dispatch<React.SetStateAction<CurrentLeadTabKeys>>;
}) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation('lead');
  const { sortByNewest, filters: othersFilters } = useBoundStore(
    store => store.lead.others,
  );

  const isSomeFilterActive = useMemo(
    () => checkHasFilter(othersFilters),
    [othersFilters],
  );

  const { toggleSort } = useBoundStore(store => store.leadActions.others);

  const [isScrollingDown, setIsScrollingDown] = useState(false);

  useEffect(() => {
    console.log('isScrollingDown: ', isScrollingDown);
  }, [isScrollingDown]);

  const {
    data,
    isLoading,
    isRefetching,
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage,
    refetch,
    isFetching,
  } = useGetLeadsOther();

  const totalCount = data?.pages?.[0]?.totalCount;

  const isEmptyList = (totalCount ?? 0) <= 0;
  const leadCount = numberToThousandsFormat(totalCount ?? 0);

  const leadListData = useMemo(() => {
    if (!data) return [];

    // const flattenedData = data?.pages.map(page => page.data).flat();
    // return flattenedData.concat(demoOtherLeadArray);

    return data?.pages
      .map(page => page?.data)
      .flat()
      .filter(lead => lead?.status !== 'inactive'); // CUBEPH-3404 Hiding inactive leads after transferred
  }, [data]);

  return (
    <>
      {isEmptyList && !isSomeFilterActive && !isFetching ? (
        <Animated.View
          entering={FadeIn}
          style={{
            flex: 1,
          }}>
          <NoLead currentTab={'otherLeads'} setCurrentTab={setCurrentTab} />
          <AddNewLeadButton />
        </Animated.View>
      ) : (
        <View style={{ flex: 1 }}>
          <LeadTodayLeadToolTip />
          <AnimatedViewWrapper style={{ flex: 1 }}>
            {country !== 'id' && (
              <>
                {isScrollingDown ? (
                  <AnimatedViewWrapper />
                ) : (
                  <ScrollToBeHiddenSection
                    layout={LinearTransition.delay(200)}
                    entering={FadeIn.delay(200)}
                    exiting={FadeOut}>
                    <Row justifyContent="space-between">
                      <QuickFilterTagsSection />
                      <LeadFilterModalButton
                        curTab="otherLeads"
                        hasFiltered={isSomeFilterActive}
                      />
                    </Row>
                  </ScrollToBeHiddenSection>
                )}
              </>
            )}
            <Animated.View
              style={{
                flex: 1,
              }}
              layout={LinearTransition.delay(100)}>
              <Row alignItems="center" justifyContent="space-between">
                <Row gap={space[2]}>
                  <Typography.Label color={colors.palette.fwdGreyDarkest}>
                    {/* {t('todayLeads') + ` (${leadCount})`} */}
                    Total ({leadCount})
                  </Typography.Label>
                  <TouchableOpacity
                    onPress={() => toggleSort()}
                    style={{
                      flexDirection: 'row',
                      alignItems: 'center',
                    }}>
                    <Typography.Label
                      fontWeight="bold"
                      color={colors.palette.fwdAlternativeOrange[100]}>
                      {/* {t('todayLeads') + ` (${leadCount})`} */}
                      {sortByNewest ? 'Newest' : 'Oldest'}
                    </Typography.Label>
                    {sortByNewest ? (
                      <Icon.ArrowDown
                        size={sizes[4]}
                        fill={colors.palette.fwdAlternativeOrange[100]}
                      />
                    ) : (
                      <Icon.ArrowUp
                        size={sizes[4]}
                        fill={colors.palette.fwdAlternativeOrange[100]}
                      />
                    )}
                  </TouchableOpacity>
                </Row>
                {country === 'id' && (
                  <LeadFilterModalButton
                    curTab="otherLeads"
                    hasFiltered={isSomeFilterActive}
                  />
                )}
              </Row>
              <Box h={space[4]} />
              <LeadList
                isToday={false}
                leadList={leadListData}
                isRefetching={isRefetching}
                isLoading={isLoading}
                isFetching={isFetching}
                setIsScrollingDown={setIsScrollingDown}
                refetch={refetch}
                ListEmptyComponent={
                  <EmptyLeadListWithFilters hasFilters={isSomeFilterActive} />
                }
              />
            </Animated.View>
          </AnimatedViewWrapper>
          <AddNewLeadButton />
        </View>
      )}
    </>
  );
}

function QuickFilterTagsSection() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('lead');
  const leadStoreKey = 'others';
  const { filters } = useBoundStore(store => store.lead)[leadStoreKey];
  const { updateFilter } = useBoundStore(store => store.leadActions)[
    leadStoreKey
  ];
  const { data: agentProfile } = useGetAgentProfile();
  return (
    <Row flex={1} mr={space[4]} gap={space[2]} alignItems="center">
      <Typography.Label color={colors.palette.fwdGreyDarkest}>
        {t('filterBy')}
      </Typography.Label>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          gap: space[1],
        }}>
        <Chip
          textPosition="right"
          label={t('source.individual')}
          focus={filters.type.individual ?? false}
          icon={() => (
            <Icon.Account
              size={space[3]}
              fill={filters.type.individual ? colors.primary : colors.secondary}
            />
          )}
          onPress={() =>
            updateFilter(
              filters.type.individual
                ? {
                    ...filters,
                    type: {
                      ...filters.type,
                      individual: !filters.type.individual,
                    },
                  }
                : {
                    ...filters,
                    type: {
                      ...filters.type,
                      individual: !filters.type.individual,
                      entity: false,
                    },
                  },
            )
          }
        />

        {agentProfile?.channel !== CHANNELS.BANCA && (
          <Chip
            focus={filters.type.entity ?? false}
            textPosition="right"
            label={t('source.entity')}
            icon={() => (
              <Icon.Office
                size={16}
                fill={filters.type.entity ? colors.primary : colors.secondary}
              />
            )}
            onPress={() =>
              updateFilter(
                filters.type.entity
                  ? {
                      ...filters,
                      type: {
                        ...filters.type,
                        entity: !filters.type.entity,
                      },
                    }
                  : {
                      ...filters,
                      type: {
                        ...filters.type,
                        entity: !filters.type.entity,
                        individual: false,
                      },
                    },
              )
            }
          />
        )}
      </ScrollView>
    </Row>
  );
}

const ScrollToBeHiddenSection = styled(Animated.View)(({ theme }) => ({
  display: 'flex',
  paddingVertical: theme.space[3],
  gap: theme.space[3],
}));

const Spacer = styled.View(({ height }: { height: number }) => ({
  height,
}));
