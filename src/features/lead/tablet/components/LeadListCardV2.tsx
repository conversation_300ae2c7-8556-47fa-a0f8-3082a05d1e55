import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import FlagLabel from 'components/FlagLabel';
import StatusTag from 'components/StatusTag';
import {
  Box,
  Column,
  Icon,
  Row,
  SvgIconProps,
  Typography,
} from 'cube-ui-components';
import {
  add,
  differenceInHours,
  differenceInMinutes,
  parseISO,
  isPast,
} from 'date-fns';
import LeadScore from 'features/lead/components/LeadProfile/LeadScore';
import { getSourceLabels } from 'features/lead/utils';
import { useRootStackNavigation } from 'hooks/useRootStack';
import _ from 'lodash';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity } from 'react-native';
import Animated, { FadeInUp, LinearTransition } from 'react-native-reanimated';
import { Lead, LeadStatus } from 'types/lead';
import { country } from 'utils/context';

export default function LeadListCard(
  props: Partial<Lead> & { isToday?: boolean; isLoading?: boolean },
) {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['lead', 'leadProfile']);
  const { navigate } = useRootStackNavigation();

  const leadSourceKeys = getSourceLabels(props.sourceIds);
  const sourcesLabel = leadSourceKeys
    .map(label => t(`lead:source.${label}`))
    .join(', ');

  const opportunityTime =
    props.updatedAt ?? props.opportunityUpdatedAt ?? props.createdAt;
  const expirationTime = add(parseISO(opportunityTime ?? ''), { hours: 24 });
  const timeInMinutesLeftToExpire = differenceInMinutes(
    expirationTime,
    new Date(),
  );
  const timeInHoursLeftToExpire = differenceInHours(expirationTime, new Date());

  const isMinutes = timeInHoursLeftToExpire < 1;

  const isToday = props.isToday ?? false;
  const isEntity = props.isIndividual === false ?? false;
  // * is Existing Customer TBC for MYS takaful
  const isCustomer = false;

  const leadName = isEntity
    ? props?.companyName ?? '--'
    : `${props?.firstName} ${props?.lastName} `;

  const isNew =
    isToday &&
    !isPast(add(parseISO(props.createdAt ?? ''), { hours: 24 })) &&
    props.status === 'created';
  return (
    <TouchableOpacity
      onPress={() =>
        navigate('LeadProfile', {
          id: props.id ?? '',
          isIndividualLead: !isEntity,
        })
      }>
      <LeadListCardContainer
        layout={LinearTransition.duration(200)}
        entering={FadeInUp}>
        {/* <Typography.H6 fontWeight="bold">Conversion Report</Typography.H6> */}
        <Column flex={1} gap={space[1]}>
          <Box flex={1} gap={space[1]}>
            {/* //* Row 1: Name */}
            <Row alignItems="center">
              <Box mr={space[1]}>
                {isEntity ? (
                  <Icon.Office size={space[4]} />
                ) : (
                  <Icon.Account size={space[4]} />
                )}
              </Box>
              <Typography.LargeLabel fontWeight="bold" color={colors.primary}>
                {/* {props?.firstName} {props?.lastName}{' '} */}
                {leadName}
              </Typography.LargeLabel>
            </Row>
            {country === 'ib' ? (
              <IBLeadListCardSource props={props} />
            ) : (
              <>
                {/* //* Row 2: Soruce */}
                <Row alignItems="center">
                  <Typography.Label color={colors.palette.fwdDarkGreen[100]}>
                    From {sourcesLabel ? sourcesLabel : '--'}
                  </Typography.Label>
                </Row>
                {/* //* Row 3: InterestedCategories */}
                <Row>
                  <Typography.Label color={colors.palette.fwdDarkGreen[50]}>
                    {'Interested in ' +
                      (props.interestedCategories &&
                      props.interestedCategories.length > 0
                        ? props.interestedCategories.join(', ')
                        : '--')}
                  </Typography.Label>
                </Row>
              </>
            )}
          </Box>
          {/* //* Row 4: Tags */}
          <Row justifyContent="space-between">
            <LeftTagContainer>
              <Row gap={sizes[1]}>
                {isNew && (
                  <StatusTag
                    text={'New'}
                    backgroundColor={colors.primaryVariant2}
                    textColor={colors.primary}
                  />
                )}
                {props?.status && <LeadStatusTag status={props.status} />}
                {isCustomer && <LeadStatusTag type="customer" />}
              </Row>
            </LeftTagContainer>
            <RightTagContainer>
              {isToday && (
                <TimeCountDownTag
                  isAlert={timeInHoursLeftToExpire < 1}
                  labelType={isMinutes ? 'mins' : 'hrs'}
                  diff={
                    isMinutes
                      ? timeInMinutesLeftToExpire
                      : timeInHoursLeftToExpire
                  }
                />
              )}
            </RightTagContainer>
          </Row>
        </Column>
        <Column justifyContent="center">
          <Icon.ChevronRight
            fill={colors.palette.fwdDarkGreen[50]}
            size={sizes[5]}
          />
        </Column>
      </LeadListCardContainer>
    </TouchableOpacity>
  );
}

const IBLeadListCardSource = ({ props }: { props: Partial<Lead> }) => {
  const { colors } = useTheme();
  const { t } = useTranslation(['lead']);
  const sourcesLabel = getSourceLabels(props.sourceIds)
    .map(label => t(`lead:source.${label}`))
    .join(', ');
  const campaignName = props?.campaignName;

  const interestProduct = !_.isEmpty(props?.interestedProducts)
    ? props?.interestedProducts?.join(', ')
    : !_.isEmpty(props?.interestedCategories)
    ? props?.interestedCategories?.join(', ')
    : undefined;
  return (
    <>
      {/* //* Row 2: LeadScore */}
      {props?.extra?.engagement_score && (
        <Row alignItems="center">
          <Typography.Label
            children={t('lead:leadScore') + ': '}
            color={colors.secondary}
          />
          <LeadScore leadScore={props?.extra?.engagement_score} />
        </Row>
      )}
      {/* //* Row 3: LeadOrigin */}
      <Row alignItems="center">
        <Typography.Label color={colors.palette.fwdDarkGreen[100]}>
          {`${t('lead:leadOrigin') + ':'} ${sourcesLabel || '--'}`}
        </Typography.Label>
      </Row>
      {/* //* Row 4: Campaign */}
      {campaignName && (
        <Row alignItems="center">
          <Typography.Label color={colors.palette.fwdDarkGreen[100]}>
            {`${t('lead:campaign') + ':'} ${campaignName}`}
          </Typography.Label>
        </Row>
      )}
      {/* //* Row 5: InterestedProducts */}
      {interestProduct && (
        <Row>
          <Typography.Label color={colors.palette.fwdDarkGreen[50]}>
            {t('lead:interestedIn') + ' ' + interestProduct}
          </Typography.Label>
        </Row>
      )}
    </>
  );
};

export const TimeCountDownTag = ({
  isAlert = false,
  diff,
  labelType,
}: {
  isAlert?: boolean;
  diff: number;
  labelType: 'mins' | 'hrs';
}) => {
  const { colors, space, borderRadius } = useTheme();
  const colorStyle = {
    default: {
      textColor: colors.palette.fwdBlue[100],
      backgroundColor: colors.palette.alertGreenLight,
    },
    alert: {
      textColor: colors.palette.alertRed,
      backgroundColor: colors.palette.alertRedLight,
    },
  } as const;

  const colorMode: keyof typeof colorStyle = isAlert ? 'alert' : 'default';
  return (
    <Row
      pl={space[1]}
      pr={space[2]}
      py={space[1] / 2}
      gap={space[1]}
      alignItems={'center'}
      backgroundColor={colorStyle[colorMode].backgroundColor}
      borderRadius={borderRadius.large}>
      <Icon.OperatingHours
        size={space[4]}
        fill={colorStyle[colorMode].textColor}
      />
      <Typography.SmallLabel color={colorStyle[colorMode].textColor}>
        {/* {dateTimeElapsed.elapsedTime + ' ' + dateTimeElapsed.unit + ' ago'} */}
        {(diff ?? '') + ' ' + (labelType ?? '') + ' left'}
      </Typography.SmallLabel>
    </Row>
  );
};
const LeadStatusTag = (
  props:
    | {
        type?: 'default';
        status: LeadStatus | null;
      }
    | {
        type: 'customer';
      },
) => {
  const { t } = useTranslation(['lead']);
  const { colors, sizes, space } = useTheme();
  const { type } = props;

  if (type === 'customer') {
    return (
      <FlagLabel
        medium
        containerStyle={{ height: space[5] }}
        type={'pending_allLightBlue'}
        content={'Existing Customer'}
        Icon={() => (
          <Icon.SuccessAccount
            fill={colors.palette.fwdBlue[100]}
            size={sizes[3]}
          />
        )}
      />
    );
  }

  const { status } = props;
  switch (status) {
    case 'contacted':
    case 'appointment':
    case 'illustration':
    case 'submitted':
    case 'not_interested':
      return (
        <FlagLabel
          medium
          containerStyle={{ height: space[5] }}
          type={'leadStatus_lightGreen20'}
          content={t(LEAD_STATUS[status].content)}
          Icon={() => {
            const Icon = LEAD_STATUS[status].Icon;
            return <Icon fill={colors.palette.alertGreen} size={sizes[3]} />;
          }}
        />
      );

    default:
      return null;
  }
};

export const LEAD_STATUS: Record<
  Extract<
    LeadStatus,
    | 'contacted'
    | 'appointment'
    | 'illustration'
    | 'submitted'
    | 'not_interested'
  >,
  { label: string; content: string; Icon: (props: SvgIconProps) => JSX.Element }
> = {
  contacted: {
    label: 'IRIS.leads.status.contacted',
    content: 'lead:status.contacted',
    Icon: Icon.Tick,
  },

  appointment: {
    label: 'IRIS.leads.status.meetingCompleted',
    content: 'lead:status.appointment',
    Icon: Icon.Tick,
  },
  illustration: {
    label: 'IRIS.leads.status.proposalCreated',
    content: 'lead:status.illustration',
    Icon: Icon.Document,
  },
  submitted: {
    label: 'IRIS.leads.status.submitted',
    content: 'lead:status.submitted',
    Icon: Icon.Document,
  },
  not_interested: {
    label: 'IRIS.leads.status.not_interested',
    content: 'lead:status.contacted',
    Icon: Icon.Tick,
  },
};

const LeadListCardContainer = styled(Animated.View)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  paddingVertical: theme.space[3],
  paddingLeft: theme.space[4],
  paddingRight: theme.space[2],
  minHeight: theme.space[28],
  gap: theme.space[2],
  flexDirection: 'row',
}));

const LeftTagContainer = styled(Row)(({ theme }) => ({
  gap: theme.sizes[1],
}));

const RightTagContainer = styled(Column)(({ theme }) => ({
  display: 'flex',
  alignSelf: 'center',
  gap: theme.sizes[1],
}));
