export default {
  'leadProfile.leadDetail': 'Lead details',
  'leadProfile.profileDetails.entityName': 'Company name',
  'leadProfile.leadInfoSection.natureOfBusiness':
    'Industry / Nature of business',
  'leadProfile.customerDetail': 'Customer details',
  'leadProfile.profile': 'Profile',
  'leadProfile.opportunity': 'Opportunity',
  'leadProfile.fna': 'CFF & RPQ',
  'leadProfile.rpq': 'RPQ',
  'leadProfile.proposals': 'Proposals',
  'leadProfile.activities': 'Activities',
  'leadProfile.policies': 'Existing policies',
  'leadProfile.insureds': 'Insureds',
  'leadProfile.primaryPolicy': 'Primary policy',
  'leadProfile.subsidiaryPolicy': 'Subsidiary Policy',
  // Lead info. section
  'leadProfile.leadInfoSection.viewProfile': 'View profile',
  'leadProfile.leadInfoSection.viewDetails': 'View details',
  'leadProfile.leadInfoSection.viewMore': 'View more',
  'leadProfile.leadInfoSection.occupation': 'Occupation',
  'leadProfile.leadInfoSection.basicInfo': 'Basic info',
  'leadProfile.leadInfoSection.yearsOld': 'years old',
  'leadProfile.leadInfoSection.leadScore': 'Lead score',
  'leadProfile.leadInfoSection.leadSource': 'Lead source',
  'leadProfile.leadInfoSection.businessPhoneNumber': 'Business phone number',
  'leadProfile.leadInfoSection.email': 'Email',

  // Lead profile bottom bar
  'leadProfile.bottomBar.proposal': 'Proposal',
  'leadProfile.bottomBar.activity': 'Activity',
  'leadProfile.bottomBar.contact': 'Contact',
  // Lead profile tablet Side bar
  'leadProfile.sideBar.profile': 'Profile',
  'leadProfile.sideBar.si': 'Benefit illustration',
  'leadProfile.sideBar.inApp': 'In application',
  'leadProfile.sideBar.activitiesRecord': 'Activities Record',

  // Lead profile details
  'leadProfile.profileDetails.leadCreateDate': 'Lead create date',
  'leadProfile.profileDetails.natureOfBusiness': 'Nature of business',
  'leadProfile.profileDetails.authorisedSignatoryName':
    'Authorised signatory name',
  'leadProfile.profileDetails.companyName': 'Company name',
  'leadProfile.profileDetails.businessPhoneNumber': 'Business phone number',
  'leadProfile.profileDetails.interestProduct': 'Interested product',
  'leadProfile.profileDetails.contactNo': 'Contact no.',
  'leadProfile.profileDetails.leadsId': 'Lead ID',
  'leadProfile.profileDetails.birthday': 'Birthday',
  'leadProfile.profileDetails.receiveDate': 'Receive Date',
  'leadProfile.profileDetails.source': 'Source',
  'leadProfile.profileDetails.profile': 'Profile',
  'leadProfile.profileDetails.profileDetails': 'Profile details',
  'leadProfile.profileDetails.personalInfo': 'Personal info',
  'leadProfile.profileDetails.salutation': 'Salutation/Title',
  'leadProfile.profileDetails.fullName': 'Full name',
  'leadProfile.profileDetails.firstName': 'First name',
  'leadProfile.profileDetails.middleName': 'Middle name',
  'leadProfile.profileDetails.lastName': 'Last name',
  'leadProfile.profileDetails.extensionName': 'Extension name',
  'leadProfile.profileDetails.gender': 'Gender',
  'leadProfile.profileDetails.dob': 'Date of birth',
  'leadProfile.profileDetails.age': 'Age',
  'leadProfile.profileDetails.maritalStatus': 'Marital status',
  'leadProfile.profileDetails.leadSource': 'Lead source',
  'leadProfile.profileDetails.contactDetails': 'Contact details',
  'leadProfile.profileDetails.mobileNumber': 'Mobile number',
  'leadProfile.profileDetails.leadScore': 'Lead score',
  'leadProfile.profileDetails.leadOrigin': 'Lead origin',
  'leadProfile.profileDetails.campaignName': 'Campaign name',
  'leadProfile.profileDetails.notes': 'Notes',
  'leadProfile.profileDetails.email': 'Email',
  'leadProfile.profileDetails.referralDetails': 'Referral details',
  'leadProfile.profileDetails.affiliateCode': 'Affiliate code',
  'leadProfile.profileDetails.campaignCode': 'Campaign code',
  'leadProfile.profileDetails.householdDetails': 'Household details',
  'leadProfile.profileDetails.noInfo': 'No Information',
  'leadProfile.profileDetails.bltsRefNumber': 'BLTS reference no.',
  'leadProfile.profileDetails.servicingBranch': 'Servicing Branch',
  'leadProfile.profileDetails.bankCustomerId': 'Bank Customer ID',
  'leadProfile.profileDetails.referrerCode': 'Referrer code',
  'leadProfile.profileDetails.entityDetails': 'Profile details',
  'leadProfile.profileDetails.registrationDate': 'Date of registration',

  //Lead profile protection score section
  'leadProfile.termLife': 'Life/ Family Coverage',
  'leadProfile.criticalIllness': 'Critical Illness Coverage',
  'leadProfile.medical': 'Medical/ Health Coverage Monthly Amount',
  'leadProfile.investmentSavings': 'Savings/ Investment Monthly Amount',
  'leadProfile.recommendedTarget': 'Recommended target: ',
  'leadProfile.month': ' / month',
  'leadProfile.yourProtectionScore': 'Your protection score',
  'leadProfile.current': 'Current',

  // Lead profile for SI tab
  'si.title': 'Saved Proposals',
  'leadProfile.si.addNewSI': 'Create new Benefit illustration',
  'leadProfile.si.addNewSIv2': 'Create new proposal',
  'leadProfile.si.tableHeaderField.si': 'Benefit illustration',
  'leadProfile.si.tableHeaderField.product': 'Product',
  'leadProfile.si.tableHeaderField.sumCovered': 'Sum Covered (RM)',
  'leadProfile.si.tableHeaderField.premium': 'Contributions (RM)',
  'leadProfile.si.tableHeaderField.lastUpdated': 'Last update date',

  // Lead profile for InApp tab
  'leadProfile.inApp.addNewSI': 'Create new Benefit illustration',
  'leadProfile.inApp.tableHeaderField.si': 'Benefit illustration',
  'leadProfile.inApp.tableHeaderField.product': 'Product',
  'leadProfile.inApp.tableHeaderField.sumCovered': 'Sum Covered (RM)',
  'leadProfile.inApp.tableHeaderField.premium': 'Contributions (RM)',
  'leadProfile.inApp.tableHeaderField.lastUpdated': 'Last update date',
  // Activities Screen
  'leadProfile.activityRecord.title': 'Activity record',
  'leadProfile.activityRecord.chart.currentStatus': 'Current status',
  'leadProfile.activityRecord.chart.not_contacted': 'Not contacted',
  'leadProfile.activityRecord.chart.contacted': 'Contacted',
  'leadProfile.activityRecord.chart.appointment': 'Appointment',
  'leadProfile.activityRecord.chart.illustration': 'Illustration',
  'leadProfile.activityRecord.chart.submitted': 'Submitted',
  'leadProfile.activityRecord.action.contact': 'Contacted',
  'leadProfile.activityRecord.action.appointment': 'Appointment',
  'leadProfile.activityRecord.action.submit': 'Submitted',
  'leadProfile.activityRecord.action.illustrate': 'Illustration',
  'leadProfile.activityRecord.action.not_interested': 'Not interested',
  'leadProfile.activityRecord.action.defer': 'Deferred',
  'leadProfile.activityRecord.action.applicationSubmitted':
    'Application submitted',
  'leadProfile.activityRecord.action.illustrateCreated': 'Illustration created',
  'leadProfile.activityRecord.feedback.deferred': 'Deferred',
  'leadProfile.activityRecord.feedback.interested': 'Interested',
  'leadProfile.activityRecord.feedback.notInterested':
    'The lead unlikely interested',
  'leadProfile.overallLeadFeedback.title': 'Overall lead feedback',
  'leadProfile.overallLeadFeedback.lastUpdate': 'Last update',
  'leadProfile.activityRecord.logTable.type': 'Activity type',
  'leadProfile.activityRecord.logTable.description': 'Description',
  'leadProfile.activityRecord.logTable.lastCreatedDate': 'Last create date',
  'leadProfile.activityRecord.logTable.date': 'Date',
  'leadProfile.activityRecord.log.title': 'Activity log',
  'leadProfile.activityRecord.log.emptyRecord': 'Empty Record',

  // Log activity form
  'leadProfile.logActivityForm.title': 'Log Activity',
  'leadProfile.logActivityForm.logActivity': 'Log activity',
  'leadProfile.logActivityForm.activityDate': 'Activity date',
  'leadProfile.logActivityForm.typeOfActivity': 'Type of activity',
  'leadProfile.logActivityForm.leadsFeedback': 'Lead’s feedback',
  'leadProfile.logActivityForm.contacted': 'Contacted',
  'leadProfile.logActivityForm.appointment': 'Appointment',

  'leadProfile.logActivityForm.interested': 'Interested',
  'leadProfile.logActivityForm.notInterested': 'Not interested',
  'leadProfile.logActivityForm.deferred': 'Deferred',
  'leadProfile.logActivityForm.notInterested.question':
    'Why is the lead not interested?',
  'leadProfile.logActivityForm.notInterested.tooExpensive':
    'Premium too expensive',
  'leadProfile.logActivityForm.notInterested.unsuitable': 'Unsuitable benefit',
  'leadProfile.logActivityForm.notInterested.windowShopping':
    'Window shopping customer',
  'leadProfile.logActivityForm.yourFeedbackOptional':
    'Your feedback (optional)',
  'leadProfile.logActivityForm.otherFeedbackOptional':
    'Other feedback (optional)',
  'leadProfile.logActivityForm.subText':
    'Thank you for meeting up with the prospect. You may use this form to report or share your feedback or any misuse by the prospect. We may also share your comments with another agent who may receive a request later on.',
  'leadProfile.logActivityForm.logSuccessToast': 'Activity logged.',
  'leadProfile.logActivityForm.logSuccessToastV2': 'A new activity is logged.',
  'leadProfile.logActivityForm.logFailedToast': 'Log activity failed.',
  'leadProfile.logActivityForm.notInterested.noResponse':
    'No response from client',
  'leadProfile.logActivityForm.notInterested.noFunds': 'No funds',
  'leadProfile.logActivityForm.notInterested.alreadyHasIes':
    'Already has insurance policy(ies)',
  'leadProfile.logActivityForm.notInterested.undecided': 'Undecided',
  'leadProfile.logActivityForm.notInterested.interestedButNotReady':
    'Interested but not ready as of the moment',
  'leadProfile.logActivityForm.notInterested.quoteTooHigh': 'Quote too high',
  'leadProfile.logActivityForm.notInterested.deferred': 'Deferred',

  // Contact
  'leadProfile.contact.contact.title': 'Contact your lead',
  'leadProfile.contact.call': 'Call',
  'leadProfile.contact.sms': 'SMS',
  'leadProfile.contact.whatsapp': 'WhatsApp',
  'leadProfile.contact.viber': 'Viber',
  'leadProfile.contact.email': 'Email',
  'leadProfile.contact.more': 'More',

  //FNA
  'leadProfile.fna.fna': 'CFF result',
  'leadProfile.fna.expiryDate': 'Expiry date',
  'leadProfile.fna.startFNA': 'Start CFF',
  'leadProfile.fna.startNewFNA': 'Start new FNA',
  'leadProfile.fna.editFNA': 'Edit FNA',
  'leadProfile.fna.viewPDF': 'View PDF',
  'leadProfile.fna.valid': 'Valid',
  'leadProfile.fna.expired': 'Expired',
  'leadProfile.fna.redoFNA': 'Redo',
  'leadProfile.fna.redoRPQ': 'Redo RPQ',
  'leadProfile.fna.reviseRPQ': 'Revise RPQ',
  'leadProfile.fna.recommendProduct': 'Recommended product',
  'leadProfile.fna.savingNeeds': 'Saving needs',
  'leadProfile.fna.totalNeeds': 'Total needs',
  'leadProfile.fna.totalCurrentAssets': 'Total current assets',
  'leadProfile.fna.totalGapToTargetAmount': 'Total gap to target amount',
  'leadProfile.fna.breakDown': 'Breakdown',
  'leadProfile.fna.currentAssets': 'Current assets',
  'leadProfile.fna.gapToTargetAmount': 'Gap to target amount',
  'leadProfile.fna.protectionNeeds': 'Protection needs',
  'leadProfile.fna.incomeProtection': 'Income Protection',
  'leadProfile.fna.php': 'PHP',
  'leadProfile.fna.totalNeedsTarget': 'Total needs target',
  'leadProfile.fna.retirement': 'Retirement Income',
  'leadProfile.fna.investment': 'Investments',
  'leadProfile.fna.childEducation': `Child's Education`,
  'leadProfile.fna.medicalPlanning': `Medical / Healthcare Planning`,
  'leadProfile.fna.lifeAchievement': 'Life achievement',
  'leadProfile.fna.healthProtection': 'Medical / Healthcare Planning',
  'leadProfile.fna.loanCoverage': 'Debt cancellation',
  'leadProfile.fna.saving': 'Savings',
  'leadProfile.fna.target': 'Target',
  'leadProfile.fna.rpq': 'RPQ',
  'leadProfile.fna.startRPQ': 'Start RPQ',
  'leadProfile.fna.notDoneYet': 'Not done yet',
  'leadProfile.fna.you': 'You',
  'leadProfile.fna.score': 'Score',
  'leadProfile.fna.riskLevel': 'Risk level',
  'leadProfile.fna.suitableProductRisk': 'Suitable product risk',
  'leadProfile.fna.conservative': 'Conservative',
  'leadProfile.fna.balanced': 'Balanced',
  'leadProfile.fna.low': 'Low',
  'leadProfile.fna.aggressive': 'Aggressive',
  'leadProfile.fna.riskProfileTitle':
    'Investor Risk Profile and Investment Policy Statement',
  'leadProfile.fna.riskProfileDescription':
    'Refers to investors who are suitable for relatively low risk asset classes and price fluctuation which achieve better yield than deposits and inflation rate. Investors with this profile may invest in funds which targets long term growth through investments in a diverse mix of high quality, medium to long term fixed securities such as government securities, corporate bonds and notes.',
  'leadProfile.fna.knowYourCustomer.title': 'Know Your Customer',
  'leadProfile.fna.knowYourCustomer.content':
    'Complete CFF to understand your customer needs and get product recommendations',
  'leadProfile.fna.knowYourCustomer.resume': 'Resume CFF',
  'leadProfile.fna.summary.title': 'Summary of Customer Fact Find',
  'leadProfile.fna.summary.viewDetails': 'View details',
  'leadProfile.fna.summary.productRecommendation': 'Product recommendation',
  'leadProfile.fna.summary.productRecommendation.sequential.option':
    '{{sequence}} recommended option',
  'leadProfile.fna.summary.financialGoals': 'Financial goals',
  'leadProfile.fna.summary.protection.low': 'Low protected',
  'leadProfile.fna.summary.protection.fair': 'Fair protected',
  'leadProfile.fna.summary.protection.high': 'High protected',

  'leadProfile.fna.savingNeeds.totalNeeds': 'Total needs:',
  'leadProfile.fna.savingNeeds.totalCurrentCoverage': 'Total current coverage:',
  'leadProfile.fna.savingNeeds.totalGapToTargetAmount':
    'Total gap to target amount:',
  'leadProfile.fna.bar.target': 'Target: RM {{target}}',
  'leadProfile.fna.bar.current': 'Current: RM {{current}}',
  'leadProfile.fna.bar.gap': 'Gap: RM {{gap}}',
  'header.productBrochure': 'Product brochure',

  // opportunity tab
  'leadProfile.opportunities.investment&savings': 'Investment & Savings',
  'leadProfile.opportunities.coverageGap': 'Coverage gap: ',
  'leadProfile.opportunities.you': 'You',
  'leadProfile.opportunities.php': 'PHP',
  'leadProfile.opportunities.peopleLikeYou': 'People like you',
  'leadProfile.opportunities.recommendation':
    'Recommended product to enhance your coverage:',
  'leadProfile.opportunities.criticalIllness': 'Critical illness',
  'leadProfile.opportunities.protection': 'Protection',
  'leadProfile.opportunities.accident&disability': 'Accident & Disability',
  'leadProfile.opportunities.boostYourProtection': 'Boost your protection',
  'leadProfile.opportunities.protected':
    'Well done! You are well protected in this area. Continue to improve your score by exploring other areas.',

  'leadProfile.protection.underProtected': 'You are under protected.',
  'leadProfile.protection.moderatelyProtected': 'You are moderately protected.',
  'leadProfile.protection.wellProtected': 'You are well protected.',
  'leadProfile.protection.viewAllScoreLevels': 'View all score levels',
  'leadProfile.protection.protectionScore': 'Protection score',
  'leadProfile.viewAllScoreLevels.protectionScore': 'AI Protection Score',
  'leadProfile.viewAllScoreLevels.underProtected': 'Under protected',
  'leadProfile.viewAllScoreLevels.underProtectedDesc':
    "You don't seem to have enough coverage. To improve your score, explore adding more coverage in suggested areas or share other existing insurance coverage for a more accurate evaluation.",
  'leadProfile.viewAllScoreLevels.moderatelyProtected': 'Moderately protected',
  'leadProfile.viewAllScoreLevels.moderatelyProtectedDesc':
    "Great job on your progress! Why don't we take it to the next level? We've spotted areas where you can further enhance your coverage and boost your overall protection.",
  'leadProfile.viewAllScoreLevels.wellProtected': 'Well protected',
  'leadProfile.viewAllScoreLevels.wellProtectedDesc':
    "Wow! Your coverage is excellent! Keep it going and remember to maintain and regularly review your coverage. We've also identified a few minor areas that you could benefit from.",
  'leadProfile.viewAllScoreLevels.digitalAward': 'Digital CX Award',
  'leadProfile.scoreInfo.title': 'What is AI Protection Score?',
  'leadProfile.scoreInfo.first':
    'It serves as a guide on how you can improve your coverage to fill your protection gap.',
  'leadProfile.scoreInfo.second':
    'The Protection Score reflects your level of protection coverage compared to people similar to you.',

  // Profile section

  'leadProfile.profileDetails.name': 'Name',
  'leadProfile.profileDetails.smokingHabit': 'Smoking habit',
  'leadProfile.profileDetails.smoker': 'Smoker',
  'leadProfile.profileDetails.nonSmoker': 'Non-smoker',
  'leadProfile.profileDetails.religion': 'Religion',
  'leadProfile.profileDetails.nationality': 'Nationality',
  'leadProfile.profileDetails.countryOfResidence': 'Country of Residence',

  'leadProfile.profileDetails.remark': 'Remark',

  'leadProfile.fna.fnaResult': 'CFF result',
  'leadProfile.fna.redo': 'Redo',
  'leadProfile.fna.viewPdf': 'View PDF',
  'leadProfile.fna.fnaRecommended': 'CFF recommended',
  'leadProfile.fna.recommendedProducts': 'Recommended products',
  'leadProfile.fna.forYou': 'For you',
  'leadProfile.fna.howMuchYouNeed': 'How much you need',
  'leadProfile.fna.disposableIncome': 'Disposable income',
  'leadProfile.fna.currentLifeStage': 'Current life stage',
  'leadProfile.fna.haveAPartner': 'Have a partner?',
  'leadProfile.fna.partnerName': 'Partner’s name',
  'leadProfile.fna.wantToMarry': 'Want to marry',
  'leadProfile.fna.noOfKids': 'No. of kids',
  'leadProfile.fna.noOfDependent': 'No. of dependant',
  'leadProfile.fna.dependant': 'Dependant',
  'leadProfile.fna.ageToRetire': 'Age to retire',
  'leadProfile.fna.totalSavingNeeds': 'Total saving needs:',
  'leadProfile.fna.currency': 'RM',
  'leadProfile.fna.priority': '{{number}} priority',
  'leadProfile.fna.otherGoal': 'Other goal',
  'leadProfile.fna.inYears': 'In {{years}} years',
  'leadProfile.fna.protectionGoals': 'Protection goals',
  'leadProfile.fna.totalCoverNeeds': 'Total cover needs:',
  'leadProfile.fna.covers': '{{number}} covers',
  'leadProfile.fna.profile.details': 'Personal details',
  'leadProfile.fna.existing.policies': 'Existing policies',

  // Log activity form
  'leadProfile.logActivityForm.date': 'Date',

  'leadProfile.pdf.fnaDocument': 'FNA document',
  'leadProfile.pdf.rpqDocument': 'RPQ document',

  'leadProfile.profileDetails.personalDetails': 'Personal details',
  'leadProfile.profileDetails.nationalityDetails': 'Nationality details',
  'leadProfile.profileDetails.occupationDetails': 'Occupation details',
  'leadProfile.profileDetails.addressInformation': 'Address information',
  'leadProfile.profileDetails.preferredContactMode': 'Preferred contact mode',
  'leadProfile.profileDetails.correspondenceViaEmail':
    'Correspondence via email',
  'leadProfile.profileDetails.homePhone': 'Home phone',
  'leadProfile.profileDetails.officePhone': 'Office phone',
  'leadProfile.profileDetails.motherMaidenName': "Mother's maiden name",
  'leadProfile.profileDetails.idType': 'ID Type',
  'leadProfile.profileDetails.idNumber': 'ID number',
  'leadProfile.profileDetails.taxId': 'Tax ID',
  'leadProfile.profileDetails.pob.country': 'Place of birth - Country',
  'leadProfile.profileDetails.pob.state': 'Place of birth - State',
  'leadProfile.profileDetails.pob.city': 'Place of birth - City',
  'leadProfile.profileDetails.cityName': 'City name',
  'leadProfile.profileDetails.industry': 'Industry',
  'leadProfile.profileDetails.occupation': 'Occupation',
  'leadProfile.profileDetails.occupationClass': 'Occupation class',
  'leadProfile.profileDetails.occupationSector': 'Occupation sector',
  'leadProfile.profileDetails.occupationPosition': 'Occupation position',
  'leadProfile.profileDetails.annualIncome': 'Annual income',
  'leadProfile.profileDetails.address': 'Address',
  'leadProfile.profileDetails.correspondenceAddress': 'Correspondence address',
  'leadProfile.profileDetails.country': 'Country',
  'leadProfile.profileDetails.province': 'Province',
  'leadProfile.profileDetails.city': 'City',
  'leadProfile.profileDetails.postCode': 'Postcode',

  'leadProfile.scoreInfo.v2.title': 'Lead score',
  'leadProfile.scoreInfo.v2.first':
    'Using Al-driven behavioural insights to predict lead engagement and likelihood to purchase.',
  'leadProfile.scoreInfo.v2.highInterest': 'High interest',
  'leadProfile.scoreInfo.v2.moderateInterest': 'Moderate interest',
  'leadProfile.scoreInfo.v2.openToExplore': 'Open to explore',
};
