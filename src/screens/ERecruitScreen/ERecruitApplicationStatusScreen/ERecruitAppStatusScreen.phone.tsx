import React from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ERecruitApplicationList } from 'types';
import { country } from 'utils/context';
import ERecruitAppStatusLayoutIB from 'features/eRecruit/ib/phone/ERecruitAppStatusLayout';

type ERAppStatusProps = NativeStackScreenProps<
  ERecruitApplicationList,
  'ERecruitApplicationStatus'
>;
export default function ERecruitAppStatusScreenPhone(props: ERAppStatusProps) {
  switch (country) {
    case 'ph':
      return <></>;
    case 'my':
    case 'ib':
    case 'id':
      return <ERecruitAppStatusLayoutIB {...props} />;
    default:
      return <></>;
  }
}
