// * Navigator
import PoliciesNewBusinessNavigator from 'features/policy/navigation/PoliciesNewBusinessNavigator';
import POSNavigator from 'features/policy/navigation/POSNavigator';

// * Screens
import FWDNewsScreen from 'screens/FWDNewsScreen';
import LeadAndCustomerSearch from 'screens/LeadAndCustomerSearchScreen';
import BadgesCollectionScreen from 'screens/BadgesCollectionScreen/BadgesCollectionScreen';
import LeadsConversionScreen from 'screens/TeamLeadConversionScreen';
import FWDNewsDetailsScreen from 'screens/FWDNewsScreen/FWDNewsDetailsScreen';
import FWDNewsBookmarkScreen from 'screens/FWDNewsScreen/FWDNewsBookmarkScreen';
import AddNewLeadOrEntityScreen from 'screens/AddNewLeadOrEntityScreen';
import LeadProfileScreen from 'screens/LeadProfileScreen';
import AddNewEntityScreen from 'screens/AddNewEntityScreen';
import ContactBookScreen from 'screens/ContactBookScreen';
import LeadProfileDetailsScreen from './LeadProfileScreen/LeadProfileDetailsScreen';
import CustomerProfileDetailsScreen from 'screens/CustomerProfileDetailsScreen/CustomerProfileDetailsScreen';
import RecognitionDetailsScreen from 'screens/RecognitionDetailsScreen/RecognitionDetailsScreen';
import LogActivityScreen from 'screens/LeadProfileScreen/LogActivityScreen';
import ERecruitAppScreen from 'screens/ERecruitScreen/ERecruitApplicationScreen';
import ERecruitAppStatusScreen from 'screens/ERecruitScreen/ERecruitApplicationStatusScreen';
import ERecruitCheckAppScreen from './ERecruitScreen/ERecruitCheckApplicationScreen/ERecruitCheckAppScreen';
import PerformanceTargetScreen from 'screens/PerformanceTargetScreen';
import EliteAgencyDetailsScreen from 'screens/EliteAgencyDetailsScreen';
import EliteAgencyRequirementsScreen from 'screens/EliteAgencyRequirementsScreen';
import EliteAgencyBenefitsScreen from 'screens/EliteAgencyBenefitsScreen';
import CampaignsDetailsScreen from './CampaignsDetailsScreen';
import SummitClubsScreen from './SummitClubsScreen';
import ERecruitCandidateProfileScreen from './ERecruitScreen/ERecruitCandidateProfileScreen';
import CreateInsuredScreen from 'screens/InsuredScreen/CreateInsuredScreen';
import InsuredDetailsScreen from 'screens/InsuredScreen/InsuredDetailsScreen';
import SearchExistingLeadScreen from 'screens/InsuredScreen/SearchExistingLeadScreen';
import AffiliateScreen from './AffiliateScreen';
import AffiliateProfileScreen from './AffiliateScreen/AffiliateProfileScreen';
import AffiliatePostDetailsScreen from './AffiliateScreen/AffiliatePostDetailsScreen';
import ERecruitReviewAgentsSubmissionScreen from './ERecruitScreen/ERecruitReviewAgentsSubmissionScreen';
import ERecruitReviewAgentsApplicationScreen from './ERecruitScreen/ERecruitReviewAgentsApplicationScreen';
import ERecruitSignatureScreen from './ERecruitScreen/ERecruitSignatureScreen';
import ERecruitRemoteSignatureScreen from './ERecruitScreen/ERecruitRemoteSignatureScreen';
import ERecruitCheckApplicationRemoteSignatureScreen from './ERecruitScreen/ERecruitCheckApplicationRemoteSignatureScreen';
import PerformanceDetailsScreen from './PerformanceScreen/PerformanceDetailsScreen';
import ImageListScreen from './ImageListScreen/ImageListScreen';
import ERecruitMaterialsScreen from 'screens/ERecruitScreen/ERecruitMaterialsScreen';
import ERecruitMaterialDetailsScreen from 'screens/ERecruitScreen/ERecruitMaterialDetailsScreen';

export default {
  AffiliatePostDetailsScreen,
  AffiliateProfileScreen,
  AffiliateScreen,
  LeadProfileScreen,
  FWDNewsScreen,
  LeadAndCustomerSearch,
  BadgesCollectionScreen,
  LeadsConversionScreen,
  FWDNewsDetailsScreen,
  FWDNewsBookmarkScreen,
  AddNewLeadOrEntityScreen,
  AddNewEntityScreen,
  ContactBookScreen,
  PoliciesNewBusinessNavigator,
  POSNavigator,
  LeadProfileDetailsScreen,
  CustomerProfileDetailsScreen,
  RecognitionDetailsScreen,
  PerformanceDetailsScreen,
  LogActivityScreen,
  ERecruitAppScreen,
  ERecruitAppStatusScreen,
  ERecruitCheckAppScreen,
  PerformanceTargetScreen,
  EliteAgencyDetailsScreen,
  EliteAgencyRequirementsScreen,
  EliteAgencyBenefitsScreen,
  CampaignsDetailsScreen,
  SummitClubsScreen,
  ERecruitCandidateProfileScreen,
  CreateInsuredScreen,
  InsuredDetailsScreen,
  SearchExistingLeadScreen,
  ERecruitReviewAgentsSubmissionScreen,
  ERecruitReviewAgentsApplicationScreen,
  ERecruitSignatureScreen,
  ERecruitRemoteSignatureScreen,
  ERecruitCheckApplicationRemoteSignatureScreen,
  ImageListScreen,
  ERecruitMaterialsScreen,
  ERecruitMaterialDetailsScreen,
};
