import SocialMarketingPostCreationReview from 'features/socialMarketing/components/SocialMarketingPostCreationReview';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';
import { country } from 'utils/context';

export default function SocialMarketingPostCreationReviewScreen() {
  switch (country) {
    case 'ib':
    case 'id':
    case 'my':
    case 'ph':
      return <SocialMarketingPostCreationReview />;
    default:
      return <NotFoundScreen />;
  }
}
