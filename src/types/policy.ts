import { AgentNewBusinessPolicyPHWithFieldsFromOWB } from 'features/policy/components/NewBusinessScreen/phone/NBListCardSet';
import { GenderKeys } from './eRecruit';
import { GenderCodeUnion } from './lead';
import { PaymentMode } from './proposal';
import { ImagePickerFile } from 'components/ImagePicker/utils';

export type PosPolicyMasterType = {
  pos: PosPolicy[];
};

export type PosPolicy = {
  lapsed?: PosPolicyDetail[];
  freelook?: PosPolicyDetail[];
  toBeMatured?: PosPolicyDetail[];
  overdue?: PosPolicyDetail[];
  due?: PosPolicyDetail[];
};

export type PolicyStatus = NBStatus | POSStatus | OWBStatusLabelKeys;
export type FilterOptionSet = Partial<
  Record<PolicyStatus, boolean | undefined>
>;

export type PosPolicyDetail = {
  status: string;
  policyNo: string;
  displayName: DisplayName;
  phoneMobile: null | string;
  policyOwnerEmail: string;
  coolingOffDate?: string;
  policyIssueDate?: Date;
  basicPremium: number;
  currency: Currency;
  paymentFrequency: DisplayName;
  dayDiff?: number;
  dueDate?: Date | string;
  lapsedDate?: Date;
  dueIn?: number;
  freeLookDate?: Date;
  policyType?: string;
  overdueAmount: number;
  title: string;
};

export enum Currency {
  Thb = 'THB',
  RM = 'RM',
}

export type DisplayName = {
  en: string;
  th: string;
};

export type Beneficiary = {
  en: string;
  th: string;
};

export type NewBusinessMasterType = {
  newBusinessPolicy: NewBusinessPolicy[];
};

export type NewBusinessPolicy = {
  submission?: NBIssued[];
  pending?: NBPending[];
  issued?: NBIssued[];
};

export type AgentPosPolicy = {
  lapseds?: PosPolicyDetail[];
  freeLooks?: PosPolicyDetail[];
  toBeMatureds?: PosPolicyDetail[];
  overdues?: PosPolicyDetail[];
  dues?: PosPolicyDetail[];
};
export type AgentPosPolicyPH = Omit<AgentNewBusinessPolicyPH, 'status'> & {
  status: 'DUE' | 'OVERDUE' | 'FREELOOK';
};

export type AgentNewBusinessPolicy = {
  submissions?: NBSubmission[];
  pendings?: NBPending[];
  issueds?: NBIssued[];
};

export type AgentNewBusinessPolicyPH = {
  type?: NBStatus;
  submitDate: string | Date;
  status: 'PENDING' | 'ISSUED' | '';
  issueDate: string | null;
  statusCode: string | null;
  planName: string;
  statusChangeDate: string | null;
  policyDate: string | null;
  agentId: string;
  policyNumber: string;
  planCategory: string;
  planCode: string;
  firstIssueDate: string | null;
  clientCode: string;
  policyOwner: string;
  dueInDays: number | null;
  overdueInDays: number | null;
  paidToDate: string | null | Date;
  premiumStatusCode: string;
  outstandingItemCount?: number | null;
  // For matching with AllNewBusinessPolicyData type
  caseId?: string | undefined; // for common type
  submissionStatus?: string; // for common type
  contribution?: number | undefined; // for common type
  policyNo?: string; // for common type
  policyStatus?: OWBStatusCodes | null; // for common type
  submissionDate?: Date | string; // for common type
  policyIssuedDate?: Date | string; // for common type
};

export type TabConfig = {
  status: string;
  label:
    | 'newBusiness.reviewSubmission'
    | 'newBusiness.pending'
    | 'newBusiness.issued'
    | 'newBusiness.leaderReviewApplication'
    | 'pos.freelookCancellation'
    | 'pos.overdue'
    | 'pos.due';
  icon: React.JSX.Element;
  count: number;
  countIcon: React.JSX.Element | undefined;
  countColor: string | undefined;
  onPress: () => void;
  isLoading?: boolean;
};

export type AllPolicyStatus =
  | ''
  | 'PENDING'
  | 'ISSUED'
  | 'DUE'
  | 'OVERDUE'
  | 'FREELOOK';

export const owbStatusCodesArray = [
  'CF',
  'FL',
  'IF',
  'NT',
  'WD',
  'LA',
  'SU',
  'RD',
  'UW',
  'DH',
  'PS',
  'NP',
  'NC',
  'PO',
  'NR',
  'VO',
  'MP',
  'AP',
  'UP',
  'NOT_CHECKED',
  'NTU',
  'DC',
  'WD',
  'UA',
] as const;

export type OWBStatusCodes = (typeof owbStatusCodesArray)[number];

export const owbStatusLabelkeysArray = [
  'cancelFromInception',
  'freelookCancellation',
  'Infore',
  'notTakenUp',
  'lapsed',
  'surrendered',
  'registerForDeath',
  'uw',
  'deathClaim',
  'reverseToProposal',
  'voidProposal',
  'componentChangeModifyPrp',
  'componentChangeAddPrpsl',
  'proposal',
  'NBPending',
  'NBCounterOffer',
  'postponed',
  'notTakenUp',
  'declined',
  'withdrawn',
  'uwApproved',
  'notChecked',
  'uwProcessing',
] as const;
export type OWBStatusLabelKeys = (typeof owbStatusLabelkeysArray)[number];

export const owbCodeToLabelStatus: Record<OWBStatusCodes, OWBStatusLabelKeys> =
  {
    CF: 'cancelFromInception',
    FL: 'freelookCancellation',
    IF: 'Infore',
    NT: 'notTakenUp',
    LA: 'lapsed',
    SU: 'surrendered',
    RD: 'registerForDeath',
    UW: 'uw',
    DH: 'deathClaim',
    NR: 'reverseToProposal',
    VO: 'voidProposal',
    MP: 'componentChangeModifyPrp',
    AP: 'componentChangeAddPrpsl',
    PS: 'proposal',
    NP: 'NBPending',
    NC: 'NBCounterOffer',
    PO: 'postponed',
    NTU: 'notTakenUp',
    DC: 'declined',
    WD: 'withdrawn',
    UA: 'uwApproved',
    NOT_CHECKED: 'notChecked',
    UP: 'uwProcessing',
  };

type PolicyLowerCaseLabelStatus =
  | 'submission'
  | 'pending'
  | 'issued'
  | 'due'
  | 'overdue'
  | 'freelook'
  | 'lapsed'
  | 'coolingOff'
  | 'toBeMatured'
  | 'alreadyMatured'
  | 'ClaimsInProgress'
  | 'ClaimsPending'
  | 'ClaimsFinal'
  | 'ClaimsDeclined'
  | 'NBPending'
  | 'NBIssued'
  | 'PosOverdue'
  | 'searchOverdue'
  | 'searchDue'
  | 'inforce'
  | 'u/w'
  | 'proposal'
  | 'nb pending';

export type PolicyLabelStatus = PolicyLowerCaseLabelStatus | AllPolicyStatus;

// merge AgentNewBusinessPolicyPH and AgentPosPolicyPH to form AgentPolicyPH
export type AgentPolicy = {
  submitDate: string | Date;
  status: AllPolicyStatus | string;
  issueDate?: string | null;
  statusCode?: string | OWBStatusCodes | null;
  planName: string | null;
  statusChangeDate?: string | null;
  policyDate?: string | null;
  agentId: string;
  policyNumber: string;
  planCategory: string;
  planCode: string;
  firstIssueDate?: string | null;
  clientType?: string | null;
  clientCode?: string; // customer ID can be undefined if the OWB deduplication is not done
  title?: string | null;
  policyOwner: string;
  insuredName: string;
  extensonName?: string | null;
  dueInDays?: number | null;
  overdueInDays?: number | null;
  paidToDate?: string | null | Date;
  premiumStatusCode: string;
  proposalEntryDate?: string | null;
  proposalDate?: string | null;
  outstandingItemCount?: number | null;
  relationOfProposer?: string | null;
  crossReferenceNumber?: string | null;
};

export type PolicyInfoFromList = Partial<
  PosPolicyDetail & NBIssued & NBPending & NBSubmission
>;

export interface NBSubmission {
  applicationNo: string;
  policyNo: string;
  product: string;
  policyOwner: Insured;
  insured: Insured;
  beneficiaryList: Insured[];
  mainProductCode: string;
  paymentMode: string;
  registerDate: string | null;
  nextPremiumPayDate: Date | null;
  currency: string;
  basicPremium: number;
  singlePremium: number;
  regularTopUp: number;
  modalPremium: number;
  riderPremium: number;
  coverageList: CoverageList[];
  phoneMobile?: null | string;
}

export interface Insured {
  fullName: string;
}

export interface CoverageList {
  productCode: string;
  productName: string;
  sumInsured: number;
  modalPremium: number;
  status: string;
  issueDate: string;
}

export type NBIssued =
  | {
      policyNo: string;
      displayName: Pick<DisplayName, 'en'>;
      phoneMobile: null | string;
      policyIssuedDate?: Date | string;
      plan: Pick<DisplayName, 'en'>;
      completionDate: string;
      sumAssured: number;
      currency: Currency;
      memoDescriptions: any[];
      submissionDate?: Date | string;
      paymentMode?: PaymentMode;
      modalPremium?: number;
      product?: string;
    }
  | NBIssuedFromOWB;

export type NBIssuedFromOWB = {
  customerId: string;
  displayName: {
    en: string;
  };
  modalPremium?: number;
  paymentMode: string;
  phoneMobile?: string;
  policyIssuedDate: string;
  policyNo: string;
  policyType: string;
  sumAssured: number;
  product?: string;
};

export interface NBPendingV2 {
  type?: NBStatus;
  displayName?: DisplayName;
  policyNo?: string;
  phoneMobile?: string | null;
  customerId?: string;
  policyType?: string;
  sumAssured?: number;
  modelPremium?: number;
  modalPremium?: number;
  paymentMode?: PaymentMode;
  policyIssuedDate?: string;
  // For matching with AllNewBusinessPolicyData type
  caseId?: string | undefined; // for common type
  status?: NBPolicyLeaderReviewStatus; // for common type
  submissionStatus?: string; // for common type
  contribution?: number | undefined; // for common type
  policyStatus?: OWBStatusCodes | null; // for common type
  policyNumber?: string; // for common type
  submissionDate?: Date | string; // for common type
  issueDate?: string; // for common type
}
export interface NBPending extends NBPendingV2 {
  policyNo: string;
  displayName: DisplayName;
  phoneMobile: null | string;
  outstandingItemCount: number;
  receivedDate: Date;
  pendingItemList: PendingItemList[];
  memoDescriptions: any[];
  registerDate: Date | string;
  policyStatus: OWBStatusCodes | null;
  pendingRequirementCount: number | null;
  rtu: number | null;
  insuredName: string | null;
  product: string | null;
  basicPremium: number | null;
  singlePremium: number | null;
  currency: string | null;
  policyRecordCount: number | null;
  policyType?: string;
}

export type PendingItemList = {
  requirementCategory: Record<'en', string>;
  status: string;
  clientName: string;
  registerDate: string;
  lastUpdateDate: string;
  documentDesc: Record<'en', string>;
  // particulars: DisplayName;
  // validFromDate: Date;
  // remark: string;
  // lastUpdatedDate: Date;
  // memoDescriptions: any[];
  // status?: string;
  // TBC, the following are only options found from API FIB
  // status: 'Received' | 'Not Received';
};

export type ClaimsMasterType = {
  claims: Claim[];
};

export type Claim = {
  policyNo: string;
  displayName: DisplayName;
  phoneMobile: string;
  totalPayAmount: number;
  currency: string;
  claimInfo: ClaimInfo;
  claimDetails: ClaimDetail[];
};

export type ClaimDetail = {
  coverage: string;
  plan: string;
  maxBenefit: number;
  presentedDayClass: number;
  payAmount: number;
  currency: string;
};

export type ClaimInfo = {
  claimType: DisplayName;
  receivedDate: Date;
  status: DisplayName;
  dischargeDate: string;
  paymentMethod: string;
};

export type PolicyDetailMasterType = {
  policyDetails: PolicyDetails;
  responseData: PolicyDetails;
};

// export type PolicyDetails = {
//   policyNo: string;
//   displayName: DisplayName;
//   mobileNumberCountryCode: string;
//   phoneMobile: string;
//   billToDate: Date;
//   productCategory: DisplayName;
//   paidToDate: Date;
//   customerID: string;
//   policyDesc: DisplayName;
//   modalPremium: number;
//   insuredName: DisplayName;
//   coverageList: Coverage[];
//   paymentMode: string;
//   plan: null;
//   submissionDate: null;
//   completionDate: null;
//   sumAssured: null;
//   currency: string;
//   outstandingItemCount: null;
//   receivedDate: null;
//   pendingItemList: any[];
//   policyIssuedDate: Date;
//   coolingOffDate: null;
//   basicPremium: number;
//   lapsedDate: Date;
//   contractEndInDays: null;
//   toBeMaturedDate: null;
//   paymentFrequency: null;
//   dayDiff: null | number;
//   dueDate: null;
//   totalPayAmount: null;
//   overdueAmount: number;
//   claimInfo: null;
//   claimDetails: any[];
//   memoDescriptions: any[];
//   beneficiary: Beneficiary;
//   singlePremium: number;
//   regularTopUp: number;
//   riderPremium: number;
//   product: string;
//   overview: Overview;
// };

export type Coverage = {
  coverageName: DisplayName;
  accountStatus: AccountStatus;
  policyAccountID: string;
  sumInsured: number;
  modalPremium: number;
};

type AccountStatus = {
  en: Status;
  th: string;
};

export const nbStatusArray = [
  'submission',
  'pending',
  'issued',
  'reviewApplication',
] as const;
export type NBStatus = (typeof nbStatusArray)[number];

export type NBPolicyLeaderReviewStatus =
  | 'pendingLeaderDeclaration'
  | 'leaderRejected'
  | 'leaderApprovedToSubmit'
  | 'leaderNotResponded'
  | 'nonRookieSubmitted';
export type NBSubmissionForReviewStatus = NBPolicyLeaderReviewStatus;

export type NBPolicyLeaderReviewSubmissionStatus =
  | 'FINISHED'
  | 'PENDING'
  | 'FAILED';

// changed posStatusArray to cater the report generation part, not sure if changing like this is ok
export const posStatusArray = [
  'due',
  'overdue',
  'freelook',
  'In Force',
  'Postponed',
  'Contract Declined',
  'Not taken Up',
] as const;
export type POSStatus = (typeof posStatusArray)[number];
export type Status = 'overdue' | 'inforce' | 'lapse';

export interface Overview {
  policyOwner: string;
  insuredName: Name;
  insureds?: Array<{
    id: string;
    fullName: string;
  }>;

  benes: Bene[];
  product: null | string;
  paymentMode: string | null;
  registerDate: null | string;
  nextPremiumDate: string | null;
  basicPremium: null | number;
  singlePremium: null | number;
  regularTopUp: null | number;
  modalPremium: number | null;
  riderPremium: number | null;
  policyNo: string | null;
  rtu: number | null;
  sumAssured?: number;
}

export interface Bene {
  displayName: Name;
}

export interface Name {
  en: string;
}
export interface Coverages {
  basicPlan: string;
  productName?: string;
  // sumInsured: number;
  sumAssured: number;
  issueDate: string | null;
  policyStatus: string;
  modalPremium: number;
  totalPrem?: number;
  isBasePlan?: boolean;
}

export interface PolicyDetails {
  id: string;
  agentCode: string;
  displayName: Name;
  outstandingItemCount: number;
  nextPremiumDueDate?: string;
  submissionDate?: Date | string;
  registerDate: string | null;
  phoneMobile: string | null;
  pendingRequirements: PendingRequirements[] | null;
  title: string | null;
  overview: Overview;
  maturityDate: string | null;
  coverages: Coverages[];
  createdAt: string;
  basicPremium: number;
  overdueAmount: number;
  paymentFrequency: string;
  product: string;
  paymentMethod: string;
  policyIssueDate: null | string;
  dueIn: number;
  currency: string;
  customerId: string;
  productCategoryEN: string | null;
  transactionDate: string | null;
  statusDescription: string | null;
  policyOwnerEmail?: string;
  policyOwnerPhoneMobile?: string;
  beneficiaries?: Array<{
    fullName: string;
  }>;
  approvedByLeaderAt: string;
}

export interface PendingRequirements {
  requirementCategory: DocumentDesc;
  status: string;
  clientName: string;
  registerDate: string;
  documentDesc: DocumentDesc;
  lastUpdateDate: string;
}

export interface DocumentDesc {
  en: string;
}

/**
 * Policy Release 2
 */

export interface Plan {
  planName: string;
  insuredName: string;
  ageAtEntry: string;
  status: string;
  sumInsured: string;
  premiumAmount: string;
  subPlan: string;
  rcd: string;
}

export interface ApplicationTrackerInfo {
  policyNumber?: string;
  currentStatus?: string;
  policyOwnerCode?: string;
  policyOwnerFName?: string;
  policyOwnerMName?: string;
  policyOwnerLName?: string;
  policyInsCode?: string;
  policyInsFName?: string;
  policyInsMName?: string;
  policyInsLName?: string;
  proposalReceived?: string;
  screening?: string;
  pendingReq?: string;
  complianceReq?: string;
  decision?: string;
  premiumStatus?: string;
  paymentReceived?: number;
  shortfallAmt?: number;
  basicPremiumDue?: number;
  underWriting?: string;
  decisionDate?: string;
  issueDate?: string;
  dispatchDate?: string;
  reIssueDate?: string;
  deliveryMode?: string;
  reissued?: boolean;
  status?: ApplicationTrackerStatus;
}

export interface ApplicationTrackerRes {
  requestId: string;
  status: string;
  message: string;
  data?: ApplicationTrackerInfo[];
}

export interface ApplicationTrackerStatus {
  isSubmission: boolean;
  isScreening: boolean;
  isUW: boolean;
  isDecision: boolean;
  isIssue: boolean;
  isDispatch: boolean;
}

export interface PolicyDetailsPolicyRes {
  data: PolicyDetailsPolicy[];
}

export interface PolicyDetailsPolicy {
  [key: string]: string | number | boolean | null | undefined;
  policyNumber?: string;
  agentCode?: string;
  submitDate?: string;
  issueDate?: string;
  billFrequency?: string;
  billingChannel?: string;
  premiumStatusCode?: string;
  premiumStatusCodeDesc?: string;
  statusCode?: string;
  statusCodeDesc?: string;
  planCode?: string;
  planCodeDesc?: string;
  singlePremium?: number | string;
  installmentPremium?: number | string;
  lifeSumInsured?: number | string;
  paymentMode?: string;
  paymentMethod?: string;
  paymentMethodDesc?: string;
  clientCode?: string;
  firstName?: string;
  middleName?: string;
  surname?: string;
  ownerBirthdate?: string;
  dispatchClientCode?: string;
  billToDate?: string;
  totalInstallmentPremium?: number | string;
  ageAtCommencement?: number | string;
  planCategory?: string;
  planCategoryDesc?: string;
  riskCessationDate?: string;
  currencyCode?: string;
  currency?: string;
  planShortDesc?: string;
  showFundProjection?: string;
  fixPremium?: string;
  riskCessationAge?: number | string;
  riskCessationTerm?: number | string;
  agentSupervisor?: string;
  owEmail?: string;
  owMobile?: string;
  lfFirstName?: string;
  lfMiddleName?: string;
  lfSurname?: string;
  lfEmail?: string;
  lfMobile?: string;
  basicPremium?: number | string;
  riderPremium?: number | string;
  paymentFrequency?: string | null;
  premiumHoliday?: boolean | string;
  activationAdaAca?: string;
  rtu?: number | string;
  fundValue?: number | string;
  dueDate?: string;
  maturityDate?: string;
}

export interface Product {
  [key: string]: string | number | undefined;
  policyNumber?: string;
  planCode?: string;
  planCodeDesc?: string;
  lifeNumber?: string | number;
  coverageNumber?: string;
  riderNumber?: string;
  premCessationTerm?: number | string;
  lifeSumInsured?: number | string;
  planType?: string;
  status?: string;
  paymentMode?: string;
  paymentMethod?: string;
  singlePremium?: number | string;
  installmentPremium?: number | string;
  riskCessationDate?: string;
  ageAtCommencement?: number | string;
  planShortDesc?: string;
  planLongDesc?: string;
  planSubDesc?: string;
  planSubType?: string;
  stFund?: string;
  riskCessationAge?: string;
  insuredName?: string;
  benefitPlan?: string;
  units?: number | string;
}

export interface Fund {
  policyNumber?: string;
  fundCode?: string;
  totalUnits?: string;
  allocationPercent?: string;
  fundPrice?: string;
  asOfDate?: Date | string;
  fundType?: string;
  fundValue?: string;
  fundCurrency?: 'RM' | 'PHP' | 'USD';
  fundDesc?: string;
  fundLongDesc?: string;
  riskProfile?: string;
}

export interface Role {
  [key: string]: string | undefined;
  clientCode?: string;
  policyNumber?: string;
  roleCode?: string;
  revocableFl?: string;
  clientRefSeq?: string;
  clientName?: string;
  birthDate?: string;
}

export interface Account {
  debitInformation: debitInformation;
  creditCardInformation: creditCardInformation;
  activation: string;
}

export interface debitInformation {
  [key: string]: string | undefined;
  bankName?: string;
  accountHolderName?: string;
  accountName?: string;
  ADAACAActivation?: string;
  accountNumber?: string;
}
export interface creditCardInformation {
  [key: string]: string | undefined;
  bankName?: string;
  holderName?: string;
  cardType?: string;
  accountNumber?: string;
  expiryYear?: string;
  expiryMonth?: string;
}

export interface History {
  [key: string]:
    | PaymentHistory[]
    | AlterationHistory[]
    | OfficialReceipts[]
    | LetterToCustomers[];
  paymentHistory: PaymentHistory[];
  alterationHistory: AlterationHistory[];
  officialReceipts: OfficialReceipts[];
  letterToCustomers: LetterToCustomers[];
}

export interface AlterationHistory {
  [key: string]: string | undefined;
  transactionDate: string;
  description: string;
}

export interface PaymentHistory {
  [key: string]: string | number | null | undefined;
  transactionDate: string;
  paymentType: string;
  paymentTypeDesc: string;
  amount: number | null;
}

export interface OfficialReceipts {
  [key: string]: string | number | undefined;
  transactionDate: string;
  file: number | string;
}

export interface LetterToCustomers {
  [key: string]: string | undefined;
  date: string;
  details: string;
}

export interface Uploads {
  fileName: string;
  uploadedAt: string | Date;
}
export interface PendingDocuments {
  id: number;
  pendingTransactionId: string;
  policyNumber: string;
  categoryDesc: string;
  documentDesc: string;
  receivedDate: string;
  acceptanceStatus: string;
  lastUpdateDate: string;
  docCategoryTypeCd: string;
  docCategoryCd: string;
  documentCd: string;
  documentClass: string;
  category: string;
  documentId: string;
  description: string;
  subCategory: string;
  print: string;
  uploads: Uploads[];
  pendMemo: string;
  remark: string;
  documentCategory: string;
  clientName: string;
  pendingDate?: string;
}

export interface UploadPendingDocumentBody {
  fileName: string;
  fileType: string;
  content: string;
  documentClass: string;
  applicationDate: Date | string;
  captureDate: Date | string | null;
  receivedDate: Date | string | null;
  applicationNumber: null;
  policyNumber: string;
  category: string;
  documentID: string;
  subCategory: string;
  print?: string | null;
  pendMemo: string;
  clientName: string;
  docCategoryTypeCd: string;
  docCategoryCd: string;
  documentCd: string;
}

export interface GetDocumentsListParams {
  policyNumber: string;
  pendMemo: string;
  documentClass: string;
  documentId: string;
  pendingTransactionId: string;
}

export interface UploadV2PendingDocumentBody {
  files: ImagePickerFile;
  pendMemo: string;
  documentClass: string;
  documentId: string;
  policyNumber: string;
  pendingTransactionId: string;
}

export interface UploadPendingDocumentResponse {
  message: string;
}

export const STATUS_PENDING = 'PENDING';
export const STATUS_ISSUED = 'ISSUED';
export const STATUS_SUBMISSION = '';
export const STATUS_DUE = 'DUE';
export const STATUS_OVERDUE = 'OVERDUE';
export const STATUS_FREELOOK = 'FREELOOK';

export type DocumentDetails = {
  uri: string;
  name: string;
  isPdf: boolean;
  status: string;
  // status: 'pendingUpload' | 'success' | 'failed';
};

export type QueryPolicyListRequest = {
  filters: {
    key: string;
    value: any;
  }[];
  customerInfo: {
    firstName?: string;
    lastName?: string;
    dob?: string;
    gender?: GenderCodeUnion;
    idType?: 'ID';
    idNumber?: string;
    customerType?: string;
    customerId?: string;
  };
  inquiryInfo: {
    from?: string;
    to?: string;
    inquiryKey: 'C' | 'A';
    requestType: 'Customer' | 'submittedPolicy';
    customerRole?: 'CUS001' | 'CUS002' | 'CUS003';
  };
};

export type QueryPolicyListResponse = {
  policies?: {
    clientId: string;
    componentCode: string;
    paymentMode: string;
    contractType: string;
    fullName: string;
    policyNo: string;
    modalPremium: number | string;
    sumAssured: number | string;
    policyIssueDate: string;
    riskCommenceDate: string;
    riskStatus: string;
    caseInfo?: {
      currentStep: string;
      businessNo: string;
      workflow: string;
      caseNo: string;
      caseStatus: string;
    }[];
  }[];
};

export type PoliciesToReviewApplicationPolicyList = {
  type?: NBStatus;
  caseId?: string | undefined;
  certNo?: string;
  certOwner: string;
  contribution?: number | undefined;
  paymentMode?: string | undefined;
  agentName?: string;
  uwDecision?: string;
  status?: NBPolicyLeaderReviewStatus;
  submissionDate?: string;
  submissionStatus?: string;
  pendingReasons?: string[];
  // For matching with AllNewBusinessPolicyData type
  policyNo?: string; // for common type
  policyStatus?: OWBStatusCodes | null; // for common type
  policyNumber?: string; // for common type
  policyIssuedDate?: Date | string; // for common type
  issueDate?: string; // for common type
};
export interface ExpressClaimPolicy {
  submitDate: string;
  status: 'ISSUED';
  issueDate: string;
  statusCode: string;
  planName: string;
  statusChangeDate: string;
  policyDate: string;
  agentId: string;
  policyNumber: string;
  planCategory: string | null;
  planCode: string;
  firstIssueDate: string;
  clientCode: string;
  policyOwner: string;
  dueInDays?: any;
  overdueInDays?: any;
  paidToDate: string;
  premiumStatusCode: string;
  proposalEntryDate: string;
  proposalDate: string;
  clientType: string;
  extensionName: string;
  title: string;
  reinstatementDate?: any;
}
export interface CaseListOverView {
  caseSequenceNum: number;
  caseNo: string;
  businessNo: string;
  policyNumber: string;
  currentActivity: string;
  caseStatus: string;
  submissionDate: string;
  policyStatus: any;
  agentId: string;
  agentChannel: any;
  productCode: any;
  productType: any;
  businessDecision: any;
  clientName: string;
  pendingDetailList: any;
  externalRequestId: any;
  productName: string;
}
export interface ClaimTrackingData {
  summary: {
    numberOfCase: number;
  };
  caseList: CaseListOverView[];
  success: boolean;
}
export interface ExpressStartClaim {
  claimId: string;
}

export type sortOrder = 'oldest' | 'newest';

export type SubmissionItemFromCase = {
  caseId: string;
  certNo: string;
  certOwner: string;
  contribution: number | undefined;
  paymentMode: PaymentMode | undefined;
  agentName: string;
  uwDecision: string;
  status: NBPolicyLeaderReviewStatus;
  submissionStatus: NBPolicyLeaderReviewSubmissionStatus | '';
  submissionDate: string;
  pendingReasons: string[];
};

export type OwbPolicyListItem = {
  policyNo: string;
  clientId: string;
  fullName?: string;
  componentCode?: string;
  contractType?: string;
  sumAssured?: string;
  modalPremium?: string;
  riskStatus?: string;
  paymentMode?: string;
  proposalDate?: string;
  riskCommenceDate?: Date;
  policyIssueDate?: Date;
  maturityDate?: Date;
  nextPremDueDate?: Date;
  crossReferenceType?: string;
  crossReferenceNumber?: string;
  insuredName?: string;
  insuredClientId?: string;
  relationOfProposer?: string;
  submissionDate?: string;
  caseInfo?: {
    caseNo?: string;
    businessNo?: string;
    caseStatus?: string;
    workflow?: string;
    currentStep?: string;
  }[];
};

export type OwbQueryPolicyListResponse = {
  policies: OwbPolicyListItem[];
};

export type insuredsArrItem = {
  fullName: string;
  id: string;
};

export type PolicyDetailMapping = {
  productName: string;
  sumCovered: number;
  modalContribution: number;
  insuredName: string;
  insureds?: insuredsArrItem[];
  mobileNo: string | null | undefined;
  ownerMobileNo?: string;
  ownerEmail?: string;
  dueIn: number | null | undefined;
  certNo: string;
  paymentMode: string;
  certOwner: string;
  paymentMethod: string;
  submissionDate?: Date | string;
  registrationDate: string;
  issueDate: string;
  maturityDate: string | null | undefined;
  nextContributionDate: string | null | undefined;
  currency: string;
  beneficiary: string;
  policyType: string | undefined;
  transactionDate: string | null | undefined;
  statusDescription: string | null | undefined;
  leaderApprovalDate: string;
  needsLeaderReview?: boolean;
};

export type PolicyHeaderLabelStatus = PolicyStatus | NBPolicyLeaderReviewStatus;

export type AllNewBusinessPolicyData =
  | PoliciesToReviewApplicationPolicyList
  | NBPending
  | AgentNewBusinessPolicyPHWithFieldsFromOWB;
