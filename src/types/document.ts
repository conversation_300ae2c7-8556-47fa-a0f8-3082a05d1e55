export enum DocumentCustomerType {
  PI = 'PI',
  PO = 'PO',
  PAY = 'PAY',
  BO = 'BO',
  B = 'B',
  TT = 'TT',
  AG = 'AG',
  NA = 'NA',
  AP = 'AP',
  IBO = 'IBO',
  RBO = 'RBO',
}

export enum DocumentType {
  FrontID = 'frontID',
  BackID = 'backID',
  StatementOfAccount = 'statementOfAccount',
  BankCertificate = 'bankCertificate',
  ATMAccountNumber = 'atmAccountNumber',
  DepositSlip = 'depositSlip',
  CertOnBO = 'certOnBO',
  IRSWForm = 'irswForm',
  Adda = 'adda',
  ACA = 'aca',
  ProofDetails = 'proofDetails',
  SI = 'si',
  FNA = 'fna',
  AppForm = 'appForm',
  ACR = 'acr',
  RPQ = 'rpq',
  Signature = 'signature',
  ConsentLetter = 'consentLetter',
  ForeignerQuestionnaire = 'foreignerQuestionnaire',
  FatcaCrsDeclarationForm = 'fatcaCrsDeclarationForm',
  LargeAmountQuestionnaire = 'largeAmountQuestionnaire',
  PermanentResident = 'permanentResident',
  OtherDocuments = 'otherDocuments',
  ConsentFormFromParentLegalGuardian = 'consentFormFromParentLegalGuardian',
  CertifiedTrueForm24 = 'certifiedTrueForm24',
  CertifiedTrueForm49 = 'certifiedTrueForm49',
  CertifiedTrueForm58 = 'certifiedTrueForm58',
  CertifiedTrueForm9 = 'certifiedTrueForm9',
  FrontMyKad = 'frontMyKad',
  BackMyKad = 'backMyKad',
  Passport = 'passport',
  AuthorisationLetter = 'authorisationLetter',
  FrontCheque = 'frontCheque',
  BackCheque = 'backCheque',
  InSlipCheque = 'inSlipCheque',
  ValidPass = 'validPass',
  ResidentialQuestionnaire = 'residentialQuestionnaire',
  Covid19Questionnaire = 'covid19Questionnaire',
  FundPieChart = 'fundPieChart',
  DirectTransfer = 'directTransfer',
  Unknown = 'Unknown',
  AgentDeclarationForm = 'agentDeclarationForm',
  BusinessEntityForm = 'businessEntityForm',
  SelfieWithNRIC = 'selfieNRIC',
  AutoDebitLetter = 'autoDebitLetter',
  SavingBook = 'savingBook',
  CreditCardLetter = 'creditCardLetter',
  CreditCardCopy = 'creditCardCopy',
  FamilyCard = 'familyCard',
  Identity1 = 'Identity1',
  Identity2 = 'Identity2',
  FrontID2 = 'frontID2',
  WNAFrontID = 'wnaFrontID',
  WNAFrontID2 = 'wnaFrontID2',
  FamilyPortrait = 'familyPortrait',
  DirectDebitAuthorization = 'directDebitAuthorization',
  DirectTransferApproval = 'directTransferApproval'
}

export interface UploadDocumentBody {
  custType: DocumentCustomerType;
  custSeq: string;
  docType: DocumentType;
  fileContent: string;
  applicationNum: string;
  fileType: string;
}

export interface UploadDocumentResponse {
  fileName: string;
  filePath: string;
}
