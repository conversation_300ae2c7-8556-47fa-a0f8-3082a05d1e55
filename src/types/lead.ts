import { CaseStatus } from './case';
import { CompositeScreenProps } from '@react-navigation/native';

import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { RootStackParamList } from 'types';
export type CurrentLeadTabKeys = 'todayLeads' | 'otherLeads';
export type CurrentLeadSearchTabKeys = 'leads' | 'customers';
export type LeadProposalSortByKeys = 'newest' | 'oldest';

export type Lead = {
  id: string;
  createdAt: string;
  opportunityUpdatedAt?: string;
  policyId?: null;
  updatedAt: string;
  agentId: string;
  agentAssignedAt: string;
  sourceIds: LeadSource[];
  isIndividual: boolean;
  customerId: string;
  caseIds: string[];
  salutation: string;
  firstName: string;
  middleName: string;
  lastName: string;
  nameExtension: string;
  nameSuffix: string;
  localNameLang: string;
  localSalutation: string;
  localFirstName: string;
  localMiddleName: string;
  localLastName: string;
  localNameExtension: string;
  localNameSuffix: string;
  email: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  workPhoneCountryCode: string;
  workPhoneNumber: string;
  homePhoneCountryCode: string;
  homePhoneNumber: string;
  addressStreet: string;
  addressCity: string;
  addressProvinceCode: string;
  addressPostalCode: string;
  addressCountryCode: string;
  acceptPhoneCall: boolean;
  acceptEmail: boolean;
  callAcceptDate: string;
  preferredContactDate: string;
  genderCode: string;
  birthDate: string;
  maritalStatusCode: string;
  kidsCount: number;
  isSmoker?: boolean | null;
  inquireReason: string;
  companyName: string;
  natureOfBusiness: string;
  leadMedium: string;
  companyDivision: string;
  jobTitle: string;
  occupationCode: string;
  occupationIndustryCode: string;
  occupationClassCode: string;
  occupationGroupCode: string;
  annualIncomeRangeLower: string;
  annualIncomeRangeUpper: string;
  monthlyIncomeRangeLower: string;
  monthlyIncomeRangeUpper: string;
  extra: any; // TODO: Update //update by Hardy
  interestedCategories: string[];
  interestedProducts: string[];
  recommendedProducts?: RecommendedProduct[];
  score: number;
  status: LeadStatus;
  isWinning: boolean;
  isLose: boolean;
  isContacted: boolean;
  leads: LeadsInLeadProfile[];
  transactions: TransactionsInLead[];
  typeOfEntity: string;
  religion: string;
  campaignCode: string;
  campaignName: string;
};

export enum ProductCategory {
  TERM_LIFE = 'term_life',
  CRITICAL_ILLNESS = 'critical_illness',
  MEDICAL = 'medical',
  INVESTMENT_SAVINGS = 'investment_savings',
}

export type RecommendedProduct = {
  productCategory: ProductCategory;
  productCode: string | null;
  productName: string;
  existingCoverage?: number | null;
  recommendedCoverage?: number | null;
  score: number | null;
  rank: number | null;
};

type LeadsInLeadProfile = {
  id: string;
  createdAt: string;
  sourceId: LeadSource;
  sourceLeadId: string;
  campaignCode: string;
  customerId: string;
  salutation: string;
  firstName: string;
  middleName: string;
  lastName: string;
  nameExtension: string;
  nameSuffix: string;
  localNameLang: string;
  localSalutation: string;
  localFirstName: string;
  localMiddleName: string;
  localLastName: string;
  localNameExtension: string;
  localNameSuffix: string;
  email: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  workPhoneCountryCode: string;
  workPhoneNumber: string;
  homePhoneCountryCode: string;
  homePhoneNumber: string;
  addressStreet: string;
  addressCity: string;
  addressProvinceCode: string;
  addressPostalCode: string;
  addressCountryCode: string;
  acceptPhoneCall: boolean;
  acceptEmail: boolean;
  callAcceptDate: string;
  preferredContactDate: string;
  genderCode: number;
  birthDate: string;
  maritalStatusCode: string;
  kidsCount: number;
  isSmoker: boolean;
  inquireReason: string;
  companyName: string;
  companyDivision: string;
  jobTitle: string;
  occupationCode: string;
  occupationIndustryCode: string;
  occupationGroupCode: string;
  occupationClassCode: string;
  annualIncomeRangeLower: string;
  annualIncomeRangeUpper: string;
  monthlyIncomeRangeLower: string;
  monthlyIncomeRangeUpper: string;
  utmSource: string;
  utmMedium: string;
  utmCampaign: string;
  utmTerm: string;
  utmContent: string;
  extra: any; // TODO: Update
  interestedCategories: string[];
};

export type TransactionsInLead = {
  id?: string;
  action: TransactionsAction;
  actionAt: string;
  rawLeadId?: string;
  agentId?: string;
  success?: string;
  reason?: string;
  contactMethod?: string;
  productCode?: string;
  customerId?: string;
  policyId?: string;
  recommendedProducts?: TransactionsRecommendedProduct[];
  extra?: {
    feedback: 'interested' | 'notInterested' | 'deferred';
    feedbackDetails: string;
  }; // TODO: Update
  quotationId?: string;
};

export type TransactionsAction =
  | 'contact'
  | 'appointment'
  | 'illustrate'
  | 'submit'
  | 'create'
  | 'assign'
  | 'consolidate'
  | 'update'
  | 'recommend'
  | 'not_interested'
  | 'defer';

export type TransactionsRecommendedProduct = {
  productCode: string;
  rating: number;
  score: number;
};

export type SavedProposalFilterTypes =
  | 'ALL'
  | 'FNA'
  | 'Quick quote'
  | 'SI'
  | 'Application'
  | 'Others';

export type DisplayLanguage = 'en' | 'th';

export type Labels = { [lang: string]: string };

export type OptionKey =
  | 'INSURED_COVERAGE'
  | 'GENDER'
  | 'GENDER_TEST'
  | 'RELATIONSHIP_TO_INSURED'
  | 'FORMULA'
  | 'OCCUPATION_TYPE'
  | 'PERMANENT_JOB'
  | 'PHONE_CODE'
  | 'DOCUMENT_TYPE'
  | 'NEWS'
  | 'PRODUCT_INTEREST_TYPE'
  | 'BIRTHDAY_REC_TYPE';

export type SliceStatus = 'idle' | 'loading';

export type LeadRank = 'L' | 'M' | 'H';

export type LeadScore = '1' | '2' | '3';

export type AiRecommendedProducts = {
  _id: string;
  leadId: string;
  order: number;
  productCode: string;
  score: number;
  rank: LeadRank;
};

export type ModuleConfigKeyType = 'PRIMARY' | 'SECONDARY';

export type LeadType = 'individual' | 'entity';

export type LeadStatus =
  | 'not_contacted'
  | 'assigned'
  | 'created'
  | 'consolidated'
  | 'updated'
  | 'recommended'
  | 'contacted'
  | 'appointment'
  | 'illustration'
  | 'declined'
  | 'deferred'
  | 'cancelled'
  | 'submitted'
  | 'issued'
  | 'not_interested'
  | 'deactivated' // hidden by backend
  | 'inactive';

export type LeadSource =
  | 'SELF'
  | 'CUBE'
  | 'SMART'
  | 'IRIS'
  | 'BLTS'
  | 'ALTS'
  | 'SFMC'
  | 'Affiliate'
  | 'SFSC'
  | 'Marketing';

export type MYSLeadSource =
  | MYSLeadSourceR1
  | MYSLeadSourceR2
  | MYSLeadSourceToBeDecommissioned
  | MYSLeadSourceTBC;

//* based on ticket CUBEMY-109
type MYSLeadSourceR1 = 'AFFILIATE_SOURCE' | 'MYSELF_SOURCE' | 'AYPC_SOURCE';
type MYSLeadSourceR2 =
  | 'TM_SOURCE'
  | 'MARKETING_SOURCE'
  | 'YPC_SOURCE'
  | 'PS_SOURCE';
type MYSLeadSourceToBeDecommissioned = 'PLU_SOURCE';
type MYSLeadSourceTBC =
  | 'ELITE_STUDIO'
  | 'SFMC_SOURCE'
  | 'BANK_SOURCE'
  | 'C2B_SOURCE';

export type LeadsFilters = {
  type: Partial<Record<LeadType, boolean>>;
  source: Partial<Record<LeadSource, boolean>>;
  campaignCode: Partial<Record<string, boolean>>;
  status: Partial<Record<LeadStatus, boolean>>;
};
export const NOT_CONTACTED_LEAD_STATUS = [
  'assigned',
  'created',
  'consolidated',
  'updated',
  'recommended',
];
export const SELF_GEN_LEAD_SOURCES = ['CUBE', 'SMART', 'IRIS'];
export const MARKETING_LEAD_SOURCES = ['SFMC', 'SFSC'];
export const AFFILIATE_LEAD_SOURCES = ['Affiliate'];

export type GetLeadsResponse = {
  data: Lead[];
  totalCount: number;
  offset: number;
  limit: number;
  sourceStats: LeadSourceStats;
  statusStats: LeadStatusStats;
};

export type GetLeadFiltersResponse = {
  typeStats: LeadTypeStats;
  sourceStats: LeadSourceStats;
  campaignStats: LeadCampaignStats;
  statusStats: LeadStatusStats;
};

export type GetLeadFiltersResponseTest = {
  sourceStats: LeadSourceStats;
  statusStats: LeadStatusStats;
};

export type LeadTypeStats = { type: LeadType; count: number }[];
export type LeadSourceStats = { sourceId: LeadSource; count: number }[];
export type LeadCampaignStats = { type: string; name: string; count: number }[];
export type LeadStatusStats = { status: LeadStatus; count: number }[];

const genderCode = ['M', 'F'] as const;
export type GenderCodeUnion = (typeof genderCode)[number];

export type AddLeadFormData = {
  firstName?: string;
  lastName?: string;
  fullName?: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  genderCode?: GenderCodeUnion | undefined;
  birthDate?: string;
  email?: string;
  interestedCategories?: string;
};

export type CreateLeadRequest = {
  campaignCode?: string;
  isIndividual?: boolean;
  customerId?: string;
  caseId?: string;
  caseStatus?: CaseStatus;
  salutation?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  fullName?: string;
  nameExtension?: string;
  nameSuffix?: string;
  localNameLang?: string;
  localSalutation?: string;
  localFirstName?: string;
  localMiddleName?: string;
  localLastName?: string;
  localNameExtension?: string;
  localNameSuffix?: string;
  email?: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  workPhoneCountryCode?: string;
  workPhoneNumber?: string;
  homePhoneCountryCode?: string;
  homePhoneNumber?: string;
  addressStreet?: string;
  addressCity?: string;
  addressProvinceCode?: string;
  addressPostalCode?: string;
  addressCountryCode?: string;
  acceptPhoneCall?: boolean;
  acceptEmail?: boolean;
  isSmoker?: boolean;
  callAcceptDate?: string;
  preferredContactDate?: string;
  genderCode?: string;
  birthDate?: string;
  maritalStatusCode?: string;
  kidsCount?: number;
  inquireReason?: string;
  companyName?: string;
  companyDivision?: string;
  jobTitle?: string;
  occupationCode?: string;
  occupationIndustryCode?: string;
  occupationGroupCode?: string;
  occupationClassCode?: string;
  annualIncomeRangeLower?: string;
  annualIncomeRangeUpper?: string;
  monthlyIncomeRangeLower?: string;
  monthlyIncomeRangeUpper?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
  utmTerm?: string;
  utmContent?: string;
  interestedCategories?: string[];
  extra?: {
    alts_blts_ref_num?: string;
    bank_customer_id?: string | null;
    referrer_code?: string | undefined;
    service_branch?: string | null | undefined;
  };
};

export type UpdateLeadRequest = {
  salutation?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  nameExtension?: string;
  nameSuffix?: string;
  localNameLang?: string;
  localSalutation?: string;
  localFirstName?: string;
  localMiddleName?: string;
  localLastName?: string;
  localNameExtension?: string;
  localNameSuffix?: string;
  email?: string;
  mobilePhoneCountryCode?: string;
  mobilePhoneNumber?: string;
  workPhoneCountryCode?: string;
  workPhoneNumber?: string;
  homePhoneCountryCode?: string;
  homePhoneNumber?: string;
  addressStreet?: string;
  addressCity?: string;
  addressProvinceCode?: string;
  addressPostalCode?: string;
  addressCountryCode?: string;
  acceptPhoneCall?: boolean;
  acceptEmail?: boolean;
  isSmoker?: boolean;
  callAcceptDate?: string;
  preferredContactDate?: string;
  genderCode?: string;
  birthDate?: string;
  maritalStatusCode?: string;
  kidsCount?: number;
  inquireReason?: string;
  companyName?: string;
  companyDivision?: string;
  jobTitle?: string;
  occupationCode?: string;
  occupationIndustryCode?: string;
  occupationGroupCode?: string;
  occupationClassCode?: string;
  annualIncomeRangeLower?: string;
  annualIncomeRangeUpper?: string;
  monthlyIncomeRangeLower?: string;
  monthlyIncomeRangeUpper?: string;
};

export type LookupLeadRequest = {
  sourceId?: string;
  isIndividual?: boolean;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  localLastName?: string;
  mobilePhoneCountryCode?: string;
  mobilePhoneNumber?: string;
  acceptPhoneCall?: boolean;
  acceptEmail?: boolean;
  isSmoker?: boolean;
  extra?: any;
  interestedCategories?: string[];
};

export type LeadSourceMappingRegionSpecific = {
  ph: LeadSource;
  my: MYSLeadSource;
};

export type AddEntityFormData = {
  companyName: string;
  occupationIndustryCode: string;
  email: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  homePhoneCountryCode: string;
  homePhoneNumber: string;
  salutation: string;
  firstName: string;
  middleName: string;
  lastName: string;
  nameExtension: string;
  jobTitle: string;
  extra: {
    alts_blts_ref_num: string;
    service_branch: string;
    referrer_code: string;
    bank_customer_id: string;
  };
};

export type CreateEntityRequest = {
  isIndividual: boolean;
  companyName?: string;
  occupationIndustryCode?: string;
  email?: string;
  mobilePhoneCountryCode?: string;
  mobilePhoneNumber?: string;
  homePhoneCountryCode?: string;
  homePhoneNumber?: string;
  salutation?: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  nameExtension?: string;
  jobTitle?: string;
  extra?:
    | {
        alts_blts_ref_num?: string;
        service_branch?: string;
        referrer_code?: string;
        bank_customer_id?: string;
      }
    | object;
};

export type LookupEntityRequest = CreateEntityRequest & {
  sourceId: 'CUBE';
  acceptPhoneCall?: boolean;
  acceptEmail?: boolean;
  isSmoker?: boolean;
};

export type LeadFormValues = {
  fullName: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  genderCode?: GenderCodeUnion;
  birthDate: string;
  email?: string;
  interestedCategories?: string;
};

export type EntityLeadFormValues = {
  companyName: string;
  firstName: string;
  mobilePhoneCountryCode: string;
  mobilePhoneNumber: string;
  email?: string;
  natureOfBusiness?: string;
};

export type DropDownItem = {
  id: string;
  value: string;
  country?: string;
};

export type TypesOfNewLeadForm = 'INDIVIDUAL' | 'ENTITY';

export const leadProfileTabsArray = [
  'profile',
  'si',
  'inApp',
  'activitiesRecord',
] as const;

export const individualLeadProfileTabsArray = [
  'profile',
  'cff',
  'si',
  'inApp',
  'activitiesRecord',
] as const;

export type LeadPending = {
  customersName: string;
  Gender: string;
  Age: string;
  MobileNumber: number;
  registerDate: Date;
};

export type LeadProfileTabs = (typeof leadProfileTabsArray)[number];
export type IndividualLeadProfileTabs =
  (typeof individualLeadProfileTabsArray)[number];

export type LeadListToolBarProps = {
  toggleSort: () => void;
  hasFilter: boolean;
  sortingText: string;
  filterText: string;
  numberOfLeads: number;
  curTab: string;
  sortByNewest: boolean;
};

export type MyLeadParamList = {
  TodayLeads: undefined;
  OtherLeads: undefined;
};

export type MyLeadRoutes = keyof MyLeadParamList;

export type MyLeadScreenProps<Screen extends keyof MyLeadParamList> =
  CompositeScreenProps<
    NativeStackScreenProps<MyLeadParamList, Screen>,
    NativeStackScreenProps<RootStackParamList>
  >;

export enum leadResponseEnum {
  I = 'interested',
  N = 'noAnswer',
  NI = 'notInterested',
  D = 'deferred',
}

interface LeadClone extends Lead {
  checked?: boolean;
}
