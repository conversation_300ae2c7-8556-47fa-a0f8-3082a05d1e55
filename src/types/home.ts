import { SvgIconProps } from 'cube-ui-components';

export type TitleKeys = 'home-dashboard-reorder' | 'home-tasks-reorder';

export interface ReorderItem<T> {
  key: T;
  type: string;
  label: string;
}

export type DashboardCardsKeys =
  | 'MarketingCard'
  | 'TrainerGuruCard'
  | 'FWDNewsCard'
  | 'PerformanceCard'
  | 'BusinessOpportunityCard';

export type TodayTasksSectionsKeys =
  | 'AgencyConfidentialReportSection'
  | 'BirthdaySection'
  | 'ContactLeadsSection'
  | 'PaymentRemindersSection'
  | 'PolicyIssuesSection';

export type TodayTasksConfig = {
  showCount: boolean;
};

export type TodayTasksInfo = ReorderItem<TodayTasksSectionsKeys>;
export type DashboardInfo = ReorderItem<DashboardCardsKeys>;

const SHORTCUTS_CONFIG_TYPE = [
  'addNewLead',
  'fna',
  'createQuickQuote',
] as const;
export type ShortcutsConfigType = (typeof SHORTCUTS_CONFIG_TYPE)[number];
export type ShortcutsConfigArray = {
  type: ShortcutsConfigType;
  Icon: (props: SvgIconProps) => JSX.Element;
  IconInactive: (props: SvgIconProps) => JSX.Element;
  isDisabled: boolean;
  label: string;
  tabletLabel: string;
  onShortcutPress: () => void;
}[];

export interface NotificationMessage {
  id: string;
  agentId: string;
  title: string;
  body: string;
  data?: {
    type?: NotificationType;
    name?: string;
    message?: string;
    customerId?: number;
    taskId?: number;
    policyNo?: string;
    caseId?: string;
  };
  language: string;
  isRead: boolean;
  translation: {
    title: {
      en: string;
      th?: string;
    };
    body: {
      en: string;
      th?: string;
    };
  };
  createdAt: string;
}

export type NotificationType =
  | 'FWDNews'
  | 'OverdueReminder'
  | 'DueReminder'
  | 'Birthday'
  | 'PendingRequirements'
  | 'Underwriting'
  | 'NewLead'
  | 'NewTasks'
  | 'PolicyApproved'
  | 'ApplicationPending'
  | 'ApplicationApproved'
  | 'ApplicationRejected'
  | 'ApplicationExpired'
  | 'ApplicationPendingReminder'
  | 'LeadTrackingReminder'
  | 'RemoteSellingConfirm';
