export type Person = {
  name: Name;
  othername?: Partial<Name>;
  dateOfBirth: {
    date: string; // yyyy-MM-dd
    isDobMonthZero?: boolean;
    isDobDayZero?: boolean;
    calendarType?: string;
  };
  age?: number;
  countryOfBirth?: string;
  placeOfBirth?: string;
  stateOfBirth?: string;
  cityOfBirth?: string;
  cityNameOfBirth?: string;
  gender: Gender;
  religion?: Religion;
  isMysPr?: boolean;
  isSmoker?: boolean;
  nationality?: string;
  registrations?: Registration[];
  occupation?: Occupation;
  locale?: string;
  casePersonalInfo?: string; // TODO: what is this
  maritalStatus?: MaritalStatus;
  industryAffiliationQuestion1?: string;
  exactAffiliationOption1?: string[];
  industryAffiliationQuestion2?: string;
  exactAffiliationOption2?: string[];
  purposeOfInsurance?: string;
  otherPurpose?: string;
  sourceOfFund?: string;
  sourcesOfFund?: string[];
  sourcesOfWealth?: string[];
  otherSource?: string;
  sourceOfPremium?: string;
  policyDeliveryMode?: string;
  greenCard?: GreenCard;
  ethnicityCode?: string;
  isOcrSuccess?: boolean;
  sourceOfPremiumCountry?: string;
  typeOfCustomer?: string;
  staffId?: string;
  purposeOfTransaction?: string;
  livenessCheck?: {
    isLivenessSuccess?: boolean;
    isFaceMatched?: boolean;
  };
  processPlace?: string;
  dukcapilServiceStatus?: string;
};

export type Entity = {
  name: string;
  natureOfBusiness?: string;
  representative?: {
    name?: Name;
    positionHeld?: string;
    designation?: string;
    gender?: Gender;
    registrations?: Registration[];
    dateOfBirth?: {
      date: string;
    };
    age?: number;
    nationality?: string;
    isMysPr?: boolean;
  };
  purposeOfInsurance?: string;
  otherPurpose?: string;
  sourceOfFund?: string;
  otherSource?: string;
  sourceOfPremium?: string;
  greenCard?: GreenCard;
  occupation?: Occupation;
  registrationNumber?: string;
  registrationOldNo?: string;
  dateOfRegistration?: string;
  taxRegistered?: string;
  taxIdNumber?: string;
  taxIdentificationNumber?: string;
  leadSource?: string;
};

export type Name = {
  title?: string;
  firstName?: string;
  middleName?: string;
  fullName?: string;
  lastName?: string;
  extensionName?: string;
};

export type Registration = {
  id: string;
  idType: string;
  type: 'PRIMARY' | 'DEFAULT' | 'ADDITIONAL';
  expiry?: string | null;
};

export enum Gender {
  MALE = 'M',
  FEMALE = 'F',
}

export enum Title {
  MR = 'MR',
  MS = 'MS',
}

export enum SmokingHabit {
  SMOKER = 'S',
  NONSMOKER = 'N',
}

export enum TaxType {
  BUSINESS = 'B',
  PRIVATE = 'P',
}

export enum MaritalStatus {
  SINGLE = 'S',
  MARRIED = 'M',
  DIVORCED = 'D',
  WIDOWED = 'W',
  SEPARATED = 'X',
}

export enum Religion {
  ISLAM = 'I',
  Budha = 'B',
  Hindu = 'H',
  Christian = 'C',
  Others = 'O',
}

export type GreenCard = {
  isGreenCardHolder: boolean;
  passportGreenCardNo?: string;
  taxId?: string;
  usaAddress?: string;
};

export type Occupation = {
  id?: string;
  nameOfEmployer?: string;
  natureOfWork: string;
  natureOfBusiness?: string;
  natureOfSubWork?: string;
  occupationDescription?: string;
  duties?: string;
  income?: number; // TODO: should it be number?
  incomeRange?: string;
  occupationSector?: string;
};
