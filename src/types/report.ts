export type ReportStatement = 'commission' | 'tax' | 'persistency';

export type ReportStatementTitle =
  | 'Commission statement'
  | 'Tax statement'
  | 'Persistency';

export type ReportListItem = {
  agentCode: string;
  agentName: string;
  agentStatus?: string;
  policyNumber?: string;
  uploadDate: string;
  documentCode: string;
  documentType: string;
  documentIndex: string;
};

export type PolicyAnniversaryItem = {
  firstName: string;
  policyDate: string;
  lastName: string;
  policyNumber: string;
  middleName: string;
  agentCode: string;
};

export interface CreditCardExpirationItem {
  policyNumber: string;
  policyHolderName: string;
  agentCode: string;
  agentName: string;
  cardHolderName: string;
  cardNumber: string;
  expiredDate: string;
  premiumDueDate: string;
  issueDate: string;
  currency: string;
  basicPremium: number;
  riderPremium: number;
  rtu: number;
  frequency: string;
  paymentMethod: string;
  product: string;
  insuredName: string;
}

export type UnsuccessfulAdaAcaItem = {
  policyNumber: string;
  policyHolderName: string;
  agentCode: string;
  agentName: string;
  adaAcaActivation: string;
  failureReason: string;
  transactionDate: string;
  product: string;
  insuredName: string;
  premiumDueDate: string;
  issueDate: string;
  currency: string;
  amount: number;
  riderPremium: number;
  frequency: string;
  paymentMethod: string;
  accountHolderName: string;
  cardNumber: string;
  bankName: string;
};

export type ReportListResponseData = {
  loginId?: string;
  loginIdType?: string;
  requestId?: string;
  documentList?: ReportListItem[];
};

export type reportDownloadRequestObj = {
  documentIndex?: string;
  documentCode: string;
  tenant: string;
  uploadDate?: string;
};

/**
 * For:
 * Lapsed policies report
 * Premium received report
 */
export type ReportResponseData = {
  summary: {
    caseCount: number;
    sum: number;
  };
  data: ReportItem[];
};

/**
 * For:
 * Lapsed policies report
 * Premium received report
 * Credit card expiration report
 * Unsuccessful ADA/ACA report
 */
export interface ReportItem {
  policyNumber: string;
  policyHolderName: string;
  agentCode: string;
  agentName: string;
  branch?: string;
  status?: string;
  premiumDueDate?: string;
  product: string;
  insuredName: string;
  submissionDate?: string;
  issueDate: string;
  surrenderDate?: string;
  transactionDate?: string;
  currency: string;
  basicPremium?: number;
  singlePremium?: number;
  riderPremium: number;
  salesAPE?: number;
  rtu?: number;
  frequency: string;
  sumInsured?: number;
  paymentMethod: string;
  adaAcaActivation?: string;
  failureReason?: string;
  amount?: number;
  accountHolderName?: string;
  cardNumber?: string;
  bankName?: string;
  description?: string;
  mandateStatus?: string;
  tranReference?: string;
}

export type PolicyHolderSearchType = 'all' | 'individual';
export type IndividualFocusedChipType = 'policyHolderName' | 'policyNumber';

export type PolicyInfoSearch = {
  searchType: PolicyHolderSearchType;
  focusedChip: IndividualFocusedChipType;
  policyHolderName: string;
  policyNumber: string;
};

/**
 * Lapse  report
 */
export type Status = 'LAPSED' | 'PREMIUM_HOLIDAY' | 'ANTICIPATED_LAPSE';

/**
 * Premium received report
 */
export type Duration =
  | 'thisMonth'
  | 'lastThirtyDays'
  | 'lastSixMonths'
  | 'custom'; // Transaction date duration (manual input)

/**
 * Credit card expiration
 * Unsuccessful ADA/ACA
 */
export type DatePeriodType = 'ISSUEDATE' | 'TRANSACTIONDATE' | 'DUEDATE';

export type DatePeriodSearch = {
  datePeriodType: DatePeriodType;
  from: string;
  to: string;
};
