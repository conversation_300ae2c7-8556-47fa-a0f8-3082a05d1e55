import {
  NavigationProp,
  NavigatorScreenParams,
} from '@react-navigation/native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import Animated from 'react-native-reanimated';
import { Contact } from 'expo-contacts';
import {
  PaymentGatewayParams,
  PaymentResultParams,
} from 'features/eApp/types/paymentTypes';
import {
  DataPrivacyReviewParams,
  FatcaReviewRouteParams,
  HealthQuestionsReviewParams,
  ImageListParams,
  PersonalInfoReviewParams,
  PolicyReplacementReviewParams,
  RiskProfileReviewParams,
} from 'features/eApp/types/reviewTypes';
import { ACRForm } from 'features/eApp/validations/acrValidation';
import { PersonalInformationReviewParams } from 'features/eAppV2/common/types/reviewTypes';
import { ApplicationInfo } from 'features/eRecruit/ph/types';
import {
  ConversationData,
  SkillDetail,
} from 'features/ecoach/api/conversationApi';
import { DifficultType } from 'features/ecoach/screens/SelectDifficulty';
import { SaleIllustrationTargetType } from 'features/home/<USER>/CreateSaleIllustrationModal/types';
import { PdfViewerOptions } from 'features/pdfViewer/components/PdfViewer.old';
import { PerfTabsConfigKeys } from 'features/performance/config';
import { RankingData } from 'features/teamManagement/types';
import { ReactNode } from 'react';
import { StackAnimationTypes } from 'react-native-screens';
import {
  ApplicationStageKeys,
  CandidateProfileResponds,
  CubeStatusKeys,
  ReviewAgentApplicationListResponds,
} from 'types/eRecruit';
import {
  CaseListOverView,
  NBPolicyLeaderReviewSubmissionStatus,
  NBStatus,
  NBSubmissionForReviewStatus,
  PolicyInfoFromList,
  PolicyStatus,
  POSStatus,
} from 'types/policy';
import { ProductId } from 'types/products';
import { ReportStatement } from 'types/report';
import { AgentTarget, TeamPerformanceViewType } from 'types/team';
import { EntityInsured } from './entityInsured';
import { Lead } from './lead';
import { AllContentStackNewsTagTypes } from './news';
import { ProductFlowType } from 'features/ecoach/store/ecouchSlice';
import { ModulePermissionKeys } from 'types';
import { OcrRole } from './event';
import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';

export type RootStackParamListMap = {
  ph: PhRootStackParamList;
  my: MyRootStackParamList;
  ib: IbRootStackParamList;
  id: IdRootStackParamList;
};

export type PhRootStackParamList = RootStackParamList;
export type MyRootStackParamList = MYMainStackParamList &
  IrisStackParamList &
  ProposalParamList &
  PdfViewerParamList &
  TeamManagementParamList &
  LeadProfileParamList &
  AiBotParamList &
  AffiliateParamList;
export type IbRootStackParamList = MyRootStackParamList & RootStackParamList;
export type IdRootStackParamList = RootStackParamList & ERecruitParamList;

export type ERecruitApplicationStatusKeys =
  | 'inProgress'
  | 'approved'
  | 'rejected'
  | 'reviewApplication';
type MYMainStackParamList = Omit<
  MainStackParamList,
  'ReportGenerationListScreen' | 'DocumentsScreen'
> &
  ERecruitParamList;

type ERecruitParamList = {
  ERecruitApplication: { registrationStagingId?: string } | undefined;
  ERecruitCandidateProfile: ERecruitCandidateProfileParamList;
  ERecruitReviewAgentsSubmission: undefined;
  ERecruitReviewAgentsApplication: ReviewAgentApplicationListResponds & {
    fromScreen?: string;
  };
  ERecruitCheckApplication: CandidateProfileResponds;
  ERecruitSignature: { registrationStagingId?: string } | undefined;
  ERecruitRemoteSignature: { registrationStagingId?: string } | undefined;
  ERecruitCheckApplicationRemoteSignature:
    | { registrationStagingId?: string }
    | undefined;
} & ERecruitApplicationList;

// export type RootStackParamList = MainStackParamList &
//   IrisStackParamList &
//   ProposalParamList &
//   PdfViewerParamList &
//   TeamManagementParamList &
//   LeadProfileParamList;

export type RootStackParamList = MainStackParamList &
  IrisStackParamList &
  ProposalParamList &
  PdfViewerParamList &
  TeamManagementParamList &
  LeadProfileParamList &
  AddLeadOrEntityParamList &
  RecruitmentStackParamList &
  AiBotParamList &
  EcoachParamList &
  AffiliateParamList;

export type MainStackParamList = OtherMenuStackParamList & {
  Main: NavigatorScreenParams<MainTabParamList> | undefined;
  // Main:
  //   | {
  //       screen?: keyof MainTabParamList;
  //     }
  //   | undefined;
  Login: undefined;
  // Search: undefined;
  LeadProfile: {
    id: string;
    customerId?: string;
    isIndividualLead?: boolean;
    isMarketingLead?: boolean;
  };
  AiBot: NavigatorScreenParams<AiBotParamList>;
  Affiliate: NavigatorScreenParams<AffiliateParamList>;
  Ecoach: NavigatorScreenParams<EcoachParamList>;
  BirthdayTasksScreen: undefined;
  BirthdayCardScreen:
    | undefined
    | { taskId?: number; customerId?: number; customerName?: string };
  Household: undefined;
  GapAnalysis: undefined;
  ProductDetails: undefined;
  SalesIllustrationForm:
    | {
        pid?: string;
        from?: 'saved_proposals' | 'quick_quote_form' | 'lead_profile';
        lockMainInsuredToProposer?: boolean;
        goBack?: boolean;
      }
    | undefined;
  RPQQuestionForm: { leadId?: string } | undefined;
  RPQResult: { isConfirmed?: boolean } | undefined;
  PoliciesNewBusiness: NavigatorScreenParams<NewBusinessParamList>;
  PoliciesPOS: NavigatorScreenParams<PosPramList>;
  ContactBook: { routeName: string };
  ProductSelection: undefined;
  CoverageDetailsScreen:
    | undefined
    | {
        leadId?: string;
        customerId?: string;
        targetType?: SaleIllustrationTargetType;
      };
  TeamListPerformance: { viewType: TeamPerformanceViewType };
  TeamPerformanceDetails: undefined;
  TeamOperation: undefined;
  AgentPolicies: { focusedTabBarRouteName: string; agentCode: string };
  AgentPoliciesReview: undefined;
  PoliciesSelectApprove: undefined;
  TeamLeadsConversion: undefined;
  TeamTarget: undefined;
  TeamView: undefined;
  TeamIndividualProfile: { agentName: string; agentCode: string };
  TeamIndividualPersistency: { agentName: string; agentCode: string };
  AgentPerformance: undefined;
  EApp: undefined;
  Ocr: { role: OcrRole };
  SimulationTable: { pid: ProductId };
  ACR:
    | {
        defaultValues: ACRForm;
      }
    | undefined;
  AgentProfile: undefined;
  Setting: undefined;
  ImageList: ImageListParams;
  SellerExpImageList: ImageListParams;
  PersonalInfoReview: PersonalInfoReviewParams;
  EntityInfoReview: PersonalInfoReviewParams;
  PolicyReplacementReview: PolicyReplacementReviewParams;
  DataPrivacyReview: DataPrivacyReviewParams;
  RiskProfileReview: RiskProfileReviewParams;
  HealthQuestionsReview: HealthQuestionsReviewParams;
  PersonalInformationReview: PersonalInformationReviewParams;
  FatcaReview: FatcaReviewRouteParams;
  RocReview: undefined;
  RopReview: undefined;
  PdpReview: undefined;
  TakeOverReview: undefined;
  DisclosureReview: undefined;
  ChargePremiumAuthorizationReview: undefined;
  InsuranceCoverageReview: undefined;
  EPolicyAndENoticesReview: undefined;
  PaymentResult: PaymentResultParams;
  PaymentProcessing: PaymentGatewayParams;
  PaymentGateway: PaymentGatewayParams;
  Submission: undefined;
  SubmissionFailed: undefined;
  PersonalDetails: undefined;
  PolicyOwnerSignature: undefined;
  PolicyOwnerSignatureBottom: undefined;
  PolicyOwnerSignatureFade: undefined;
  InsuredSignature: undefined | { index: number; totalSteps?: number };
  SolicitingOfficerSignature:
    | undefined
    | { index: number; totalSteps?: number };
  AgentSignature: undefined | { index: number; totalSteps?: number };
  PlaceOfSigning: { value: string; onChange: (text: string) => void };
  RemotePolicyOwnerSignature: undefined;
  RemotePolicyOwnerSignatureBottom: undefined;
  RemotePolicyOwnerSignatureFade: undefined;
  RemoteInsuredSignature: undefined | { index: number; totalSteps?: number };
  RemoteAgentSignature: undefined | { index: number; totalSteps?: number };
  BadgesCollection: undefined;
  FundIllustrationForm: undefined;
  Fna: undefined;
  SavingsGoal: { tab?: string } | undefined;
  ProtectionGoal: { tab?: string } | undefined;
  TeamMemberTargetsEdit: undefined;
  TeamTargetsEdit: undefined;
  ProductRecommendation: undefined;
  LogActivity: { leadId: string };
  DocumentsScreen: undefined;
  CustomerFactFind: undefined;
  CreateInsured: { leadId: string };
  InsuredDetails: { data: EntityInsured };
  SearchExistingLead: { onSelect: (lead: Lead) => void };
  ReportGenerationListScreen: { reportStatement: ReportStatement };
  PolicyAnniversaryListScreen: undefined;
  DueDateReportScreen: {
    to: string;
    from: string;
    agent: MemberInfo;
    team: boolean;
  };
  ApplicationNotProceedScreen: {
    to: string;
    from: string;
    agent: MemberInfo;
    team: boolean;
  };
  InquiriesReportScreen: {
    to: string;
    from: string;
    agent: MemberInfo;
    team: boolean;
    biro: boolean;
    status: { code: string; meaning: string }[];
  };
  TransactionReportScreen: {
    to: string;
    from: string;
    agent: MemberInfo;
    team: boolean;
    policyNumber: string;
  };
  LapsedPoliciesReportScreen: undefined;
  PremiumReceivedReportScreen: undefined;
  CreditCardExpirationReportScreen: undefined;
  UnsuccessfulAdaAcaScreen: undefined;
  CandidatesSearch: { isShowFilter?: boolean } | undefined;
  StartClaim: undefined;
  agentAssistDetail: { data: CaseListOverView };
  PerformanceTarget: undefined;
  PassionSurveyReview: undefined;
  UnderwritingDecisionReview: undefined;
  ClosingAgentReview: undefined;
  StatementAndPowerOfAttorneyReview: undefined;
  StatementOfTruthReview: undefined;
  TemporaryCoverageReview: undefined;
};

export type PolicyDomains = 'NEW_BUSINESS' | 'POS';

export type IRISPolicyDetailParam = {
  type: 'policy';
  policyId: string;
  status: PolicyStatus | null;
  policyInfo?: PolicyInfoFromList;
  // * For Policy Details, if coming from pending / inprogress tabs, show the outstanding doc card
  fromFrontendTab?: NBStatus;
};

export type IrisStackParamList = {
  FWDNews: undefined;
  PRQ: undefined;
  FWDNewsBookmarks: undefined;
  FWDNewsDetails: {
    id: string;
    title: string;
    type_of_news_post: AllContentStackNewsTagTypes;
  };
  EliteAgencyDetails: undefined;
  EliteAgencyRequirements: undefined;
  EliteAgencyBenefits: undefined;
  CampaignsDetails: { title: string; percent: number };
  AddNewLeadOrEntity: undefined;
  AddCandidate: undefined;
  ProfileDetails: undefined;
  CustomerProfileDetails: { customerId: string };
  LeadAndCustomerSearch: undefined;
  RecognitionDetails: {
    title: string;
    percent: number;
    agentCode?: string | null;
    agentName?: string | null;
  };
  PerformanceDetails: { type: PerfTabsConfigKeys };
  ExistingPolicyDetail: IRISPolicyDetailParam | PolicyDetailsByCaseParam;
  NotificationScreen: undefined;
  SummitClubsDetails: undefined;
};

type PolicyDetailsByCaseParam = {
  type: 'case';
  caseId: string;
  reviewStatus: NBSubmissionForReviewStatus;
  submissionStatus: NBPolicyLeaderReviewSubmissionStatus | '';
  isLeaderReview?: boolean;
  fromFrontendTab?: NBStatus;
  submissionDate?: string;
};

type ProposalParamList = {
  ProposalTable: { params: any };
};
//'FWD' | 'FWBM' | 'AFWBM' | 'FWM' | 'AFWM' | 'FWP' | 'FWA'
type TeamManagementParamList = {
  tm: { title: string; tabIndex: number };
  fwd: { title: string; tabIndex: number };
  afwd: { title: string; tabIndex: number };
  fwm: { title: string; tabIndex: number };
  afwm: { title: string; tabIndex: number };
  fwo: { title: string; tabIndex: number };
  afwo: { title: string; tabIndex: number };
  fwp: { title: string; tabIndex: number };
  fwa: { title: string; tabIndex: number };
  fwbm: { title: string; tabIndex: number };
  afwbm: { title: string; tabIndex: number };
};

export type MainTabParamList = ShownMainTabParamList &
  OtherMenuTabParamList &
  OtherMenuStackParamList;

export type NewApplicationFormParmaList = {
  newApplicationPersonalDetails: { registrationStagingId?: string };
  newApplicationOccupationDetails: { registrationStagingId?: string };
  newApplicationOtherDetails: { registrationStagingId?: string };
  newApplicationDocuments: { registrationStagingId?: string };
  newApplicationConsent: { registrationStagingId?: string };
};

export type IDNNewApplicationFormParmaList = Omit<
  NewApplicationFormParmaList,
  'newApplicationOccupationDetails'
> &
  NewApplicationReviewInfoParam;

export type NewApplicationReviewInfoParam = {
  newApplicationReviewInfo: { registrationStagingId?: string };
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackScreenProps<RootStackParamList, T>;

export type BusinessOppCardPreFilters = 'inProgress' | 'inSI' | 'inApplication';

export type ShownMainTabParamList = {
  Home: { openLeadForm?: boolean } | undefined;
  Lead: undefined;
  Proposals: { preFilter: BusinessOppCardPreFilters } | undefined;
  AiBot?: {
    restoreSessionID: string;
  };
  Policies: NavigatorScreenParams<PoliciesTabParamList> | undefined;
  myLMS: undefined;
  Merchandise: undefined;
  Others: undefined;
};

export type OtherMenuTabParamList = {
  Customers: undefined;
  Performance: undefined;
  ReportGeneration: undefined;
  Document: undefined;
  TeamManagement: undefined;
  ERecruit: { shareUrl: string; shareMessage: string } | undefined;
  Affiliate: undefined;
  Policies: NavigatorScreenParams<PoliciesTabParamList> | undefined;
  AgentPolicies: { focusedTabBarRouteName: string; agentCode: string };
  TrainerAiBot: undefined;
  AgentAssist: undefined;
  Proposals: undefined;
};

export type OtherMenuStackParamList = {
  SocialMarketing: undefined;
} & SocialMarketingParamList;

export type ScreenComponentType = React.ComponentType<object>;

export type PoliciesTabParamList = {
  NewBusiness:
    | NavigatorScreenParams<NBDrawerParamList>
    | NavigatorScreenParams<NBDrawerParamListPH>
    | undefined;
  POS: undefined;
  // Claims: undefined;
};

export type NBDrawerParamList = {
  nbPending: undefined;
  nbSubmissionForReview: undefined;
  nbLeaderReview: undefined;
  issued: undefined;
};
export type NBDrawerParamListPH = {
  submission: undefined;
  pending: undefined;
  issued: undefined;
};
export type POSDrawerParamListPH = {
  due: undefined;
  overdue: undefined;
  freelook: undefined;
};
export type PoliciesDetailTabParamListTablet = {
  outstandItem: {
    status?: PolicyStatus;
    policyNo: string;
  };
  applicationTracker: {
    status?: PolicyStatus;
    policyNo: string;
  };
  policyDetails: {
    status?: PolicyStatus;
    policyNo: string;
  };
};

export type PoliciesDetailTabParamList = {
  PendingRequirements: {
    status?: PolicyStatus | null;
    policyNo: string;
  };
  PolicyOverview: {
    status?: PolicyStatus | null;
    policyNo: string;
  };
  Coverage: { status?: PolicyStatus | null; policyNo: string };
  Policy: { status?: PolicyStatus | null; policyNo: string };
  Account: { status?: PolicyStatus | null; policyNo: string };
  Product: { status?: PolicyStatus | null; policyNo: string };
  Beneficiary: { status?: PolicyStatus | null; policyNo: string };
  FundInfo: { status?: PolicyStatus | null; policyNo: string };
  History: { status?: PolicyStatus | null; policyNo: string };
};

export type TopFiveTeamTabParamList = {
  FWM: { scrollY?: Animated.SharedValue<number> };
  AFWM: { scrollY?: Animated.SharedValue<number> };
  FWO: { scrollY?: Animated.SharedValue<number> };
  AFWO: { scrollY?: Animated.SharedValue<number> };
};

export type NewBusinessParamList = {
  NBList: { status: NBStatus; viewingAgentCode: string | undefined };
  NBDetail: IRISPolicyDetailParam | PolicyDetailsByCaseParam;
};

export type AiBotParamList = {
  AiBotHistory: undefined;
  AiBotPromptLibrary: undefined;
  AiBotMaintenance: undefined;
  AiBotFeedback: undefined;
  AiBotTable: { tableNode: ReactNode[] };
  AiBotChat: undefined;
};

export type AffiliateParamList = {
  AffiliateScreen: undefined;
  AffiliateProfile: undefined;
  AffiliatePostDetails: { id: number };
};
export type EcoachParamList = {
  EcoachHome: undefined;
  Splash: undefined;
  UserProfile: undefined;
  SelectPolicy: {
    productFlowType: ProductFlowType;
  };
  SelectDifficulty: {
    productFlowType: ProductFlowType;
    productSelectionCode: string;
  };
  GuideLinesPage: {
    productFlowType: ProductFlowType;
    productSelectionCode: string;
    difficultType: DifficultType;
  };
  VideoCallPage: {
    productFlowType: ProductFlowType;
    productSelectionCode: string;
    difficultType: DifficultType;
  };
  AppointmentSummary: {
    conversationId: string;
    session?: ConversationData;
  };
  AppointmentSummaryTablet: {
    conversationId: string;
    session?: ConversationData;
  };

  Summary: {
    conversationId: string;
    session?: ConversationData;
    reTake?: () => void;
  };
  SummaryTablet: {
    conversationId: string;
    session?: ConversationData;
    reTake?: () => void;
  };
  DetailSummary: {
    skillName: string;
    skillSet: { [key: string]: SkillDetail }[];
  };
  DetailSummaryTablet: {
    skillName: string;
    skillSet: { [key: string]: SkillDetail }[];
  };
  SessionHistory: {
    activeTabIndex?: number;
  };
  OverallFeedback: undefined;
  WatchVideoPage: {
    url: string;
  };
};

export type ERecruitApplicationList = {
  ERecruitApplicationStatus: { status: CubeStatusKeys | undefined };
};

export type ERecruitAppCheckParamList = {
  ERecruitCheckApplication: CandidateProfileResponds;
};

export type ERecruitReviewAgentsApplicationParamList = {
  ERecruitReviewAgentsApplication: ReviewAgentApplicationListResponds & {
    fromScreen?: string;
  };
};

export type CheckApplicationScreensParamList = {
  additionalInformation: undefined;
  reviewInformation: undefined;
  documents: undefined;
};

export type ReviewAgentsApplicationParamList = {
  personalDetails: undefined;
  documents: undefined;
};

export type ERecruitCandidateProfileParamList = {
  cubeStatus: CubeStatusKeys;
  stage: ApplicationStageKeys;
  registrationId: number | null | undefined;
  registrationStagingId: number | null | undefined;
  id: number;
};

export type PosPramList = {
  POSList: {
    status: POSStatus;
    viewingAgentCode: string | undefined;
  };
  POSDetail: IRISPolicyDetailParam | PolicyDetailsByCaseParam;
};

export type PdfViewerParamList = {
  PdfViewer: PdfViewerOptions & Partial<ScreenTransitionProps>;
};

export type TeamPerformanceParamList = {
  TeamListPerformance: { viewType: TeamPerformanceViewType };
  TeamView: { data: RankingData };
  AgentPerformance: { data: RankingData };
};

export type TeamOperationParamList = {
  TeamOperation: undefined;
  AgentPolicies: {
    focusedTabBarRouteName: 'PoliciesNewBusiness' | 'PoliciesPOS';
    agentCode?: string;
    showHeader: boolean;
  };
};

export type TeamPerformanceActivitiesParamList = {
  TeamOperation: undefined;
  TeamLeadsConversion: undefined;
  TeamTarget: undefined;
  TeamTargetsEdit: undefined;
  TeamMemberTargetsEdit: {
    data: AgentTarget[];
    year: number;
  };
};

export type LeadProfileParamList = {
  Opportunities: undefined;
  FNA: undefined;
  RPQ: undefined;
  Proposals: undefined;
  Activities: undefined;
};

export type AddLeadOrEntityParamList = {
  AddNewLead: { contactInfo?: Contact };
  AddNewEntity: { contactInfo?: Contact };
};

export type RecruitmentStackParamList = {
  GYBAttendees: undefined;
  CandidateProfile: { applicationInfo: ApplicationInfo };
  ReviewCandidateApplication: { applicationInfo: ApplicationInfo };
  Materials: undefined;
  MaterialDetails: { id?: string };
  CandidatesSearch: { isShowFilter?: boolean } | undefined;
  ReviewAgentsSubmission: undefined;
  CandidateList: undefined;
};

export enum LoginFormRoutes {
  Login = 0,
  ForgetPw = 1,
}

export type LeadProfileTabParamList = {
  LeadProfile: undefined;
  ExistingPolicy: undefined;
  Opportunities: undefined;
  FNA: undefined;
  RPQ: undefined;
  Proposals: undefined;
  Activities: undefined;
  Insureds: undefined;
};

export type ScreenTransitionProps = {
  screenTransition: StackAnimationTypes;
};

export type IBTeamTopTabParamList = {
  TeamPerformance: undefined;
  SalesRecord: undefined;
};

export type MainTab = {
  name: keyof MainTabParamList;
  component?: ScreenComponentType;
  icon: ReactNode | JSX.Element;
  focusedIcon?: ReactNode | JSX.Element;
  onPress?: (
    // e: GestureResponderEvent,
    e: unknown,
    navigation?: NavigationProp<RootStackParamList>,
  ) => void;
  focusedDarkIcon?: ReactNode | JSX.Element;
  darkIcon?: ReactNode | JSX.Element;
  showHeader: boolean;
  headerTitle?: string;
  hideTitle?: boolean;
  feature?: ModulePermissionKeys;
};

export type SocialMarketingParamList = {
  SocialMarketingTemplates: undefined;
  SocialMarketingMyPosts: undefined;
  SocialMarketingCreateNew: undefined;
  SocialMarketingPostCreationReview: undefined;
};
