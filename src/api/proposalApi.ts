import { TranslationField } from 'types';
import { ProductListRequest, ProductListResponse } from 'types/products';
import { ProposalInitRequest, ProposalInitResponse } from 'types/proposalInit';
import { QuotationRequest } from 'types/quotation';
import { CubeResponse } from 'types/response';
import { cubeClient } from './cubeClient';

export type SIPdfWarnings = {
  pid?: string;
  type?: string;
  code?: string;
  id?: string | string[];
  productType?: string;
  label?: TranslationField;
  params?: {
    [k: string]: number;
  };
};

export interface SIPdfResponse {
  uploadDocument?: {
    base64: string;
    language: null;
    mimeType: 'application/pdf';
    parentId: {
      $oid: string;
    };
    docType: 'SI';
    template?: string;
  };
  warnings?: SIPdfWarnings[];
}

export type RiderListRequest = ProductListRequest;

export type RiderListResponse = {
  riderPID: string;
  riderName: string;
  riderGroup: string;
};

const endpoints = {
  getSiPdf: '/exp/st/proposal/getSiPdf',
  getProductList: '/exp/st/proposal/productList',
  initializeProposal: '/exp/st/propsal/initialize',
  getRiderList: '/exp/st/proposal/riders',
};

export const generateSIPdf = async (body: QuotationRequest) => {
  try {
    const response = await cubeClient.post<QuotationRequest, SIPdfResponse>(
      endpoints.getSiPdf,
      body,
      {
        headers: {
          tenant: 'ph',
          region: 'ph',
        },
      },
    );

    if (Array.isArray(response.warnings) && response.warnings.length > 0)
      throw response;

    return response;
  } catch (e) {
    const warnings = (e as SIPdfResponse)?.warnings;
    if (Array.isArray(warnings) && warnings.length > 0) {
      throw warnings;
    }
    throw e;
  }
};

export const initializeProposal = async (body: ProposalInitRequest) => {
  return await cubeClient.post<ProposalInitRequest, ProposalInitResponse>(
    endpoints.initializeProposal,
    body,
  );
};

export const getProductList = async (body: ProductListRequest) => {
  const response = await cubeClient.post<
    ProductListRequest,
    CubeResponse<ProductListResponse>
  >(endpoints.getProductList, body);

  return response.responseData.products;
};

const base64ImagePattern =
  /^data:(?<type>image\/(png|tiff|jpg|gif|jpeg|bmp|webp));base64,(?<data>[A-Z0-9+/=])*$/i;
export const getImage = async (url = ''): Promise<string> => {
  if (url && base64ImagePattern.test(url)) return url;

  const response = await cubeClient.get<string>(url);
  return response;
};

export const getRiderList = async (body: RiderListRequest) => {
  const response = await cubeClient.post<RiderListRequest, RiderListResponse[]>(
    endpoints.getRiderList,
    body,
  );

  return response;
};
