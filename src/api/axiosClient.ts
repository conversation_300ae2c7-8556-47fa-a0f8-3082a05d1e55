import axios, { AxiosInstance, CreateAxiosDefaults } from 'axios';
import { addErrorToast } from 'cube-ui-components';
import { loginWithRefreshToken, idpTokenUrl } from 'features/login/commonLogic';

import useBoundStore from 'hooks/useBoundStore';
import { jwtDecode } from 'jwt-decode';
import { CubeAuthToken } from 'types';

export type FailedQueue = {
  resolve: (value?: unknown) => void;
  reject: (error: any) => void;
}[];

export type CreateAxiosClientArgs = {
  options: CreateAxiosDefaults;
  failedQueue: FailedQueue;
};

const ERROR_STATUS = {
  upgradeRequired: 'UPGRADE_REQUIRED',
  serviceUnavailable: 'SERVICE_UNAVAILABLE',
  maintenanceMode: 'MAINTENANCE_MODE',
};

let isRefreshing = false;

const getAccessToken = () =>
  useBoundStore.getState().auth.authInfo?.accessToken;

const getRefreshToken = () =>
  useBoundStore.getState().auth.authInfo?.refreshToken;

const setTokens = (
  accessToken: string,
  refreshToken: string,
  idToken?: string,
) => {
  useBoundStore
    .getState()
    .authActions.login(accessToken, refreshToken, idToken);
};
const logout = useBoundStore.getState().authActions.logout;

const processQueue = (error: any, failedQueue: FailedQueue) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve();
    }
  });

  failedQueue = [];
};

export function createAxiosClient({
  options,
  failedQueue,
}: CreateAxiosClientArgs) {
  const client = axios.create(options);

  client.interceptors.request.use(
    config => {
      const token = getAccessToken();
      if (config.authorization !== false) {
        if (token) {
          config.headers.Authorization = 'Bearer ' + token;
        }
      }
      
      // //Remove when refresh token is available
      if (token) {
        const { exp, sub } = jwtDecode(String(token)) as CubeAuthToken;
        if (sub) {
          config.headers['x-agent-id'] = sub;
        }
        // const isExpired = isPast(new Date(exp * 1000));
        // isExpired && logout();
      }

      return config;
    },
    error => {
      return Promise.reject(error);
    },
  );

  client.interceptors.response.use(
    response => {
      // Any status code that lie within the range of 2xx cause this function to trigger
      // Do something with response data
      return response;
    },
    error => {
      console.log('🔴🔴🔴🚀🔴🔴🔴 ~ file: axiosClient.ts ~ error:', error);
      if (
        error.response?.status === 426 &&
        error.response?.data?.status === ERROR_STATUS.upgradeRequired
      ) {
        logout();
      }

      if (
        error.response?.status === 503 &&
        error.response?.data?.status === ERROR_STATUS.maintenanceMode
      ) {
        logout();
      }

      const originalRequest = error.config;
      // In "axios": "^1.1.3" there is an issue with headers, and this is the workaround.
      originalRequest.headers = JSON.parse(
        JSON.stringify(originalRequest.headers || {}),
      );
      const refreshToken = getRefreshToken();

      // If error, process all the requests in the queue and logout the user.
      const handleError = (error: any, failedQueue: FailedQueue) => {
        processQueue(error, failedQueue);
        logout();
        return Promise.reject(error);
      };

      // Refresh token conditions
      if (
        refreshToken &&
        error.response?.status === 401 &&
        // error.response.data.message === 'TokenExpiredError' &&
        originalRequest?.url !== idpTokenUrl &&
        originalRequest?._retry !== true
      ) {
        if (isRefreshing) {
          return new Promise(function (resolve, reject) {
            failedQueue.push({ resolve, reject });
          })
            .then(() => {
              return client(originalRequest);
            })
            .catch(err => {
              return Promise.reject(err);
            });
        }
        isRefreshing = true;
        originalRequest._retry = true;

        return loginWithRefreshToken(refreshToken)
          .then(
            res => {
              setTokens(
                res.data?.access_token,
                res.data?.refresh_token,
                res.data?.id_token,
              );
              processQueue(null, failedQueue);

              return client(originalRequest);
            },
            reason => {
              return handleError(reason, failedQueue);
            },
          )
          .finally(() => {
            isRefreshing = false;
          });
      }

      // Refresh token missing or expired => logout user...
      if (
        error.response?.status === 401 ||
        error.response?.data?.message === 'TokenExpiredError'
      ) {
        console.log('🚀 ~ file: axiosClient.ts ~ :', refreshToken);

        return handleError(error, failedQueue);
      }

      // Any status codes that falls outside the range of 2xx cause this function to trigger
      // Do something with response error
      // Error Toast for other errors
      // if (__DEV__) {
      //   addErrorToast([{ message: error.message }]);
      // }
      return Promise.reject(error);
    },
  );

  return client;
}
