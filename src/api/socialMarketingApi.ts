import * as FileSystem from 'expo-file-system';
import {
  SocialMarketingCreatePostPayload,
  SocialMarketingCreatePostResponse,
  SocialMarketingRegeneratePostPayload,
  SocialMarketingTemplate,
} from 'features/socialMarketing/types';
import useBoundStore from 'hooks/useBoundStore';
import { CONTENT_TYPES } from 'hooks/useContentStack';
import {
  baseUrl,
  contentStackDeliveryToken,
  contentStackEnvironment,
  contentStackKey,
} from 'utils/context';
import { ensureDirectoryExists } from 'utils/helper/fileUtils';
import apiClient from './apiClient';
import { cubeClient } from './cubeClient';

const AGENT_PROFILE_API = '/proc/agent';

async function fetchAndCacheImage(imagePath: string): Promise<string> {
  const localUri = FileSystem.cacheDirectory + imagePath;

  await ensureDirectoryExists(localUri);

  await FileSystem.downloadAsync(
    `${baseUrl}/api-gateway${imagePath}`,
    localUri,
    {
      headers: {
        Authorization: `Bearer ${
          useBoundStore.getState().auth.authInfo?.accessToken
        }`,
      },
    },
  );

  return localUri;
}

async function parsePostImageFromResponse(
  response: SocialMarketingCreatePostResponse,
): Promise<SocialMarketingCreatePostResponse> {
  try {
    if (response.mediaUrl) {
      // Fetch the image and cache it locally
      // This will return the local URI of the cached image
      // or the original URL if caching fails
      const localUrl = await fetchAndCacheImage(response.mediaUrl);
      return {
        ...response,
        localUrl,
      };
    }
  } catch (error) {
    console.error('Error fetching image:', error);
  }
  return response;
}

async function createPost(payload: SocialMarketingCreatePostPayload) {
  const response = await cubeClient.postDirectPayLoad<
    SocialMarketingCreatePostPayload,
    SocialMarketingCreatePostResponse
  >(`${AGENT_PROFILE_API}/post/create`, payload);

  return parsePostImageFromResponse(response);
}

async function regeneratePost(
  postId: string,
  payload: SocialMarketingRegeneratePostPayload,
) {
  const response = await cubeClient.postDirectPayLoad<
    SocialMarketingRegeneratePostPayload,
    SocialMarketingCreatePostResponse
  >(`${AGENT_PROFILE_API}/post/${postId}/regenerate`, payload);

  return parsePostImageFromResponse(response);
}

async function getTemplates() {
  const ENDPOINT = `https://cdn.contentstack.io/v3/content_types/${CONTENT_TYPES.IGNITE_TEMPLATE}/entries?environment=${contentStackEnvironment}`;
  const response = await apiClient.get<{
    entries: {
      template: SocialMarketingTemplate[];
    }[];
  }>(ENDPOINT, {
    headers: {
      api_key: contentStackKey,
      access_token: contentStackDeliveryToken,
    },
  });

  return response?.data?.entries?.[0]?.template ?? [];
}

export default {
  createPost,
  regeneratePost,
  getTemplates,
};
