import { StringBoolean } from 'types/quotation';
import { build, country } from 'utils/context';
import { smartClient } from './smartClient';
import { cubeClient } from './cubeClient';

type Attachment = {
  [key: string]: string;
};

export type SendEmailBody = {
  attachments: Attachment;
  emailBccRecipients: string[];
  emailBody: string;
  emailCcRecipients: string[];
  emailTitle: string;
  emailToRecipients: string[];
  isHtmlText: boolean;
  isFIB?: StringBoolean;
  extra?: Record<string, string>;
};

export type SendEmailResponse = {
  message: string;
  status: string;
};

const endpoints = {
  sendEmail:
    build === 'uat' && country === 'ib'
      ? '/capricorn-integration/email/sendEmail'
      : '/smart-integration/email/sendEmail',
};

export const sendEmail = async (body: SendEmailBody) => {
  const data = await smartClient.post<SendEmailBody, SendEmailResponse>(
    endpoints.sendEmail,
    body,
  );
  return data;
};

const SEND_ILLUSTRATION_EMAIL_ENDPOINT = '/proc/notification/email/send';

export const sendIllustrationEmail = async (body: SendEmailBody) => {
  const formData = new FormData();
  Object.entries(body.attachments).forEach(([fileName, uri]) => {
    formData.append('attachments', {
      uri,
      name: fileName,
      type: 'application/pdf',
    } as unknown as File);
  });

  formData.append('addresses', body.emailToRecipients.join(','));
  formData.append('subject', body.emailTitle);
  formData.append('extra', JSON.stringify(body.extra ?? {}));

  await cubeClient.postDirectPayLoad<FormData, SendEmailResponse>(
    SEND_ILLUSTRATION_EMAIL_ENDPOINT,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
  );

  return { status: '200' };
};
