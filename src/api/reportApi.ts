import { cubeClient } from './cubeClient';
import {
  ReportStatement,
  ReportListResponseData,
  reportDownloadRequestObj,
  Status,
  PolicyAnniversaryItem,
  UnsuccessfulAdaAcaItem,
  CreditCardExpirationItem,
  ReportResponseData,
} from 'types/report';

/**
 * Commission statement report
 * Tax statement report
 * Persistency report
 */
export async function getReportListByReportStatement(
  ReportStatement: ReportStatement,
) {
  const ENDPOINT = `/exp/api/report/list/${ReportStatement}`;
  return await cubeClient.get<ReportListResponseData>(ENDPOINT, {});
}

export async function getDownloadReport(
  reportDownloadRequestObj: reportDownloadRequestObj,
) {
  const ENDPOINT = `/exp/api/report/download`;
  return await cubeClient.get<ReportListResponseData>(ENDPOINT, {
    params: {
      documentIndex: reportDownloadRequestObj.documentIndex,
      documentCode: reportDownloadRequestObj.documentCode,
    },
    headers: {
      tenant: reportDownloadRequestObj.tenant,
    },
  });
}

/**
 * Lapsed policies report
 * Premium received report
 * Credit card expiration report
 * Unsuccessful ADA/ACA report
 * Policy anniversary
 */
export async function getInquiriesReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
  status,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  status: { code: string; meaning: string }[];
}) {
  const ENDPOINT = '/exp/api/report/policy-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: dueDateReportPolicyInfo?.policyHolderName,
      policy_number: dueDateReportPolicyInfo?.policyNumber,
      team: team,
      policy_status: status && status.map(item => item.code).join(','),
    },
  });
}

export async function getTransactionReport({
  agentId,
  from,
  to,
  transactionReportPolicyInfo,
  team,
  dateType,
}: {
  agentId?: string;
  from: string;
  to: string;
  transactionReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  dateType: string;
}) {
  const ENDPOINT = '/exp/api/report/policy-transaction-report';

  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      policy_number: transactionReportPolicyInfo?.policyNumber,
      policy_owner_name: transactionReportPolicyInfo?.policyHolderName,
      team: team,
      date_from: from,
      date_to: to,
      date_type: dateType,
    },
  });
}

export async function getDueDateReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/due-date-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: dueDateReportPolicyInfo?.policyHolderName,
      policy_number: dueDateReportPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getApplicationNotProceedReport({
  agentId,
  from,
  to,
  policyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  policyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/application-not-proceed-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: policyInfo?.policyHolderName,
      policy_number: policyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getLapseReport({
  agentId,
  from,
  to,
  status,
  lapseReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from?: string;
  to?: string;
  status: Status;
  lapseReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/lapse-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_status: status,
      policy_owner_name: lapseReportPolicyInfo?.policyHolderName,
      policy_number: lapseReportPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getPremiumReceivedReport({
  agentId,
  from,
  to,
  premiumReceivedPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  premiumReceivedPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/premium-received';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: premiumReceivedPolicyInfo?.policyHolderName,
      policy_number: premiumReceivedPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getCreditCardExpirationReport({
  agentId,
  dateFilter,
  from,
  to,
  creditCardExpiationPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  creditCardExpiationPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const { policyHolderName, policyNumber } = creditCardExpiationPolicyInfo;
  const ENDPOINT = '/exp/api/report/credit-card-expiration';
  return cubeClient.get<CreditCardExpirationItem[]>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_filter: dateFilter,
      date_from: from,
      date_to: to,
      policy_owner_name: policyHolderName,
      policy_number: policyNumber,
      team: team,
    },
  });
}

export async function getUnsuccessfulAdaAcaReport({
  agentId,
  dateFilter,
  from,
  to,
  unsuccessfulAdaAcaPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  unsuccessfulAdaAcaPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const { policyHolderName, policyNumber } = unsuccessfulAdaAcaPolicyInfo;
  const ENDPOINT = '/exp/api/report/unsuccessful-ada-aca';
  return cubeClient.get<UnsuccessfulAdaAcaItem[]>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_filter: dateFilter,
      date_from: from,
      date_to: to,
      policy_owner_name: policyHolderName,
      policy_number: policyNumber,
      team: team,
    },
  });
}

export async function getPolicyAnniversaryReport({
  from,
  to,
}: {
  from: string;
  to: string;
}) {
  const ENDPOINT = '/exp/api/report/policy-anniversary';
  return await cubeClient.get<PolicyAnniversaryItem[]>(ENDPOINT, {
    params: { date_from: from, date_to: to },
  });
}
